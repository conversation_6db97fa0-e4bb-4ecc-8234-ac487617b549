#Footer  subscription section

footer.subscription.title= Subscribe to our awesome emails.
footer.subscription.info = Get our latest offers and news straight in your inbox.
footer.subscription.error.email = Please enter an email address
footer.subscription.error.email.enter = Enter your email address
footer.subscription.error.valid.email = Please enter a valid email address
footer.subscription.success.msg = Thanks for signing up to our newsletter.


#Footer Download section
footer.download.app.text = Download our apps
footer.download.app.subtext = Shop our products and offers on-the-go.



#Help page from footer
help.search.input = Enter your search term here...
help.title.text =How can we help you today?
help.title.gift = Gift Card
help.title.tracking = Tracking and Delivery
help.title.clickAndCollect = Click & Collect
help.title.return = Returns Policy
help.title.returnProcess = Returns Process
help.title.myAccount = My Account
help.title.orders = Orders
help.title.shipping = Shipping
help.title.payments = Payments
help.title.inStore = In-Store
help.title.shukran = Shukrans
help.title.otherInformation = Other Information
help.title.BuyNow = Buy Now

#Help subsection:
help.section.giftcard.header = Gift Card
help.section.tracking.header = Tracking and Delivery
help.section.clickAndCollect.header = Click & Collect
help.section.return.header = Returns Policy
help.section.returnProcess.header = Returns Process
help.section.myAccount.header = My Account
help.section.orders.header = Orders
help.section.shipping.header = Shipping
help.section.payments.header = Payments
help.section.inStore.header = In-Store
help.section.shukran.header = Shukrans
help.section.otherInformation.header = Other Information
help.section.BuyNow.header = Buy Now


#My Account page
myAccountPage.title.text = My Account
myAccountPage.title.subTitle = Manage your profile, orders, preferences and more.
myAccountPage.profile.section = Profile
myAccountPage.profile.p = Manage your personal details.
myAccountPage.myLists.section= My Lists
myAccountPage.myList.p   = View your most wanted products.
myAccountPage.payment.section= Payment
myAccountPage.payment.p  = Manage your payment preferences.
myAccountPage.reviews.section= Reviews
myAccountPage.reviews.p  = View all your reviews
myAccountPage.myShukranCard.section= My Shukran Card
myAccountPage.myShukranCard.p  = Earn and spend valuable loyalty points for instant savings.
myAccountPage.addressBook.section = My Addresses
myAccountPage.addressBook.p = Manage your shipping and billing addresses.
myAccountPage.myCredit.section = My Credit
myAccountPage.myCredit.p = View your available credit balance.
myAccountPage.orderHistory.section   = Order History
myAccountPage.orderHistory.p   = View and track your orders.










#MyAccount reviews

myAccountPage.reviews.reviews = Reviews
myAccountPage.reviews.viewAllYourReviews = View all your reviews.
myAccountPage.reviews.status = Status
myAccountPage.reviews.pending = Pending
myAccountPage.reviews.approved = Approved
myAccountPage.reviews.rejected = Rejected
myAccountPage.reviews.outOfFiveStars = out of five stars
myAccountPage.reviews.delete = Delete
myAccountPage.reviews.areYouSure = Are you sure ?
myAccountPage.reviews.deleteReviews = Delete Review
myAccountPage.reviews.neverMind = Nevermind


#My Account DropDown

myaccount.dropdown.item.MyAccount = My Account
myaccount.dropdown.item.MyLists=My Lists
myaccount.dropdown.item.OrderHistory=Order History
myaccount.dropdown.item.MyAddresses= My Addresses
myaccount.dropdown.item.Payment=Payment
myaccount.dropdown.item.MyCredit=My Credit
myaccount.dropdown.item.Reviews=Reviews
myaccount.dropdown.item.MyShukranCard=My Shukran Card
myaccount.dropdown.item.SignOut=Sign Out


#My account - MyList
myAccountPage.myList.title = My Lists
myAccountPage.myList.createANewList = Create a new list
myAccountPage.myList.prompt.createANewList = Create a new list
myAccountPage.myList.prompt.paraOne = Create lists by occasion or need, like a friend's wedding or new apartment shopping
myAccountPage.myList.prompt.paraTwo = Share lists with friends & family, so they know just what to get!
myAccountPage.myList.prompt.createList = Create List

#My account - Profile
myAccountPage.profile.label.profile = Profile
myAccountPage.profile.label.manageYourPersonalDetails = Manage your personal details.
myAccountPage.profile.label.firstName = First Name
myAccountPage.profile.label.lastName = Last Name
myAccountPage.profile.label.yourEmail = Your Email
myAccountPage.profile.label.mobileNumber = Mobile Number
myAccountPage.profile.label.updateProfile = Update Profile



#My Account - Add address book
myAccountPage.addAddress.label.confirm.are = Are you sure ?
myAccountPage.addAddress.label.confirm.deleteAddress = Delete Address
myAccountPage.addAddress.label.confirm.neverMind = Nevermind


#My Account - Error messages
myAccountPage.addAddress.error.city = Select your City
myAccountPage.addAddress.error.area = Select your area
myAccountPage.addAddress.error.buildingName = Please enter Building Name/Villa No.
myAccountPage.addAddress.error.streetName = Please enter Floor, Apartment No.
myAccountPage.addAddress.error.fullName =Please enter your name
myAccountPage.addAddress.error.alpha.fullName = Please enter your full name using alphabet characters only
myAccountPage.addAddress.error.ten.mobileNumber= Please enter a valid 10-digit mobile number
myAccountPage.addAddress.error.mobileNumber= Please enter mobile number


#My Account - Error messages
myAccountPage.myshukran.shukran = Shukran
myAccountPage.myshukran.earnAndSpendValue = Earn and spend valuable loyalty points for instant savings.
myAccountPage.myshukran.earnAndSpendShukran =Earn and spend Shukrans when shop online.
myAccountPage.myshukran.itseasy = It\u2019s easy. Just link your shopping account with your Shukran account. If you don\u2019t have a Shukran account, we\u2019ll help you create one right away.
myAccountPage.myshukran.createAShukranAccount =Create a Shukran account
myAccountPage.myshukran.doYouHave = Do you have a Shukran account? Link it now
myAccountPage.myshukran.mobileNumber = Mobile number
myAccountPage.myshukran.shukranCard = Shukran card number
myAccountPage.myshukran.linkYourshukranAccount =Link your Shukran account
myAccountPage.myshukran.whyYouShould = Why you should definitely sign up
myAccountPage.myshukran.acceptedAtOver = Accepted at over 1,500 locations
myAccountPage.myshukran.earnAndSpendShukranAcross = Earn and spend Shukrans across all our participating brands.
myAccountPage.myshukran.55brands = 55+ brands to reward yourself
myAccountPage.myshukran.accrossFashion = Across fashion, electronics, furniture, restaurants and more.
myAccountPage.myshukran.over15Millino = Over 15 million members
myAccountPage.myshukran.bePartOf = Be part of the largest retail loyalty programme in the region.

myAccountPage.myshukran.createAShukranAcount.enterMobile = Please enter your mobile number
myAccountPage.myshukran.createAShukranAcount.thisWill= This will help us verify and setup your account.
myAccountPage.myshukran.createAShukranAcount.pleaeEnter =Please enter mobile number
myAccountPage.myshukran.createAShukranAcount.valid9 = Please enter a valid 9-digit mobile number.
myAccountPage.myshukran.createAShukranAcount.continue = Continue
myAccountPage.myshukran.createAShukranAcount.dontWantToLink = Don?t want to link? 
myAccountPage.myshukran.createAShukranAcount.cancel = Cancel


#shukran OTP verification
myAccountPage.myshukran.otp.verification=Verification
myAccountPage.myshukran.otp.confirmation=Confirmation
myAccountPage.myshukran.otp.pleaseConfirYourMobileNumber = Please confirm your mobile number
myAccountPage.myshukran.otp.pleaseEnterCode = Please enter the code we?ve sent to **** below.
myAccountPage.myshukran.otp.enterTheVerificationCode = Enter the verification code
myAccountPage.myshukran.otp.continue = Continue
myAccountPage.myshukran.otp.donotGetACode = Didn?t get a code?
myAccountPage.myshukran.otp.resend = Resend
myAccountPage.myshukran.otp.dontWant = Don?t want to link?
myAccountPage.myshukran.otp.cancel = Cancel



#Store Locator page
store.title = Store Locator
store.subTitle.text = We are near to you!
store.select.city.text = Select a City

#Header strip
header.strip.freeShipping = Free Shipping
header.strip.clickAndCollect = Click & Collect
header.strip.homeDelivery = Home Delivery
header.strip.deliveryToArea = Deliver to area
header.strip.giftCard = Gift Card
header.strip.aboutShukran = About Shukran
header.strip.joinNow = Join Now
header.strip.downLoadOurApps = Download our Apps
header.strip.storeLocator = Store Locator
header.strip.help = Help






#Sign In page UI
signIn.title = Sign In Now
signIn.header.text = Enjoy the convenience of a single account across all participating brands.
signIn.facebook.label = Sign In With Facebook
signIn.orViaEmail.text = OR VIA EMAIL
signIn.yourEmail.label= E-mail
signIn.email.input= Enter your email
signIn.email.input.error= Enter your email address
signIn.email.input.error.valid= Please Enter a valid email address
signIn.password= Password
signIn.password.input= Enter your password
signIn.password.input.error= Enter your password
signIn.email.input.remember= Remember Me
signIn.forgotpassword.link = Forgot Password
signIn.cta = Sign In
signIn.donotHaveAnAccount.text= DON\u2019T HAVE AN ACCOUNT
signIn.signupForOne.cta = Sign Up For One
signIn.shukran.link.text = Link your Shukran account to earn benefits when you shop
signIn.shukran.earnShukran.text=Earn Shukrans on every purchase
signIn.shukran.get.exclusive.text=Get exclusive offers & coupons
signIn.shukran.instant=Instant refund with Shukran Pay
signIn.shukran.use.balance=Use balance to shop online and in-store
signIn.error.invalid.credentials= Your Username or Password is incorrect.
signIn.otp.invalid.error.message = The code you have entered is incorrect. Please check and try again.
signIn.verify.your.account = Verify your account
signIn.please.enter.your.code.sent.text = Please enter the code sent to you on
signIn.confirm.text = Confirm








#Forgot password

forgotPassword.title = Forgot Your Password ?
forgotPassword.sub.title =No worries, just enter your email and we will help you reset it.

forgotPassword.email.label =E-mail
forgotPassword.placeHolder.enterYourEmail=Enter your email
forgotPassword.submit.label =Submit

forgotPassword.error.email = Enter your email address
forgotPassword.error.validEmail = Please Enter a valid email address
forgotPassword.text.remember =DID YOU REMEMBER YOUR PASSWORD?
forgotPassword.signInNow.label=Sign In Now


#Rest password Email
reset.title= Reset Password Email Sent
reset.sub.title= A reset password link is on its way to your inbox.
reset.ResendLink =Resend Link
reset.signIN =Sign In



site.comma = ,
site.affiliation = Landmarkshops.in
site.currency.symbol = â¹
site.currency.markup = <span class="rupee">Rs</span>
account.confirmation.address.added = Your address was created.
account.confirmation.address.removed = Your address was removed.
account.confirmation.address.updated = Your address was updated.
account.confirmation.default.address.changed = Your default address was updated.
account.confirmation.forgotten.password.link.sent = You have been sent an email with a link to change your password.
account.confirmation.password.updated = Your password has been changed. Please log in to access your account.
account.confirmation.payment.details.removed = Your payment details were removed.
account.confirmation.profile.updated = Your profile has been updated.
account.confirmation.signout.subtitle = Your cart will be waiting for you when you sign back in.
account.confirmation.signout.title = You have signed out of your account.
account.error.account.exists.with.email.address.subtitle = Please choose an alternative email address.
account.error.account.exists.with.email.address.title = An account already exists for email address {0}
account.error.account.not.found = No account was found for the email address provided.
account.error.login.please = You must login to access this page. Please log in with your credentials below.

address.building = Building name or number (based on your pin location)
address.building_and_room = Building Name and Room Number
address.country = Country
address.country.invalid = Please select a country
address.default = Make this my default address
address.district = District
address.district.invalid = Please enter a district
address.district_and_street = District and Street
address.error.formentry.invalid = Errors were found with the address you provided. Please check the errors below and re-submit your address.
address.firstName = First Name
address.firstName.invalid = Please enter a first name
address.furtherSubarea = Further subarea number, house number
address.furtherSubarea.invalid = Please enter further subarea number
address.lastName.invalid = Please enter a last name
address.line1 = Address Line 1
address.line1.invalid = Please enter address Line 1
address.line2 = Address Line 2
address.line2.invalid = Please enter address Line 2
address.postalcode = Postal Code
address.postcode = Post Code
address.postcode.invalid = Please enter post code
address.postcodeJP = Postal Code preceded by postal symbol
address.postcodeJP.invalid = Please enter post code preceded by postal symbol
address.prefecture = Prefecture name
address.prefecture.invalid = Please select a prefecture
address.province = Province
address.province.invalid = Please enter a province
address.regionIso.invalid = Please enter a region
address.required = Fields marked* are required
address.room = Room Number
address.room.invalid = Please enter a room number
address.selectCountry = Country
address.selectPrefecture = Prefecture
address.selectProvince = Province
address.selectState = State / Province
address.state = State / Province
address.street = Street Name with Street Number
address.subarea = Subarea
address.subarea.invalid = Please enter a subarea
address.surname = Last Name
address.title = Title
address.title.invalid = Please select a title
address.title.pleaseSelect = title
address.townCity = City
address.townCity.invalid = Please enter a City or Town
address.townJP = City, village, city ward
address.townJP.invalid = Please enter a city, village, city ward
address.zipcode = Zip / Postal Code

aria.pickupinstore.loading = Loading... Please wait...
aria.pickupinstore.storesloaded = Stores loaded

basket.add.free.gift = Add your free gift to Cart
basket.add.to.basket = Add to cart
basket.add.to.cart = Add
basket.added.to.basket = Added to Your Shopping Cart
basket.added.to.mobile.basket = Added Item
basket.confirmation.items.added = Your saved items were added to your cart.
basket.error.no.pickup.location = Please select stores for the entries highlighted in red
basket.error.occurred = Error occurred while adding to Cart
basket.error.product.removed = Sorry, one or more products were removed from your cart as they are not in stock or are no longer available.
basket.error.quantity.invalid = Please provide a positive number to update the quantity of an item.
basket.error.quantity.invalid.binding = Invalid quantity: please provide a valid quantity number to add this product to your cart
basket.error.quantity.notNull = Quantity field cannot be empty.
basket.information.merge.successful = The cart saved against your user account and your current cart have been merged. Click here to visit your cart.
basket.information.quantity.adjusted = Your items have been modified to match available stock levels
basket.information.quantity.noItemsAdded.maxOrderQuantityExceeded = Unfortunately the quantity you chose exceeded the maximum order quantity for this product. The quantity in your cart has been reduced to the maximum order quantity.
basket.information.quantity.noItemsAdded.noStock = Sorry, there is insufficient stock for your cart.
basket.information.quantity.reducedNumberOfItemsAdded.lowStock = A lower quantity of this product has been added to your cart due to insufficient stock
basket.information.quantity.reducedNumberOfItemsAdded.maxOrderQuantityExceeded = Unfortunately the quantity you chose exceeded the maximum order quantity for this product. The quantity in your cart has been reduced to the maximum order quantity.
basket.page.cartHelpContent = Need Help? Contact us or call Customer Service at 1-###-###-####. If you are calling regarding your shopping cart, please reference the Shopping Cart ID above.
basket.page.cartHelpMessageMobile = Need help? <br />Call our team to help you directly <a href="tel:1-222-333-444">1-222-333-444</a><br />Your cart ID for assistance is {0}
basket.page.cartId = Cart ID:
basket.page.free = FREE
basket.page.itemNumber = Item no.
basket.page.itemPrice = Item price
basket.page.message.remove = Product has been removed from your cart.
basket.page.message.update = Product quantity has been updated.
basket.page.message.update.pickupinstoreitem = Pickup in store item was updated.
basket.page.message.update.pickupinstoreitem.toship = Pickup in store item was updated to shipping.
basket.page.message.update.reducedNumberOfItemsAdded.lowStock = The cart quantity of <a href="{3}" style="text-decoration: underline">{0}</a> has been reduced to {1} from {2} due to insufficient stock.
basket.page.message.update.reducedNumberOfItemsAdded.noStock = <a href="{1}" style="text-decoration: underline">{0}</a> has been removed from the cart due to insufficient stock.
basket.page.needHelp = Need help?
basket.page.number = No.
basket.page.price = Price
basket.page.availability = Availability
basket.page.product = Product
basket.page.productdetails = Product Details
basket.page.qty = Qty
basket.page.quantity = Quantity
basket.page.remove = Remove
basket.page.shipping = Shipping
basket.page.shipping.change.store = Change Store
basket.page.shipping.find.store = Find a Store
basket.page.shipping.pickup = Pickup
basket.page.shipping.ship = Ship
basket.page.title = Item
basket.page.title.pickupFrom = Pick Up from:
basket.page.title.yourDeliveryItems = Your Delivery Items
basket.page.title.yourDeliveryItems.pickup = Order items to be picked up will appear later in the checkout.
basket.page.title.yourItems = Your Cart
basket.page.title.yourPickUpItems = Your Pick Up Items
basket.page.total = Total
basket.page.total.includingVat = Including VAT
basket.page.totals.delivery = Delivery:
basket.page.totals.grossTax = Your order includes {0} tax.
basket.page.totals.netTax = Tax:
basket.page.totals.noNetTax = *No taxes are included in the total
basket.page.totals.savings = Savings:
basket.page.totals.subtotal = Subtotal:
basket.page.totals.standard.delivery = Standard Delivery:
basket.page.totals.total = Total:
basket.page.totals.total.items = Total: ({0} items)&nbsp
basket.page.totals.total.items.one = Total: ({0} item)&nbsp
basket.page.totals.estimatetaxesbutton = Estimate tax
basket.page.totals.estimatedtotaltax = Estimated tax:
basket.page.totals.estimatedtotal = Estimated total:
basket.page.totals.estimatedZip = Zip code:
basket.page.totals.deliverycountry = Delivery destination:
basket.page.totals.error.wrongzipcode = Please enter a valid zip code
basket.page.totals.error.wrongcountry = Please select a country
basket.page.update = Update
basket.page.validation.message = Product(s) have been updated/removed in your cart.
basket.pickup.product.variant = {0}: {1}
basket.potential.promotions = Potential Promotions
basket.received.promotions = Received Promotions
basket.restoration.restorationError = Your cart could not be restored.
basket.restoration = Welcome back, items from your saved cart have been added to your cart.
basket.restoration.delivery.changed = {0} was restored to your cart, but the delivery method has changed due to low local stock.
basket.restoration.errorMsg = Your cart could not be restored.
basket.restoration.lowStock = Unfortunately a lower quantity of {0} was restored to your cart due to low stock. {3} were added to your cart, you now have {2}.
basket.restoration.noStock = Unfortunately <a href="{1}" {4}>{0}</a> could not be restored to your cart as it is out of stock. You previously had {2} in your cart.
basket.restoration.success = {0} was successfully restored to your cart.
basket.restoration.view.cart = <a href="{0}">  Click to view cart</a>.
basket.restoration.unavailable = Unfortunately {0} is no longer available. You previously had {2} in your cart.
basket.validation.lowStock = Unfortunately a lower quantity of <a href="{1}" {4}>{0}</a> was applied to your cart due to low stock. You previously had {2} in your cart, you now have {3}.
basket.validation.movedFromPOSToStore = Your items have been modified to match available stock levels. One or more pickup in store items were updated to shipping.
basket.validation.noStock = Unfortunately <a href="{1}">{0}</a> was removed from your cart as it is out of stock. You previously had {2} in your cart.
basket.validation.unavailable = Unfortunately {0} was removed from your cart as it is no longer available.
basket.view.basket = View cart
basket.your.shopping.basket = Your Shopping Cart

breadcrumb.cart = Cart
breadcrumb.home = Home
breadcrumb.login = Login
breadcrumb.not.found = Page Not Found

cart.page.shop = Shop
cart.page.parameter.voucher.code.empty=Parameter voucherCode must not be empty

checkout.order.summary = Your Order Summary
checkout.checkout = Checkout
checkout.Pay.Now=Pay Now
checkout.checkout.flow.select = Select an alternative checkout flow
checkout.checkout.multi = Checkout Multi
checkout.checkout.multi.deliveryAddress.viewAddressBook = Address Book
checkout.multi.deliveryAddress.continue = Continue
checkout.checkout.multi.pci = Checkout Multi with PCI
checkout.checkout.multi.pci-hop = PCI-HOP
checkout.checkout.multi.pci-sop = PCI-SOP
checkout.checkout.multi.pci-ws = PCI-Default
checkout.checkout.multi.pci.select = Select a PCI option
checkout.checkout.single = Checkout Single
checkout.login.guestCheckout = Checkout as a Guest
checkout.login.loginAndCheckout = Login and Checkout
checkout.login.registerAndCheckout = Register and Checkout
checkout.multi.deliveryMethod.continue = Continue
checkout.multi.paymentMethod.continue = Continue
checkout.page.order.summary = Your order summary
forgottenPwd.description = Please enter the email address for your account. A link to change your password will be emailed to you.
forgottenPwd.email = Email Address
forgottenPwd.email.invalid = Please enter a valid email
forgottenPwd.submit = Send Email
forgottenPwd.title = Recover Password

form.field.required = Required
form.global.error = Please correct the errors below.
form.select.empty = Please select

general.continue.shopping = Continue Shopping
general.find.a.store = Find a Store
general.mobile.store = Mobile Store
general.month.april = April
general.month.august = August
general.month.december = December
general.month.february = February
general.month.january = January
general.month.july = July
general.month.june = June
general.month.march = March
general.month.may = May
general.month.november = November
general.month.october = October
general.month.september = September
general.required = Required
general.unknown.identifier = Unknown identifier was passed
general.zoom = Enlarged view of picture

guest.checkPwd = Confirm Password
guest.checkout = New to {0}?
guest.checkout.existingaccount.register.error = An account with email id {0} already exists.
guest.confirm.email = Confirm Email Address
guest.description = Prefer to shop without creating an account? <br />It's quick and simple!
guest.email = Email Address
guest.pwd = Password
guest.register = Create an Account
guest.register.description = Benefit from a faster checkout for your next purchase and access your order history
guest.required.message = Fields marked * are required
guest.register.submit = Register

header.hello = Hello
header.link.account = My Account
header.link.login = Sign in / Register
header.link.logout = Sign Out
header.mobile.link.login = Login
header.welcome = Welcome {0}

j_password = Password

j_username = Login

login.description = Already have an account? Sign in to retrieve your account settings.
login.email = Email Address
login.error.account.not.found.subtitle = If you forgot your password, please use the Forgotten Password link.
login.error.account.not.found.title = Your username or password was incorrect.
login.error.incorrect.password.subtitle = If you forgot your password, please use the Forgotten Password link.
login.error.incorrect.password.title = Your username or password was incorrect.
login.link.forgottenPwd = Forgot your password?
login.login = Login
login.password = Password
login.required = Required
login.optional = (optional)
login.required.message = Fields marked * are required
login.title = Returning Customer
login.signup.header.title = Sign Up Now
login.signup.header.text = Enjoy the convenience of a single account across all participating brands.
login.signup.header.text_HC=Enjoy the convenience of a single account across allparticipating brands.
login.signup.facebook.button.text = Sign up with Facebook
login.signup.header.note = Or via email
login.signup.firstname = First Name
login.signup.firstname.required = Enter your first name
login.signup.Newfirstname.required = Enter your full name
login.signup.firstname.valid = Please enter valid first name
login.signup.lastname = Last Name
login.signup.lastname.required = Enter your last name
login.signup.lastname.valid = Please enter valid last name
login.signup.email = Your Email
login.signup.email.required = Enter your email address
login.signup.email.invalid = Please Enter a valid email address
login.signup.phone = Your Phone
login.signup.phone.required = Enter your correct phone number
login.signup.password = Password
login.signup.password.required = Enter your password
login.signup.password.minlength = Your password must have at least 6 characters
login.signup.checkbox.showpassword = Show
login.signup.password.strength.veryweak = Very weak
login.signup.password.strength.data.text = Very weak, Weak, Medium, Good, Strong
login.signup.newsletters.subscribe = Send me awesome offers and updates by email
login.signup.action.button = Sign up
login.signup.terms.agree = By creating your account you agree to our Terms and Conditions
login.signup.terms =By creating your account you agree to our  Terms and Conditions
login.signup.terms.conditions = Terms and Conditions
login.signup.footer.lefttext = Or just &nbsp;
login.signup.footer.righttext = checkout as a guest
login.signup.footer.signin.lefttext =Already have an account?  Sign in now
login.signup =Already have an account? Sign in now
login.signup.footer.signin.righttext = Sign in now
text.footer.originalVersion = You are viewing original version of the {0}.
text.footer.switchLighter = Switch to the Lighter version
login.signup.header.error.message = Oops! There was a problem. Please correct the highlighted issues
login.signup.password.new = Please enter new password
login.signin.password.current = Please enter your current password
login.signin.header.title = Sign In Now
login.signin.header.lmsnote = Go ahead and use your LandmarkShops account if you have one.
login.signin.facebook.button.text = Sign in with Facebook
login.signin.username = Your Email
login.signin.username.required = Enter your email address
login.signin.password = Password
login.signin.password.required = Enter Your Password
login.signin.forgotpassword = Forgot your password?
login.signin.keepmeloggedin = Keep me logged in
login.signin.button.text = Sign In
login.signin.footer.signup.lefttext =Don't have an account?  Sign up for one
login.signin.footer.signup.righttext =   Sign up for one
login.signin.incorrectdetails = Your Username or Password is incorrect
login.facebook.rejectaccess = We need your email address to continue. Click on the Sign In With Facebook button, share your email address and try again.
login.facebook.emailnotfound = Your facebook account does not have email id. Please provide your email id to sign in.
login.signup.userexist = Email entered is registered already
login.signup.captcha = Your recaptcha challenge answer is invalid. Please check and try again.

menu.button.home = Home

mobile.basket.page.update = Update quantity
mobile.search.add.refinements = Refine
mobile.search.nav.clearSelections = Clear all selections
mobile.search.nav.facetValueCount = {0}
mobile.storelocator.title = Store Locator

order.free = FREE
order.itemPrice = Item Price
order.order.totals = Order Totals
order.orderItems = Order Items
order.product = Product
order.productDetails = Product Details
order.quantity = Quantity
order.total = Total
order.totals.delivery = Delivery:
order.totals.savings = Savings:
order.totals.subtotal = Subtotal:
order.totals.total = Total:

password.strength.medium = Medium
password.strength.minchartext = Minimum length is %d characters
password.strength.strong = Strong
password.strength.tooshortpwd = Too short
password.strength.verystrong = Very strong
password.strength.veryweak = Very weak
password.strength.weak = Weak

pickup.back.to.product.page = &lt; Back to Product Page
pickup.buy.online.message = Buy your products now and pick them up later in store.
pickup.force.in.stock = IN STOCK
pickup.here.button = Pick Up Here
pickup.in.stock = {0} In Stock
pickup.in.store = Pick Up in Store
pickup.in.store.back.to.results = Back
pickup.location.required = Please provide your location to see products available in your area.
pickup.mobile.back.to.cart.page = Back to Shopping Bag
pickup.mobile.back.to.product.page = Back to Product Page
pickup.out.of.stock = Out Of Stock
pickup.pagination.first = First
pickup.pagination.last = Last
pickup.pagination.next = Next
pickup.pagination.from = from&nbsp
pickup.pagination.stores = &nbspstores found
pickup.pagination.page.details = {0} to {1} of {2}
pickup.pagination.previous = Previous
pickup.product.availability = Product Availability by Store Location
pickup.product.by.store.location = Product by store location
pickup.results.button = Pick Up
pickup.search.button = Find Stores
pickup.search.message = Enter a City or Town or Zip Code:

popup.cart.empty = Empty Cart
popup.cart.pickup = Pick Up
popup.cart.quantity = Quantity
popup.cart.quantity.added = Quantity Added
popup.cart.showing = Showing {0} of {1} Items
popup.cart.title = Your Shopping Cart
popup.cart.total = Total
popup.close = Close
popup.quick.view.select = Select Options
popup.cart.showall = Show All


product.average.review.rating = Products Average Rating {0} / 5
product.bookmark.and.share = Bookmark and Share
product.close = Close
product.image.zoom.in = Click To Zoom
product.image.zoom.out = Zoom Out
product.overview = Details
product.price.from = From {0}
product.product.details = Product details
product.product.details.more = More product details
product.product.spec = Specs
product.related.products = Related Products
product.share.email = E-mail
product.share.print = Print
product.share.sendToFacebook = Send to Facebook
product.share.share = Share
product.share.tweet = Tweet this
product.share.viewMoreServices = View more services
product.variants.available = available online
product.variants.colour = Colour
product.variants.in.stock = in stock online
product.variants.only.left = only {0} left online
product.variants.out.of.stock = out of stock online
product.variants.select.size = Select a size
product.variants.select.style = Please select style first
product.variants.select.variant = Please select variant
product.variants.size = Size
product.variants.size.guide = Size Guide
product.variants.type = Type
product.variants.update = update
product.volumePrices.column.price = Price each
product.volumePrices.column.qa = Quantity
product.facebook.share.success = Shared successfully

profile.checkEmail = Re-enter email address
profile.checkEmail.invalid = Please confirm your email address
profile.checkNewPassword = Confirm New Password
profile.checkNewPassword.invalid = Please confirm your new password
profile.checkPwd = Confirm Password
profile.checkPwd.invalid = Please confirm your password
profile.currentPassword = Current Password
profile.currentPassword.invalid = Please enter a valid current password
profile.email = E-mail
profile.email.invalid = Please enter a valid email
profile.email.unique = The email you entered is not available
profile.firstName = First Name
profile.firstName.invalid = Please enter a first name
profile.lastName = Last Name
profile.lastName.invalid = Please enter a last name
profile.newPassword = New Password
profile.newPassword.invalid = Please enter your new password
profile.pwd = Password
profile.pwd.invalid = Please enter your password
profile.submit = Update
profile.title = Title
profile.title.invalid = Please select a title

register.back.login = Back to login
register.checkPwd = Confirm Password
register.checkPwd.invalid = Please confirm your password
register.description = For a fast checkout, easy access to previous orders, and the ability to create an address book and store settings. Register below.
register.email = Email Address
register.email.invalid = Please enter a valid email
register.firstName = First Name
register.firstName.invalid = Please enter a first name
register.lastName = Last Name
register.lastName.invalid = Please enter a last name
register.name.invalid = Combined length of first name and last name must be less than 255 characters
register.new.customer = Create an Account
register.phone = Phone Number (Mobile Preferred)
register.pwd = Password
register.pwd.invalid = Please enter a strong password (at least 6 chars)
register.remember = Remember me on this computer
register.submit = Register
register.title = Title
register.title.invalid = Please select a title
recaptcha.challenge.field.invalid=Invalid answer to captcha challenge
recaptcha.required=Enter Captcha

registration.confirmation.message.subtitle = Please check your email to verify your email address.
registration.confirmation.message.title = Thank you for registering.
registration.error.account.exists.subtitle = If you forgot your password, please use the Forgotten Password link.
registration.error.account.exists.title = An account already exists for this email address.

review.alias = Your Name
review.back = Back To Reviews
review.based.on = ({0})
review.based.on.one = Based on {0} review
review.comment = Review Description
review.comment.invalid = Please enter a description
review.confirmation.thank.you.subtitle = We try to put all reviews on the site within 24 hours.
review.confirmation.thank.you.title = Thank you for your review.
review.general.error = Please fill all mandatory review fields
review.headline = Review Title
review.headline.invalid = Please enter a title
review.no.reviews = Be the first to write a review.
review.number.of = of
review.number.reviews = Reviews
review.rating = Your Rating
review.rating.alt = stars
review.rating.invalid = Please enter a rating
review.required = Fields marked* are required
review.reviews = REVIEWS
review.see.reviews = Show Reviews
review.show.all = Show All
review.show.less = Show Less
review.show.more = Show More
review.submit = Submit Review
review.submitted.anonymous = Anonymous
review.submitted.by = Submitted by
review.write.description = Please enter your review
review.write.review = Write Review
review.write.title = Write a Review
review.write.title.product = Write a Review for {0}

search.back.to.product.list = Back to product list
search.meta.description.on = on
search.meta.description.results = Search results for
search.meta.title = Search
search.mobile.no.results = No Results Found
search.mobile.page.currentPage = {0} - {1} of {2} products
search.mobile.page.linkNextPage = Next &raquo;
search.mobile.page.linkPreviousPage = &laquo; Previous
search.mobile.page.searchText = You searched for "{0}"
search.mobile.page.showAllResults = Show all
search.mobile.page.sortTitle = Sort by:
search.mobile.page.totalResults = {0} item(s)
search.nav.appliedFilters = Remove
search.nav.categoryNav = Shop by Category
search.nav.changeLocation = Change Location
search.nav.done.button = Done
search.nav.facetShowLess = less...
search.nav.facetShowLess_availableInStores = less stores...
search.nav.facetShowLess_brand = less brands...
search.nav.facetShowLess_category = less categories...
search.nav.facetShowLess_collection = less collections...
search.nav.facetShowLess_colour = less colours...
search.nav.facetShowLess_price = less prices...
search.nav.facetShowLess_size = less sizes...
search.nav.facetShowLess_style = less styles...
search.nav.facetShowMore = more...
search.nav.facetShowMore_availableInStores = more stores...
search.nav.facetShowMore_brand = more brands...
search.nav.facetShowMore_category = more categories...
search.nav.facetShowMore_collection = more collections...
search.nav.facetShowMore_colour = more colours...
search.nav.facetShowMore_price = more prices...
search.nav.facetShowMore_size = more sizes...
search.nav.facetShowMore_stores = Show More Stores
search.nav.facetShowMore_style = more styles...
search.nav.facetTitle = Shop by {0}
search.nav.facetValueCount = ({0})
search.nav.refinements = Refinements
search.nav.removeAttribute = Remove Attribute
search.nav.resultsForStore = Results for: {0}
search.no.results = 0 items found for keyword <strong>{0}</strong>
search.page.currentPage = Page {0} of {1}
search.page.firstPage = &laquo;
search.page.lastPage = &raquo;
search.page.linkNextPage = Next Page
search.page.linkPreviousPage = Previous Page
search.page.nearbyStores = You searched for nearby stores
search.page.searchText = You searched for "{0}"
search.page.showAllResults = Show all
search.page.showPageResults = Show paginated
search.page.sortTitle = Sort by:
search.page.totalResults = {0} Products found
search.placeholder = I'm looking for
search.spellingSuggestion.prompt = Did you mean:
search.seeAll = See all
search.recentSearches = Recent searches
search.in = in
search.productMatching = product matching
search.productsMatching = products matching
search.noProductsMatching = Sorry, there are no matches for 
search.noResults=No Results ssssssssssssssssssssssssssss

storeDetails.map.link = Map
storeDetails.table.address = Address
storeDetails.table.distance = Distance
storeDetails.table.distanceFromCurrentLocation = {0} from Current Location
storeDetails.table.distanceFromSource = {0} from {1}
storeDetails.table.email = Email
storeDetails.table.features = Features
storeDetails.table.from = from
storeDetails.table.opening = Hours
storeDetails.table.opening.closed = Closed
storeDetails.table.opening.opened = Opened
storeDetails.table.openingSpecialDays = Special Opening Times
storeDetails.table.telephone = Telephone
storeDetails.title = Store Details

storeFinder.currentPosition = Current Position
storeFinder.find.a.store = Find a Store
storeFinder.findStoresNearMe = Find stores near me
storeFinder.line.text = or
storeFinder.link = Store Finder
storeFinder.meta.description.results = store Locations near to
storeFinder.meta.title = Stores near
storeFinder.navigateTo = Navigate To
storeFinder.nearby.stores = Nearby Stores
storeFinder.orSearchBy = Or search by:
storeFinder.pagination.from = from&nbsp
storeFinder.pagination.next = Next
storeFinder.pagination.previous = Previous
storeFinder.pagination.stores = &nbspstores found
storeFinder.postcode.town = Postcode / Town
storeFinder.search = Search
storeFinder.see.more = See more...
storeFinder.store.locator = Store Locator
storeFinder.stores.nearby = Stores Nearby
storeFinder.stores.nearto = Stores near: {0}
storeFinder.table.address = Address
storeFinder.table.distance = Distance
storeFinder.table.opening = Opening Hours
storeFinder.table.store = Store
storeFinder.table.view.map = View map
storeFinder.table.view.store = View store
storeFinder.use.this.form = Use this form to search for a store
storeFinder.viewMap = View Map

storefinder.searchterm.invalid = Please enter search term or zip code

storelocator.error.no.results.subtitle = Check that you entered a valid postcode or place name.
storelocator.error.no.results.title = No store results were found for your search criteria.
storelocator.postcode.city.search = Postcode/City Search
storelocator.query = Postcode / Town
storelocator.search.results.go = Go
storelocator.search.totalResults = Showing {0} nearest stores

system.error.link.expired.subtitle = Complete the forgotten password form again.
system.error.link.expired.title = Sorry, this link has expired
system.error.page.not.found = 404 Page Not Found

text.account.account = Account
text.account.addressBook = Address Book
text.account.addressBook.addAddress = Add new address
text.account.addressBook.addEditAddress = Add/Edit Address
text.account.addressBook.addEditform = Please use this form to add/edit an address
text.account.addressBook.addressDetails = Address Details
text.account.addressBook.confirmationUpdated = Your address was updated
text.account.addressBook.manageDeliveryAddresses = Manage your delivery address
text.account.addressBook.manageYourAddresses = Manage your address book
text.account.addressBook.noSavedAddresses = No Saved Addresses
text.account.addressBook.saveAddress = Save address
text.account.addressBook.setDefaultDeliveryAddress = Set default delivery address
text.account.addressBook.yourDefaultAddress = This is your default address
text.account.change.email.address = Change Email Address
text.account.confirmation.password.updated = Your password has been changed
text.account.order.consignment.status.cancelled = Cancelled
text.account.order.consignment.status.pickedup = Picked Up
text.account.order.consignment.status.readyforpickup = Pick Up Before
text.account.order.consignment.status.shipped = Shipped
text.account.order.consignment.status.delivered = Delivered
text.account.order.consignment.trackingID.notavailable = Not available.
text.account.order.delivery = Delivery:
text.account.order.includesTax = Your order includes {0} tax
text.account.order.netTax = Tax:
text.account.order.orderBreadcrumb = Order {0}
text.account.order.orderNumber = Order Number is {0}
text.account.order.orderNumberShort = Order #: {0}
text.account.order.orderPlaced = Placed on {0}
text.account.order.orderStatus = The order is {0}
text.account.order.orderTotals = Order Totals
text.account.order.pickup.location = Pick Up Location:
text.account.order.receivedPromotions = Received Promotions
text.account.order.savings = Savings:
text.account.order.status = Status: {0}
text.account.order.status.display.cancelled = Cancelled
text.account.order.status.display.cancelling = Cancelling
text.account.order.status.display.completed = Completed
text.account.order.status.display.created = Created
text.account.order.status.display.error = Error
text.account.order.status.display.open = Open
text.account.order.status.display.processing = Processing
text.account.order.status.display.submitted = Placed
text.account.order.subtotal = Subtotal:
text.account.order.summary = A summary of your order is below:
text.account.order.title.deliveryItems = Delivery Items
text.account.order.title.inProgressItems = In Progress Order Items
text.account.order.title.storePickUpItems = Store Pick Up Items
text.account.order.title.details = Order Details
text.account.order.total = Total:
text.account.order.tracking = Tracking #:
text.account.order.warning.storePickUpItems = Reminder - Please pick up your items(s) soon.
text.account.order.yourOrder = Your Order
text.account.orderHistory = Order History
text.account.orderHistory.actions = Actions
text.account.orderHistory.datePlaced = Date Placed
text.account.orderHistory.mobile.page.currentPage = {0} of {1}
text.account.orderHistory.mobile.page.currentResults = {0} - {1} of {2} orders
text.account.orderHistory.mobile.page.linkNextPage = Next &raquo;
text.account.orderHistory.mobile.page.linkPreviousPage = &laquo; Previous
text.account.orderHistory.mobile.page.sort.byDate = Date
text.account.orderHistory.mobile.page.sort.byOrderNumber = Order Number

text.account.orderHistory.mobile.page.sortTitle = Sort by:
text.account.orderHistory.mobile.page.totalResults = {0} Orders found
text.account.orderHistory.noOrders = You have no orders
text.account.orderHistory.orderNumber = Order number
text.account.orderHistory.orderStatus = Order Status
text.account.orderHistory.status = Status
text.account.orderHistory.page.currentPage = Page {0} of {1}
text.account.orderHistory.page.firstPage = &laquo;
text.account.orderHistory.page.lastPage = &raquo;
text.account.orderHistory.page.linkNextPage = Next Page
text.account.orderHistory.page.linkPreviousPage = Previous Page
text.account.orderHistory.page.showAllResults = Show all
text.account.orderHistory.page.showPageResults = Show paginated
text.account.orderHistory.page.sort.byDate = Date
text.account.orderHistory.page.sort.byOrderNumber = Order Number
text.account.orderHistory.page.sortTitle = Sort by:
text.account.orderHistory.page.totalResults = {0} Orders found
text.account.orderHistory.total = TOTAL
text.account.orderHistory.viewOrders = View your order history
text.account.purchases.instore.viewdetails = View Details
text.account.purchases.instore.location = Location
text.account.purchases.instore.status = Status
text.account.purchases.instore.total = Total Amount
text.account.purchases.instore.nonShukraninfoTitle = No need to save the store receipts anymore!
text.account.purchases.instore.nonshukran.createOrLinkMessage = Create or Link your Shukran account
text.account.purchases.instore.nonShukran.viewAlltext =View all the store receipts on your browser
text.account.purchases.instore.linkShukranButton = Link Shukran
text.account.purchases.instore.registerLinkButton = Register Now
text.account.purchases.instore.ShukranUserinfoTitle = You donât have any purchases yet!
text.account.purchases.instore.ShukranUserinfosubTitle = Looks like you havenât shopped from our

# In store Order Receipts
text.orderdetails.instore.backToPurchases = Back to Purchases
text.orderdetails.instore.completed = Completed
text.orderdetails.instore.viewReceipt =  View Receipt
text.orderdetails.instore.purchaseSummary = Purchase Summary
text.orderdetails.instore.returnsPolicy = Returns Policy
text.orderdetails.instore.viewReturnsPolicy = View Returns Policy
text.orderdetails.instore.paymentDetails = Payment Details
text.orderdetails.instore.cash = Cash
text.orderdetails.instore.Points = Points
text.orderdetails.instore.purchaseSummarytitle = Purchase Summary
text.orderdetails.instore.quanity = Quantity
text.orderdetails.instore.subtotal = Subtotal:
text.orderdetails.instore.VAT = VAT:
text.orderdetails.instore.Total= Total:


text.acc.od.
text.account.paymen
text.account.purchase.instore.completed = Completed
text.account.purchase.instore.vat = (VAT Exclusive)
tDetails = Payment Details
text.account.paymentDetails.billingAddress = Billing Address
text.account.paymentDetails.managePaymentDetails = Manage your payment details
text.account.paymentDetails.noPaymentInformation = No Saved Payment Details
text.account.paymentDetails.paymentCard = Payment Card
text.account.paymentDetails.payment = Payment
text.account.paymentDetails.paymentCard.default = My Default Payment Card
text.account.paymentDetails.setDefaultPaymentDetails = Set default payment details
text.account.profile = Profile
text.account.profile.cancel = Cancel
text.account.profile.changePassword = Change your password
text.account.profile.changePassword.mobile = Change Password
text.account.profile.confirmationUpdated = Your profile has been updated
text.account.profile.emailNotChanged = Your email was not updated
text.account.profile.paymentCart.removed = Payment Card removed successfully
text.account.profile.saveUpdates = Save Updates
text.account.profile.updateEmail = Update your email
text.account.profile.updateEmail.mobile = Change Email
text.account.profile.updateEmailAddress = Enter your new email address and confirm with your password
text.account.profile.updateForm = Please use this form to update your personal details
text.account.profile.updatePassword = Please use this form to update your account password
text.account.profile.updatePasswordForm = Update Password
text.account.profile.updatePersonalDetails = Update personal details
text.account.profile.updatePersonalDetails.mobile = Edit
text.account.profile.updateProfile.mobile = Update Profile
text.account.trackOrders = Track your orders
text.account.viewOrderHistory = View order history
text.account.yourAccount = My Account
text.address.remove.confirm = Are you sure you want to delete this address?
text.backToMobileStore = Mobile
text.button.cancel = Cancel
text.button.menu = Menu
text.button.new = New
text.button.newarabic = Ø¬Ø¯ÙØ¯
text.button.save = Save
text.button.showall = Show All
text.button.use = Use
text.cart = Cart
text.connect = Connect
text.copyright = &copy; 2014 hybris software
text.guest.customer = Guest
text.header.connect = Connect
text.header.language = Click here to change the language
text.header.languageandcurrency = Language and Currency
text.header.loginandaccount = Login and Account
text.header.menu = Menu
text.header.storefinder = Store Finder
text.headertext = Confirm
text.headertext.conf = Confirm
text.headertext.error = Error(s)
text.headertext.info = Info
text.headline.addaddress = Click to add an address
text.headline.addtocart = Click to add the product to cart
text.headline.bottombanner = Bottom Banner
text.headline.breadcrumbs = Breadcrumbs
text.headline.categories = Click here the menu button to get to the categories
text.headline.findstore = Find your store
text.headline.footer.navigationbar = Browse through the footer navigation bar
text.headline.homebanner = Top Banner
text.headline.login = Click here to login
text.headline.myaccount = Click here to login or get to your Account
text.headline.navigationbar = Browse through the navigation bar
text.headline.orderinfo = All information about delivery address, delivery method and payment method
text.headline.orderitems = All information about your order items
text.headline.orders = View your orders
text.headline.productcategories = Product categories
text.headline.productinfo = Click to get more information about product overview, product review or delivery method
text.headline.profile = Click here to view your profile, address book, payment details or order history
text.headline.refinements = Choose the relevance or add refinements
text.headline.register = Click here to register a new customer
text.headline.search = Here you can search for products
text.headline.sortandrefine = Sort and refine
text.headline.terms = Agree with the terms and conditions
text.help = Help
text.javascript.disabled = JavaScript is required to access the page. Please enable JavaScript.
text.label.loadingmoreresults = Loading more results...
text.label.showmoreresults = Show more results
text.link.home.label = Home
text.loadingMessage = Loading...
text.logout = Logout
hc.text.logout = Download the Homecentre App and see how it looks in
text.myaccount = &laquo; My Account
text.myaccount.orderHistory = &laquo; Order History
text.paymentcard.remove.confirm = Are you sure you want to delete this payment card?
text.productreviews = Reviews
text.storefinder.desktop.page.currentPage = {0} of {1}
text.storefinder.desktop.page.firstPage = &laquo;
text.storefinder.desktop.page.lastPage = &raquo;
text.storefinder.desktop.page.linkNextPage = Next
text.storefinder.desktop.page.linkPreviousPage = Previous
text.storefinder.desktop.page.showAllResults = Show all
text.storefinder.desktop.page.showPageResults = Show paginated
text.storefinder.desktop.page.sort.byName = By Name
text.storefinder.desktop.page.sortTitle = Sort by:
text.storefinder.desktop.page.totalResults = {0} Stores found
text.storefinder.mobile.page.currentResults = {0} - {1} of {2} stores
text.storefinder.mobile.page.currentPage = {0} of {1}
text.storefinder.mobile.page.description = Please provide your location to see stores in your area or let your device find nearby stores for you
text.storefinder.mobile.page.linkNextPage = Next &raquo;
text.storefinder.mobile.page.linkPreviousPage = &laquo; Previous
text.storefinder.mobile.page.noResults = No results for your area.
text.storefinder.mobile.page.totalResults = {0} Stores found
text.storefinder.responsive.page.currentPage = {0} of {1}
text.storefinder.responsive.page.firstPage = &laquo;
text.storefinder.responsive.page.lastPage = &raquo;
text.storefinder.responsive.page.linkNextPage = Next
text.storefinder.responsive.page.linkPreviousPage = Previous
text.storefinder.responsive.page.showAllResults = Show all
text.storefinder.responsive.page.showPageResults = Show paginated
text.storefinder.responsive.page.sort.byName = By Name
text.storefinder.responsive.page.sortTitle = Sort by:
text.storefinder.responsive.page.totalResults = 1-10 from {0} Stores found
text.stores = Stores
text.swithToMobileStore = Switch back to mobile
text.viewfullsite = View Full Site
text.expresscheckout.header = Express Checkout
text.expresscheckout.title = Benefit from a faster checkout by:
text.expresscheckout.line1 = setting a default Delivery Address in your account or when you checkout
text.expresscheckout.line2 = setting a default Payment Details when you checkout
text.expresscheckout.line3 = using a default shipping method
text.expresscheckout.info1 = By succesfully configuring your settings this way, signed users will proceed directly to the Final Review step when proceeding to checkout
text.expresscheckout.info2 = Users who have not yet signed in may choose Express Checkout in the shopping cart
text.expresscheckout.info3 = The express checkout feature is not available to Guest Users
text.iconCartRemove = Remove
text.payment.saved.card.removed = Selected saved Card details not found, try different card
updatePwd.checkPwd = Confirm Password
updatePwd.checkPwd.invalid = Please confirm your password
updatePwd.description = Please enter a new password.
updatePwd.pwd = Password
updatePwd.pwd.invalid = Please enter a strong password (at least 6 chars)
updatePwd.submit = Update Password
updatePwd.title = Update Password
updatePwd.token.invalid = The link used to access the update page was invalid.
updatePwd.token.invalidated = Your password has already been updated.

validation.checkEmail.equals = Email and Re-enter email address does not match
validation.checkPwd.equals = Password and password confirmation do not match

paymentMethod.paymentDetails.expires = Expires {0} / {1}
text.payment.error.debit.card.optiob.disabled = Debit card payment option is not supported for Pin code, Please try different Payment method
text.payment.error.card.optiob.disabled = Selected payment option is not supported for Pin code, Please try different Payment method
text.payment.error.cod.optiob.disabled = We're sorry, Cash on Delivery is not available for your Pincode. Please select a different payment method.
text.payment.error.nbk.optiob.disabled = Net Banking payment option is not supported for Pin code, Please try different Payment method
text.payment.error.wallet.optiob.disabled = Wallet payment option is not supported for Pin code, Please try different Payment method
text.payment.error.cod.optiob.exceed.threshold = You have exceeded COD order limit, It should be below {0} {1} amount
text.page.message.underconstruction = <strong>Information:</strong> Page Under Construction - Not Completely Functional
text.payment.error.nbk.optiob.invalid.bank = Selected Bank for Net banking is not supported, Please try with Different Bank
text.payment.error.wallet.optiob.invalid.bank = Selected wallet for payment is not supported, Please try with Different Wallet
text.payment.error.sadad.not.supported=Sadad payment option is not supported.
text.payment.error.knet.not.supported=Knet payment option is not supported.
landMarkOffersPage.filterby.department = Filter by Department
landMarkOffersPage.filterby.brand = Filter by Brand
landMarkOffersPage.filterby.all = All
landMarkOffersPage.offers.notfound = Sorry, we do not have any offers currently.

invalid.card.number = Enter Valid Card Number
invalid.holder.name = Enter Valid Card Holder Name
invalid.card.type = Select Valid Card Type
payment.generic.failure =Please Enter Valid Info

###<!--------- Landmark Shops Localised value for JSP -->

landMarkHomePage.footer.newsletter.submit = Subscribe
landMarkHomePage.footer.contactus = Contact us
landMarkHomePage.footer.storefinder = Store Locator
landMarkHomePage.footer.Help = Help
landMarkHomePage.footer.About				= About
landMarkHomePage.footer.landmarkOnline =
landMarkHomePage.footer.terms&condition		= Terms &amp; Conditions
landMarkHomePage.footer.privacyPolicy		= Privacy Policy
landMarkHomePage.footer.EnterYourEmailHere       = Please enter an email address
landMarkHomePage.productcarousel.previouslink	= previous
landMarkHomePage.productcarousel.nextlink	= next
landMarkHomePage.navigation.shopbydepartment = Shop by Department
landMarkHomePage.navigation.shopbybrand = Shop by Brand

## header values
landMarkHomePage.header.user.greeting = Hi,
landMarkHomePage.header.user.signup = Sign Up
landMarkHomePage.header.user.or = or
landMarkHomePage.header.user.signin = Sign In
landMarkHomePage.header.user.help = Help
landMarkHomePage.header.user.my.account = My Account
landMarkHomePage.header.user.order.history = Order History
landMarkHomePage.header.user.subscriptions = Communication
landMarkHomePage.header.user.rewards = Landmark Rewards
landMarkHomePage.header.user.logout = Sign Out
landMarkHomePage.header.cart.count.message = You have 0 items in your basket
landMarkHomePage.header.cart.subtotal = Subtotal:
landMarkHomePage.header.cart.item.count = 0
landMarkHomePage.header.cart.checkout.message = Checkout Now
landMarkHomePage.header.mobile.menu = Menu
landMarkHomePage.header.lifestyle.caption = Your Style. Your Store.
landMarkHomePage.header.max.caption = Look Good. Feel Good.
landMarkHomePage.header.babyshop.caption = Nice Baby shop. you feel good.
landMarkHomePage.header.splash.caption = Nice Splash. Stylish.

landMarkHomePage.header.centerpoint.caption = Center point. Shopping Point.
landMarkHomePage.header.shoemart.caption = Nice Shoemart. Stylish.
landMarkHomePage.header.homecentre.caption = Home centre. Make you home beatiful.

landMarkHomePage.header.search.placeholder = What are you looking for?
landMarkHomePage.header.minibasketempty = Your basket is empty.<br>Go on, add something!

pdp.priceVAT = Inclusive of VAT
pdp.freeShopping = Free shipping on EGP 399+ | Free returns
pdp.color.label = Colour
pdp.size.label = Size
pdp.addToBasket = Add to Basket
pdp.recipientDetailsSection.label = Recipient Details
pdp.fullNameInRecipientDetailsSection.label = Full Name
pdp.emailInRecipientDetailsSection.label = Email
pdp.writeANoteInRecipientDetailsSection.label = Write a note (optional)

plp.sortBy.label = Sort by
plp.sortBy.value.list = New Arrivals:Discount:Price - Low to High:Price - High to Low:Relevance
plp.sortBy.value.newArrivals = New Arrivals
plp.sortBy.value.discount = Discount
plp.sortBy.value.priceLowToHigh = Price - Low to High
plp.sortBy.value.priceHighToLow = Price - High to Low
plp.sortBy.value.relevance = Relevance

pdp.size.guide = SIZE GUIDE
pdp.notify.email.required = Enter you email here
pdp.webreserve.failed = Oops! something went wrong, please try again later.
pdp.threshold.breach = Maximum of {0} allowed
pdp.size.required = Please select a size.
pdp.color.required = Please select a colour.
pdp.missed.offer = Missed Offer
pdp.bowser.promotion = Browse promotion
pdp.bowser.promotion = <a href="/search?q=allPromotions.en:{0}">Browse promotion</a>
pdp.next = Next
pdp.offer = Promotion Offer
pdp.previous = Previous
pdp.you.may.like.label = YOU MAY ALSO LIKE
pdp.customer.also.viewed.label = CUSTOMERS ALSO VIEWED
Basket.Page=Customers also viewed
pdp.see.all.label = See all
pdp.please.select.size = Please select a size
pdp.recently.viewed.label = Recently Viewed Products
pdp.rating.one = Meh
pdp.rating.two = Okay
pdp.rating.three = Good
pdp.rating.four = Fab
pdp.rating.five = Awesome
pdp.review.content.required = Please write a review
pdp.write.your.review.here = Write your reviews here
pdp.give.title.here=Give it a title
pdp.review.rating.required = Please rate this product
pdp.review.title.required = Don't forget the title
pdp.write.review.textarea.placeholder = Write your review here
pdp.write.review.text.placeholder = Give it a title
pdp.thankyou = Thank you
pdp.suggestion.header = Writing a review? Try these tips.
pdp.suggestion.line1 = Explain what you like or dislike about this product.
pdp.suggestion.line2 = Compare it with similar products.
pdp.suggestion.line3 = Who would you recommend it to?
pdp.suggestion.line4 = Make sure you don't include personal information such as your address or phone number.
pdp.review.success.msg = Yay! Your review has been submitted successfully. It is under moderation and will be published soon.
pdp.addreview = Write a Review
pdp.review.submit = Submit
pdp.review.save = Save
pdp.reviews = {0} Reviews
pdp.review = {0} Review
pdp.orderDeliveryQuestion = When will I receive my order?
pdp.bulkOrderMsg = Interested in bulk orders? <a href="{0}">Know more</a>
pdp.fbLike = Like
pdp.tweet = Tweet
pdp.via = via
pdp.share = Share
pdp.pin = Pin
pdp.by = By

cta.addedToBasket = Added to Basket
cta.addAllToBasket = Add all to Basket
cta.addAllToBasketSuccess = Successfully Added
preBasket.byThemTogether = Buy them together
preBasket.succesfullyAdded = Successfully added to your basket
preBasket.subtotal = Basket subtotal ({0}
preBasket.subtotal.item=&nbsp;item):
preBasket.subtotal.items=&nbsp;items):
pdp.checkoutWithFastPay = or Checkout with FastPay
pdp.shipTo = Ship to
pdp.payNowWithFastPay = Pay Now with FastPay
pdp.notify.me.when.back = Notify me when it's back in stock
pdp.notifyMe = Notify Me
pdp.addToWishList = Add to Wishlist
pdp.save = Save {0}<i>{1}</i> (<i>{2}</i>%)
pdp.quantity = Quantity
pdp.inStockOnline = In stock online
pdp.soldOut = Sold out
pdp.overview = Overview
pdp.tapToZoom = Tap to zoom
pdp.helpful = Useful?
pdp.helpfulMsg = {0} out of {1} visitors found this review useful
pdp.reviews.label = Reviews
pdp.beFirstToReview = Be the first to review
pdp.writeReview = Write a review
pdp.averageRatings = Average rating
pdp.showAllReviews = Show all Reviews
pdp.showLessReviews = Show fewer Reviews
pdp.returnProcess = Returns Process
pdp.returnPolicy = Returns Policy
pdp.shipping = Shipping
pdp.sizeGuide = Size Guide
pdp.checkDelivery = Check
pdp.color = Colour
pdp.color.name = Colour: {0}
pdp.outOf5Star = Out of 5 stars
pdp.pincode.failMsg = We're sorry, delivery is not available to
pdp.pincode.emptyValMsg = Please enter your pincode
pdp.pincode.checkDiffPin = Try a different pincode
pdp.cvv.label = Enter CVV
pdp.cvv.required = Enter CVV
pdp.cvv.valid = Enter valid CVV
pdp.notify.subscribed = You'll hear from us when it's back in stock.
pdp.email.id.required = Email id is required
pdp.outofstock = Oops! Product sold out
pdp.notify.subscribed = We'll let you know when it's back in stock
pdp.enter.valid.email.id = Enter a valid email address
pdp.email.id.required = Your email address is required
pdp.outofstock = Oops! This product is sold out.
pdp.features = Features
pdp.showAllFeatures = Show all Features
pdp.showLessFeatures = Show less Features
pdp.adjust.quantity = Please adjust quantity
pdp.product.notfound = Selected product unavailable
pdp.let.us.help.you = let us help you
pdp.tab.overview = Overview
pdp.tab.dimensions = Dimensions

##--------- Landmark Shops Cart Page Localization ------

basket.page.start.shopping = Start shopping
basket.page.item = Item
basket.page.description = Description
basket.page.totalPrice = Total Price
basket.page.appliedOffer = Offer claimed
basket.page.salePrice = Actual price
basket.page.yourSavings = You save
basket.page.missedOffer = Get the offer
basket.page.empty.cart = An empty shopping basket is a lonely shopping basket.
basket.page.empty.cart.add.something = Go on, add something!
basket.page.checkout.now = Checkout Now
basket.page.proceed.to.shipping = Proceed to Shipping
basket.page.your.shopping.basket = Your <span class="hidden-xs">Shopping</span> Basket
basket.page.want.to.use = I want to use
basket.page.i.will.save = I will save

basket.page.cod.charges = COD Charges:
basket.page.shipping.standardGround = Standard Ground Shipping:
basket.page.shipping.babyshop.standardGround=Babyshop Standard Ground Shipping:
basket.page.shipping.homecentre.standardGround=Homecentre Standard Ground Shipping:
basket.page.shipping.express = Express delivery:
basket.page.shipping.homecentre.express = Homecentre Express Delivery:
basket.page.shipping.babyshop.express = Babyshop Express Delivery:
basket.page.shipping.businessDays = ({0} business days)
basket.page.capsTotal = Total
basket.page.shipping.free = Free
basket.page.shipping.charges.may.apply = Shipping charges may apply
basket.page.free.shipping.charges.over.egp200 = FREE SHIPPINGon orders over EGP 200
basket.page.free.shipping.charges.below139 = 139
basket.page.free.shipping.checkout.now = You\u2019ve got FREE SHIPPING! Checkout Now
basket.page.free.shipping.checkout.now.above200 = 200
basket.page.still.need.free.shipping.first.string = Still Need Free Shipping ?Add EGP\u0020
basket.page.still.need.free.shipping.last.string = \u0020more for Free Shipping
basket.page.still.need.free.shipping.first.value= 140
basket.page.still.need.free.shipping.last.value= 199

basket.page.freeVoucherMsg = You\u2019ve got {1} free vouchers worth a total of {0}{2}. Your voucher will be sent by email after order delivery
basket.page.singlefreeVoucherMsg = You\u2019ve got a free voucher worth a total of {0}{1}. Your voucher will be sent by email after order delivery
basket.page.footer.promoCode = Promos & Vouchers
basket.page.footer.promoCode.view.enter.voucher.code.text = Voucher Applied. You save EGP 100!
basket.page.footer.promoCode.already.used.this.voucher.egypt = We're sorry, you've already used this voucher.
basket.page.footer.promoCode.got.promocode.text.popup = Got a Promo/Voucher Code?
basket.page.footer.applyIt = Apply it
basket.page.footer.fromHere = here
basket.page.footer.payment.payWays = Ways you can pay:
basket.page.footer.contact.needHelp = Need help? We're here for you.
basket.page.footer.contact.callCustomer = Call Customer Support at
basket.page.footer.contact.callCustomer.egypt = Call Customer Support at 0020-221294301
basket.page.footer.contact.emailCustomer = Email Customer Support at
basket.page.multiple.quantities = Multiple quantities
basket.page.undo.text = was removed from your Basket.
basket.page.undo = Undo
basket.page.offerClaimedPromo.textColor = #00ae42
basket.page.offerClaimedPromo.backgroundColor = #e5f7ec

## Apply voucher code response
apply.voucher.message.success = Congrats! Your Voucher Code has been applied.
apply.voucher.message.failure = Oops! Your Voucher Code cannot be applied.
release.voucher.message.success = Congrats! Your Voucher Code has been released.
release.voucher.message.failure = Oops! Your Voucher Code has not been released.
apply.voucher.invalid.vouchercode = Please enter a valid Voucher Code
voucher.apply.limit.exceeded = You have exceed the number of vouchers limit for an order.
voucher.not.found = We're sorry, this promo code is invalid. Please try again.
voucher.cannot.be.redeemed = Voucher cannot be redeemed
voucher.apply.limit.concept.exceeded = We\u2019re sorry, you\u2019ve already used a voucher for {0}.
voucher.already.applied= We\u2019re sorry, you\u2019ve already used a voucher.

#Cart related pages localization
orderEntry.qty = Qty:
orderEntry.price = Price:
smallcart.itemsInCart = You have {0} items in your Basket
smallcart.itemInCart = You have 1 item in your Basket
smallcart.show.all.details = Show all details
smallcart.hide.all.details = Hide details
smallcart.viewAll = view all

appliedVouchers.gotaPromocode = Got a Promo Code?
appliedVouchers.fromhere = Use it here
appliedVouchers.gotanotherPromocode = Got another Promo Code?
appliedVouchers.promocodeapplied = Promo Code applied
appliedVouchers.remove = Remove
appliedVouchers.applyithere = Apply
appliedVouchers.apply = Apply
invalid.expired.promo.code = Voucher {0} is invalid or has expired.
appliedVouchers.retry = Retry
appliedVouchers.questionmark = ?
cart.product.discountinued.failure = So sorry, this product is no longer available.
basket.enter.promo.code = Enter Promo Code
basket.enter.apply.voucher= Apply Voucher

cartItem.color = Colour:
cartItem.size = Size:

multiproductpromo.plus = +
multiproductpromo.more = more
multiproductpromo.show.details = <span>Show</span> details


## Category Page
landmarkCategoryPage.filter.size = Size
landmarkCategoryPage.filter.view.all = Show more
landmarkCategoryPage.filter.Less = Show
landmarkCategoryPage.filter.brands = less
landmarkCategoryPage.brand = Brand
landmarkCategoryPage.filter.colour = Colour
landmarkCategoryPage.apply = Apply
landmarkCategoryPage.sortby = Sort by
landmarkCategoryPage.sortbyM = Sort by:
landmarkCategoryPage.index.relevance = Relevance
landmarkCategoryPage.index.alphabetical = Alphabetical
landmarkCategoryPage.index.low.to.high = Price - low To high
landmarkCategoryPage.index.high.to.low = Price - high To low
landmarkCategoryPage.index.recent.products = New Arrivals
landmarkCategoryPage.index.discount = Discount
landmarkCategoryPage.narrowby = Narrow by
landmarkCategoryPage.filters = Filters
landmarkCategoryPage.filter.clearall = Clear all filters
landmarkCategoryPage.filter.price = Price
landmarkCategoryPage.product = Product
landmarkCategoryPage.products = Products
landmarkCategoryPage.categories = Categories
landmarkCategoryPage.close = Close
landmarkCategoryPage.items3 = 3 items
landmarkCategoryPage.items5 = 5 items
landmarkCategoryPage.currency = <span class='rupee'>Rs</span>
landmarkCategoryPage.title = Online Shopping From LandmarkShops.in
landmarkCategoryPage.text.clearance = Clearances
landmarkCategoryPage.quickview.notify.subscribed = You'll hear from us when it's back in stock.
landmarkCategoryPage.show.more.colors = + More Colours
landmarkCategoryPage.text.clearance.subheading = Get amazing discounts every day of the year.
landmarkCategoryPage.text.new = NEW

#Billing Page
payment.zero.payment.message = Hooray! This covers the total cost of your order.
paymentPage.year.format = YYYY
paymentPage.month.format = MM
paymentPage.comodoMessage = Your credit card details are securely encrypted and passed directly to our PCI DSS compliant Payment Gateway for processing. We only store your credit card's last 4 digits and the expiration date. Your traffic to this page is secured using either a 256-bit or 128-bit SSL certificate depending on your browser version.
paymentPage.selectPayentMethod = Select a payment method
paymentPage.selectPayentMethod.Credit.Debit.Card=Credit/Debit Card
paymentPage.selectPayentMethod.Payusing.Mastercard.Visacards=Pay using Mastercard or Visa cards
paymentPage.CashOnDeliveryForHomeDelivery = Only available with home delivery.
paymentPage.continueBtn = Continue
paymentPage.addNewCard = Card Number
paymentPage.saveThisCard = Save this card
paymentPage.allFieldsMandatory =
paymentPage.cardNumber = Card Number
paymentPage.enter16DigNumber = Enter your 16 digit Card number
paymentPage.nameOnCard = Name on Card
paymentPage.expiryDate = Expiry Date
paymentPage.cvv = CVV
paymentPage.saveCard = Save this card for a faster checkout <span class="hidden-xs">experience</span>
paymentPage.doNotSaveCVV = We use best-in-class 256-bit SSL encryption to secure and protect your shopping experience.
paymentPage.beSure = We use a global security best practice known as "tokenization", where we only save the last 4 digits of your card along with the month and year of expiry.
paymentPage.worldClassEncryption = We do not save your card's CVV
paymentPage.wishToDelete = If you want to delete your card, you can do so in the "Payment" section under the "My Account" section.
paymentPage.useAsDefaultCard = Use this as my default Debit or Credit Card
paymentPage.billingAddress = Billing Address
paymentPage.addNewAddressLink = + Add <span class="hidden-xs">new</span> billing address
paymentPage.chooseBillingAddress = Choose a billing address
paymentPage.noSavedBillingAddress = You don't seem to have a billing address saved
paymentPage.selectACard = Select a card
paymentPage.selectACard.error = Please select a card
paymentPage.EnterCVV = Enter CVV
paymentPage.cvvNumber = CVV Number
paymentPage.thanksForCOD = <span>Here's how it works:</span>
paymentPage.callToVerify = Before we ship your order, we\u2019ll call you to confirm it.
paymentPage.deliveryPartner = Our delivery partner will arrive with your order and collect the cash.
paymentPage.remeberToKeepCash = Remember to keep your payment handy.
paymentPage.ifYouWantToCall = If you want to return your order for any reason, call us on <strong>1800-123-1555</strong> and we'll be happy to help.
paymentPage.chooseABank = Select a Bank
paymentPage.chooseAWallet = Select your preferred wallet
paymentPage.chooseYourBank = Choose your bank
paymentPage.chooseYourWallet = Choose your wallet
paymentPage.walletError = Please select a wallet
paymentPage.showMyAddress = Show my address
paymentPage.addABillingAddress = Add your billing address
paymentPage.addNewBillingAddress = Add new billing address
paymentPage.fullName = Full Name
paymentPage.fullNamePlaceHolder = Enter your full name
paymentPage.addressLine1 = Address Line 1
paymentPage.addressLine1PlaceHolder = Flat or house number, floor and building
paymentPage.addressLine2 = Address Line 2 (optional)
paymentPage.addressLine2PlaceHolder = Colony, street and locality
paymentPage.town = City or Town
paymentPage.state = State
paymentPage.selectCity = Select your City or Town
paymentPage.selectState = Select your state
paymentPage.useAsDefaultShipping = Use this as my default shipping address
paymentPage.addThisAddress = Add a new address
paymentPage.select.bankname = Please select bank name
paymentPage.select.walletname = Please select wallet name

payment.generic.failure = Oop! something went wrong, please try again
checkoutPages.shippingStep = Shipping
checkoutPages.paymentStep = Payment
checkoutPages.orderConfirmation = Place <span class="hidden-xs">your</span> order

#Payment page
payment.valid.card.error.data = Please enter a valid card number.
payment.card.not.supported=We\u2019re sorry, we only accept MasterCard or Visa cards.
payment.supported.card.error.data = This card is not supported.
payment.enter.cvv.data = Please enter at least 3 digits.
payment.valid.card.error = Please enter a 16 digit number
payment.valid.card.error1 = Please enter a true digit number
payment.valid.card.required.length = Enter valid CVV
payment.enter.your.name = Please enter your name
payment.select.date = Enter expiry date
payment.select.month = Please select a valid month
payment.select.year = Please select a valid year
payment.enter.cvv = Enter your CVV
payment.enter.fullname = Please enter your full name
payment.select.billing.address = Please select your billing address
payment.card.expired = Please select a valid date
payment.mode.credit.card.not.supported = We're sorry, this payment mode cannot be supported. Please try another method.
payment.mode.not.supported = We're sorry, this payment mode cannot be supported. Please try another method.
cod.exceed.threshold.price = Oops, you've exceeded the maximum order price for Cash on Delivery. Remove one or two items and try again.
payment.address.enter.name = Please enter your name


payment.address.enter.line1 = please enter address line1
payment.address.enter.line2 = please enter address line2
payment.address.enter.town = please enter City or Town
payment.address.select.address = please select your state
paymentPage.expire = expire
paymentPage.expires = Expires
payment.address.select.state = please select your state

## Fast Pay
fastpay.signin.href.tag = to pay now with FastPay
fastpay.quantity.minvalue = Please adjust your quantity
fastpay.quantity.maxvalue = Please adjust your quantity
fastpay.quantity.required = Please enter a quantity
fastpay.quantity.validvalue = Please enter a valid quantity
fastpay.quantity.valueexceed.error = Please enter a value less than or equal to
fastpay.turnon.href = Turn on
fastpay.turnon.tag = FastPay
fastpay.cart.threshold.breach = You have exceeded the maximum quantity allowed
fastpay.cart.lowstock = Quantity you requested is not available
fastpay.pageReloaded = Oops, there was a hiccup from our end, sorry. Just try again, it'll work.
fastpay.system.error = Oops! Something went wrong. Please try again.
fastpay.text.payment.error.cod.exceed.threshold = Oops. FastPay is not possible because you have exceeded your Cash on Delivery order limit of {0} {1}. To complete your order, use the Add to Basket button and checkout. You can also change your FastPay preference to Credit or Debit Cards and complete your order.
fastpay.freebundle.promotion.error =We're sorry, this offer is no longer available.
pincode_notserved = We're sorry, we don't deliver to this address. Please add a new address.
deliveryaddress_notfound = We're sorry, your delivery address is missing.
paymentinfo_notfound = We're sorry, your payment information is missing.

landmarkCategoryPage.pagination.previouspage = Previous page
landmarkCategoryPage.pagination.nextpage = Next page
landmarkCategoryPage.quickview = Quick View
landmarkCategoryPage.quickview.viewfulldetails = View full details

## Category Page
landmarkCategoryPage.filter.size				= Size
landmarkCategoryPage.filter.view.all			= View all
landmarkCategoryPage.filter.Less				= Show less
landmarkCategoryPage.filter.brands				= brands
landmarkCategoryPage.brand						= Brand
landmarkCategoryPage.filter.color				= Color
landmarkCategoryPage.apply						= APPLY
landmarkCategoryPage.sortby						= SORT BY
landmarkCategoryPage.sortbyM					= Sort by:
landmarkCategoryPage.index.relevance			= Relevance
landmarkCategoryPage.index.alphabetical			= Alphabetical
landmarkCategoryPage.index.low.to.high			= Price - Low to High
landmarkCategoryPage.index.high.to.low			= Price - High to Low
landmarkCategoryPage.narrowby					= Narrow by
landmarkCategoryPage.filters					= Filters
landmarkCategoryPage.filter.clearall			= Clear all filters
landmarkCategoryPage.filter.price				= Price
landmarkCategoryPage.product					= Product
landmarkCategoryPage.products					= Products
landmarkCategoryPage.categories					= Categories
landmarkCategoryPage.close						= Close
landmarkCategoryPage.items3						= 3 items
landmarkCategoryPage.items5					    = 5 items
landmarkCategoryPage.currency					= <span class='rupee'>Rs</span>
landmarkCategoryPage.title 						= Online Shopping From LandmarkShops.com
landMarkAddressBook.Nevermind.text=Nevermind
landMarkPayment.header.user.payment.delete=Delete Payment

## My Account Page
landMarkMyAccountPage.header.user.my.account = My Account
landMarkMyAccountPage.header.user.my.account.description = Manage your profile, orders, preferences and more.
landMarkMyAccountPage.header.user.my.profile = Profile
landMarkMyAccountPage.header.user.my.profile.description = Manage your personal details.
landMarkMyAccountPage.header.user.my.orders = Orders
landMarkMyAccountPage.header.user.my.orders.description = View and track your orders.
landMarkMyAccountPage.header.user.my.addressbook = Address Book
landMarkMyAccountPage.header.user.my.addressbook.description = Manage your shipping and billing addresses.

landMarkMyAccountPage.header.user.my.payment = Saved Cards
landMarkMyAccountPage.header.user.my.payment.description = Manage your payment preferences
landMarkMyAccountPage.header.user.my.payment.CreditOrDebit = Credit or Debit Card
landMarkMyAccountPage.header.user.my.subscriptions = Communication
landMarkMyAccountPage.header.user.my.subscriptions.description = Manage your newsletter and email preferences.

landMarkMyAccountPage.header.user.my.reviews = Reviews
landMarkMyAccountPage.header.user.my.reviews.description = View all your reviews.

landMarkMyAccountPage.header.user.my.fastPay = FastPay
landMarkMyAccountPage.header.user.my.fastPay.description = Enjoy 1-click payments.

landMarkMyAccountPage.header.user.my.signout = Sign out
landMarkMyAccountPage.header.user.my.signout.description = Sign out from your account.

landMarkMyAccountPage.header.user.my.wishlist = Favourites
landMarkMyAccountPage.header.user.my.wishlist.description = View your most wanted products.
landMarkMyAccountPage.header.user.my.landmarkRewards.description = Earn and spend valuable loyalty points for instant savings.

landMarkMyAccountPage.header.user.my.profile.fname = First Name
landMarkMyAccountPage.header.user.my.profile.lname = Last Name
landMarkMyAccountPage.header.user.my.profile.email = Email
landMarkMyAccountPage.header.user.my.profile.gender = Gender
landMarkMyAccountPage.header.user.my.profile.gender.male = Male
landMarkMyAccountPage.header.user.my.profile.gender.female = Female
landMarkMyAccountPage.header.user.my.profile.update.title = Update profile

lmgUpdateProfile.firstName.invalid = Please enter your first name
lmgUpdateProfile.lastName.invalid = Please enter your last name
lmgUpdateProfile.facebook.disconnet = Disconnect
lmgUpdateProfile.facebook.connect = &nbsp; Connect to Facebook
lmgUpdateProfile.facebook.connect.favourite.products = Share your favourite products with friends and family on Facebook. We will never post anything without your permission.
lmgUpdateProfile.facebook.permission =
lmgUpdateProfile.facebook.connected.message = Share your favourite products with friends and family on Facebook.
lmgUpdateProfile.profile.good = Good
lmgUpdateProfile.email.invalid = Please enter a valid email address
lmgUpdateProfile.profile.genderType.required=Choose Gender Type

landMarkMyAccountPage.header.user.my.profile.update.success = Your new details have been successfully saved
landMarkMyAccountPage.header.user.my.profile.changePassword = Change your Password
landMarkMyAccountPage.header.user.my.profile.oldPassword = Old Password
landMarkMyAccountPage.header.user.my.profile.newPassword = New Password
landMarkMyAccountPage.header.user.my.profile.showPassword = Show
landMarkMyAccountPage.header.user.my.profile.button.updatePassword = Change Password
landMarkMyAccountPage.header.user.my.profile.currentPassword.mismatch = Your passwords don't match. Check them and try again.
landMarkMyAccountPage.header.user.my.profile.picture = Profile Picture
landMarkMyAccountPage.placeholder.user.my.profile.oldPassword = Enter your current password
landMarkMyAccountPage.placeholder.user.my.profile.newPassword = Enter your new password
## Payment Page
landMarkPayment.header.user.payment = Payment
landMarkPayment.header.user.manage.payment = Manage your payment preferences.
landMarkPayment.header.user.add.card = Add a new card
landMarkPayment.header.user.payment.sure = Are you sure you want to delete this card?
landMarkPayment.header.user.payment.address.delete = Delete Address
landMarkPayment.header.user.payment.nevermind = Nevermind
landMarkPayment.header.user.payment.default = DEFAULT
landMarkPayment.header.user.payment.default.set = Default
landMarkPayment.header.user.payment.name = Name on Card
landMarkPayment.header.user.payment.expiry = Expires on
landMarkPayment.header.user.payment.billing.address = Billing Address
landMarkPayment.header.user.payment.edit = Edit
landMarkPayment.header.user.payment.delete.text = Delete
landMarkPayment.header.user.payment.set.default = Set as default
landMarkPayment.header.user.payment.cash.last=Dude! Cash is so last year man!
landMarkPayment.header.user.payment.add.card.faster=Add your card for a faster checkout experience
landMarkPayment.header.user.payment.no.card=Whoops! You have no credit card
landMarkPayment.header.user.payment.shopping=Lets go shopping
landMarkPayment.header.user.payment.start.shopping=Start shopping
landMarkPayment.header.user.payment.card.number = Card Number
landMarkPayment.header.user.payment.expiry.date = Expiry Date
landMarkPayment.header.user.payment.number = Number
landMarkPayment.header.user.payment.add = Add
landMarkPayment.header.user.payment.new = New
landMarkPayment.header.user.payment.save = Save
landMarkPayment.header.user.payment.billing.address.text = Billing Address
landMarkPayment.header.user.payment.save.card.faster = Save this card for a faster checkout
landMarkPayment.header.user.payment.experience = experience
landMarkPayment.header.user.payment.default.card = Use this as my default card
landMarkPayment.header.user.payment.show.address = Show my address
landMarkPayment.header.user.payment.new=+ Add new billing address
landMarkPayment.header.user.payment.newadress = Add new billing address
landMarkPayment.header.user.payment.newadress.name = Full Name
landMarkPayment.header.user.payment.newadress.adressline1 = Address Line 1
landMarkPayment.header.user.payment.newadress.adressline2 = Address Line 2
landMarkPayment.header.user.payment.newadress.town = City or Town
landMarkPayment.header.user.payment.newadress.state = State
landMarkPayment.header.user.payment.address.add = Add a new address
landMarkPayment.header.user.payment.address.mandatory.fields =
landMarkPayment.header.user.payment.newadress.default.shipping = Use this as my default shipping address
landMarkPayment.header.user.payment.this.address = Add this address
landMarkPayment.header.user.payment.continue = Continue
landMarkPayment.header.user.payment.default.billing.address = Use this as my default billing address
landMarkPayment.header.user.payment.wallet = Wallets
landmarkPayment.addcard.new = Add a new card
landMarkPayment.header.user.address.addressEmpty = Oops! You have no shipping address.
landMarkPayment.header.user.address.addressEmptyText = Tell us where to send your goodies.
landMarkPayment.header.user.address.addressEmptyButton = Add your Address Now
landMarkPayment.header.user.address.adrsForm.header1 = Add your shipping address
landMarkPayment.header.user.address.adrsForm.header2 =
landMarkPayment.header.user.address.optional=(optional)
landMarkPayment.header.user.address.mobileno.message = (for order or delivery updates)
landMarkPayment.header.user.address.savebox.mobile = Mobile:
landMarkPayment.header.user.orders.savebox.mobile = Mobile Number:
landMarkPayment.header.user.payment.cvv = CVV
landMarkPayment.myaccount.user.payment.cardType = Card Type
landMarkPayment.myaccount.user.payment.creditCard = Credit Card
landMarkPayment.myaccount.user.payment.debitCard = Debit Card
landMarkPayment.myaccount.user.payment.debitCardType = DBCRD
landMarkPayment.myaccount.user.payment.creditCardType = CRDC
landMarkPayment.myaccount.user.payment.cvv.required = Enter your CVV
landMarkPayment.myaccount.user.payment.cardType.required = Choose Card Type
landMarkPayment.header.user.payment.creditcards = Credit Cards
landMarkPayment.header.user.payment.creditcard = Credit Card
landMarkPayment.header.user.payment.debitcards = Debit Cards
landMarkPayment.header.user.payment.debitcard = Debit Card
landMarkPayment.header.user.payment.addacard = Add a card
landMarkPayment.header.user.payment.netbanking = Net Banking
landMarkPayment.header.user.payment.cashondelivery = Cash On Delivery
landMarkPayment.header.user.payment.cod.charges  = COD Charges:
landMarkPayment.header.user.payment.cod.notavailable = We\u2019re sorry, this option is not available for Click & Collect.
landMarkPayment.header.user.payment.proudlyacceptfollowingmethods = We proudly accept the following payment methods
landMarkPayment.header.user.payment.cardNumber.placeholder= Enter your 16 digit card number
landMarkPayment.header.user.payment.wallets = Wallets
###Orders
landMarkOrders.ordersPlacedIn.text = orders placed in the
landMarkOrders.oneOrdersPlacedIn.text = order placed in the
landMarkOrders.orderRefine.one.month = 1 month
landMarkOrders.orderRefine.three.months = 3 months
landMarkOrders.orderRefine.six.months = 6 months
landMarkOrders.orderRefine.one.year = 1 year
landMarkOrders.orderId.text = Order number
landMarkOrders.orderId.text.short = Order
landMarkOrders.totalAmount.text = Total amount
landMarkOrders.status.text = Status
landMarkOrders.payment.text = Payment
landMarkOrders.viewDetails.text = View details
landMarkOrders.shipto.text = Ship to
landMarkOrders.header.orderDetails = Order Details
landMarkOrders.header.purchaseSummary = Purchase Summary
landMarkOrders.header.address = Address
landMarkOrders.header.courierTrackingId.text = Courier Tracking Number
landMarkOrders.header.shippedTo = Shipping Addresses
landMarkOrders.header.expires = Expires on
landMarkOrders.header.subTotal = Subtotal:
landMarkOrders.header.promoDiscount = Order Discount:
landMarkOrders.header.RefundVoucherDiscount = Credit Notes:
landMarkOrders.header.voucherDiscount = {0}
landMarkOrders.header.shukranSaving = Landmark Rewards Saving:
landMarkOrders.ordersPlacedIn.shukran.saving = -456
landMarkOrders.header.Buy2Get1Free = Buy 2 Get 1 Free
landMarkOrders.header.pickupFrom = Pickup From
landMarkOrders.header.maxFashion = Max Fashion
landMarkOrders.header.standardGroundShipping = Standard Ground Shipping:
landMarkOrders.header.standardGroundShipping.part2 = Standard Ground Shipping
landMarkOrders.header.paymentCost = Payment Cost
landMarkOrders.header.total = TOTAL
landMarkOrders.header.orderSummary = Order Summary
landMarkOrders.header.item = Item
landMarkOrders.header.description = Description
landMarkOrders.header.deliveryOption = Delivery Option
landMarkOrders.header.totalPrice = Total Price
landMarkOrders.header.quantity = Quantity:
landMarkOrders.header.change = Change
landMarkOrders.header.OMG = Hmmm. You have no orders.
landMarkOrders.header.start = Start Shopping
landMarkOrders.header.clothes=Hey! Your clothes are looking old!
landMarkOrders.header.newlook=Why not give yourself a new look?
landMarkOrders.deliveryOption.description=4-5 business days
landMarkOrders.standardGroundShipping.description=(4-5 business days)
landMarkOrders.standardGroundShipping.free=Free
landMarkOrders.order.filter.last=Last
landMarkOrders.order.filter.months= months
landMarkOrders.order.filter.month= month
landMarkOrders.order.filter.all= All orders
landMarkOrders.order.go.shopping = Let\u2019s fix that right away.
landMarkOrders.order.more= more
landMarkOrders.order.add= +
landMarkOrders.Order.expires= - Expires {0}, {1}
landMarkOrders.orderDiscounts.symbol=&#45;&nbsp;
landMarkOrders.paymentMode.zeroCost.voucher = Voucher
landMarkOrders.paymentMode.zeroCost.loyalty = Landmark Rewards
landMarkOrders.delivery.number.text = Delivery
landMarkOrders.courier.tracking.text = Courier Tracking
landMarkOrders.delivery.count = You\u2019ll receive your order in {0} {1}.
landMarkOrders.delivery.count.single = delivery
landMarkOrders.delivery.count.multiple = deliveries
landMarkOrders.product.unavailable = We're sorry, this product is no longer available.

## Verify order COD verification section
landMarkOrders.cod.verification.header = You\u2019re almost done! Please verify your order.
landMarkOrders.cod.verification.description.start = All you need to do is enter the verification code sent to
landMarkOrders.cod.verification.description.end =  &nbsp;below.
landMarkOrders.cod.verification.error = You\u2019ve entered an incorrect code. Please try again.
landMarkOrders.cod.verification.resend.description = I haven\u2019t received my code.
landMarkOrders.cod.verification.resend.link = Resend it now
landMarkOrders.cod.verification.resend.description2 = We\u2019ve re-sent your code. Please try again in
landMarkOrders.cod.verification.description = If you\u2019re unable to verify your order right now, no worries. We\u2019ll call you shortly to confirm it.
landMarkOrders.cod.verification.confirmation = After confirmation, your order should arrive
landMarkOrders.cod.verification.confirmation.success = Your order has been confirmed. <strong>It should arrive </strong>
landmarkOrders.cod.verification.gdms.confirmation.success = Your order has been confirmed.
landMarkOrders.cod.verification.button = Verify

##Address Book =
landMarkAddressBook.mobileno.invalid.length = Your mobile number must be 10 digits long. Please try again.
landMarkAddressBook.mobileno.invalid = Hmm, that doesn't seem to be a valid mobile number. Please check and try again.
landMarkAddressBook.Landmark = Landmark

landMarkAddressBook.Lmgaddresstype = Address Type
landMarkAddressBook.City =Town/City
landMarkAddressBook.state =State
landMarkAddressBook.area= Area
landMarkAddressBook.state.invalid = Which state do you live in?
landMarkAddressBook.mobileno=Mobile Number
landMarkAddressBook.submit=Save
landMarkAddressBook.fullname= Full Name
landMarkAddressBook.text.account.Address = Manage your shipping and billing addresses.
landMarkAddressBook.text.account.NewAddress = Add a new address
landMarkAddressBook.fullname.invalid = Please enter your full name.
landMarkAddressBook.Edit = Edit
landMarkAddressBook.Delete = Delete
landMarkAddressBook.default = Set as default
landMarkAddressBook.invalid.address.postcode = Reenter
landMarkAddressBook.form.field.invalid.numeric = Your details haven't been entered in the correct format. Try again.
landMarkAddressBook.text.account.yourAccount = My Account
landMarkAddressBook.line1 = Address Line 1
landMarkAddressBook.postcode = Pincode
landMarkAddressBook.line2 = Address Line 2
landMarkAddressBook.deletePopup.text = Are you sure?
landMarkAddressBook.deleteReview.text = Delete Review
landMarkAddressBook.deleteAddress.text = Delete Address
landMarkAddressBook.nevermind.text = Nevermind
landMarkAddressBook.default.text = Default
landMarkAddressBook.mobile = Mobile
landMarkAddressBook.line1.invalid = Your details haven't been entered in the correct format. Try again.
landMarkAddressBook.townCity.invalid = Which town or city do you live in?
landMarkAddressBook.postcode.invalid = Shipping is not available for this Pincode
landMarkAddressBook.addressType = Home, Office, Other
landMarkAddressBook.fullNamePlaceHolder = Enter your full name
landMarkAddressBook.address.enter.name = Please enter your name
landMarkAddressBook.placeholder.pincode=Enter your Pincode
landMarkAddressBook.address.enter.pincode = Please enter pincode
landMarkAddressBook.address.enter.line1 = Please enter address line1
landMarkAddressBook.addressLine1PlaceHolder = Flat/House number, Floor, Building
landMarkAddressBook.address.enter.town = Please Enter your City or Town
landMarkAddressBook.address.enter.mobile2 = Please enter mobile number
landMarkAddressBook.address.enter.mobile1 = Please enter mobile number
landMarkAddressBook.address.enter.mobile = Please enter mobile number
landMarkAddressBook.emptyText = Whoops! You have no shipping address.
landMarkAddressBook.emptyText.description = Let's find you a home
landMarkAddressBook.header.start = Add Your Address Now

##fastpay
landmarkFastPayAddress.shipping.address=Shipping Addresses
landmarkFastPayAddress.shipping.default.shipping=Please add a default shipping address
landmarkFastPayAddress.shipping.address.add= Add a new address
landmarkFastPayAddress.shipping.address.add.new=Add a new shipping address
landmarkFastPayAddress.shipping.address.mandatory.fields =All fields are mandatory unless otherwise specified
landmarkFastPayAddress.shipping.address.fullname=Full Name
landmarkFastPayAddress.shipping.address.addressline1=Address Line 1
landmarkFastPayAddress.shipping.addressaddressline2=Address Line 2
landmarkFastPayAddress.shipping.address.landmark=Landmark
landmarkFastPayAddress.shipping.address.type = Address Type
landmarkFastPayAddress.shipping.address.town=City or Town
landmarkFastPayAddress.shipping.address.state=State
landmarkFastPayAddress.shipping.address.mobile=Mobile Number
landmarkFastPayAddress.shipping.address.save=Save
landmarkFastPayAddress.shipping.address.state = Select your state
landmarkFastPayLanding.fastpay = FastPay
landmarkFastPayLanding.content = FastPay is our lightning-fast, 1-click payment feature. With FastPay, enjoy faster shopping by instantly checking out right from the product page.
landmarkFastPayLanding.start = Get Started Now
landmarkFastPayLanding.work = How does it work?
landmarkFastPayLanding.paymentmethod = Setup your default payment method
landmarkFastPayLanding.cardaccept = We accept Visa or MasterCard Debit or Credit Cards and Cash on Delivery.
landmarkFastPayLanding.defaultship = Setup your default shipping address
landmarkFastPayLanding.deliverytime = So we know where to ship your FastPay order to.
landmarkFastPayLanding.descr = That's it!
landmarkFastPayLanding.paynow = The  \u2018Pay Now With FastPay\u2019 button will be activated for you.
landmarkFastPay.fastpay = FastPay
landmarkFastPay.waytoshop = Enjoy 1-click payments with FastPay
landmarkFastPay.paymentmethods = Payment Methods
landmarkFastPay.default = Default
landmarkFastPay.card.name = Name on card
landmarkFastPay.card.expires = Expires on
landmarkFastPay.defaultpayment.method = Please Choose/add default payment method
landmarkFastPay.carditem=Default
landmarkFastPay.cashondelivery=Cash on Delivery
landmarkFastPay.payment.mode.standardchagre =Payment is made after delivery and standard charges at {0} apply
landmarkFastPay.boxbar.item=Set as default
landmarkFastPay.boxbar.item1=Set as default
landmarkFastPay.Shipping.address=Shipping Addresses
landmarkFastPay.adress.default=Default
landmarkFastPay.defaultshippingaddress=Please add a default shipping address
landmarkFastPay.myaction.about=About FastPay
landmarkFastPay.myaction.multisteporderprocess=FastPay allows you to shop instantly and lets you skip the usual multi-step order process.
landmarkFastPay.myaction.aboutfastpay=Please click here to <a href="{0}/faq#faq-question-group-03">read more</a> about FastPay.
landmarkFastPay.info.secuirity=256-bit Security
landmarkFastPay.info.secuirity.industry=Powered by industry-leading 256-bit security encryption to safeguard your data.
landmarkFastPay.lighting.fast=Lightning Fast
landmarkFastPay.shop.instantly=Shop instantly with just the press of a button.
landmarkFastPay.info.paymentmode=Pay by cash or card
landmarkFastPay.info.paymentmode.preferred = Choose your preferred payment method - Cash On Delivery, or Credit or Debit card.
landmarkFastPay.info.mobileshopping = Faster Mobile Shopping
landmarkFastPay.info.mobilefriendly = Enjoy FastPay on our mobile-friendly site.
landmarkFastPay.addcard.new = Add a new card
landmarkFastPay.addcard.mandatoryfield=
landmarkFastPay.addcard.number = Card Number
landmarkFastPay.addcard.name = Name on Card
landmarkFastPay.addcard.expiry = Expiry Date
landmarkFastPay.addcard.cvv = CVV
landmarkFastPay.billingaddress = Billing Address
landmarkFastPay.billingaddress.new = + Add new billing address
landmarkFastPay.billingaddress.show = Show my address
landmarkFastPay.billingaddress.newadress = Add new billing address
landmarkFastPay.billingaddress.newadress.name = Full Name
landmarkFastPay.billingaddress.newadress.adressline1 = Address Line 1
landmarkFastPay.billingaddress.newadress.adressline2 = Address Line 2
landmarkFastPay.billingaddress.newadress.town = Town or City
landmarkFastPay.billingaddress.newadress.state = State
landmarkFastPay.save = Save
landmarkFastPay.addadress.new = Add a new shipping address
landmarkFastPay.addadress.mandatoryfield =
landmarkFastPayAddress.shipping.address.type = Select your address type
landmarkFastPayAddress.shipping.address.type.home = Home
landmarkFastPay.linkbox.addcard = Add a new card
landmarkFastPayadress.default = Set as default
landmarkFastPay.shippingaddress.add.new = Add a new address
landmarkFastPayAddress.shipping.address.pincode = Pincode
landmarkFastPayAddress.shipping.address.city = City or Town
landmarkFastPayAddress.shipping.address.state = State
landmarkFastPayAddress.shipping.address.state.select = Select your state
landmark.shipping.address.area.select = Select your area
landmarkFastPayAddress.shipping.address.type.office = Office
landmarkFastPayAddress.shipping.address.optional = (optional)
landmarkFastPayAddress.placeholder.fullname = Please enter your full name
landmarkFastPayAddress.placeholder.pincode = Please enter your pincode
landmarkFastPayAddress.placeholder.address.line1 = Flat or house number, floor, and building
landmarkFastPayAddress.placeholder.address.line2 = Colony, street and locality
landmarkFastPayAddress.placeholder.address.landmark = A landmark will help us locate your address faster
landmarkFastPayAddress.placeholder.city = Select your City or Town
landmarkFastPayAddress.placeholder.mobile = Mobile Number
landmarkFastPayAddress.required.for.order = (for order
landmarkFastPayAddress.delivery = or delivery
landmarkFastPayAddress.related.update = updates)
landmarkFastPay.billingaddress.continue = Continue
landmarkFastPay.billingaddress.default.billing.address = Use this as my default billing address
landmarkFastPay.billingaddress.required = (Required for order or delivery updates)
landmarFastPay.toggle.button.error.message = Oops! Something's gone wrong. Please select a default payment method and shipping address and try again.
landmarkFastPay.toggle.button.error.message.address = Please select or add your default shipping address.
landmarkFastPay.toggle.button.error.message.payment = Please select a Default Payment method
landmarkFastPay.toggle.button.error.message = Oops! Something's wrong. Please select a default payment method and shipping address and try again.
landmarkFastPay.default.address.error.message = Please set a different default address for this payment method.
landmarkFastPay.default.payment.error.message = This payment method can't be set default for the selected address.
landmarkFastPayAddress.off = OFF
landmarkFastPayAddress.on = ON
landmarkFastPayAddress.pincode.available = Given Pincode is not Available
landmarkFastPayAddress.optional = (optional)
landmarkFastPay.mobile = Mobile:
landmarkFastPay.cardName.required = Please enter your name
landmarkFastPay.cardNumber.required = Enter your card number
landmarkFastPay.address.fullName.required = Enter your full name
landmarkFastPay.address.addressLine1.required = Enter your address line 1
landmarkFastPay.address.town.required = Enter your City or Town
landmarkFastPay.address.state.required = Enter your state
landmarkFastPay.address.pincode.required = Enter your pincode
landmarkFastPay.address.mobile.required = Enter your mobile no
landmark.address.area.required = Select your area
###Subscriptions
landmarkSubscriptions.Newsletter.Preferences = Newsletter Preferences
landmarkSubscriptions.Language.Preferences = Please select your preferred language:
landmarkSubscriptions.hear = How often do you want to hear from us?
landmarkSubscriptions.Daily = Daily
landmarkSubscriptions.Weekly = Weekly
landmarkSubscriptions.Never = Never
landmarkSubscriptions.preferredLang.required = Please select a language.
landmarkSubscriptions.Women.Fashion = Women's Fashion
landmarkSubscriptions.Baby.Girl = Baby Girl
landmarkSubscriptions.Electronics = Electronics
landmarkSubscriptions.Beauty = Beauty
landmarkSubscriptions.Men.Fashion = Men's Fashion
landmarkSubscriptions.Baby.Boy = Baby Boy
landmarkSubscriptions.Home = Home
landmarkSubscriptions.Shoes = Shoes
landmarkSubscriptions.Update.Subscrptions = Update subscriptions
landmarkSubscriptions.departments = What departments do you care about?
landmarkSubscriptions.departments.description = Please select the brands you want to hear from.
landmarkSubscriptions.Subscription = Communication
landmarkSubscriptions.Stock.Availability = Out of Stock Notifications
landmarkSubscriptions.Stock.Availability.BackInStock = We'll let you know once it's back in stock.
landmarkSubscriptions.Unsubscribe = Unsubscribe
landMarkAddressBook.unsubscribe.text = Unsubscribe
inStockNotification.product.color = Colour:
inStockNotification.product.size = Size:
multiproductpromo.show.details = <span>Show all</span> details
landMarkMyAccountPage.header.user.my.subcrption.update.success = Your preferences have been saved.
landmarkSubscriptions.subscribe.fashion.newsletter = Remove
landmarkSubscriptions.error.never = &nbsp;Please register from the footer
landmarkSubscriptions.subscribe.various.news = Subscribe to our newsletter to get the latest offers and updates.
landmarkSubscriptions.subscribe.start = Get Started Now

#### MyAccount-Review Page
landMarkReviews.reviewsPlacedIn.text = Reviews posted in
landMarkReviews.starsCount = out of five stars
landMarkReviews.statusText = Status
landMarkReviews.editLink = Edit
landMarkReviews.deleteLink = Delete
landMarkReviews.emptyText = You have no reviews yet.
landMarkReviews.emptyText.description = Shop your favourite products and tell us what you think.
landMarkReviews.comment.readMore = Read more

### MyAccount WishList
landMarkWishList.emptyText = Hmmm. No favourites as yet.
landMarkWishList.emptyText.description = Favourite the products you love and buy them whenever you like!
landMarkWishList.removeText = Remove All
landMarkWishList.notAvailable = Discontinued product
landMarkWishList.outOfStock = Out of stock
landMarkWishList.undoText = was removed from your Favourites.

### Department Page
landmarkDepartmentPage.showMore = Show more
landmarkDepartmentPage.showLess = Show less
landmarkDepartmentPage.showAllBrands = Show All Brands
landmarkDepartmentPage.brand = Brand
landmarkDepartmentPage.browse = Browse
landmarkDepartmentPage.backto = Back to
landmarkDepartmentPage.gallery.previouslink = Previous
landmarkDepartmentPage.gallery.nextlink = Next

## Brand Pages
landMarkLifestylePage.footer.heading = ALL ABOUT LIFESTYLE
landMarkBrandPage.banner.next = Next
landMarkBrandPage.banner.previous = Previous
landMarkMaxPage.footer.heading = ALL ABOUT MAX
landMarkBrandPage.categorycarousel.previouslink = Previous
landMarkBrandPage.categorycarousel.nextlink = Next
landMark.pagetitle.lifestyle = LIFESTYLE
landMark.pagetitle.max = MAX
landMarkBrandPage.storelocator = STORE LOCATOR
landMarkBrandPage.locate = LOCATE
landMarkBrandPage.viewall = View all {0} locations
landMarkBrandPage.subscribe = Subscribe
landMarkBrandPage.enter.email = Enter your email

##Clearance pages
landmarkClearancePage.categories = Categories
landmarkClearancePage.top.seller = Top Sellers
landmarkClearancePage.text.shop = Shop

##Store Locator
storelocator.type.city = Type or select city
storelocator.address = Address:
storelocator.phone = Phone
storelocator.timing = Timing
storelocator.getdirections = Get Directions
storelocator.close = close
## Search Page
landmarkSearchPage.results = Results
landmarkSearchPage.search = Search
landmarkSearchPage.filter.size = Size
landmarkSearchPage.filter.view.all = Show more
landmarkSearchPage.filter.Less = Show less
landmarkSearchPage.filter.brands = Not required
landmarkSearchPage.brand = Brand
landmarkSearchPage.filter.color = Color
landmarkSearchPage.apply = Apply
landmarkSearchPage.sortBy = Sort by
landmarkSearchPage.sortByM = Sort by:
landmarkSearchPage.index.relevance = Relevance
landmarkSearchPage.index.alphabetical = Alphabetical
landmarkSearchPage.index.low.to.high = Price - Low to High
landmarkSearchPage.index.high.to.low = Price - High to Low
landmarkSearchPage.index.recent.products = New Arrivals
landmarkSearchPage.index.discount = Discount
landmarkSearchPage.index.numeric = Numeric
landmarkSearchPage.index.alpha = Alpha
landmarkSearchPage.narrowby = Narrow by
landmarkSearchPage.filters = Filters
landmarkSearchPage.filter.clearall = Clear all filters
landmarkSearchPage.filter.price = Price
landmarkSearchPage.product = Product
landmarkSearchPage.products = Products
landmarkSearchPage.categories = Categories
landmarkSearchPage.close = Close
landmarkSearchPage.items3 = 3 items
landmarkSearchPage.items5 = 5 items
landmarkSearchPage.currency = <span class='rupee'>Rs</span>
landmarkSearchPage.title = Online Shopping From LandmarkShops.com
landmarkSearchPage.pagination.previouspage = Previous page
landmarkSearchPage.pagination.nextpage = Next page
landmarkSearchPage.youSearchedFor = You searched for
landmarkSearchPage.noResult = No Results

## Thank you page
landmarkCheckoutThankYouPage.thankyou.msg = Thank you
landmarkCheckoutThankYouPage.order.placed = We appreciate your order.
landmarkCheckoutThankYouPage.order.number = Your order number is
landmarkCheckoutThankYouPage.cod.msg= We'll call you shortly to confirm your order and once confirmed, you'll get it
landmarkCheckoutThankYouPage.cod.hc.msg = We'll call you shortly to confirm your order.
landmarkCheckoutThankYouPage.area.extradays = If your order is in <a href="#" data-toggle="modal" class="_link-grey" data-target="#arealist-modal">these areas</a>, then delivery may take a few extra days.
landmarkCheckoutThankYouPage.delvery.msg = You'll get your order
landmarkCheckoutThankYouPage.view.account.before = View
landmarkCheckoutThankYouPage.view.account.link = My Account
landmarkCheckoutThankYouPage.view.account.after = to know more
landmarkCheckoutThankYouPage.you.may.enjoy = YOU MAY ALSO LIKE
landmarkCheckoutThankYouPage.create.account = Create a new account
landmarkCheckoutThankYouPage.create.account.benefit = Your account makes shopping with us faster,smarter, more rewarding and simpler.
landmarkCheckoutThankYouPage.create.account.password = Password
landmarkCheckoutThankYouPage.create.account.password.show = Show
landmarkCheckoutThankYouPage.create.account.password.good = Good
landmarkCheckoutThankYouPage.create.account.label = Create an account
landmarkCheckoutThankYouPage.gift.voucher= We are sending you a gift voucher!
landmarkCheckoutThankYouPage.gift.voucher.details= Your voucher code will be sent to you by email once your order is complete.
landmarkCheckoutThankYouPage.extradays.title = Areas that require a few extra days for delivery
landmarkCheckoutThankYouPage.getDirections.label = Get Directions

# click and collect texts
landmarkCheckoutThankYouPage.clickcollect.paragraph.title = Here's what happens next:
landmarkCheckoutThankYouPage.clickcollect.paragraph.line1 = You'll receive an email and a text message when your order is ready to be collected.
landmarkCheckoutThankYouPage.clickcollect.paragraph.line2 = Head to your selected collection point with a copy of the email or text message, along with your ID card or passport.

# Delivery Estimate in Thankyou Page =
thankyou.delivery.nextday = on the next business day.
thankyou.delivery.estimate = within 4-5 business days.
thankyou.delivery.pincode.estimate.pre = within
thankyou.delivery.pincode.estimate.post = business days

##Forgot password pop up
landmarkForgotPassword.request.email.ajax.failed.message = We're sorry, we've run into a technical issue. Please try again later.
landmarkForgotPassword.header = No worries. Enter your email address and we'll help you reset it.
landmarkForgotPassword.label.email = Email
landmarkForgotPassword.label.submit = Submit
landmarkForgotPassword.label.rest.pwd = A reset password link is on its way to your inbox.
landmarkForgotPassword.label.thankyou = Thank you,
landmarkForgotPassword.label.resend = Resend?

landmarkForgotPassword.required.email = Tell us where to send the link to reset your password and we'll get right on it.
landmarkForgotPassword.invalid.email = Oops! This doesn't seem to be a valid email address. Please try again.
landmarkForgotPassword.email.sent.success.message = Please follow the instructions sent to your email address to reset your password.
landmarkForgotPassword.email.sent.fail.message = We don't have this email address saved. Please check and try again.
landmarkForgotPassword.captcha.failed = Your recaptcha challenge answer is invalid. Please check and try again.

##Reset password page
landmarkResetPassword.title = Reset Your Password
landmarkResetPassword.label.newpwd = New Password
landmarkResetPassword.label.confirmpwd = Confirm Password
landmarkResetPassword.label.show = Show
landmarkResetPassword.label.submit = Submit
landmarkResetPassword.placeholder.newpwd = Enter your new password
landmarkResetPassword.placeholder.confirmpwd = Confirm your password

landmarkResetPassword.unmatchedpwd.message = Your passwords do not match. Please check and try again.
landmarkResetPassword.required.newpwd = You must enter a new password
landmarkResetPassword.required.confirmpwd = Please confirm your password
landmarkResetPassword.minlength.message = Come on, you can do better. You need a password with at least 6 characters

landmarkResetPassword.newpasswordtips.title = New password TIPS
landmarkResetPassword.newpasswordtips.one = Make sure your password is a minimum of six characters and includes a combination of letters and at least one number or special character. Capitalization matters, but it is not required.
landmarkResetPassword.newpasswordtips.two = Do not use personal information.

#Forgot password pop up
landmarkForgotPassword.updated.success.message = Great! Your password has been changed successfully. Sign in to access your account.
landmarkForgotPassword.update.token.invalidated.message = Hmm. It looks your reset password link is no longer valid. Click on <a href="#" id="standalone-signin-form-link" class="link" data-toggle="modal" data-target="#forgotpassword-modal" data-dismiss="modal" aria-label="Close">Forgot password?</a> to try again.
landmarkForgotPassword.update.token.invalid.message = We're sorry, this link is invalid.
##Register page
landmarkRegister.invalid.form.attribute = Your details haven't been entered correctly. Please try again.

## Help page
landmarkHelp.stillHaveQuestions.desktop.header.title = Still have questions?
landmarkHelp.stillHaveQuestions.mobile.header.title = Contact us
landmarkHelp.stillHaveQuestions.desktop.header.desc = Hello from our Customer Support team! We're here to help.
landmarkHelp.stillHaveQuestions.mobile.header.desc = We\u2019re here to help you. Get in touch with us from any of these ways:
landmarkHelp.stillHaveQuestions.email.title = Write to us
landmarkHelp.stillHaveQuestions.email.desc = Drop us a line and we'll get back to you as fast as we can.
landmarkHelp.stillHaveQuestions.phone.title = Talk to us
landmarkHelp.stillHaveQuestions.phone.desc = Call us on 1800-123-1555 </br> Monday to Saturday, 9:00 AM to 9:00 PM
landmarkHelp.stillHaveQuestions.twitter.title = Tweet us
landmarkHelp.stillHaveQuestions.twitter.desc = Reach out in 140 characters! We\u2019re <a href="https://twitter.com/{0}">@{0}<a>
landmarkHelp.stillHaveQuestions.facebook.title = Facebook us
landmarkHelp.stillHaveQuestions.facebook.desc = <a href="https://www.facebook.com/{0}">Connect with us on your favourite social network.</a>
landmarkHelp.stillHaveQuestions.chat.title = Chat with us
landmarkHelp.stillHaveQuestions.chat.online.desc = Get answers in real-time. We\u2019re here to help.
landmarkHelp.stillHaveQuestions.chat.online.text = Chat now!
landmarkHelp.stillHaveQuestions.chat.offline.desc = We'll be back online soon.
landmarkStoreLocator.search.text = Find a store by city or area
landmarkHelp.stillHaveQuestions.chat.msg = Call and Chat availability time,
landmarkHelp.stillHaveQuestions.email.desc.contactus = Write us an email now!
homecentreae.landmarkHelp.stillHaveQuestions.phone.desc.contactus = Saturday to Thursday, 9:00AM to 10:00PM
lifestyleae.landmarkHelp.stillHaveQuestions.phone.desc.contactus = 7 days a week, 9:00AM to 9:00PM
babyshopae.landmarkHelp.stillHaveQuestions.phone.desc.contactus = 7 days a week, 9:00AM to 9:00PM
centrepointae.landmarkHelp.stillHaveQuestions.phone.desc.contactus = 7 days a week, 9:00AM to 9:00PM
maxae.landmarkHelp.stillHaveQuestions.phone.desc.contactus = 7 days a week, 9:00AM to 9:00PM
shoemartae.landmarkHelp.stillHaveQuestions.phone.desc.contactus = 7 days a week, 9:00AM to 9:00PM
splashae.landmarkHelp.stillHaveQuestions.phone.desc.contactus = 7 days a week, 9:00AM to 9:00PM
homeboxae.landmarkHelp.stillHaveQuestions.phone.desc.contactus = Saturday to Thursday, 9:00AM to 6:00PM
landmarkHelp.stillHaveQuestions.phone.tel.contactus = {0}
landmarkHelp.stillHaveQuestions.phone.tel.contactus.text = Call us on
landmarkHelp.stillHaveQuestions.twitter.desc.contactus = Reach out in 140 characters!
landmarkHelp.stillHaveQuestions.twitter.desc2.contactus = We're coming soon!
landmarkHelp.stillHaveQuestions.facebook.title.contactus = Chat with us
landmarkHelp.stillHaveQuestions.facebook.desc.contactus = Get answers in real-time. We\u2019re here to help.
landmarkHelp.stillHaveQuestions.chat.online = Online
landmarkHelp.stillHaveQuestions.heading.title.contactus = Contact us
lifestyleae.landmarkHelp.stillHaveQuestions.heading.desc.contactus = Help, support and information on LifestyleShops? You will find it here.
babyshopae.landmarkHelp.stillHaveQuestions.heading.desc.contactus = Help, support and information on BabyshopStores? You will find it here.
centrepointae.landmarkHelp.stillHaveQuestions.heading.desc.contactus = Help, support and information on CentrepointStores? You will find it here.
homecentreae.landmarkHelp.stillHaveQuestions.heading.desc.contactus = Help, support and information on Home centre? You will find it here.
maxae.landmarkHelp.stillHaveQuestions.heading.desc.contactus = Help, support and information on MaxFashion? You will find it here.
shoemartae.landmarkHelp.stillHaveQuestions.heading.desc.contactus = Help, support and information on ShoemartStores? You will find it here.
splashae.landmarkHelp.stillHaveQuestions.heading.desc.contactus = Help, support and information on SplashFashions? You will find it here.
landmarkHelp.stillHaveQuestions.heading.para.desc.contactus = We\u2019re here to help you. Get in touch with us from any of these ways:

## Feedback form
landmarkFeedback.heading.title.feedback.message.text = Feedback
landmarkFeedback.heading.title.feedback = Feedback
landmarkFeedback.heading.title.feedback.text = Enter your feedback here
landmarkFeedback.heading.title.feedback.description = Got suggestions, improvements or comments? We\u2019re all ears.
landmarkFeedback.heading.title.feedback.name = Full Name
landmarkFeedback.heading.title.feedback.name.invalid = Please enter your name
landmarkFeedback.heading.title.feedback.name.valid = Please enter valid name
landmarkFeedback.heading.title.feedback.name.text = Your name
landmarkFeedback.heading.title.feedback.email.invalid = Please enter valid email
landmarkFeedback.heading.title.feedback.name.maxlength.limit = Please enter no more than {0} characters
landmarkFeedback.heading.title.feedback.email.valid = Please enter your email
landmarkFeedback.heading.title.feedback.email.text = Your email address
landmarkFeedback.heading.title.feedback.button.title = Send your Feedback
landmarkFeedback.heading.title.feedback.about = Your feedback is about our:
landmarkFeedback.heading.title.feedback.online = Online experience
landmarkFeedback.success.msg = <strong>Your feedback is successfully sent.</strong></br> Thanks for sharing your feedback we appreciate it.
landmarkFeedback.heading.title.feedback.instore=In-store experience
landmarkFeedback.heading.title.feedback.online.value=Online
landmarkFeedback.heading.title.feedback.instore.value=In-Store
#Order Summary validation localized error messages
cart.delivery.address.missing = Please fill in your Delivery Address.
cart.paymentmode.missing = Payment Mode is missing
cart.paymentinfo.missing = Please fill in your payment information.
cart.voucher.expired = Expired or Invalid voucher applied
cart.multiple.vouchers.concept=We\u2019re sorry, you\u2019ve already used a voucher.
product.discontinued.error.msg = Product is discontinued
product.outofstock.error.msg = Product is Out of stock
product.threshold.error.msg = Product has reached its threshold limit.
product.mimimumprice.error.msg = Product price is lower then the minimum price.
cart.voucher.limit.exceeded=Exceeded the number of vouchers limit.

#lmgAddressTpe
landmarkAddressTypes.availableTypes = Home,Office
landmark.basket.validation.cart.generic.global.error = Oops, we've spotted some errors. Please fix them to continue.
landmark.basket.validation.cart.delivery.address.missing = You must provide a delivery address in order to go to the next step.
landmark.basket.validation.cart.delivery.mode.missing = Delivery Mode is Missing.
landmark.basket.validation.cart.paymentmode.missing = Payment Mode is Missing. Please select a Payment method.
landmark.basket.validation.cart.paymentinfo.missing = Please fill in your payment information.
landmark.basket.validation.cart.voucher.expired = We\u2019re sorry, this voucher is invalid. Please remove it to continue {0}
landmark.basket.validation.cart.entries.missing = Cart Entries are Missing.
landmark.basket.validation.cart.calculation.error = Oops! looks like you've run into an issue. Please fix it to continue shopping.
landmark.basket.validation.cart.validation.failed = Card Validation is Failed.
landmark.basket.validation.product.discontinued.error.msg = Oops! This product has now been discontinued. Please remove it from your basket to continue
landmark.basket.validation.product.outofstock.error.msg = Oops! This product has gone Out Of Stock. Please remove it from your basket to continue
landmark.basket.validation.product.lowstock.error.msg = Oops! This product has gone Low Stock. Please change the quantity to continue
landmark.basket.validation.product.threshold.error.msg = We\u2019re sorry, you\u2019ve exceeded the maximum permitted quantity of {2}. Please reduce the quantity and try again.
landmark.basket.validation.product.minimum.price.display = The Product <a href="{1}">{0}</a> price has gone Minimum than the Display Price.
landmark.basket.validation.promotion.group.general.error = Something went wrong in Promotion Grouping. Please Expand and fix the Error.
landmark.basket.validation.text.payment.error.cod.optiob.exceed.threshold = You have exceeded COD order limit, It should be below {0} {1} amount
landmark.basket.validation.payment.availability.missing = The Payment method "{0}" is not available for Pincode {1}. Please select different payment method.
landmark.basket.validation.cart.doublePromotion.error = Oops! Looks like we've run into an issue. Please recheck the total amount and place your order.
landmark.basket.validation.loyalty.partner.unreachable=Oops! We can not redeem your points at this moment because of some technical problem.
landmark.basket.validation.loyalty.not.enough.points=Oops! We can not redeem your points at this moment because you have only {0} points left in your rewards card.
landmark.basket.validation.loyalty.redeem.failed=Oops! We can not redeem your points at this moment because {0}
landmark.basket.validation.loyalty.minimum.points.to.redeem=Oops! The minimum points to redeem is {0}
landmark.basket.validation.loyalty.card.is.not.ok.to.redeem=Oops! Your card is not ready to redeem points. Please contact customer care.
landmark.loyalty.point = point
landmark.loyalty.points = points
landmark.basket.validation.cart.multiple.vouchers.concept=We\u2019re sorry, you\u2019ve already used a voucher for {0}
landmark.basket.validation.cart.voucher.limit.exceeded=You have exceed the number of vouchers limit for an order {0}
landmark.basket.validation.clickcollect.cart.ineligible= Your cart is not eligible for click and collect
landmark.basket.validation.clickcollect.pos.invalid.ineligible= Selected store is not eligible for cart

#My-Account localized js messages
myaccount.error.lettersonly = Please enter alphabets only
myaccount.error.numbersonly = Pincode should contain numbers only
myaccount.error.alphanumericsOnly = Please enter alphanumerics only
myaccount.error.emailonly = Please enter a valid email address
myaccount.select.state = Select your State
myaccount.state.required = State required
myaccount.select.address = Select your address
myaccount.select.expiryMonth = Select your expiry month
myaccount.select.expiryYear = Select your expiry year
myaccount.address.required = Address required
myaccount.payment.nameOnCard = Please enter your name

landmark.article.helpful.question = Was this article helpful?
landmark.article.helpful.yes = Yes
landmark.article.helpful.no = No
landmark.article.helpful.msg = % of people found this helpful.

landmark.label.refine = Refine
landmark.label.cancel = Cancel

# footer newsletter messages
landmarkNewsletter.submit.processing.message= Please wait, processing your subscription request...
landmarkNewsletter.submit.new.signup.message= Thanks for signing up to our newsletter.
landmarkNewsletter.submit.already.signup.message= Thanks, you are already subscribed to our newsletter.
landmarkNewsletter.submit.unable.signup.message= Oops, we didn't recognise this email address. Please try again.
landmarkNewsletter.submit.server.error.message= Server error, please try again later.


# Help page messages
landmarkHelpPage.title = Search for help
landmarkHelpPage.no.faq = No FAQ found for your
landmarkHelpPage.faqs.text = Help Centre
landmarkHelpPage.faq.text = FAQ
landmarkHelpPage.faq.found.for = found for
landmarkHelpPage.faq.notFoundFor = No FAQ found for
landmarkHelpPage.help.text = What are you look for?

landmark.brands.morebrands = More Brands
landmark.brands.comingsoon = Coming Soon...


#####################################################################################################
#######################################Checkout properties#############################################
#####################################################################################################
##Starts : Progress bar changes
lmgcheckout.progresbar.shipping=Shipping
lmgcheckout.progresbar.payment=Payment
lmgcheckout.progresbar.place=Place
lmgcheckout.progresbar.your=your
lmgcheckout.progresbar.order=order
lmgcheckout.progresbar.step.one=1
lmgcheckout.progresbar.step.two=2
lmgcheckout.progresbar.step.three=3
##Ends : Progress bar changes

##Starts : Shipping page changes
lmgcheckout.shippingpage.select.shipping.title=Select a shipping method
lmgcheckout.shippingpage.clickcollect.title=Click & Collect
lmgcheckout.shippingpage.clickcollect.label=Collect your order from a store of your choice.
lmgcheckout.shippingpage.clickcollect.pickuppoint.text=Find a pickup point
lmgcheckout.shippingpage.clickcollect.pickuppoint.subtext = Search or use our map view to find a pickup point
lmgcheckout.shippingpage.clickcollect.selectstore.button =Select store
lmgcheckout.shippingpage.homedelivery.title=Home Delivery
lmgcheckout.shippingpage.homedelivery.label=Get your product delivered to your home.
lmgcheckout.shippingpage.mapview.label=Map view
lmgcheckout.shippingpage.selectstore.label=Select your collection store
lmgcheckout.shippingpage.searchstore.label=Search or use our map view to find your nearest store
lmgcheckout.shippingpage.searchbymalloremirate.label=Search by Mall or Emirate
lmgcheckout.shippingpage.nostorefound.message=We're sorry, there are no stores available in
lmgcheckout.shippingpage.viewfullstorelist.label=View our full list of stores
lmgcheckout.shippingpage.aacontactdetails.label=Add your contact details
lmgcheckout.shippingpage.shipping.title =Shipping
lmgcheckout.shippingpage.Proceed.To.Payment=Proceed To Payment

lmgcheckout.reviewpage.collectfrom.label=Collect from:
lmgcheckout.reviewpage.addresss.label=Address:
lmgcheckout.reviewpage.workinghours.label= Working Hours:
lmgcheckout.reviewpage.phonenumber.colon=Phone Number:

lmgcheckout.shippingPage.selectShippingMethod=Select a shipping method
lmgselectaddress.label=Select a shipping address
lmgselectyouraddress.label=Select your shipping address
lmgaddaddress.link=Add a new Address
lmgshiptoaddress.text=Ship to this Address
lmgaddyouraddress.title=Add your shipping address
lmgaddaddress.title=Add a new Address
lmgaddaddress.message=
checkout.page.order.price= Price:
checkout.page.order.subtotal= Subtotal:
lmgaddaddress.showalladdress=Show all Addresses
lmgaddaddress.hidealladdress=Hide all Addresses
lmgaddaddress.label.fullname=Full Name
lmgaddaddress.label.mobile=Mobile
lmgaddaddress.label.email=Email
lmgaddaddress.label.pincode=Pincode
lmgaddaddress.label.addressline1=Address Line 1
lmgaddaddress.label.addressline2=Address Line 2
lmgaddaddress.label.landmark=Landmark
lmgaddaddress.label.addresstype=Address Type
lmgaddaddress.label.townorcity=City or Town
lmgaddaddress.label.city=City
lmgaddaddress.label.area=Area
lmgaddaddress.label.street.name=Floor, Apartment No.
lmgaddaddress.label.building.name=Building Name/Villa No
lmgaddaddress.label.Landmark.optional=Landmark (optional)
lmgaddaddress.label.Address.Type.optional=Address Type (optional)
lmgaddaddress.label.state=State
lmgaddaddress.label.mobilenumber=Mobile
lmgaddaddress.label.defaultaddress=Use this as my default shipping address
lmgaddaddress.termsandcondition.part1=By continuing to checkout you agree to our
lmgaddaddress.termsandcondition.part3=By continuing to checkout you agree to our Terms and Conditions
lmgaddaddress.termsandcondition.part2=Terms and Conditions
lmgaddaddress.placeholder.fullname=Enter your first name & last name
lmgaddaddress.placeholder.email=Enter your email
lmgaddaddress.placeholder.pincode=Enter your Pincode
lmgaddaddress.placeholder.addressline1=Flat/House number, Floor, Building
lmgaddaddress.placeholder.addressline2=Colony/Society, Street, Locality/Area
lmgaddaddress.placeholder.landmark=A landmark helps us in locating your address better
lmgaddaddress.placeholder.addresstype=Select your address type
lmgaddaddress.placeholder.city=Select your City or Town
lmgaddaddress.placeholder.state=Select your State
lmgaddaddress.placeholder.mobilenumber.part1=
lmgaddaddress.placeholder.mobilenumber.part2=
lmgaddaddress.placeholder.mobilenumber.part3=
lmgaddaddress.optional=(optional)
lmgaddaddress.placeholder.countrycode=+91
lmgaddaddress.defaultaddress.text=Default
##Ends : Shipping page changes

##Starts : Shipping page validation message
lmgform.field.required.firstname=Full name is required
lmgform.field.invalid.firstname=Full name should contain alphabets only
lmgform.field.outofrange.firstname=Full name should not be more than 35 characters
lmgform.field.required.fullname=Full name is required
lmgform.field.required.pickupPoint=Please select a pickup point
lmgform.required.fullname = Please enter your full name
lmgform.required.fullname1 = Please enter your name
lmgform.field.required.email=Email is required
lmgform.field.invalid.email=Please enter a valid email address
lmgform.field.required.postcode=Pincode is required
lmgform.field.invalid.postcode=Pincode should contain numbers only
lmgform.field.required.line1=Address line1 is required
lmgform.field.invalid.line1=Address line1 should contain alphanumerics only
lmgform.field.invalid.line2=Address line2 should contain alphanumerics only
lmgform.field.invalid.landmark=Landmark should contain alphanumerics only
lmgform.field.required.town=City or Town is required
lmgform.field.invalid.town=City or Town should contain alphanumerics only
lmgform.field.required.region=State is required

lmgform.field.required.mobilenumber=Mobile number is required
unavailable.selected.delivery.address.mssg=Selected delivery address not found
selected.delivery.address.area.notvalid.msg=Please select a valid area code
lmg.location.map.label = <strong>Pin your location</strong> (optional - this will help us for faster delivery)
lmg.location.map.location =Pin your location
##Ends : Shipping page validation message

##Starts : Order review page messages
lmgcheckout.order.review.summary=Order Summary
lmgcheckout.order.review.palce.button=Place your order
lmgcheckout.order.review.bank.message=You will be securely redirected to {0} to sign in and approve this payment.
lmgcheckout.order.review.wallet.message=You will be redirected to your wallet's website to authorize the payment.
lmgcheckout.order.review.shipto=Ship to:
lmgcheckout.order.review.change.link=Change
lmgcheckout.order.review.zerocost.label=Voucher
lmgcheckout.order.review.Expires.label=Expires
lmgcheckout.order.review.payment.method=Payment Method:
lmgcheckout.order.review.color=Color:
lmgcheckout.order.review.size=Size:
lmgcheckout.order.review.quantity=Quantity:
lmgcheckout.order.review.points.spent=Points spent
lmgcheckout.order.review.earned.points=Earned points
lmgcheckout.order.review.total=TOTAL
lmgcheckout.order.review.subtotal=Subtotal:
lmgcheckout.order.review.promo.discount=Promo Discount
lmgcheckout.order.review.rewards.savings=Landmark Rewards savings:
lmgcheckout.order.review.your.order=Review your order
lmgcheckout.order.review.item=Item
lmgcheckout.order.review.description=Description
lmgcheckout.order.review.total.price=Total Price
lmgcheckout.order.review.multiproduct.more=more
lmgcheckout.order.review.show.details=<span>Show all</span> details
lmgcheckout.order.review.shipping.businessDays=({0} business days)
lmgcheckout.order.review.address.mobile=Mobile:
lmgcheckout.express = Express
lmgcheckout.free =
lmgcheckout.today = Today
checkout.error.authorization.failed                                                       = We're sorry, your payment attempt has failed. Please check your details and try again.
checkout.error.cart.notcalculated                                                         = Oops! looks like you've run into an issue. Please fix it to continue shopping.
lmgcheckout.checkout.discount=Promo Discount:
pdp.tellWorld = What do you think about this product?

##Ends : Order review page messages


#Change Payment mode
cod.exceed.threshold.price=Cash on delivery, exceed threshold price .
undeliverable.postcode.mssg=Shipping is not available for this Pincode
gdms.slot.notavailable.error.message=Oops! The delivery slot you've chosen is no longer available. Please select a new delivery date and time.

landmark.homecentre.express.delivery.legend.message = Express delivery (<b>{0}</b> extra)
landmark.homecentre.normal.delivery.legend.message = Scheduled delivery
landmark.homecentre.express.delivery.slot.message = Express delivery slot message
landmark.homecentre.delivery.error.message=We're sorry, we're unable to deliver your Home Centre products at the moment. Check back later or continue shopping our other brands.
landmark.homecentre.delivery.items.remove.error.message=We're sorry, we're unable to deliver your Home Centre products at the moment. Please remove them from your basket and proceed to checkout.
landmark.homecentre.delivery.restricted.slot.error.message=We're sorry, we're unable to deliver your Home Centre products at the moment. Check back later or continue shopping our other brands
landmark.babyshop.delivery.error.message=We're sorry, we're unable to deliver your Babyshop products at the moment. Check back later or continue shopping our other brands.
landmark.homebox.delivery.error.message=We're sorry, we're unable to deliver your Home Box products at the moment. Check back later or continue shopping our other brands.
landmark.babyshop.delivery.items.remove.error.message=We're sorry, we're unable to deliver your Babyshop products at the moment. Please remove them from your basket and proceed to checkout.
landmark.homebox.delivery.items.remove.error.message=We're sorry, we're unable to deliver your Home Box products at the moment. Please remove them from your basket and proceed to checkout.
landmark.babyshop.delivery.restricted.slot.error.message=We're sorry, we're unable to deliver your Babyshop products at the moment. Check back later or continue shopping our other brands
landmark.homebox.delivery.restricted.slot.error.message=We're sorry, we're unable to deliver your Home Box products at the moment. Check back later or continue shopping our other brands
landmark.babyshop.homecentre.delivery.error.message=We're sorry, we're unable to deliver your Babyshop and Home Centre products to this address. Please select a different shipping address or continue shopping our other brands.
landmark.babyshop.homebox.delivery.error.message=We're sorry, we're unable to deliver your Babyshop and Home Box products to this address. Please select a different shipping address or continue shopping our other brands.
landmark.babyshop.homebox.homecentre.delivery.error.message=We're sorry, we're unable to deliver your Babyshop, Home Box and Home Centre products to this address. Please select a different shipping address or continue shopping our other brands.
landmark.homebox.homecentre.delivery.error.message=We're sorry, we're unable to deliver your Home Box and Home Centre products to this address. Please select a different shipping address or continue shopping our other brands.
landmark.babyshop.homecentre.nonGdms.delivery.error.message=We're sorry, we're unable to deliver the Babyshop and Home Centre products below. Please remove these products or select a different shipping address to continue. 
landmark.babyshop.homebox.nonGdms.delivery.error.message=We're sorry, we're unable to deliver the Babyshop and Home Box products below. Please remove these products or select a different shipping address to continue. 
landmark.babyshop.homebox.homecentre.nonGdms.delivery.error.message=We're sorry, we're unable to deliver the Babyshop, Home Box and Home Centre products below. Please remove these products or select a different shipping address to continue. 
landmark.homebox.homecentre.nonGdms.delivery.error.message=We're sorry, we're unable to deliver the Home Box and Home Centre products below. Please remove these products or select a different shipping address to continue. 
landmark.homecentre.normal.delivery.message=Free delivery slot message
landmark.homecentre.thankyou.page.express.delivery.legend.message=Express Delivery
landmark.homecentre.thankyou.page.express.delivery.message=<strong>Home Centre</strong> furniture should arrive by Express delivery on <p class="delivery-time"><strong>{0}</strong> between <strong>{1} - {2}</strong> hours</p>
landmark.homecentre.thankyou.page.free.delivery.message=<strong>Home Centre</strong> furniture should arrive by Scheduled delivery on <p class="delivery-time"><strong>{0}</strong> between <strong>{1} - {2}</strong> hours</p>
landmark.homecentre.thankyou.page.normal.delivery.legend.message=Your Scheduled Delivery products
landmark.homecentre.thankyou.page.normal.delivery.message=<strong>Home Centre</strong> furniture should arrive on <p class="delivery-time"><strong>{0}</strong> between <strong>{1} - {2}</strong> hours</p>
landmark.homecentre.thankyou.page.nonconcept.products.delivery.message=The rest of your products should arrive
landmark.homecentre.customer.delivery.slot.error.message=Adding more furniture to your order? Please select a new delivery date & time.
landmark.homecentre.thankyou.delivery.title=Here's how your deliveries will be made:

# TODO: Replace the below error messages with the expected ones (JIRA - HCIE-164)
landmark.homecentre.delivery.slot.reservation.error.extn_001=Your furniture cannot be delivered to this Pincode. Please try a different Pincode or remove these products.
landmark.homecentre.delivery.slot.reservation.error.extn_002=We're sorry, a few products are out of stock. Please remove them to continue.
landmark.homecentre.delivery.slot.reservation.error.extn_003=We're sorry, we're unable to deliver your Home Centre products at the moment. Check back later or continue shopping our other brands.
landmark.homecentre.delivery.slot.reservation.error.extn_004=We're sorry, we're unable to deliver your Home Centre products at the moment. Check back later or continue shopping our other brands.
landmark.homecentre.delivery.slot.reservation.error.extn_005=We're sorry, we're unable to deliver your Home Centre products at the moment. Check back later or continue shopping our other brands.
landmark.homecentre.delivery.slot.reservation.error.extn_006=We're sorry, we're unable to deliver your Home Centre products at the moment. Check back later or continue shopping our other brands.
gdms.stock.error.msg=We're sorry, some products are not available. Please remove them to continue.
checkout.placeOrder.failed =We're sorry, something went wrong while placing the Order. Please check back later.

# for homebox thank you page
landmark.homebox.thankyou.page.express.delivery.message=<strong>Home Box</strong> furniture should arrive by Express delivery on <p class="delivery-time"><strong>{0}</strong> between <strong>{1} - {2}</strong> hours</p>
landmark.homebox.thankyou.page.free.delivery.message=<strong>Home Box</strong> furniture should arrive by Scheduled delivery on <p class="delivery-time"><strong>{0}</strong> between <strong>{1} - {2}</strong> hours</p>
landmark.homebox.thankyou.page.normal.delivery.message=<strong>Home Box</strong> furniture should arrive on <p class="delivery-time"><strong>{0}</strong> between <strong>{1} - {2}</strong> hours</p>

landmark.summary.express.slot = ({0} {1} between {2} hours)
landmark.summary.delivery.slot = ({0} {1} {2} {3} between {4} hours)

orderreview.between = between
orderreview.hrs = hours
landmark.babyshop.thankyou.page.express.delivery.message=<strong>Babyshop</strong> furniture should arrive by Express delivery on <p class="delivery-time"><strong>{0}</strong> between <strong>{1} - {2}</strong> hours</p>
landmark.babyshop.thankyou.page.free.delivery.message=<strong>Babyshop</strong> furniture should arrive by Scheduled delivery on <p class="delivery-time"><strong>{0}</strong> between <strong>{1} - {2}</strong> hours</p>
landmark.babyshop.thankyou.page.normal.delivery.message=<strong>Babyshop</strong> furniture should arrive on <p class="delivery-time"><strong>{0}</strong> between <strong>{1} - {2}</strong> hours</p>
landmark.homecentre.thankyou.page.standard.delivery.legend.message=Standard Delivery
landmark.homecentre.thankyou.page.standard.delivery.message=The rest of your products should arrive <strong class="_font-dark">within 4-5 business day</strong>

homecentre.schedule.delivery.title = Schedule your Home Centre furniture delivery
homebox.schedule.delivery.title = Schedule your Home Box furniture delivery
schedule.delivery.description = Let us know when we can deliver the following:
schedule.delivery.progress.label.one = Choose a delivery date
schedule.delivery.progress.label.two = Pick a convenient time

schedule.delivery.noslotsavailable = Sorry, no delivery dates are available for this area now, please check back later.
schedule.delivery.expressdelivery = Express delivery
schedule.delivery.selected = Your delivery is scheduled!
schedule.deliveryexpress.selected = Your Express delivery is scheduled!
schedule.delivery.datetime = We will deliver your furniture on <br class="_visible-xs"/><time><strong class="date" class="font-black"></strong> between <strong class="time" class="font-black"></strong></time> hours
schedule.delivery.date.error = Please choose a date
schedule.delivery.time.error = Please choose a time
schedule.delivery.confirm = Confirm Delivery
schedule.delivery.selected.title = Your <span class="status">Express</span> delivery is scheduled!
schedule.delivery.months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"]
schedule.delivery.days = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"]
schedule.delivery.today = Today
schedule.delivery.datetime.error = Please schedule you delivery
schedule.delivery.title = Schedule your delivery
schedule.delivery.letusknow = Let us know when can we deliver to you.

homecentre.schedule.delivery.notification.title =Your Home Centre <span class="status">Express</span> delivery is scheduled!
homebox.schedule.delivery.notification.title = Your Home Box <span class="status">Express</span> delivery is scheduled!
babyshop.schedule.delivery.notification.title =  Your Babyshop <span class="status">Express</span> delivery is scheduled!

site.basket.qty = Qty
site.months = ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"]
site.button.loading = Loading

checkout.multi.deliveryAddress.notprovided=You should provide a delivery address in order to go to the next step.
landmark.back.to.department=Back to departments
landmark.back.to.menu=Back
account.order.details.normal.delivery.option=Standard Shipping

## Delivery message

landmark.non.homecentre.nextday.delivery.message = Order by 3:30 PM for Next Business Day Delivery
landmark.homecentre.express.delivery.message = Select a convenient delivery date and time from the Shipping page


## Favourite

favourite.tooltip.title = Favourite this product!
favourite.tooltip.desc = Buy it whenever you like from Favourites in your account.
favourite.wishlist.undo = Undo

## Favourite Signin Messages

favourite.signin.header.title.first = Sign in to
favourite.signin.header.title.last  = this product

## Common Favourite modal description text

favourite.modal.header.text  = Favourite the products you love today and buy them anytime.

feedback.form.message.validation=Please share your feedback here

#smart banner properties
apps.smart.banner.description=Now available on your app store
apps.smart.banner.open.button.text=Open
apps.smart.banner.download.button.text=Download

#Address map properties
landmark.address.google.map.text=Search Google Maps
landmark.address.google.map.location.text=Get my location

## Language Switch
app.language.switch.text = Ø§ÙØ¹Ø±Ø¨ÙØ©

##Address Type Display value in checkout address form page
#addresstype.Home=Home
#addresstype.Office=Office

text.cart.pincode.threshold.exceed = Your order value exceeds this Pincode\u2019s limit of {0} {1}.<br/> Please update your order value or try another Pincode.

landmark.basket.validation.text.cart.pincode.threshold.exceed = You have exceeded {0} order limit, it should be below {1} {2} amount

fastpay.text.payment.error.prepaid.exceed.threshold = Your order value exceeds this Pincode''s limit of {0} {1}. Please try a different Pincode

checkout.payment.threshold.errormsg=Your order value exceeds this Payment mode\u2019s limit of {0} {1}, please update your order value or try another Pincode or Payment mode.

text.payment.error.credit.card.optiob.disabled = Credit card payment option is not supported for Pin code, Please try different Payment method

#online cancellation
landMarkOrders.cancel.items.select.text.value = Please select the items you want to cancel
landMarkOrders.return.items.select.text.value = Please select the items you want to return
landMarkOrders.items.select.all = Select All
landMarkOrders.cancel.items.button.text = Cancel Items
landMarkOrders.return.items.button.text = Return Items
landMarkOrders.cancel.selected.items.button.text = Cancel Selected Items
landMarkOrders.return.selected.items.button.text = Return These Items
landMarkOrders.items.never.mind = Never mind
landMarkOrders.refund.text = Your Refund
landMarkOrders.return.reason.select.your.comments = Your comment
landMarkOrders.return.reason.select.characters = characters remaining
landMarkOrders.return.reason.select.value = Please select a reason
landMarkOrders.return.reason.error.value = Please select a reason to continue
landMarkOrders.return.reason.other.error.value = Please enter your comments to continue
landMarkOrders.cancel.success.message.CREDIT_CARD= your Card.
landMarkOrders.cancel.success.message.APPLE_PAY=your Card.

#click & Collect
landMarkOrders.clickAndCollect.addPickupAddress.title = Add a pickup address
landMarkOrders.clickAndCollect.selectPickupAddress.title = Select a pickup address
landMarkOrders.clickAndCollect.addPickupAddressModal.submit = Continue

#Order entry status for cancellation and return in order history
landMarkOrders.orderEntry.status.C = Cancelled
landMarkOrders.orderEntry.status.CI = Cancellation In Progress
landMarkOrders.orderEntry.status.R = Returned
landMarkOrders.orderEntry.status.RI = Return In Progress
order.return.cancel.failed.IN = We\u2019re sorry, we\u2019re unable to process your request. Please call <span>{0}</span> customer support at 1800-123-1444.
order.return.cancel.failed.AE = We\u2019re sorry, we\u2019re unable to process your request. Please call <span>{0}</span> customer support at 800-{1}UAE (800-629823).


order.return.cancel.failed = We\u2019re sorry, we\u2019re unable to process your request. Please call <span>{0}</span> customer support at {1}.

landMarkOrders.cancel.success.message.caption = Your items have been successfully cancelled.
landMarkOrders.cancel.success.message = {0} will be refunded to
landMarkOrders.cancel.success.message.general = you.
landMarkOrders.cancel.success.message.NET_BANKING= your bank account.
landMarkOrders.cancel.success.message.WALLET= your wallet.
landMarkOrders.return.success.message.caption = Your request is successfully submitted.
landMarkOrders.return.success.message.info = We&#39;ll send you an email with the next steps right away. <p class='_normal'> <a href="{0}">Read more</a> about returns.</p>

landMarkOrders.returns.cancel.refund.your.WALLET.text=To your wallet: {0}
landMarkOrders.returns.cancel.refund.your.NET_BANKING.text=To your bank account: {0}
landMarkOrders.returns.cancel.refund.cash.amount.text=Cash Amount: {0}
landMarkOrders.returns.cancel.refund.your.CREDIT_CARD.text=To your Card: {0}
landMarkOrders.returns.cancel.refund.your.DEBIT_CARD.text=To your Card: {0}
landMarkOrders.returns.cancel.refund.your.APPLE_PAY.text=To your Card: {0}
landMarkOrders.returns.cancel.refund.voucher =As a <span>{0}</span> Voucher: {1}
landMarkOrders.returns.cancel.refund.voucher.amount=Voucher Amount: {0}
landMarkOrders.returns.cancel.refund.shipping.charge =Shipping charge:  {0}
landMarkOrders.returns.cancel.nonRefund.gift.voucher=Non-refundable gift voucher:  {0}
landMarkOrders.returns.cancel.refund.nonRefundable.voucher=Non-refundable voucher:  {0}
landMarkOrders.returns.cancel.cod.nonrefund.amount=COD Non-refund amount:  {0}

#General validation errors
form.field.required=This field is required
form.field.invalid.max=Field length can not be more than {0} chars
form.field.invalid.min=Field length should be at least {0} chars
form.field.invalid.email=Please enter valid email id
form.field.invalid.mobile=Please enter valid {0} digits mobile number
form.field.invalid.value=Please enter valid value

#COD OTP Messages
wrong.otp.entered.for.cod.confirmation.error=You\u2019ve entered an incorrect code. Please try again.
cod.otp.resent.message=We\u2019ve re-sent your code. Please try again in
cod.order.confirmed.message=Your order has been confirmed.
cod.otp.verify.error=We\u2019re sorry, something\u2019s gone wrong. Please try again later.
cod.otp.verify.limit.exceede.error = Please call us at <span>{0}</span> customer support at {1}, or wait for us to get in touch with you in little while and we\u2019ll help you confirm your order.<br>

form.field.invalid.char=Please enter valid characters

#click and collect error messages
clickcollect.cart.ineligible= Your cart is not eligible for click and collect
clickcollect.pos.invalid= Selected store does not exist
clickcollect.pos.invalid.ineligible= Selected store is not eligible for cart
clickcollect.invalid.input= Certain input provided by you is invalid
clickcollect.invalid.payment.mode=We're sorry, Cash on Delivery is not available for Click & Collect. Please use your Credit or Debit Card to pay for your order.

landMarkOrders.clickcollect.header.store.address=Address:


babyshop.schedule.delivery.title = Schedule the delivery of these Babyshop products

basket.page.shipping.homecentre.express = Home Centre Express delivery:
basket.page.shipping.babyshop.express = Babyshop Express delivery:
basket.page.shipping.homebox.express = Home Box Express delivery:
basket.page.shipping.homecentre.standardGround = Home Centre Standard Ground Shipping:
basket.page.shipping.babyshop.standardGround = Babyshop Standard Ground Shipping:
basket.page.shipping.homebox.standardGround = Home Box Standard Ground Shipping:

#Google map related properties
landmark.address.google.map.error.text = Please allow access to the geolocation feature and try again.
landmark.address.google.map.response.error = We're sorry, we couldn't find your address. Click on 'Get my Location' or 'Search Google Maps'.
landmark.address.google.map.text=Search Google Maps
landmark.address.google.map.location.text=Get my location

## Language Switch
app.language.switch.text = Ø§ÙØ¹Ø±Ø¨ÙØ©

##Address Type Display value in checkout address form page
addresstype.Home=Home
addresstype.Office=Office

paymentPage.callToVerify = Once your order is complete, We will start processing it.
paymentPage.tlsVersionError = You seem to be using an outdated browser which only supports payment by Cash on Delivery.<br> We recommend that you get the latest version of <span class='browser-name'>Chrome</span>,<span class='browser-name'>Firefox</span> or <span class='browser-name'>Safari</span> for a better experience
paymentPage.tlsnCODError= You seem to be using an outdated browser  which does not support any payment method and ordering from a Pincode that does not support Cash on Delivery.<br> We recommend that you get the latest version of <span class='browser-name'>Chrome</span>,<span class='browser-name'>Firefox</span> or <span class='browser-name'>Safari</span> for a better experience

## Common
common.language.english=English
common.language.arabicInEn=Arabic
common.language.arabic.display=Ø§ÙØ¹Ø±Ø¨ÙØ©
pdp.review.on = on
dateformate = dd MMM YYYY


## ********* GLOBALS *********
# Its where these properties will be used globally (Ex: Form fields)
# Please, add carefully, remove any redundant, and if move parts to here if you think they are globally used.
# Naming conventions, PLEASE its a crucial thing.

#____________ Forms ____________

# > Labels and placeholders
form.name.first.label = First Name
form.name.last.label = Last Name

#___________ Cart related ____________
site.vouchers.title = Vouchers

## ********* / GLOBALS ENDS *********

# > Errors
form.error.name.maxlength = {0} Should not be more than {1} characters length!
form.error.name.required = Please enter your {0}
##NPS Section
nps.write.your.reason.here=Enter your reason here
nps.rating.field.requiredmsg=This field is required
nps.comment.field.requiredmsg=This field is required
nps.encdata.field.requiredmsg=This field is required
nps.comment.field.invalid.length=Maximum 250 characters allowed.
nps.page.tryagain.message=Please try again
nps.page.thankyou.message=Thank you
nps.page.appreciate.message=We appreciate you taking the time to rate us.
nps.page.appreciate.feedback=We appreciate your feedback.
nps.page.score.message=You gave us a score of:
nps.page.notlikely.message=Not at all Likely
nps.page.likely.message=Extremely Likely
nps.page.important.reason.for.score.message=What is the most important reason for your score?(optional)
nps.page.feedback.exist.message=You have already given feedback
nps.reason.submit=Submit
nps.rating.invalid.digit=Rating can only be from 0 to 10
nps.rating.modified.message=You have selected a wrong rating!! Please select the correct rating again.
nps.order.not.exist=Sorry! We did not find your order. Please Try Again!!
flyout.all = All
## ********* ï£¿ Pay *********
applepay.button.cta = Pay with Apple Pay

#Write to us properties
landmarkWriteToUs.heading.title = Write to us
landmarkWriteToUs.heading.title.description = We\u2019d love to hear suggestions and comments.
landmarkWriteToUs.heading.title.name = Your name
landmarkWriteToUs.heading.title.email.text = Enter your email
landmarkWriteToUs.heading.title.name.text = Enter your name
landmarkWriteToUs.heading.title.message.text = Tell us what you think
landmarkWriteToUs.heading.title.text = Enter your comments here
landmarkWriteToUs.success.msg = <strong>Thank you for writing in&#x21;</strong></br> We\u2019ll get back to you in a little while.
landmarkWriteToUs.heading.title.button.title = Submit
landMarkMyAccountPage.header.user.my.profile.write.to.us.email = Your email

landmarkHelp.stillHaveQuestions.write.to.us.email.desc = Drop us a line and we\u2019ll get back to you as fast as we can.Email us at
landmarkHelp.stillHaveQuestions.write.to.us.email = <strong><EMAIL></strong>
write.to.us.url = /feedback
write.to.us = Write to us
payment.card.not.supported=We\u2019re sorry, we only accept MasterCard or Visa cards.


write.to.us.url = /write-to-us
write.to.us = Write to us
call.to.us = Call us:
landmark.checkout.zopim.support=Support


#COD OTP Messages
wrong.otp.entered.for.cod.confirmation.error=You\u2019ve entered an incorrect code. Please try again.
cod.otp.resent.message=We\u2019ve re-sent your code. Please try again in
cod.order.confirmed.message=Your order has been confirmed.
cod.otp.verify.error=We\u2019re sorry, something\u2019s gone wrong. Please try again later.
cod.otp.verify.limit.exceede.error = Please call us at <span>{0}</span> customer support at {1}, or wait for us to get in touch with you in little while and we\u2019ll help you confirm your order.<br>

applepay.button.cta = Pay with Apple Pay
applepay.button.cta.mobile = Pay with Apple Pay
landmark.or.pay.with = Or pay with
landmark.shipping.address.city.select = Select your city

#Fastpay toggle button
landmarkFastPay.toggle.button.on = on
landmarkFastPay.toggle.button.off = off

prebasket.page.global.size.error = <span class="visible-xs">Please select a size for the products below</span><span class="hidden-xs">Please select a size for the products below to continue shopping</span>
prebasket.page.add.all.to.basket = Add all to Basket
pp.page.view.the.collection = View the collection
pp.page.complete.the.look = COMPLETE THE LOOK - 
pp.page.view.all = <span class="visible-xs view">View All</span><span class="hidden-xs">View the collection</span>

landmarkFeedback.heading.title.feedback.contactnumber.text = Mobile Number
landmarkFeedback.heading.title.feedback.contactnumber.invalid = Please enter valid mobile number
landmarkFeedback.heading.title.feedback.contactnumber.required = Please enter your mobile number
landmarkFeedback.heading.title.feedback.city.text = City
landmarkFeedback.heading.title.feedback.city.required = Please enter your city
landmarkFeedback.heading.title.feedback.city.invalid = Please enter a valid city name.
landmarkFeedback.heading.title.feedback.city.placeholder = Enter your city
landmarkFeedback.heading.title.feedback.landmarkrewardsnumber.text = Landmark Rewards number
landmarkFeedback.heading.title.feedback.landmarkrewardsnumber.invalid = Please enter a valid LMR number
landmarkFeedback.heading.title.feedback.landmarkrewardsnumber.placeholder = Your Landmark Rewards number
landmarkFeedback.heading.title.feedback.query.text = Your query relates to
landmarkFeedback.heading.title.feedback.query.required = Please select the reason of query
landmarkFeedback.heading.title.feedback.query.placeholder = Select your reason of query
landmarkFeedback.heading.title.feedback.store.text = Store
landmarkFeedback.heading.title.feedback.store.required = Please select the store
landmarkFeedback.heading.title.feedback.store.placeholder = Select your store
landmarkFeedback.heading.title.feedback.optional = (optional)
landmarkFeedback.heading.title.feedback.help.text = What can we help you with?
landmarkFeedback.heading.title.feedback.help.required = Please select a topic
landmarkFeedback.heading.title.feedback.help.placeholder = Please Select a topic
lmg.complete.collection=Complete The Collection
lmg.complete.look=Complete The Look
vat.included.message=(Inclusive of any applicable VAT)





landMarkAddressBook.Emirate=Emirate
#landMarkAddressBook.Building.Error.Msg = Please enter building name
landMarkAddressBook.Building.Error.Msg = Please enter your address

#My Account shukran
loyalty.title = Shukran
landMarkMyAccountPage.header.user.my.landmarkRewards.description=Earn and spend valuable loyalty points for instant savings.

#Opc
Cnc.ChangeLink=Change the store

#My Account MyCredit
landMarkMyAccountPage.header.user.my.credit = My Credit
landMarkMyAccountPage.header.user.my.credit.description = View your available credit balance.
landMarkMyCredit.emptyText = You have no credit at the moment.
landMarkMyCredit.emptyText.description = Your My Credit balance will be updated when you receive a refund.
landMarkMyCredit.uses.title = Why should I use My Credit?
landMarkMyCredit.refunds.title = Get instant refunds
landMarkMyCredit.refunds.description = Your refund will be credited directly to your account.
landMarkMyCredit.spend.title = Spend credit when you shop
#landMarkMyCredit.spend.description = At checkout, select Use My Credit to easily pay with your available balance.
landMarkMyCredit.spend.description = Your credit balance is
landMarkMyCredit.help = For more information, go to 
landMarkMyCredit.help.link = Help Centre
landMarkMyCredit.balance.title = Your credit balance is
landMarkMyCredit.transaction.filter.last=Last
landMarkMyCredit.transaction.filter.months= months
landMarkMyCredit.transaction.filter.month= month
landMarkMyCredit.transaction.filter.all= All transactions
landMarkMyCredit.transaction.order= Order
landMarkMyCredit.transaction.debit= Credit spent
landMarkMyCredit.transaction.credit= Refund credit
landMarkMyCredit.transaction.dateformate = dd MMM, YYYY
landMarkMyCredit.transaction.statement= Your statement
landmark-ae.landMarkMyCredit.help.link = <a href="https://help.maxfashion.com/hc/en-us/sections/************-Returns-Policy" class="myCredit__help__link">Help Centre</a>
landmark-bh.landMarkMyCredit.help.link = <a href="https://bh.help.maxfashion.com/hc/en-us/sections/************-Returns-Policy" class="myCredit__help__link">Help Centre</a>

#My Account Date of Birth
login.signup.add.dob.title.optional = Date of Birth (optional)
login.signup.add.dob.birthdayText = Let us know and we'll send you a birthday treat.
login.signup.select.date = Date
login.signup.select.month = Month
login.signup.incorrect.date = Please select a valid date



#Homecentre Home Page
landMarkHomePage.Department= Shop by Department
landMarkHomePage.YouMayLike= You May Also Like
landMarkHomePage.Recently= Recently Viewed
landMarkHomePage.SocialMedia= Instagram
landMarkHomePage.SocialMedia.Homecentre= #HomeCentre
landMarkHomePage.Instagram= Share photos, shop photos and get inspired. Follow Us on Instagram @homecentre

#Shop More Brand
Conceptone=babyshop
ConceptTwo=Lifestyle
ConceptThree=SHOEMART
ConceptFour=centrpoint
ConceptFive=Splash


#My Account Referrals
landMarkMyAccountPage.header.user.my.referral = My Referrals
landMarkMyAccountPage.header.user.my.referral.description = Invite your friends and earn shopping credit.
landMarkMyReferrals.share.text = Share your referral link with your friends. They get a discount on their first online order and you receive credit once they shop. Win-win!
landMarkMyReferrals.steps.title = Hereâs how it works
landMarkMyReferrals.steps.refer = Refer a friend
landMarkMyReferrals.steps.refer.description = Simply share your personal link with your friends and family.
landMarkMyReferrals.steps.earn = Your friend earns discounts
landMarkMyReferrals.steps.earn.description = Once they sign in and shop, your friend will enjoy a discount on their first order from any Landmark brand site or app. The discount will depend on the brand.
landMarkMyReferrals.steps.get.credit = You get shopping credit
landMarkMyReferrals.steps.get.credit.description = This will be added to your account 30 days after your friend shops using your referral link.
landMarkMyReferrals.share.facebook = Facebook
landMarkMyReferrals.share.twitter = Twitter
landMarkMyReferrals.share.link = Share a link
landMarkMyReferrals.history.title = Your referral history
landMarkMyReferrals.share.link.copy = Copy link
landMarkMyReferrals.share.link.copied = Link copied
landMarkMyReferrals.credit.date = Date claimed
landMarkMyReferrals.credit.status = My Credit status
landMarkMyReferrals.credit.total = Total earned&nbsp

#Referral discount Modal
referralDiscount.success.title = Great!
referralDiscount.failure.title = Uh oh!
referralDiscount.success.description = Your referral discount will be automatically applied at checkout.<br/>Happy saving!
referralDiscount.failure.description = It looks like youâve already shopped with us.
Weâre sorry, you canât claim this referral discount.
referralDiscount.failure.ok.button = Ok, got it
referralDiscount.success.ok.button = Start Shopping

#Referral Basket 
lmg.referral.incentive.status.inprocess = Pending
lmg.referral.incentive.status.completed = Credited
lmg.referral.incentive.status.rejected = Order Cancelled
#cart page referral notification modal
basketPage.referralEligibilty.modal.title = Youâre eligible for a referral discount!
basketPage.referralEligibilty.modal.description = Please sign up now to enjoy it.
basketPage.referralEligibilty.modal.signin = Sign in
basketPage.referralDiscount.list.modal.title = Referral Discount
basketPage.referralDiscount.list.modal.description = Your referral discount may vary from brand to brand and can only be used on your first purchase. Hereâs how it works:
basketPage.referralDiscount.list.modal.brand = Brand
basketPage.referralDiscount.list.modal.discount = Discount
appliedVouchers.referralcodeapplied = referral discount applied.

#Referral Thank You Page

referralDiscount.Guestuser.thankyou.title= Thatâs right, you can get shopping credit now.
Learn more about referrals.
referralDiscount.Registreduser.thankyou.title= Share your referral link with your friends. Theyâll earn a discount on their first order and you get credit when they shop.


#EMI

pdp.emi.description = Pay in easy installments on orders of â¹ 3,000 or more. Available for select banks.
emiModal.title = Eligible Banks
emiModal.emi.eligible = Choose your credit card issuing bank
pdp.Duration.message.title = Duration
landMarkPayment.emi.interest= interest
landMarkPayment.emi.installments= Available on listed bank credit cards. Pay easy monthly installments instead of lump-sum amount. Find suitable EMI option below and choose same option at payments step while placing the order.
landMarkPayment.emi.title= Congrats! You can pay in installments as follows:
landMarkPayment.emi.processing.fee= A processing fee of {0}<span></span> is applicable on <span></span> credit card transactions converted to <span></span>% interest payment plan.
landMarkPayment.emi.terms= By clicking on the Pay Now button, you agree to the Bankâs <a href="#" class="emi-terms-link">Terms & Conditions</a>.
landMarkPayment.emi.pay.full= Pay in full
basket.page.emi.eligible = Congrats! You can pay for this order in easy installments Learn More.
basket.page.emi.eligible.ae = Congrats! You can pay for this order in easy installments at 0% interest.
basket.page.emi.learnMore = Learn More.


#FurnitureExchange
furniture.exchange.heading=Exchange your furniture and earn instant savings
furniture.exchange.steps.heading=Hereâs how your furniture exchange works:
furniture.exchange.step.one = 1. Tell us what you want to exchange and share your details.
furniture.exchange.step.two = 2. Use the code weâve emailed you to shop new furniture.
furniture.exchange.step.three = 3. Weâll call to confirm a time to pick up your old furniture.
furniture.exchange.step.four = 4. We complete the exchange & deliver your new furniture. 
furniture.exchange.share.details= Share the details below so we can arrange your pickup.
furniture.exchange.share.details.extrainfo = Please upload only one item of furniture at a time.
furniture.exchange.share.details.exchangebox.label =What furniture do you want to exchange?
furniture.exchange.share.details.exchangebox.placeholder = Select a category
furniture.exchange.share.details.pickup.label = Select a pickup address
furniture.exchange.share.details.upload.label = Upload an image of your furniture
furniture.exchange.share.details.upload.text.highlight = Click to add a file 
furniture.exchange.share.details.upload.text.normal  = or drop files here
furniture.exchange.share.details.notebox.label = Note
furniture.exchange.share.details.button.submit = Request a pickup
furniture.exchange.success.message = Great, Your discount is on its way
furniture.exchange.success.info = Weâve received your exchange request. Your exclusive voucher code will be sent to
furniture.exchange.success.extra.info = Got more furniture to exchange? 
furniture.exchange.success.extra.info.mobile = Have another furniture to exchange?
furniture.exchange.success.extra.info.link = Do it now 
furniture.exchange.success.view.all.link = View all 
furniture.exchange.placeholder.note = eg. The wooden base is broken. 
furniture.exchange.addAddress.continue = Continue
furniture.exchange.remove = Remove
furniture.exchange.strands.heading = Best Selling Products
image.not.readable.format = Please add a valid image or file
image.not.readable.default = Please add a valid image or file
image.size.exceeded = Please add image up to 5 mb
image.error.default = Please add a valid image or file 
image.required.error = Please add a image or file 
furniture.exchange.category.required = Please select a category
furniture.exchange.address = Address
furniture.exchange.share.details.upload.text.highlight.mobile = Add a photo

pdp.priceVAT = Inclusive of VAT
pdp.freeShopping = Free shipping on EGP 399+ | Free returns
pdp.color.label = Colour
pdp.size.label = Size
pdp.addToBasket = Add to Basket

plp.shopFor.label = Shop For
plp.sortBy.label = Sort by
plp.sortBy.value.list = New Arrivals:Discount:Price - Low to High:Price - High to Low:Relevance
plp.sortBy.value.newArrivals = New Arrivals
plp.sortBy.value.discount = Discount
plp.sortBy.value.priceLowToHigh = Price - Low to High
plp.sortBy.value.priceHighToLow = Price - High to Low
plp.sortBy.value.relevance = Relevance