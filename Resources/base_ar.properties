
#Footer  subscription section

footer.subscription.title= Ø§Ø´ØªØ±Ù ÙÙ ÙØ´Ø±ØªÙØ§ Ø§ÙØ¨Ø±ÙØ¯ÙØ©.
footer.subscription.info = ØªØ¹Ø±ÙÙ Ø¹ÙÙ Ø£Ø­Ø¯Ø« Ø§ÙØ¹Ø±ÙØ¶ ÙØ§ÙØ£Ø®Ø¨Ø§Ø± ÙØ¨Ø§Ø´Ø±Ø© Ø¹Ø¨Ø± Ø¨Ø±ÙØ¯Ù Ø§ÙØ§ÙÙØªØ±ÙÙÙ
footer.subscription.error.email = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø¹ÙÙØ§Ù Ø§ÙØ¨Ø±ÙØ¯ Ø§ÙØ¥ÙÙØªØ±ÙÙÙ
footer.subscription.error.valid.email = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø¹ÙÙØ§Ù Ø¨Ø±ÙØ¯ Ø¥ÙÙØªØ±ÙÙÙ ØµØ­ÙØ­
footer.subscription.success.msg = ÙØ´ÙØ±Ù Ø¹ÙÙ Ø§ÙØ§Ø´ØªØ±Ø§Ù ÙÙ ÙØ´Ø±ØªÙØ§ Ø§ÙØ¥Ø®Ø¨Ø§Ø±ÙØ©.


#Header strip
header.strip.freeShipping = Ø´Ø­Ù ÙØ¬Ø§ÙÙ
header.strip.clickAndCollect = Ø§Ø³ØªÙÙÙØ§ Ø¨ÙÙØ³Ù
header.strip.homeDelivery = ØªÙØµÙÙ ÙÙÙÙØ²Ù
header.strip.deliveryToArea = ØªÙØµÙÙ Ø¥ÙÙ Ø§ÙÙÙØ·ÙØ©
header.strip.giftCard = Ø¨Ø·Ø§ÙØ© Ø§ÙÙØ¯Ø§ÙØ§
header.strip.aboutShukran = Ø¹Ù Ø´ÙØ±Ø§Ù
header.strip.joinNow = Ø¥ÙØ¶Ù Ø§ÙØ§Ù
header.strip.downLoadOurApps =ØªØ­ÙÙÙ ØªØ·Ø¨ÙÙØ§ØªÙØ§
header.strip.storeLocator = ÙØ­Ø¯ÙØ¯ ÙÙÙØ¹ Ø§ÙÙØªØ¬Ø±
header.strip.help = ÙØ³Ø§Ø¹Ø¯Ø©


#Footer Download section
footer.download.app.text= Ø§Ø³ØªÙØªØ¹ Ø¨ØªØ·Ø¨ÙÙØ§ØªÙØ§ Ø§ÙÙØ°ÙÙØ©
footer.download.app.subtext= ØªØ³ÙÙ ÙÙØªØ¬Ø§ØªÙØ§ ÙØ¹Ø±ÙØ¶ÙØ§ Ø£ÙÙÙØ§ ÙÙØª.

#Help page


#Help page from footer
help.search.input =Ø£Ø¯Ø®Ù ÙØµØ·ÙØ­ Ø§ÙØ¨Ø­Ø« ÙÙØ§...
help.title.text = ÙÙÙ ÙÙÙÙÙØ§ ÙØ³Ø§Ø¹Ø¯ØªÙ Ø§ÙÙÙÙØ
help.title.gift = ÙØ³Ø§Ø¦Ù Ø§ÙÙØ¯Ø§ÙØ§
help.title.tracking = ØªØªØ¨Ø¹ Ø§ÙØ·ÙØ¨ ÙØ§ÙØªÙØµÙÙ
help.title.clickAndCollect = Ø§Ø³ØªÙÙÙØ§ ÙÙ Ø§ÙÙØªØ¬Ø±
help.title.return = Ø³ÙØ§Ø³Ø© Ø§ÙØ¥Ø±Ø¬Ø§Ø¹
help.title.returnProcess = Ø¥Ø¬Ø±Ø§Ø¡Ø§Øª Ø§ÙØ¥Ø±Ø¬Ø§Ø¹
help.title.myAccount = Ø­Ø³Ø§Ø¨Ù
help.title.orders = Ø§ÙØ·ÙØ¨Ø§Øª
help.title.shipping = Ø§ÙØ´Ø­Ù
help.title.payments = Ø·Ø±Ù Ø§ÙØ¯ÙØ¹
help.title.inStore = Ø¯Ø§Ø®Ù Ø§ÙÙØªØ¬Ø±
help.title.shukran = Ø´ÙØ±ÙØ²
help.title.otherInformation = ÙØ¹ÙÙÙØ§Øª Ø£Ø®Ø±Ù
help.title.BuyNow = Ø§Ø´ØªØ±Ù Ø§ÙØ¢Ù


#Help subsection:
help.section.giftcard.header = ÙØ³Ø§Ø¦Ù Ø§ÙÙØ¯Ø§ÙØ§
help.section.tracking.header = ØªØªØ¨Ø¹ Ø§ÙØ·ÙØ¨ ÙØ§ÙØªÙØµÙÙ
help.section.clickAndCollect.header = Ø§Ø³ØªÙÙÙØ§ ÙÙ Ø§ÙÙØªØ¬Ø±
help.section.return.header = Ø³ÙØ§Ø³Ø© Ø§ÙØ¥Ø±Ø¬Ø§Ø¹
help.section.returnProcess.header = Ø¥Ø¬Ø±Ø§Ø¡Ø§Øª Ø§ÙØ¥Ø±Ø¬Ø§Ø¹
help.section.myAccount.header = Ø­Ø³Ø§Ø¨Ù
help.section.orders.header = Ø§ÙØ·ÙØ¨Ø§Øª
help.section.shipping.header = Ø§ÙØ´Ø­Ù
help.section.payments.header = Ø·Ø±Ù Ø§ÙØ¯ÙØ¹
help.section.inStore.header = Ø¯Ø§Ø®Ù Ø§ÙÙØªØ¬Ø±
help.section.shukran.header = Ø´ÙØ±ÙØ²
help.section.otherInformation.header = ÙØ¹ÙÙÙØ§Øª Ø£Ø®Ø±Ù
help.section.BuyNow.header = Ø§Ø´ØªØ±Ù Ø§ÙØ¢Ù




#My Account page
myAccountPage.title.text = Ø­Ø³Ø§Ø¨Ù
myAccountPage.title.subTitle = Ø¥Ø¯Ø§Ø±Ø© ÙÙÙÙ Ø§ÙØ´Ø®ØµÙ ÙØ·ÙØ¨Ø§ØªÙ ÙØªÙØ¶ÙÙØ§ØªÙ ÙØºÙØ± Ø°ÙÙ.
myAccountPage.profile.section = Ø§ÙÙÙÙ Ø§ÙØ´Ø®Øµ
myAccountPage.profile.p = Ø¥Ø¯Ø§Ø±Ø© Ø¨ÙØ§ÙØ§ØªÙ Ø§ÙØ´Ø®ØµÙØ©.
myAccountPage.myLists.section= ÙÙØ§Ø¦ÙÙ
myAccountPage.myList.p   = Ø¹Ø±Ø¶ Ø£ÙØ«Ø± Ø§ÙÙÙØªØ¬Ø§Øª Ø§ÙÙÙØ¶ÙÙØ© ÙÙ.
myAccountPage.payment.section= Ø§ÙØ¯ÙØ¹
myAccountPage.payment.p  = Ø¥Ø¯Ø§Ø±Ø© ØªÙØ¶ÙÙØ§Øª Ø¹ÙÙÙØ© Ø§ÙØ¯ÙØ¹.
myAccountPage.reviews.section= ØªÙÙÙÙØ§ØªÙ
myAccountPage.reviews.p  = Ø¹Ø±Ø¶ ÙØ§ÙØ© ØªÙÙÙÙØ§ØªÙ.
myAccountPage.myShukranCard.section= Ø¨Ø·Ø§ÙØ© Ø´ÙØ±ÙØ§ Ø§ÙØ®Ø§ØµØ© Ø¨Ù
myAccountPage.myShukranCard.p  = Ø§ÙØ³Ø¨ ÙØ§Ø³ØªØ®Ø¯Ù ÙÙØ§Ø· Ø§ÙÙÙØ§Ø¡ Ø§ÙØ«ÙÙÙØ© ÙÙØªÙÙÙØ± Ø§ÙÙÙØ±Ù.
myAccountPage.addressBook.section = Ø¹ÙØ§ÙÙÙÙ
myAccountPage.addressBook.p = ØªØ¹Ø¯ÙÙ Ø¹ÙØ§ÙÙÙ Ø§ÙØ´Ø­Ù ÙØ§Ø³ØªÙØ§Ù Ø§ÙÙÙØ§ØªÙØ± Ø§ÙØ®Ø§ØµØ© Ø¨Ù.
myAccountPage.myCredit.section = Ø±ØµÙØ¯Ù
myAccountPage.myCredit.p = Ø¹Ø±Ø¶ Ø±ØµÙØ¯ Ø§ÙØ§Ø¦ØªÙØ§Ù Ø§ÙØ®Ø§Øµ Ø¨Ù.
myAccountPage.orderHistory.section   = Ø³Ø¬Ù Ø§ÙØ·ÙØ¨Ø§Øª
myAccountPage.orderHistory.p   = Ø¹Ø±Ø¶ ÙØªØªØ¨Ø¹ Ø·ÙØ¨Ø§ØªÙ.

#My Account - Reviews

myAccountPage.reviews.reviews = ØªÙÙÙÙØ§ØªÙ
myAccountPage.reviews.viewAllYourReviews = Ø¹Ø±Ø¶ ÙØ§ÙØ© ØªÙÙÙÙØ§ØªÙ.
myAccountPage.reviews.status = Ø¥ÙØºØ§Ø¡
myAccountPage.reviews.pending = Pending
myAccountPage.reviews.approved = Approved
myAccountPage.reviews.rejected = Rejected
myAccountPage.reviews.outOfFiveStars = Ø¯ÙØªØ± Ø¹ÙØ§ÙÙÙÙ
myAccountPage.reviews.delete = Ø­Ø°Ù
myAccountPage.reviews.areYouSure = ÙÙ ØªØ±ØºØ¨ ÙÙ ØªØ£ÙÙØ¯ Ø°ÙÙØ
myAccountPage.reviews.deleteReviews = Ø­Ø°Ù Ø§ÙØ¹ÙÙØ§Ù
myAccountPage.reviews.neverMind = Ø±Ø¬ÙØ¹
myAccountPage.reviews.p  = Ø¹Ø±Ø¶ ÙØ§ÙØ© ØªÙÙÙÙØ§ØªÙ.

#My account - MyList
myAccountPage.myList.title = ÙÙØ§Ø¦ÙÙ
myAccountPage.myList.createANewList = Ø¥ÙØ´Ø§Ø¡ ÙØ§Ø¦ÙØ© Ø¬Ø¯ÙØ¯Ø©
myAccountPage.myList.title = ÙÙØ§Ø¦ÙÙ
myAccountPage.myList.createANewList = Ø¥ÙØ´Ø§Ø¡ ÙØ§Ø¦ÙØ© Ø¬Ø¯ÙØ¯Ø©
myAccountPage.myList.prompt.createANewList = Ø¥ÙØ´Ø§Ø¡ ÙØ§Ø¦ÙØ© Ø¬Ø¯ÙØ¯Ø©
myAccountPage.myList.prompt.paraOne = ÙÙ Ø¨Ø¥ÙØ´Ø§Ø¡ ÙÙØ§Ø¦Ù Ø­Ø³Ø¨ Ø§ÙÙÙØ§Ø³Ø¨Ø© Ø£Ù Ø§ÙØ­Ø§Ø¬Ø©Ø ÙØ«Ù Ø²ÙØ§Ù ØµØ¯ÙÙ Ø£Ù Ø§ÙØªØ³ÙÙ ÙÙÙØ²Ù Ø¬Ø¯ÙØ¯.
myAccountPage.myList.prompt.paraTwo = !Ø´Ø§Ø±Ù Ø§ÙÙÙØ§Ø¦Ù ÙØ¹ Ø£ØµØ¯ÙØ§Ø¦Ù ÙØ¹Ø§Ø¦ÙØªÙØ ÙÙÙÙÙÙÙ ÙØ¹Ø±ÙØ© Ø§ÙÙÙØªØ¬Ø§Øª Ø§ÙØªÙ ÙØ¬Ø¨ ØªØ³ÙÙÙØ§!
myAccountPage.myList.prompt.createList = Ø¥ÙØ´Ø§Ø¡ Ø§ÙÙØ§Ø¦ÙØ©



#My account - Profile
myAccountPage.profile.label.profile = Profile
myAccountPage.profile.label.manageYourPersonalDetails = Manage your personal details.
myAccountPage.profile.label.firstName = Ø§ÙØ§Ø³Ù Ø§ÙØ§ÙÙ
myAccountPage.profile.label.lastName = Ø§Ø³Ù Ø§ÙØ¹Ø§Ø¦ÙØ©
myAccountPage.profile.label.yourEmail = Ø¨Ø±ÙØ¯Ù Ø§ÙØ§ÙÙØªØ±ÙÙÙ
myAccountPage.profile.label.mobileNumber = Ø±ÙÙ Ø§ÙÙØ§ØªÙ Ø§ÙÙØªØ­Ø±Ù (Ø³ÙÙÙÙ Ø¨Ø¥Ø±Ø³Ø§Ù Ø±ÙØ² Ø§ÙØªØ­ÙÙ)
myAccountPage.profile.label.updateProfile = Ø§ÙØ±Ø¨Ø· ÙØ¹ ÙÙØ³Ø¨ÙÙ





#My Account - Add address book
myAccountPage.addAddress.label.confirm.are = ÙÙ ØªØ±ØºØ¨ ÙÙ ØªØ£ÙÙØ¯ Ø°ÙÙØ
myAccountPage.addAddress.label.confirm.deleteAddress = Ø­Ø°Ù Ø§ÙØ¹ÙÙØ§Ù
myAccountPage.addAddress.label.confirm.neverMind = Ø±Ø¬ÙØ¹



#My Account - Error messages
myAccountPage.addAddress.error.city = ÙØ±Ø¬Ù Ø§Ø®ØªÙØ§Ø± Ø§ÙÙØ¯ÙÙØ©
myAccountPage.addAddress.error.area = ÙØ±Ø¬Ù Ø§Ø®ØªÙØ§Ø± ÙÙØ·ÙØªÙ
myAccountPage.addAddress.error.buildingName = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø§Ø³Ù/Ø±ÙÙ Ø§ÙÙØ¨ÙÙ
myAccountPage.addAddress.error.streetName = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø§ÙØ¹ÙÙØ§Ù
myAccountPage.addAddress.error.fullName = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø§Ø³ÙÙ
myAccountPage.addAddress.error.alpha.fullName = Ø§ÙØ±Ø¬Ø§Ø¡ Ø¥Ø¯Ø®Ø§Ù Ø§Ø³ÙÙ Ø¨Ø§ÙÙØ§ÙÙ Ø¨Ø§Ø³ØªØ®Ø¯Ø§Ù Ø£Ø­Ø±Ù Ø£Ø¨Ø¬Ø¯ÙØ© ÙÙØ·
myAccountPage.addAddress.error.ten.mobileNumber= Ø£Ø¯Ø®Ù Ø±ÙÙ Ø§ÙÙØ§ØªÙ Ø§ÙÙØªØ­Ø±Ù Ø§ÙÙØ¤ÙÙ ÙÙ 10 Ø£Ø±ÙØ§Ù
myAccountPage.addAddress.error.mobileNumber= Ø­ÙÙ Ø±ÙÙ Ø§ÙÙØ§ØªÙ Ø§ÙÙØªØ­Ø±Ù Ø¥ÙØ²Ø§ÙÙ


#MyAccount-DropDownItems

myaccount.dropdown.item.MyAccount = Ø­Ø³Ø§Ø¨Ù
myaccount.dropdown.item.MyLists=ÙÙØ§Ø¦ÙÙ
myaccount.dropdown.item.OrderHistory=Ø³Ø¬Ù Ø§ÙØ·ÙØ¨Ø§Øª
myaccount.dropdown.item.MyAddresses= Ø¹ÙØ§ÙÙÙÙ
myaccount.dropdown.item.Payment=Ø§ÙØ¯ÙØ¹
myaccount.dropdown.item.MyCredit=Ø±ØµÙØ¯Ù
myaccount.dropdown.item.Reviews=ØªÙÙÙÙØ§ØªÙ
myaccount.dropdown.item.MyShukranCard=Ø¨Ø·Ø§ÙØ© Ø´ÙØ±ÙØ§ Ø§ÙØ®Ø§ØµØ© Ø¨Ù
myaccount.dropdown.item.SignOut=ØªØ³Ø¬ÙÙ Ø§ÙØ®Ø±ÙØ¬



#My Account - Error messages
myAccountPage.myshukran.shukran = Ø´ÙØ±ÙØ§
myAccountPage.myshukran.earnAndSpendValue = Ø§ÙØ³Ø¨ ÙØ§ÙÙÙ ÙÙØ§Ø· Ø§ÙÙÙØ§Ø¡ Ø§ÙØ«ÙÙÙØ© ÙÙ Ø£Ø¬Ù Ø®ØµÙÙØ§Øª ÙÙØ±ÙÙØ©.
myAccountPage.myshukran.earnAndSpendShukran = Ø§ÙØ³Ø¨ ÙØ£ÙÙÙ Ø´ÙØ±ÙØ² Ø¹ÙØ¯ Ø§ÙØªØ³ÙÙ Ø£ÙÙÙØ§ÙÙ.
myAccountPage.myshukran.itseasy = ÙØ°Ø§ Ø³ÙÙ. ÙÙØ· ÙÙ Ø¨Ø±Ø¨Ø· Ø­Ø³Ø§Ø¨ Ø§ÙØªØ³ÙÙ Ø§ÙØ®Ø§Øµ Ø¨Ù ÙØ¹ Ø­Ø³Ø§Ø¨ Ø´ÙØ±ÙØ§ Ø§ÙØ®Ø§Øµ Ø¨Ù. Ø¥Ù ÙÙ ÙÙÙ ÙØ¯ÙÙ Ø­Ø³Ø§Ø¨ Ø´ÙØ±ÙØ§Ø Ø³ÙØ³Ø§Ø¹Ø¯Ù ÙÙ Ø¥ÙØ´Ø§Ø¡ Ø­Ø³Ø§Ø¨ Ø§ÙØ¢Ù.
myAccountPage.myshukran.createAShukranAccount = ÙÙ Ø¨Ø¥ÙØ´Ø§Ø¡ Ø­Ø³Ø§Ø¨ Ø´ÙØ±ÙØ§
myAccountPage.myshukran.doYouHave = ÙØ¯ÙÙ Ø­Ø³Ø§Ø¨ Ø´ÙØ±ÙØ§Ø ÙÙ Ø¨Ø±Ø¨Ø· Ø­Ø³Ø§Ø¨Ù Ø§ÙØ¢Ù
myAccountPage.myshukran.mobileNumber = Ø±ÙÙ Ø§ÙÙØ§ØªÙ Ø§ÙÙØªØ­Ø±Ù:
myAccountPage.myshukran.shukranCard = Ø±ÙÙ Ø¨Ø·Ø§ÙØ© Ø´ÙØ±ÙØ§
myAccountPage.myshukran.linkYourshukranAccount = ÙÙ Ø¨Ø±Ø¨Ø· Ø­Ø³Ø§Ø¨ Ø´ÙØ±ÙØ§
myAccountPage.myshukran.whyYouShould = ÙÙØ§Ø°Ø§ ÙÙØ¨ØºÙ Ø¹ÙÙÙ Ø§ÙØªØ³Ø¬ÙÙØ
myAccountPage.myshukran.acceptedAtOver = ØªÙ ÙØ¨ÙÙÙ ÙÙ Ø£ÙØ«Ø± ÙÙ 1,500 ÙÙÙØ¹
myAccountPage.myshukran.earnAndSpendShukranAcross = Ø§ÙØ³Ø¨ ÙØ£ÙÙÙ Ø´ÙØ±ÙØ² Ø¹Ø¨Ø± Ø¬ÙÙØ¹ Ø§ÙÙØ§Ø±ÙØ§Øª Ø§ÙÙØ´Ø§Ø±ÙØ©.
myAccountPage.myshukran.55brands = Ø£ÙØ«Ø± ÙÙ 55 Ø¹ÙØ§ÙØ© ØªØ¬Ø§Ø±ÙØ© ÙÙÙØ§ÙØ£Ø© ÙÙØ³Ù
myAccountPage.myshukran.accrossFashion = ÙÙ Ø§ÙØ£Ø²ÙØ§Ø¡Ø Ø§ÙØ§ÙÙØªØ±ÙÙÙØ§ØªØ Ø§ÙØ£Ø«Ø§Ø«Ø Ø§ÙÙØ·Ø§Ø¹Ù ÙØ§ÙÙØ²ÙØ¯.
myAccountPage.myshukran.over15Millino = Ø£ÙØ«Ø± ÙÙ 15 ÙÙÙÙÙ Ø¹Ø¶Ù
myAccountPage.myshukran.bePartOf = ÙÙ Ø¬Ø²Ø¡ÙØ§ ÙÙ Ø¨Ø±ÙØ§ÙØ¬ Ø§ÙÙÙØ§Ø¡ Ø§ÙØ£ÙØ¨Ø± ÙÙØ·Ø§Ø¹ Ø§ÙØªØ¬Ø²Ø¦Ø© ÙÙ Ø§ÙÙÙØ·ÙØ©.

myAccountPage.myshukran.createAShukranAcount.enterMobile = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø±ÙÙ Ø§ÙÙØ§ØªÙ Ø§ÙÙØªØ­Ø±Ù Ø§ÙØ®Ø§Øµ Ø¨Ù
myAccountPage.myshukran.createAShukranAcount.thisWill= Ø³ÙØ³Ø§Ø¹Ø¯ÙØ§ ÙØ°Ø§ ÙÙ Ø§ÙØªØ­ÙÙ ÙÙ Ø­Ø³Ø§Ø¨Ù ÙØ¥Ø¹Ø¯Ø§Ø¯Ù.
myAccountPage.myshukran.createAShukranAcount.pleaeEnter =Please enter mobile number
myAccountPage.myshukran.createAShukranAcount.valid9 = Please enter a valid 9-digit mobile number.
myAccountPage.myshukran.createAShukranAcount.continue = Continue
myAccountPage.myshukran.createAShukranAcount.dontWantToLink = Donât want to link?
myAccountPage.myshukran.createAShukranAcount.cancel = Cancel


#shukran OTP verification
myAccountPage.myshukran.otp.verification=Verification
myAccountPage.myshukran.otp.confirmation=Confirmation
myAccountPage.myshukran.otp.pleaseConfirYourMobileNumber = ØªØ­ÙÙÙ ÙÙ Ø­Ø³Ø§Ø¨Ù ÙØ¯Ù Ø§ÙØ¹ÙØ§ÙØ§Øª Ø§ÙØªØ¬Ø§Ø±ÙØ© Ø§ÙÙØ´Ø§Ø±ÙØ© ÙÙ ÙÙØ§Ù ÙØ§Ø­Ø¯.
myAccountPage.myshukran.otp.pleaseEnterCode = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø§ÙØ±ÙØ² Ø§ÙØ°Ù ØªÙ Ø¥Ø±Ø³Ø§ÙÙ Ø¹Ø¨Ø± Ø±Ø³Ø§ÙØ© ÙØµÙØ© Ø¥ÙÙ
myAccountPage.myshukran.otp.enterTheVerificationCode = Ø§ÙØ±ÙØ² Ø§ÙØ°Ù Ø£Ø¯Ø®ÙØªÙ ØºÙØ± ØµØ­ÙØ­
myAccountPage.myshukran.otp.continue = ÙØªØ§Ø¨Ø¹Ø©
myAccountPage.myshukran.otp.donotGetACode = ÙÙ Ø£Ø­ØµÙ Ø¹ÙÙ Ø§ÙØ±ÙØ²Ø
myAccountPage.myshukran.otp.resend = Ø¥Ø¹Ø§Ø¯Ø© Ø§ÙØ¥Ø±Ø³Ø§Ù
myAccountPage.myshukran.otp.dontWant = ÙØ§ ØªØ±ÙØ¯ Ø±Ø¨Ø· Ø­Ø³Ø§Ø¨ÙØ
myAccountPage.myshukran.otp.cancel = Ø¥ÙØºØ§Ø¡


#Sign In page UI
signIn.title = ÙØ±Ø¬Ù ØªØ³Ø¬ÙÙ Ø§ÙØ¯Ø®ÙÙ Ø§ÙØ¢Ù
signIn.header.text = Ø§Ø³ØªÙØªØ¹ Ø¨Ø±Ø§Ø­Ø© Ø­Ø³Ø§Ø¨ ÙØ§Ø­Ø¯ Ø¹Ø¨Ø± Ø§ÙØ¬ÙÙØ¹
signIn.facebook.label = ØªØ³Ø¬ÙÙ Ø§ÙØ¯Ø®ÙÙ Ø¨ÙØ§Ø³Ø·Ø© Ø­Ø³Ø§Ø¨Ù Ø¹ÙÙ Facebook
signIn.orViaEmail.text = Ø£Ù Ø¨Ø§ÙØ¨Ø±ÙØ¯ Ø§ÙØ¥ÙÙØªØ±ÙÙÙ
signIn.yourEmail.label= Ø§ÙØ¨Ø±ÙØ¯ Ø§ÙØ¥ÙÙØªØ±ÙÙÙ
signIn.email.input= Ø£Ø¯Ø®Ù Ø¨Ø±ÙØ¯Ù Ø§ÙØ¥ÙÙØªØ±ÙÙÙ
signIn.email.input.error= ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø¹ÙÙØ§Ù Ø¨Ø±ÙØ¯Ù Ø§ÙØ¥ÙÙØªØ±ÙÙÙ
signIn.email.input.error.valid= ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø¹ÙÙØ§Ù Ø¨Ø±ÙØ¯ Ø¥ÙÙØªØ±ÙÙÙ ØµØ­ÙØ­
signIn.password= ÙÙÙØ© Ø§ÙÙØ±ÙØ±
signIn.password.input= ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù ÙÙÙØ© Ø§ÙÙØ±ÙØ±
signIn.password.input.error= ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù ÙÙÙØ© Ø§ÙÙØ±ÙØ±
signIn.email.input.remember= ØªØ°ÙØ±ÙÙ
signIn.forgotpassword.link = ÙØ³ÙØª ÙÙÙØ© Ø§ÙÙØ±ÙØ±
signIn.cta = Ø³Ø¬ÙÙ Ø§ÙØ¯Ø®ÙÙ
signIn.donotHaveAnAccount.text= ÙÙØ³ ÙØ¯ÙÙ Ø­Ø³Ø§Ø¨Ø
signIn.signupForOne.cta = ØªÙØ¶Ù Ø¨Ø§ÙØªØ³Ø¬ÙÙ
signIn.shukran.link.text = Ø§ÙØ³Ø¨ ÙØ£ÙÙÙ ÙÙØ§Ø· Ø´ÙØ±ÙØ² Ø£ÙÙÙØ§ÙÙ
signIn.shukran.earnShukran.text= Ø§ÙØ³Ø¨ ÙÙØ§Ø· Ø´ÙØ±ÙØ² ÙØ¹ ÙÙ Ø¹ÙÙÙØ© Ø´Ø±Ø§Ø¡ ÙÙ Ø§ÙÙØªØ¬Ø± Ø£Ù Ø£ÙÙÙØ§ÙÙ.
signIn.shukran.get.exclusive.text=ØªÙØªØ¹ Ø¨Ø¹Ø±ÙØ¶ Ø­ØµØ±ÙØ©
signIn.shukran.instant=Ø¥Ø±Ø¬Ø§Ø¹ ÙÙØ±Ù ÙÙÙÙØ¯Ù ÙØ¹ ÙØ­ÙØ¸Ø© Ø´ÙØ±ÙØ§
signIn.shukran.use.balance=Ø§Ø³ØªØ®Ø¯Ù Ø§ÙØ±ØµÙØ¯ ÙÙØªØ³ÙÙ Ø¹Ø¨Ø± Ø§ÙØ¥ÙØªØ±ÙØª ÙÙÙ Ø§ÙÙØªØ¬Ø±
signIn.error.invalid.credentials= Ø§Ø³Ù Ø§ÙÙØ³ØªØ®Ø¯Ù Ø£Ù ÙÙÙØ© Ø§ÙÙØ±ÙØ± ØºÙØ± ØµØ­ÙØ­Ø©..
signIn.otp.invalid.error.message = This verification code is incorrect, please try again.
signIn.verify.your.account = Verify your account
signIn.please.enter.your.code.sent.text = Please enter the code sent to you on
signIn.confirm.text = Confirm



#Forgot password

forgotPassword.title = ÙØ³ÙØª ÙÙÙØ© Ø§ÙÙØ±ÙØ±Ø
forgotPassword.sub.title = ÙØ§ Ø¯Ø§Ø¹Ù ÙÙÙÙÙ. Ø£Ø¯Ø®Ù Ø¹ÙÙØ§Ù Ø¨Ø±ÙØ¯Ù Ø§ÙØ¥ÙÙØªØ±ÙÙÙØ ÙØ³ÙØ³Ø§Ø¹Ø¯Ù Ø¹ÙÙ Ø¥Ø¹Ø§Ø¯Ø© ØªØ¹ÙÙÙÙØ§.

forgotPassword.email.label = Ø§ÙØ¨Ø±ÙØ¯ Ø§ÙØ¥ÙÙØªØ±ÙÙÙ
forgotPassword.placeHolder.enterYourEmail= Ø£Ø¯Ø®Ù Ø¨Ø±ÙØ¯Ù Ø§ÙØ¥ÙÙØªØ±ÙÙÙ
forgotPassword.submit.label = Ø¥Ø±Ø³Ø§Ù
forgotPassword.error.email = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø¹ÙÙØ§Ù Ø¨Ø±ÙØ¯Ù Ø§ÙØ¥ÙÙØªØ±ÙÙÙ
forgotPassword.error.validEmail = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø¹ÙÙØ§Ù Ø¨Ø±ÙØ¯ Ø¥ÙÙØªØ±ÙÙÙ ØµØ­ÙØ­
forgotPassword.text.remember = ÙÙ ØªØ°ÙØ±Øª ÙÙÙØ© Ø§ÙÙØ±ÙØ± Ø§ÙØ®Ø§ØµØ© Ø¨ÙØ
forgotPassword.signInNow.label= ÙØ±Ø¬Ù ØªØ³Ø¬ÙÙ Ø§ÙØ¯Ø®ÙÙ Ø§ÙØ¢Ù


#Rest password Email
reset.title= ØªÙ Ø¥Ø±Ø³Ø§Ù Ø±Ø³Ø§ÙØ© Ø¨Ø±ÙØ¯ Ø¥ÙÙØªØ±ÙÙÙ ÙØ¥Ø¹Ø§Ø¯Ø© ØªØ¹ÙÙÙ ÙÙÙØ© Ø§ÙÙØ±ÙØ±
reset.sub.title= Ø±Ø§Ø¨Ø· Ø¥Ø¹Ø§Ø¯Ø© ØªØ¹ÙÙÙ ÙÙÙØ© Ø§ÙÙØ±ÙØ± ÙÙ Ø·Ø±ÙÙÙ Ø¥ÙÙ ØµÙØ¯ÙÙ Ø§ÙØ¨Ø±ÙØ¯ Ø§ÙÙØ§Ø±Ø¯.
reset.ResendLink = Ø¥Ø¹Ø§Ø¯Ø© Ø¥Ø±Ø³Ø§Ù Ø§ÙØ±Ø§Ø¨Ø·
reset.signIN = Ø³Ø¬ÙÙ Ø§ÙØ¯Ø®ÙÙ


site.comma = Ø
site.affiliation  = Landmarkshops.in
account.confirmation.address.added  = ØªÙ Ø¥ÙØ´Ø§Ø¡ Ø¹ÙÙØ§ÙÙ.
account.confirmation.address.removed  = ØªÙ Ø­Ø°Ù Ø¹ÙÙØ§ÙÙ.
account.confirmation.address.updated  = ØªÙ ØªØ­Ø¯ÙØ« Ø¹ÙÙØ§ÙÙ.
account.confirmation.default.address.changed  = ØªÙ ØªØ­Ø¯ÙØ« Ø¹ÙÙØ§ÙÙ Ø§ÙØ£Ø³Ø§Ø³Ù.
account.confirmation.forgotten.password.link.sent  = ÙÙØ¯ ØªÙ Ø¥Ø±Ø³Ø§Ù Ø¨Ø±ÙØ¯ Ø¥ÙÙØªØ±ÙÙÙ ÙØ¹ Ø±Ø§Ø¨Ø· ÙØªØºÙÙØ± ÙÙÙØ© Ø§ÙÙØ±ÙØ± Ø§ÙØ®Ø§ØµØ© Ø¨Ù.
account.confirmation.password.updated  = ØªÙ ØªØºÙÙØ± ÙÙÙØ© Ø§ÙÙØ±ÙØ± Ø§ÙØ®Ø§ØµØ© Ø¨Ù. ÙØ±Ø¬Ù ØªØ³Ø¬ÙÙ Ø§ÙØ¯Ø®ÙÙ ÙÙÙØµÙÙ Ø¥ÙÙ Ø­Ø³Ø§Ø¨Ù.
account.confirmation.payment.details.removed  = ØªÙ Ø­Ø°Ù Ø¨ÙØ§ÙØ§Øª Ø§ÙØ¯ÙØ¹ Ø§ÙØ®Ø§ØµØ© Ø¨Ù.
account.confirmation.profile.updated  = ØªÙ ØªØ­Ø¯ÙØ« ÙÙÙÙ Ø§ÙØ´Ø®ØµÙ.
account.confirmation.signout.subtitle  = Ø³ØªÙÙÙ Ø³ÙØ© Ø§ÙØªØ³ÙÙ Ø§ÙØ®Ø§ØµØ© Ø¨Ù ÙØ­ÙÙØ¸Ø© Ø¨Ø§ÙØªØ¸Ø§Ø±Ù Ø¹ÙØ¯ Ø§ÙÙÙØ§Ù Ø¨ØªØ³Ø¬ÙÙ Ø§ÙØ¯Ø®ÙÙ ÙÙ Ø§ÙÙØ±Ø© Ø§ÙÙØ§Ø¯ÙØ©.
account.confirmation.signout.title  = ÙÙØ¯ ÙÙØª Ø¨ØªØ³Ø¬ÙÙ Ø§ÙØ®Ø±ÙØ¬ ÙÙ Ø­Ø³Ø§Ø¨Ù.
account.error.account.exists.with.email.address.subtitle  = ÙØ±Ø¬Ù Ø§Ø®ØªÙØ§Ø± Ø¹ÙÙØ§Ù Ø¨Ø±ÙØ¯ Ø¥ÙÙØªØ±ÙÙÙ Ø¨Ø¯ÙÙ.
account.error.account.exists.with.email.address.title  = ÙÙØ¬Ø¯ Ø­Ø³Ø§Ø¨ ÙØ³Ø¬ÙÙ ÙØ³Ø¨ÙÙØ§ Ø¨Ø¹ÙÙØ§Ù Ø§ÙØ¨Ø±ÙØ¯ Ø§ÙØ¥ÙÙØªØ±ÙÙÙ {0}
account.error.account.not.found  = ÙÙ ÙØªÙ Ø§ÙØ¹Ø«ÙØ± Ø¹ÙÙ Ø­Ø³Ø§Ø¨ Ø¨Ø¹ÙÙØ§Ù Ø§ÙØ¨Ø±ÙØ¯ Ø§ÙØ¥ÙÙØªØ±ÙÙÙ Ø§ÙÙÙØ¯ÙÙ.
account.error.login.please  = ÙØ¬Ø¨ ØªØ³Ø¬ÙÙ Ø§ÙØ¯Ø®ÙÙ ÙÙÙØµÙÙ Ø¥ÙÙ ÙØ°Ù Ø§ÙØµÙØ­Ø©. ÙÙØªØ³Ø¬ÙÙ ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø¨ÙØ§ÙØ§ØªÙ Ø£Ø¯ÙØ§Ù.
address.building  = Ø§Ø³Ù Ø£Ù Ø±ÙÙ Ø§ÙØ¨ÙØ§ÙØ© Ø­Ø³Ø¨ Ø§ÙÙÙÙØ¹ Ø§ÙÙÙØ­Ø¯ÙØ¯ ÙÙ ÙØ¨ÙÙ
address.building_and_room  = Ø§Ø³Ù Ø§ÙÙØ¨ÙÙ ÙØ±ÙÙ Ø§ÙØºØ±ÙØ©
address.country  = Ø§ÙØ¯ÙÙØ©
address.country.invalid  = ÙØ±Ø¬Ù Ø§Ø®ØªÙØ§Ø± Ø§ÙØ¯ÙÙØ©
address.default  = Ø­ÙØ¸ ÙØ£Ø³Ø§Ø³Ù
address.district  = Ø§ÙØ­Ù
address.district.invalid  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø§ÙØ­Ù
address.district_and_street  = Ø§ÙØ­Ù ÙØ§ÙØ´Ø§Ø±Ø¹
address.error.formentry.invalid  = ÙÙØ§Ù Ø£Ø®Ø·Ø§Ø¡ ÙÙ Ø§ÙØ¨ÙØ§ÙØ§Øª Ø§ÙØªÙ Ø£Ø¯Ø®ÙØªÙØ§ ÙØ±Ø¬Ù Ø§ÙØªØ­ÙÙÙ ÙÙÙØ§ ÙØ¥Ø¹Ø§Ø¯Ø© Ø§ÙÙØ­Ø§ÙÙØ©.
address.firstName  = Ø§ÙØ§Ø³Ù Ø§ÙØ§ÙÙ
address.firstName.invalid  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø§ÙØ§Ø³Ù Ø§ÙØ£ÙÙ
address.furtherSubarea  = Ø±ÙÙ Ø§ÙÙÙØ·ÙØ© Ø§ÙÙØ±Ø¹ÙØ©Ø Ø±ÙÙ Ø§ÙÙÙØ²Ù
address.furtherSubarea.invalid  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø±ÙÙ ÙÙØ·ÙØ© ÙØ±Ø¹ÙØ© Ø¥Ø¶Ø§ÙÙ
address.lastName.invalid  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø§ÙÙÙØ¨
address.line1  = Ø¹ÙÙØ§Ù 1
address.line1.invalid  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø¹ÙÙØ§Ù 1
address.line2  = Ø¹ÙÙØ§Ù 2
address.line2.invalid  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø¹ÙÙØ§Ù 2
address.postalcode  = Ø§ÙØ±ÙØ² Ø§ÙØ¨Ø±ÙØ¯Ù
address.postcode  = Ø§ÙØ±ÙØ² Ø§ÙØ¨Ø±ÙØ¯Ù
address.postcode.invalid  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø§ÙØ±ÙØ² Ø§ÙØ¨Ø±ÙØ¯Ù
address.postcodeJP  = Ø§ÙØ±ÙØ² Ø§ÙØ¨Ø±ÙØ¯Ù ÙØ³Ø¨ÙÙÙØ§ Ø¨Ø£Ù Ø±ÙÙØ² Ø°Ø§Øª ØµÙØ©
address.postcodeJP.invalid  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø§ÙØ±ÙØ² Ø§ÙØ¨Ø±ÙØ¯Ù ÙØ³Ø¨ÙÙÙØ§ Ø¨Ø£Ù Ø±ÙÙØ² Ø°Ø§Øª ØµÙØ©
address.prefecture  = Ø§Ø³Ù Ø§ÙÙØ­Ø§ÙØ¸Ø©
address.prefecture.invalid  = Ø§Ø®ØªØ± Ø§ÙÙØ­Ø§ÙØ¸Ø©
address.province  = Ø§ÙÙÙØ§Ø·Ø¹Ø©
address.province.invalid  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø§ÙÙÙØ§Ø·Ø¹Ø©
address.regionIso.invalid  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø§ÙÙÙØ·ÙØ©
address.required  = ÙØ¬Ø¨ ÙÙØ¡ Ø§ÙØ­ÙÙÙ Ø§ÙØªÙ ØªØ­ÙÙ Ø¹ÙØ§ÙØ© "*"
address.room  = Ø±ÙÙ Ø§ÙØ´ÙØ©/Ø§ÙØºØ±ÙØ©
address.room.invalid  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø±ÙÙ Ø§ÙØ´ÙØ©/Ø§ÙØºØ±ÙØ©
address.selectCountry  = Ø§ÙØ¯ÙÙØ©
address.selectPrefecture  = Ø§ÙÙØ­Ø§ÙØ¸Ø©
address.selectProvince  = Ø§ÙÙÙØ§Ø·Ø¹Ø©
address.selectState  = Ø§ÙÙØ­Ø§ÙØ¸Ø© / Ø§ÙÙÙØ§Ø·Ø¹Ø©
address.state  = Ø§ÙÙØ­Ø§ÙØ¸Ø© / Ø§ÙÙÙØ§Ø·Ø¹Ø©
address.street  = Ø§Ø³Ù ÙØ±ÙÙ Ø§ÙØ´Ø§Ø±Ø¹
address.subarea  = Ø§ÙÙÙØ·ÙØ© Ø§ÙÙØ±Ø¹ÙØ©
address.subarea.invalid  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø§ÙÙÙØ·ÙØ© Ø§ÙÙØ±Ø¹ÙØ©
address.surname  = Ø§Ø³Ù Ø§ÙØ¹Ø§Ø¦ÙØ©
address.title  = Ø§ÙÙÙØ¨
address.title.invalid  = ÙØ±Ø¬Ù Ø§Ø®ØªÙØ§Ø± Ø§ÙÙÙØ¨
address.title.pleaseSelect  = Ø§ÙÙÙØ¨
address.townCity  = Ø§ÙÙØ¯ÙÙØ©
address.townCity.invalid  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø§ÙÙØ¯ÙÙØ© Ø£Ù Ø§ÙØ¨ÙØ¯Ø©
address.townJP  = Ø§ÙÙØ¯ÙÙØ©Ø Ø§ÙÙØ±ÙØ©Ø Ø§ÙØ­Ù
address.townJP.invalid  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø§Ø³Ù Ø§ÙÙØ¯ÙÙØ©Ø Ø§ÙÙØ±ÙØ©Ø Ø§ÙØ­Ù
address.zipcode  = Ø§ÙØ±ÙØ² Ø§ÙØ¨Ø±ÙØ¯Ù
aria.pickupinstore.loading  = Ø¬Ø§Ø±Ù Ø§ÙØªØ­ÙÙÙ... ÙØ±Ø¬Ù Ø§ÙØ§ÙØªØ¸Ø§Ø±...
aria.pickupinstore.storesloaded  = Ø¬Ø§Ø±Ù ØªØ­ÙÙÙ Ø§ÙÙØªØ§Ø¬Ø±
basket.add.free.gift  = Ø£Ø¶Ù ÙØ¯ÙØªÙ Ø§ÙÙØ¬Ø§ÙÙØ© Ø¥ÙÙ Ø³ÙØ© Ø§ÙØªØ³ÙÙ
basket.add.to.basket  = Ø£Ø¶Ù Ø¥ÙÙ Ø³ÙØ© Ø§ÙØªØ³ÙÙ
basket.add.to.cart  = Ø¥Ø¶Ø§ÙØ©
basket.added.to.basket  = ØªÙØª Ø¥Ø¶Ø§ÙØ© Ø§ÙÙÙØªØ¬ Ø¥ÙÙ Ø³ÙØ© Ø§ÙØªØ³ÙÙ
basket.added.to.mobile.basket  = ØªÙØª Ø¥Ø¶Ø§ÙØ© Ø§ÙÙÙØªØ¬
basket.confirmation.items.added  = ØªÙØª Ø¥Ø¶Ø§ÙØ© Ø§ÙÙÙØªØ¬Ø§Øª Ø§ÙÙØ­ÙÙØ¸Ø© Ø¥ÙÙ Ø³ÙØ© Ø§ÙØªØ³ÙÙ.
basket.error.no.pickup.location  = ÙØ±Ø¬Ù Ø§Ø®ØªÙØ§Ø± Ø§ÙÙØªØ§Ø¬Ø± ÙÙÙÙØªØ¬Ø§Øª Ø§ÙÙØ¸ÙÙÙØ© Ø¨Ø§ÙÙÙÙ Ø§ÙØ£Ø­ÙØ±
basket.error.occurred  = Ø­Ø¯Ø« Ø®Ø·Ø£ Ø£Ø«ÙØ§Ø¡ Ø¥Ø¶Ø§ÙØ© Ø§ÙÙÙØªØ¬ Ø¥ÙÙ Ø³ÙØ© Ø§ÙØªØ³ÙÙ
basket.error.product.removed  = ÙØ£Ø³ÙØ ØªÙ Ø­Ø°Ù ÙÙØªØ¬ Ø£Ù Ø£ÙØ«Ø± ÙÙ Ø³ÙØ© Ø§ÙØªØ³ÙÙ ÙØ¸Ø±ÙØ§ ÙÙÙØ§Ø° Ø§ÙÙØ®Ø²ÙÙ Ø£Ù Ø§ÙØªÙØ§Ø¡ ÙØªØ±Ø© Ø¹Ø±Ø¶ Ø§ÙÙÙØªØ¬.
basket.error.quantity.invalid  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø±ÙÙ ÙÙØ¬Ø¨ ÙØªØ­Ø¯ÙØ« Ø§ÙÙÙÙØ© Ø§ÙÙØ·ÙÙØ¨Ø© ÙÙ Ø§ÙÙÙØªØ¬.
basket.error.quantity.invalid.binding  = Ø§ÙÙÙÙØ© ØºÙØ± ØµØ­ÙØ­Ø©: ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø±ÙÙ ØµØ­ÙØ­ ÙÙÙÙÙØ© Ø§ÙÙØ·ÙÙØ¨Ø© ÙÙ Ø£Ø¬Ù Ø¥Ø¶Ø§ÙØ© ÙØ°Ø§ Ø§ÙÙÙØªØ¬ Ø¥ÙÙ Ø³ÙØ© Ø§ÙØªØ³ÙÙ Ø§ÙØ®Ø§ØµØ© Ø¨Ù
basket.error.quantity.notNull  = ÙØ¬Ø¨ ÙÙØ¡ Ø­ÙÙ Ø§ÙÙÙÙØ© Ø§ÙÙØ·ÙÙØ¨Ø© .
basket.information.merge.successful  = ØªÙ Ø¯ÙØ¬ Ø³ÙØ© Ø§ÙØªØ³ÙÙ Ø§ÙÙØ­ÙÙØ¸Ø© Ø¨Ø­Ø³Ø§Ø¨Ù ÙØ¹ Ø§ÙØ³ÙØ© Ø§ÙØ­Ø§ÙÙØ©. ÙØ±Ø¬Ù Ø§ÙÙÙØ± ÙÙØ§ ÙÙØ§Ø·ÙØ§Ø¹ Ø¹ÙÙ ÙØ­ØªÙÙØ§Øª Ø³ÙØ© ØªØ³ÙÙÙ.
basket.information.quantity.adjusted  = ØªÙ ØªØ¹Ø¯ÙÙ Ø§ÙÙÙØªØ¬Ø§Øª Ø§ÙØ®Ø§ØµØ© Ø¨Ù Ø¨ÙØ§ ÙØªÙØ§Ø´Ù ÙØ¹ Ø§ÙÙÙÙØ§Øª Ø§ÙÙØªÙÙØ±Ø© ÙÙ Ø§ÙÙØ®Ø²ÙÙ
basket.information.quantity.noItemsAdded.maxOrderQuantityExceeded  = ÙÙØ£Ø³ÙØ Ø§ÙÙÙÙØ© Ø§ÙØªÙ Ø§Ø®ØªØ±ØªÙØ§ ØªØªØ¬Ø§ÙØ² Ø§ÙØ­Ø¯ Ø§ÙØ£ÙØµÙ Ø§ÙÙØ³ÙÙØ­ ÙÙÙÙØ© Ø·ÙØ¨ Ø§ÙÙÙØªØ¬Ø ÙØ°ÙÙ ØªÙ ØªØ®ÙÙØ¶ Ø§ÙÙÙÙØ© ÙÙ Ø³ÙØ© Ø§ÙØªØ³ÙÙ Ø§ÙØ®Ø§ØµØ© Ø¨Ù Ø¥ÙÙ Ø£ÙØµÙ Ø­Ø¯ ÙÙÙÙ.
basket.information.quantity.noItemsAdded.noStock  = Ø¹Ø°Ø±ÙØ§Ø Ø§ÙÙØ®Ø²ÙÙ Ø§ÙÙØªÙÙØ± ÙØ§ ÙÙÙÙ Ø·ÙØ¨Ù.
basket.information.quantity.reducedNumberOfItemsAdded.lowStock  = ØªÙØª Ø¥Ø¶Ø§ÙØ© ÙÙÙØ© Ø£ÙÙ ÙÙ Ø§ÙÙØ·ÙÙØ¨Ø© Ø¥ÙÙ Ø³ÙØ© Ø§ÙØªØ³ÙÙØ ÙØ¸Ø±ÙØ§ ÙØ¹Ø¯Ù ÙÙØ§ÙØ© Ø§ÙÙØ®Ø²ÙÙ.
basket.information.quantity.reducedNumberOfItemsAdded.maxOrderQuantityExceeded  = ÙÙØ£Ø³ÙØ Ø§ÙÙÙÙØ© Ø§ÙØªÙ Ø§Ø®ØªØ±ØªÙØ§ ØªØªØ¬Ø§ÙØ² Ø§ÙØ­Ø¯ Ø§ÙØ£ÙØµÙ Ø§ÙÙØ³ÙÙØ­ ÙÙÙÙØ© Ø§ÙØ·ÙØ¨ ÙÙ ÙØ°Ø§ Ø§ÙÙÙØªØ¬Ø ÙÙØ°ÙÙ ÙÙØ¯ ØªÙ ØªØ®ÙÙØ¶ Ø§ÙÙÙÙØ© ÙÙ Ø³ÙØ© Ø§ÙØªØ³ÙÙ Ø§ÙØ®Ø§ØµØ© Ø¨Ù Ø¥ÙÙ Ø§ÙØ­Ø¯ Ø§ÙØ£ÙØµÙ Ø§ÙÙÙÙÙ ÙÙØ°Ø§ Ø§ÙØ·ÙØ¨.
basket.page.cartHelpContent  = Ø¨Ø­Ø§Ø¬Ø© Ø¥ÙÙ ÙØ³Ø§Ø¹Ø¯Ø©Ø ÙÙÙÙÙ ÙØ±Ø§Ø³ÙØªÙØ§ Ø£Ù Ø§ÙØ§ØªØµØ§Ù Ø¨Ø®Ø¯ÙØ© Ø§ÙØ¹ÙÙØ§Ø¡ Ø¹ÙÙ Ø§ÙØ±ÙÙ - ### - ### - ####. ÙÙÙ Ø­Ø§Ù ÙØ§Ù Ø§ÙØ§ØªØµØ§Ù ÙØªØ¹ÙÙ Ø¨Ø³ÙØ© Ø§ÙØªØ³ÙÙ Ø§ÙØ®Ø§ØµØ© Ø¨ÙØ ÙØ±Ø¬Ù Ø°ÙØ± Ø§ÙØ±ÙÙ Ø§ÙØªØ¹Ø±ÙÙÙ ÙÙØ³ÙØ© ÙØ§ÙÙØ¨ÙÙ Ø£Ø¹ÙØ§Ù.
basket.page.cartHelpMessageMobile  = Ø¨Ø­Ø§Ø¬Ø© Ø¥ÙÙ ÙØ³Ø§Ø¹Ø¯Ø©Ø ÙØ±Ø¬Ù Ø§ÙØ§ØªØµØ§Ù Ø¨ÙØ±ÙÙÙØ§ ÙÙØ³Ø§Ø¹Ø¯ØªÙ Ø¨ØµÙØ±Ø© ÙØ¨Ø§Ø´Ø±Ø© 1-222-333-444 ÙØ¹ Ø°ÙØ± Ø§ÙØ±ÙÙ Ø§ÙØªØ¹Ø±ÙÙÙ Ø§ÙØ®Ø§Øµ Ø¨Ø³ÙØ© ØªØ³ÙÙÙ {0} ÙÙÙ
basket.page.cartId  = Ø§ÙØ±ÙÙ Ø§ÙØªØ¹Ø±ÙÙÙ ÙÙØ³ÙØ©:
basket.page.free  = ÙØ¬Ø§ÙÙØ§
basket.page.itemNumber  = Ø±ÙÙ Ø§ÙÙÙØªØ¬
basket.page.itemPrice  = Ø³Ø¹Ø± Ø§ÙÙÙØªØ¬
basket.page.message.remove  = ØªÙ Ø­Ø°Ù Ø§ÙÙÙØªØ¬ ÙÙ Ø³ÙØ© Ø§ÙØªØ³ÙÙ.
basket.page.message.update  = ØªÙ ØªØ­Ø¯ÙØ« ÙÙÙØ© Ø§ÙÙÙØªØ¬.
basket.page.message.update.pickupinstoreitem  = ØªÙ ØªØ­Ø¯ÙØ« Ø§ÙÙÙØªØ¬ Ø§ÙØ°Ù Ø³ÙÙØ³ØªÙÙ ÙÙ Ø§ÙÙØªØ¬Ø±.
basket.page.message.update.pickupinstoreitem.toship  = ØªÙ ØªØ­Ø¯ÙØ« Ø§ÙÙÙØªØ¬ Ø§ÙØ°Ù Ø³ÙÙØ³ØªÙÙ ÙÙ Ø§ÙÙØªØ¬Ø± ÙØ³ÙØ¬Ø±Ù ØªÙØµÙÙÙ.
basket.page.message.update.reducedNumberOfItemsAdded.lowStock  = ØªÙ Ø®ÙØ¶ ÙÙÙØ© Ø§ÙÙÙØªØ¬ ÙÙ Ø³ÙØ© Ø§ÙØªØ³ÙÙ Ø¥ÙÙ {0} ÙÙ  {1} ÙØ¸Ø±ÙØ§ ÙØ¹Ø¯Ù ÙÙØ§ÙØ© Ø§ÙÙØ®Ø²ÙÙ.
basket.page.message.update.reducedNumberOfItemsAdded.noStock  = ØªÙØª  Ø­Ø°ÙÙ ÙÙ Ø§ÙØ³ÙØ© ÙØ¸Ø±ÙØ§ ÙØ¹Ø¯Ù ÙÙØ§ÙØ© Ø§ÙÙØ®Ø²ÙÙ.
basket.page.needHelp  = Ø¨Ø­Ø§Ø¬Ø© Ø¥ÙÙ ÙØ³Ø§Ø¹Ø¯Ø©Ø
basket.page.number  = ÙØ§.
basket.page.price  = Ø§ÙØ³Ø¹Ø±
basket.page.availability  = ØªÙÙÙØ± Ø§ÙÙÙØªØ¬
basket.page.product  = Ø§ÙÙÙØªØ¬
basket.page.productdetails  = ØªÙØ§ØµÙÙ Ø§ÙÙÙØªØ¬
basket.page.qty  = Ø§ÙÙÙÙØ©
basket.page.quantity  = Ø§ÙÙÙÙØ©
basket.page.remove  = Ø­Ø°Ù
basket.page.shipping  = Ø§ÙØ´Ø­Ù ÙØ§ÙØªÙØµÙÙ
basket.page.shipping.change.store  = ØªØºÙÙØ± Ø§ÙÙØªØ¬Ø±
basket.page.shipping.find.store  = Ø§ÙØ¨Ø­Ø« Ø¹Ù ÙØªØ¬Ø±
basket.page.shipping.pickup  = Ø§ÙØ§Ø³ØªÙØ§Ù
basket.page.shipping.ship  = Ø§ÙØ´Ø­Ù ÙØ§ÙØªÙØµÙÙ
basket.page.title  = Ø§ÙÙÙØªØ¬
basket.page.title.pickupFrom  = Ø§ÙØ§Ø³ØªÙØ§Ù ÙÙ:
basket.page.title.yourDeliveryItems  = Ø§ÙÙÙØªØ¬Ø§Øª Ø§ÙØªÙ Ø³ÙØªÙ ØªÙØµÙÙÙØ§
basket.page.title.yourDeliveryItems.pickup  = Ø§ÙÙÙØªØ¬Ø§Øª Ø§ÙÙØ·ÙÙØ¨Ø© ÙÙØ§Ø³ØªÙØ§Ù Ø³ØªØ¸ÙØ± ÙØ§Ø­ÙÙØ§ Ø®ÙØ§Ù Ø¹ÙÙÙØ© Ø§ÙØ¯ÙØ¹.
basket.page.title.yourItems  = Ø³ÙØ© Ø§ÙØªØ³ÙÙ
basket.page.title.yourPickUpItems  = Ø§ÙÙÙØªØ¬Ø§Øª Ø§ÙØªÙ Ø³ØªÙÙÙ Ø¨Ø§Ø³ØªÙØ§ÙÙØ§
basket.page.total  = Ø§ÙÙØ¬ÙÙØ¹ Ø§ÙØ¥Ø¬ÙØ§ÙÙ
basket.page.total.includingVat = Ø¨ÙØ§ ÙÙ Ø°ÙÙ Ø¶Ø±ÙØ¨Ø© Ø§ÙÙÙÙØ© Ø§ÙÙØ¶Ø§ÙØ©
basket.page.totals.delivery  = Ø§ÙØªÙØµÙÙ:
basket.page.totals.grossTax  = ÙØ´ÙÙ Ø·ÙØ¨Ù Ø¶Ø±ÙØ¨Ø©  Ø¨ÙÙÙØ© {0} .
basket.page.totals.netTax  = Ø§ÙØ¶Ø±ÙØ¨Ø©:
basket.page.totals.noNetTax  = *ÙØ§ ÙØªØ¶ÙÙ Ø§ÙÙØ¬ÙÙØ¹ Ø£Ù Ø¶Ø±Ø§Ø¦Ø¨
basket.page.totals.savings  = Ø§ÙØªÙÙÙØ±:
basket.page.totals.subtotal  = Ø§ÙÙØ¬ÙÙØ¹:
basket.page.totals.standard.delivery = ØªÙØµÙÙ Ø¹Ø§Ø¯Ù:
basket.page.totals.total  = Ø§ÙÙØ¬ÙÙØ¹
basket.page.totals.total.items  = Ø§ÙÙØ¬ÙÙØ¹: ( {0} ÙÙØªØ¬Ø§Øª)  &nbsp;
basket.page.totals.total.items.one  = Ø§ÙÙØ¬ÙÙØ¹: ( {0} ÙÙØªØ¬Ø§Øª)  &nbsp;
basket.page.totals.estimatetaxesbutton  = ØªÙØ¯ÙØ± Ø§ÙØ¶Ø±ÙØ¨Ø©
basket.page.totals.estimatedtotaltax  = Ø§ÙØ¶Ø±ÙØ¨Ø© Ø§ÙØªÙØ¯ÙØ±ÙØ©:
basket.page.totals.estimatedtotal  = Ø§ÙÙØ¬ÙÙØ¹ Ø§ÙØªÙØ¯ÙØ±Ù:
basket.page.totals.estimatedZip  = Ø§ÙØ±ÙØ² Ø§ÙØ¨Ø±ÙØ¯Ù:
basket.page.totals.deliverycountry  = ÙØ¬ÙØ© Ø§ÙØªÙØµÙÙ:
basket.page.totals.error.wrongzipcode  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø±ÙØ² Ø¨Ø±ÙØ¯Ù ØµØ­ÙØ­
basket.page.totals.error.wrongcountry  = ÙØ±Ø¬Ù Ø§Ø®ØªÙØ§Ø± Ø§ÙØ¯ÙÙØ©
basket.page.update  = ØªØ­Ø¯ÙØ«
basket.page.validation.message  = ØªÙ ØªØ­Ø¯ÙØ«/Ø­Ø°Ù Ø§ÙÙÙØªØ¬ (Ø§ÙÙÙØªØ¬Ø§Øª) ÙÙ Ø³ÙØ© Ø§ÙØªØ³ÙÙ Ø§ÙØ®Ø§ØµØ© Ø¨Ù.
basket.pickup.product.variant  =  {0}: {1}
basket.potential.promotions  = Ø¹Ø±ÙØ¶ ÙØ­ØªÙÙØ©
basket.received.promotions  = Ø§ÙØ¹Ø±ÙØ¶ Ø§ÙÙØ³ØªÙÙØ©
basket.restoration.restorationError  = ÙØ§ ÙÙÙÙ Ø§Ø³ØªØ¹Ø§Ø¯Ø© ÙØ­ØªÙÙØ§Øª Ø³ÙØ© Ø§ÙØªØ³ÙÙ.
basket.restoration  = ÙØ±Ø­Ø¨ÙØ§ ÙØ¬Ø¯Ø¯ÙØ§Ø ÙÙØ¯ ØªÙØª Ø¥Ø¶Ø§ÙØ© Ø§ÙÙÙØªØ¬Ø§Øª ÙÙ Ø³ÙØ© Ø§ÙØªØ³ÙÙ Ø§ÙÙØ­ÙÙØ¸Ø© Ø¥ÙÙ Ø³ÙØ© Ø§ÙØªØ³ÙÙ Ø§ÙØ®Ø§ØµØ© Ø¨Ù.
basket.restoration.delivery.changed  = {0} ØªÙØª Ø¥Ø¹Ø§Ø¯Ø© Ø§ÙÙÙØªØ¬ Ø¥ÙÙ Ø³ÙØ© Ø§ÙØªØ³ÙÙ Ø§ÙØ®Ø§ØµØ© Ø¨ÙØ Ø¥ÙØ§ Ø£Ù Ø·Ø±ÙÙØ© Ø§ÙØªØ³ÙÙÙ ØªÙ ØªØºÙÙØ±ÙØ§ ÙØ¸Ø±ÙØ§ ÙØ§ÙØ®ÙØ§Ø¶ ÙØ®Ø²ÙÙÙ Ø§ÙÙØ­ÙÙ.
basket.restoration.errorMsg  = ÙØ§ ÙÙÙÙ Ø§Ø³ØªØ¹Ø§Ø¯Ø© ÙØ­ØªÙÙØ§Øª Ø³ÙØ© Ø§ÙØªØ³ÙÙ.
basket.restoration.lowStock  = ÙÙØ£Ø³Ù ØªÙ Ø§Ø³ØªØ¹Ø§Ø¯Ø© ÙÙÙØ© Ø£ÙÙ ØªØ¨ÙØº {0} ÙØ·Ø¹ Ø¥ÙÙ Ø³ÙØ© Ø§ÙØªØ³ÙÙ Ø§ÙØ®Ø§ØµØ© Ø¨Ù ÙØ¸Ø±ÙØ§ ÙØ¹Ø¯Ù ÙÙØ§ÙØ© Ø§ÙÙØ®Ø²ÙÙ. ØªÙØª Ø¥Ø¶Ø§ÙØ© {3} ÙØ·Ø¹  Ø¥ÙÙ Ø§ÙØ³ÙØ© ÙÙØµØ¨Ø­ ÙØ¯ÙÙ Ø§ÙØ¢Ù {2} ÙØ·Ø¹.
basket.restoration.noStock  =ÙÙØ£Ø³Ù ÙÙØ³ Ø¨Ø§ÙØ¥ÙÙØ§Ù Ø¥Ø¹Ø§Ø¯ØªÙ Ø¥ÙÙ Ø³ÙØ© Ø§ÙØªØ³ÙÙ Ø§ÙØ®Ø§ØµØ© Ø¨Ù ÙØ¸Ø±ÙØ§ ÙÙÙØ§Ø°Ù ÙÙ Ø§ÙÙØ®Ø²ÙÙ.
basket.restoration.success  = {0} ØªÙØª Ø¥Ø¹Ø§Ø¯ØªÙ Ø¨ÙØ¬Ø§Ø­ Ø¥ÙÙ Ø³ÙØ© Ø§ÙØªØ³ÙÙ Ø§ÙØ®Ø§ØµØ© Ø¨Ù.
basket.restoration.view.cart  = Ø§ÙÙØ± ÙÙØ§ ÙÙØ§Ø·ÙØ§Ø¹ Ø¹ÙÙ ÙØ­ØªÙÙØ§Øª Ø³ÙØ© Ø§ÙØªØ³ÙÙ.
basket.restoration.unavailable  = ÙÙØ£Ø³ÙØ ÙÙ ÙØ¹Ø¯ {0} ÙØªÙÙØ±ÙØ§. ÙØ§Ù ÙØ¯ÙÙ ÙØ³Ø¨ÙÙØ§ {2} ÙÙÙ ÙÙ Ø³ÙØ© Ø§ÙØªØ³ÙÙ.
basket.validation.lowStock  = ÙÙØ£Ø³Ù ÙÙ ÙØªÙÙÙ ÙÙ Ø¥Ø¶Ø§ÙØ© Ø§ÙÙÙÙÙØ© Ø§ÙÙØ®ØªØ§Ø±Ø© ÙØ§ÙÙØ©Ø ÙØ¸Ø±ÙØ§ ÙØ¹Ø¯Ù ÙÙØ§ÙØ© Ø§ÙÙØ®Ø²ÙÙ.
basket.validation.movedFromPOSToStore  = ØªÙ ØªØ¹Ø¯ÙÙ Ø§ÙÙÙØªØ¬Ø§Øª Ø§ÙØ®Ø§ØµØ© Ø¨Ù ÙÙÙÙØ§ ÙÙÙØ®Ø²ÙÙ Ø§ÙÙØªÙÙØ±. ÙÙØ¯ ØªÙ ØªØ­Ø¯ÙØ« Ø¨ÙØ§ÙØ§Øª ÙØ§Ø­Ø¯Ø© Ø£Ù Ø£ÙØ«Ø± ÙÙÙØ§ ÙØªØºÙÙØ± ØªØµÙÙÙÙØ§ ÙÙ "Ø§ÙØ§Ø³ØªÙØ§Ù ÙÙ Ø§ÙÙØªØ¬Ø±" Ø¥ÙÙ "Ø§ÙØªÙØµÙÙ".
basket.validation.noStock  = ÙÙØ£Ø³Ù ØªÙ Ø­Ø°Ù Ø§ÙÙÙØªØ¬ ÙÙ Ø³ÙØ© Ø§ÙØªØ³ÙÙ Ø§ÙØ®Ø§ØµØ© Ø¨Ù ÙØ¸Ø±ÙØ§ ÙÙÙØ§Ø° Ø§ÙÙØ®Ø²ÙÙ.
basket.validation.unavailable  =ÙÙØ£Ø³Ù ØªÙ Ø­Ø°Ù {0} ÙÙ Ø³ÙÙØ© Ø§ÙØªØ³ÙÙ ÙØ¸Ø±ÙØ§ ÙÙÙØ§Ø° Ø§ÙÙÙÙÙØ©.
basket.view.basket  = Ø§ÙØ§Ø·ÙÙØ§Ø¹ Ø¹ÙÙ ÙØ­ØªÙÙØ§Øª Ø§ÙØ³ÙØ©
basket.your.shopping.basket  = Ø³ÙØ© Ø§ÙØªØ³ÙÙ Ø§ÙØ®Ø§ØµØ© Ø¨Ù
basket.page.shipping.charges.may.apply = ÙØ¯ ÙØªÙ ØªØ·Ø¨ÙÙ Ø±Ø³ÙÙ Ø§ÙØ´Ø­Ù
basket.page.footer.contact.callCustomer.egypt = Ø§ØªØµÙ Ø¨ÙØ³Ù Ø®Ø¯ÙØ© Ø§ÙØ¹ÙÙØ§Ø¡ Ø¹ÙÙ Ø§ÙØ±ÙÙ 0020-221294301
basket.page.footer.promoCode.already.used.this.voucher.egypt = Ø¹Ø°Ø±Ø§Ø ÙÙØ¯ Ø§Ø³ØªØ®Ø¯ÙØª ÙØ³ÙÙØ© Ø¨Ø§ÙÙØ¹Ù
breadcrumb.cart  = Ø³ÙØ© Ø§ÙØªØ³ÙÙ
breadcrumb.home  = Ø§ÙØµÙØ­Ø© Ø§ÙØ±Ø¦ÙØ³ÙØ©
breadcrumb.login  = ØªØ³Ø¬ÙÙ Ø§ÙØ¯Ø®ÙÙ
breadcrumb.not.found  = Ø§ÙØµÙØ­Ø© ØºÙØ± ÙÙØ¬ÙØ¯Ø©
cart.page.shop  = ÙØªØ¬Ø±
cart.page.parameter.voucher.code.empty=ÙØ¬Ø¨ ÙØªØ§Ø¨Ø© Ø±ÙØ² Ø§ÙÙØ³ÙÙØ©
checkout.order.summary  = ÙÙØ®Øµ Ø§ÙØ·ÙØ¨
checkout.checkout  = Ø§ÙØ¯ÙØ¹
checkout.Pay.Now=Ø§Ø¯ÙØ¹ Ø§ÙØ£Ù
checkout.checkout.flow.select  = Ø§Ø®ØªÙØ§Ø± Ø·Ø±ÙÙØ© Ø¯ÙØ¹ Ø¨Ø¯ÙÙØ©
checkout.checkout.multi  = Ø¹ÙÙÙØ§Øª Ø¯ÙØ¹ ÙØªØ¹Ø¯Ø¯Ø©
checkout.checkout.multi.deliveryAddress.viewAddressBook  = Ø¯ÙØªØ± Ø¹ÙØ§ÙÙÙÙ
checkout.multi.deliveryAddress.continue  = ÙØªØ§Ø¨Ø¹Ø©
checkout.checkout.multi.pci  = Ø¹ÙÙÙØ§Øª Ø¯ÙØ¹ ÙØªØ¹Ø¯Ø¯Ø© ÙØªÙØ§ÙÙØ© ÙØ¹ ÙØ¹ÙØ§Ø± PCI
checkout.checkout.multi.pci-hop  = PCI-HOP
checkout.checkout.multi.pci-sop  = PCI-SOP
checkout.checkout.multi.pci-ws  = PCI-Default
checkout.checkout.multi.pci.select  = ÙØ±Ø¬Ù ØªØ­Ø¯ÙØ¯ Ø£Ø­Ø¯ Ø®ÙØ§Ø±Ø§Øª PCI
checkout.checkout.single  = Ø¹ÙÙÙØ© Ø¯ÙØ¹ ÙØ±Ø¯ÙØ©
checkout.login.guestCheckout  = Ø¥ØªÙØ§Ù Ø¹ÙÙÙØ© Ø§ÙØ¯ÙØ¹ ÙØ²Ø§Ø¦Ø±
checkout.login.loginAndCheckout  = ØªØ³Ø¬ÙÙ Ø§ÙØ¯Ø®ÙÙ ÙØ§ÙØ¯ÙØ¹
checkout.login.registerAndCheckout  = Ø§ÙØªØ³Ø¬ÙÙ ÙØ§ÙØ¯ÙØ¹
checkout.multi.deliveryMethod.continue  = ÙØªØ§Ø¨Ø¹Ø©
checkout.multi.paymentMethod.continue  = ÙØªØ§Ø¨Ø¹Ø©
checkout.page.order.summary  = ÙÙØ®Øµ Ø§ÙØ·ÙØ¨
forgottenPwd.description  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø¹ÙÙØ§Ù Ø§ÙØ¨Ø±ÙØ¯ Ø§ÙØ¥ÙÙØªØ±ÙÙÙ Ø§ÙÙØ±ØªØ¨Ø· Ø¨Ø­Ø³Ø§Ø¨ÙØ ÙØ³ÙÙ ÙØ±Ø³Ù Ø¥ÙÙÙ Ø±Ø§Ø¨Ø· ÙØªØºÙÙØ± ÙÙÙØ© Ø§ÙÙØ±ÙØ± Ø§ÙØ®Ø§ØµØ© Ø¨Ù.
forgottenPwd.email  = Ø¹ÙÙØ§Ù Ø§ÙØ¨Ø±ÙØ¯ Ø§ÙØ¥ÙÙØªØ±ÙÙÙ
forgottenPwd.email.invalid  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø¹ÙÙØ§Ù Ø¨Ø±ÙØ¯ Ø¥ÙÙØªØ±ÙÙÙ ØµØ­ÙØ­
forgottenPwd.submit  = Ø¥Ø±Ø³Ø§Ù Ø¨Ø±ÙØ¯ Ø¥ÙÙØªØ±ÙÙÙ
forgottenPwd.title  = Ø§Ø³ØªØ¹Ø§Ø¯Ø© ÙÙÙØ© Ø§ÙÙØ±ÙØ±
form.field.required  = Ø­ÙÙ Ø¥ÙØ²Ø§ÙÙ
form.global.error  = ÙØ±Ø¬Ù ØªØµØ­ÙØ­ Ø§ÙØ£Ø®Ø·Ø§Ø¡ Ø§ÙÙØ§Ø±Ø¯Ø© Ø£Ø¯ÙØ§Ù.
form.select.empty  = ÙØ±Ø¬Ù Ø§ÙØ§Ø®ØªÙØ§Ø±
general.continue.shopping  = ÙØªØ§Ø¨Ø¹Ø© Ø§ÙØªØ³ÙÙ
general.find.a.store  = Ø§ÙØ¨Ø­Ø« Ø¹Ù ÙØªØ¬Ø±
general.mobile.store  = ÙØªØ¬Ø± Ø§ÙÙÙØ§ØªÙ Ø§ÙÙØªØ­Ø±ÙØ©
general.month.april  = Ø£Ø¨Ø±ÙÙ
general.month.august  = Ø£ØºØ³Ø·Ø³
general.month.december  = Ø¯ÙØ³ÙØ¨Ø±
general.month.february  = ÙØ¨Ø±Ø§ÙØ±
general.month.january  = ÙÙØ§ÙØ±
general.month.july  = ÙÙÙÙÙ
general.month.june  = ÙÙÙÙÙ
general.month.march  = ÙØ§Ø±Ø³
general.month.may  = ÙØ§ÙÙ
general.month.november  = ÙÙÙÙØ¨Ø±
general.month.october  = Ø£ÙØªÙØ¨Ø±
general.month.september  = Ø³Ø¨ØªÙØ¨Ø±
general.required  = Ø¥ÙØ²Ø§ÙÙ
general.unknown.identifier  = ÙÙ ÙØªÙ Ø§ÙØªØ¹Ø±Ù Ø¹ÙÙ Ø§ÙØ±ÙØ² Ø§ÙØªØ¹Ø±ÙÙÙ
general.zoom  = Ø¹Ø±Ø¶ Ø§ÙØµÙØ±Ø© ÙÙØ¨ÙØ±Ø©
guest.checkPwd  = ØªØ£ÙÙØ¯ ÙÙÙØ© Ø§ÙÙØ±ÙØ±
guest.checkout  = Ø¬Ø¯ÙØ¯ Ø¨Ø§ÙÙØ³Ø¨Ø© ÙÙÙØªØ¬ {0}Ø
guest.checkout.existingaccount.register.error  = ÙÙØ¬Ø¯ Ø­Ø³Ø§Ø¨ ÙØ³Ø¬Ù ÙØ³Ø¨ÙÙØ§ Ø¨Ø¹ÙÙØ§Ù Ø§ÙØ¨Ø±ÙØ¯ Ø§ÙØ¥ÙÙØªØ±ÙÙÙ {0}
guest.confirm.email  = ØªØ£ÙÙØ¯ Ø¹ÙÙØ§Ù Ø§ÙØ¨Ø±ÙØ¯ Ø§ÙØ¥ÙÙØªØ±ÙÙÙ
guest.description  = ÙÙ ØªÙØ¶Ù Ø§ÙØªØ³ÙÙ Ø¯ÙÙ Ø¥ÙØ´Ø§Ø¡ Ø­Ø³Ø§Ø¨Ø<br /> Ø§ÙØ¹ÙÙÙØ© Ø³Ø±ÙØ¹Ø© ÙØ¨Ø³ÙØ·Ø© ÙÙØºØ§ÙØ©
guest.email  = Ø¹ÙÙØ§Ù Ø§ÙØ¨Ø±ÙØ¯ Ø§ÙØ¥ÙÙØªØ±ÙÙÙ
guest.pwd  = ÙÙÙÙ Ø§ÙÙØ±ÙØ±
guest.register  = Ø¥ÙØ´Ø§Ø¡ Ø­Ø³Ø§Ø¨
guest.register.description  = Ø¥Ø¬Ø±Ø§Ø¡ Ø£Ø³Ø±Ø¹ ÙØ¹ÙÙÙØ© Ø§ÙØ¯ÙØ¹ ÙÙ Ø¹ÙÙÙØ§Øª Ø§ÙØ´Ø±Ø§Ø¡ Ø§ÙÙÙØ¨ÙØ© ÙØ¹ Ø­ÙØ¸ Ø³Ø¬Ù Ø¨ÙØ§ÙØ© Ø·ÙØ¨Ø§ØªÙ Ø§ÙØ³Ø§Ø¨ÙØ© ÙØ¥ÙÙØ§ÙÙØ© Ø§ÙØ§Ø·ÙØ§Ø¹ Ø¹ÙÙÙ
guest.required.message  = ÙØ¬Ø¨ ÙÙØ¡ Ø§ÙØ­ÙÙÙ Ø§ÙØªÙ ØªØ­ÙÙ Ø¹ÙØ§ÙØ© "*"
guest.register.submit  = ØªØ³Ø¬ÙÙ
header.hello  = ÙØ±Ø­Ø¨ÙØ§
header.link.account  = Ø­Ø³Ø§Ø¨Ù
header.link.login  = ØªØ³Ø¬ÙÙ Ø§ÙØ¯Ø®ÙÙ / ØªØ³Ø¬ÙÙ Ø­Ø³Ø§Ø¨
header.link.logout  = ØªØ³Ø¬ÙÙ Ø§ÙØ®Ø±ÙØ¬
header.mobile.link.login  = ØªØ³Ø¬ÙÙ Ø§ÙØ¯Ø®ÙÙ
header.welcome  = Ø£ÙÙØ§Ù Ø¨Ù {0}
j_password  = ÙÙÙÙ Ø§ÙÙØ±ÙØ±
j_username  = ØªØ³Ø¬ÙÙ Ø§ÙØ¯Ø®ÙÙ
login.description  = ÙÙ ÙØ¯ÙÙ Ø­Ø³Ø§Ø¨Ø ÙØ±Ø¬Ù ØªØ³Ø¬ÙÙ Ø§ÙØ¯Ø®ÙÙ ÙØ§Ø³ØªØ¹ÙØ§Ù Ø§ÙØ¥Ø¹Ø¯Ø§Ø¯Ø§Øª Ø§ÙØ®Ø§ØµØ© Ø¨Ø­Ø³Ø§Ø¨Ù.
login.email  = Ø¹ÙÙØ§Ù Ø§ÙØ¨Ø±ÙØ¯ Ø§ÙØ¥ÙÙØªØ±ÙÙÙ
login.error.account.not.found.subtitle  = ÙÙ Ø­Ø§Ù ÙØ³ÙØª ÙÙÙØ© Ø§ÙÙØ±ÙØ±Ø ÙØ±Ø¬Ù Ø§Ø³ØªØ®Ø¯Ø§Ù Ø±Ø§Ø¨Ø· "ÙØ³ÙØª ÙÙÙØ© Ø§ÙÙØ±ÙØ±".
login.error.account.not.found.title  = Ø§Ø³Ù Ø§ÙÙØ³ØªØ®Ø¯Ù Ø£Ù ÙÙÙØ© Ø§ÙÙØ±ÙØ± ØºÙØ± ØµØ­ÙØ­Ø©.
login.error.incorrect.password.subtitle  = ÙÙ Ø­Ø§Ù ÙØ³ÙØª ÙÙÙØ© Ø§ÙÙØ±ÙØ±Ø ÙØ±Ø¬Ù Ø§Ø³ØªØ®Ø¯Ø§Ù Ø±Ø§Ø¨Ø· "ÙØ³ÙØª ÙÙÙØ© Ø§ÙÙØ±ÙØ±".
login.error.incorrect.password.title  = Ø§Ø³Ù Ø§ÙÙØ³ØªØ®Ø¯Ù Ø£Ù ÙÙÙØ© Ø§ÙÙØ±ÙØ± ØºÙØ± ØµØ­ÙØ­Ø©.
login.link.forgottenPwd  = ÙØ³ÙØª ÙÙÙØ© Ø§ÙÙØ±ÙØ±Ø
login.login  = ØªØ³Ø¬ÙÙ Ø§ÙØ¯Ø®ÙÙ
login.password  = ÙÙÙÙ Ø§ÙÙØ±ÙØ±
login.required  = Ø¥ÙØ²Ø§ÙÙ
login.optional  = (Ø§Ø®ØªÙØ§Ø±Ù)
login.required.message  = ÙØ¬Ø¨ ÙÙØ¡ Ø§ÙØ­ÙÙÙ Ø§ÙØªÙ ØªØ­ÙÙ Ø¹ÙØ§ÙØ© "*"
login.title  = Ø²Ø¨ÙÙ Ø¹Ø§Ø¦Ø¯
login.signup.header.title  = Ø³Ø¬ÙÙ Ø­Ø³Ø§Ø¨ÙØ§ Ø§ÙØ¢Ù
login.signup.header.text  = Ø§Ø³ØªÙØ¯ ÙÙ ÙÙØ²Ø© Ø§Ø³ØªØ¹ÙØ§Ù Ø­Ø³Ø§Ø¨ ÙØ§Ø­Ø¯ ÙØ¬ÙÙØ¹ Ø§ÙØ¹ÙØ§ÙØ§Øª Ø§ÙØªØ¬Ø§Ø±ÙØ© Ø§ÙÙØ´Ø§Ø±ÙØ©.
login.signup.facebook.button.text  = Ø§ÙØªØ³Ø¬ÙÙ Ø¨Ø§Ø³ØªØ®Ø¯Ø§Ù Ø­Ø³Ø§Ø¨Ù Ø¹ÙÙ ÙÙØ³Ø¨ÙÙ
login.signup.header.note  = Ø£Ù Ø¨Ø§ÙØ¨Ø±ÙØ¯ Ø§ÙØ¥ÙÙØªØ±ÙÙÙ
login.signup.firstname  = Ø§ÙØ§Ø³Ù Ø§ÙØ§ÙÙ
login.signup.firstname.required  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø§Ø³ÙÙ Ø§ÙØ£ÙÙ
login.signup.firstname.valid  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø§Ø³Ù Ø£ÙÙ ØµØ­ÙØ­
login.signup.lastname  = Ø§Ø³Ù Ø§ÙØ¹Ø§Ø¦ÙØ©
login.signup.lastname.required  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø§Ø³Ù Ø§ÙØ¹Ø§Ø¦ÙØ©
login.signup.lastname.valid  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø§Ø³Ù Ø¹Ø§Ø¦ÙØ© ØµØ­ÙØ­
login.signup.email  = Ø¨Ø±ÙØ¯Ù Ø§ÙØ§ÙÙØªØ±ÙÙÙ
login.signup.email.required  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø¹ÙÙØ§Ù Ø¨Ø±ÙØ¯Ù Ø§ÙØ¥ÙÙØªØ±ÙÙÙ
login.signup.email.invalid  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø¹ÙÙØ§Ù Ø¨Ø±ÙØ¯ Ø¥ÙÙØªØ±ÙÙÙ ØµØ­ÙØ­
login.signup.phone  = Ø±ÙÙ ÙØ§ØªÙÙ
login.signup.phone.required  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø±ÙÙ Ø§ÙÙØ§ØªÙ Ø§ÙØµØ­ÙØ­
login.signup.password  = ÙÙÙÙ Ø§ÙÙØ±ÙØ±
login.signup.password.required  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù ÙÙÙØ© Ø§ÙÙØ±ÙØ±
login.signup.password.minlength  = ÙØ¬Ø¨ Ø£Ù ØªØªØ£ÙÙ ÙÙÙØ© Ø§ÙÙØ±ÙØ± ÙÙ 6 Ø®Ø§ÙØ§Øª Ø¹ÙÙ Ø§ÙØ£ÙÙ
login.signup.checkbox.showpassword  = Ø¥Ø¸ÙØ§Ø±
login.signup.password.strength.veryweak  = Ø¶Ø¹ÙÙØ© Ø¬Ø¯ÙØ§
login.signup.password.strength.data.text  = Ø¶Ø¹ÙÙØ© Ø¬Ø¯ÙØ§, Ø¶Ø¹ÙÙØ©, ÙØªÙØ³Ø·Ø©, Ø¬ÙØ¯Ø©, ÙÙÙØ©
login.signup.newsletters.subscribe  = Ø£Ø±ØºØ¨ ÙÙ ØªÙÙÙ Ø£Ø®Ø¨Ø§Ø± Ø§ÙØ¹Ø±ÙØ¶ Ø§ÙÙØªÙÙØ²Ø© ÙØ§ÙÙØ³ØªØ¬Ø¯Ø§Øª Ø¹Ù Ø·Ø±ÙÙ Ø§ÙØ¨Ø±ÙØ¯ Ø§ÙØ¥ÙÙØªØ±ÙÙÙ
login.signup.action.button  = ØªØ³Ø¬ÙÙ Ø­Ø³Ø§Ø¨
login.signup.terms.agree  = ÙÙØ§ÙÙ Ø¨Ø¥ÙØ´Ø§Ø¡ Ø­Ø³Ø§Ø¨ ÙØ¹ÙÙ ÙÙØ§ÙÙØªÙ Ø¹ÙÙ
login.signup.terms.conditions  = Ø§ÙØ£Ø­ÙØ§Ù ÙØ§ÙØ´Ø±ÙØ· Ø§ÙØ®Ø§ØµØ© Ø¨ÙØ§
login.signup.footer.lefttext  = Ø£Ù Ø¨Ø¥ÙÙØ§ÙÙ &nbsp;
login.signup.footer.righttext  = Ø¥Ø¬Ø±Ø§Ø¡ Ø¹ÙÙÙØ© Ø§ÙØ¯ÙØ¹ ÙØ²Ø§Ø¦Ø± ÙÙÙÙÙØ¹
login.signup.footer.signin.lefttext  = ÙÙ ÙØ¯ÙÙ Ø­Ø³Ø§Ø¨Ø
login.signup.footer.signin.righttext  = ÙØ±Ø¬Ù ØªØ³Ø¬ÙÙ Ø§ÙØ¯Ø®ÙÙ Ø§ÙØ¢Ù
text.footer.originalVersion = Ø§ÙØª ØªØ³ØªØ¹Ø±Ø¶ Ø§ÙÙØ³Ø®Ù Ø§ÙØ§ØµÙÙÙ ÙÙ {0}
text.footer.switchLighter = Ø§ÙÙØ³Ø®Ù Ø§ÙØ£Ø®Ù
login.signup.header.error.message  = Ø¹Ø°Ø±ÙØ§! ÙÙØ§Ù ÙØ´ÙÙØ©. ÙØ±Ø¬Ù ØªØµØ­ÙØ­ Ø§ÙØ£Ø®Ø·Ø§Ø¡ Ø§ÙÙØ¸ÙÙØ©
login.signup.password.new  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù ÙÙÙØ© Ø§ÙÙØ±ÙØ± Ø§ÙØ¬Ø¯ÙØ¯Ø©
login.signin.password.current  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù ÙÙÙØ© Ø§ÙÙØ±ÙØ± Ø§ÙØ­Ø§ÙÙØ©
login.signin.header.title  = ÙØ±Ø¬Ù ØªØ³Ø¬ÙÙ Ø§ÙØ¯Ø®ÙÙ Ø§ÙØ¢Ù
login.signin.header.lmsnote  = ÙØ±Ø¬Ù Ø§Ø³ØªØ®Ø¯Ø§Ù Ø­Ø³Ø§Ø¨Ù ÙÙ "ÙØ§ÙØ¯ÙØ§Ø±Ù Ø´ÙØ¨Ø³" Ø¥Ù ÙØ§Ù ÙØ¯ÙÙ.
login.signin.facebook.button.text  = ØªØ³Ø¬ÙÙ Ø§ÙØ¯Ø®ÙÙ Ø¨ÙØ§Ø³Ø·Ø© Ø­Ø³Ø§Ø¨Ù Ø¹ÙÙ ÙÙØ³Ø¨ÙÙ
login.signin.username  = Ø¨Ø±ÙØ¯Ù Ø§ÙØ§ÙÙØªØ±ÙÙÙ
login.signin.username.required  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø¹ÙÙØ§Ù Ø¨Ø±ÙØ¯Ù Ø§ÙØ¥ÙÙØªØ±ÙÙÙ
login.signin.password  = ÙÙÙØ© Ø§ÙÙØ±ÙØ±
login.signin.password.required  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù ÙÙÙØ© Ø§ÙÙØ±ÙØ±
login.signin.forgotpassword  = ÙØ³ÙØª ÙÙÙØ© Ø§ÙÙØ±ÙØ±Ø
login.signin.keepmeloggedin  = Ø£ÙØ¯ Ø¨ÙØ§Ø¡ ØªØ³Ø¬ÙÙ Ø¯Ø®ÙÙÙ
login.signin.button.text  = ØªØ³Ø¬ÙÙ Ø§ÙØ¯Ø®ÙÙ
login.signin.footer.signup.lefttext  = ÙÙØ³ ÙØ¯ÙÙ Ø­Ø³Ø§Ø¨Ø
login.signin.footer.signup.righttext  = ØªÙØ¶Ù Ø¨Ø§ÙØªØ³Ø¬ÙÙ
login.signin.incorrectdetails  = Ø§Ø³Ù Ø§ÙÙØ³ØªØ®Ø¯Ù Ø£Ù ÙÙÙØ© Ø§ÙÙØ±ÙØ± ØºÙØ± ØµØ­ÙØ­Ø©.
login.facebook.rejectaccess  = ÙØ­ØªØ§Ø¬ Ø¹ÙÙØ§Ù Ø¨Ø±ÙØ¯Ù Ø§ÙØ¥ÙÙØªØ±ÙÙÙ ÙÙ Ø£Ø¬Ù Ø§ÙÙØªØ§Ø¨Ø¹Ø©. ÙØ±Ø¬Ù Ø§ÙÙÙØ± Ø¹ÙÙ Ø²Ø± "ØªØ³Ø¬ÙÙ Ø§ÙØ¯Ø®ÙÙ Ø¨ÙØ§Ø³Ø·Ø© ÙÙØ³Ø¨ÙÙ"Ø Ø«Ù ØªÙØ¯ÙÙ Ø¹ÙÙØ§Ù Ø§ÙØ¨Ø±ÙØ¯ Ø§ÙØ¥ÙÙØªØ±ÙÙÙ Ø§ÙØ®Ø§Øµ Ø¨Ù ÙØ¥Ø¹Ø§Ø¯Ø© Ø§ÙÙØ­Ø§ÙÙØ©.
login.facebook.emailnotfound  = Ø­Ø³Ø§Ø¨Ù Ø¹ÙÙ ÙÙØ³Ø¨ÙÙ ÙØ§ ÙØ­ÙÙ Ø¹ÙÙØ§Ù Ø¨Ø±ÙØ¯ Ø¥ÙÙØªØ±ÙÙÙ. ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø¹ÙÙØ§Ù Ø¨Ø±ÙØ¯Ù Ø§ÙØ¥ÙÙØªØ±ÙÙÙ ÙØªØ³Ø¬ÙÙ Ø§ÙØ¯Ø®ÙÙ.
login.signup.userexist  = Ø§ÙØ¨Ø±ÙØ¯ Ø§ÙØ¥ÙÙØªØ±ÙÙÙ Ø§ÙØ°Ù ØªÙ Ø¥Ø¯Ø®Ø§ÙÙ ÙÙØ¬ÙØ¯ ÙØ³Ø¨ÙÙØ§
menu.button.home  = Ø§ÙØµÙØ­Ø© Ø§ÙØ±Ø¦ÙØ³ÙØ©
mobile.basket.page.update  = ØªØ­Ø¯ÙØ« Ø§ÙÙÙÙØ©
mobile.search.add.refinements  = ØªØµÙÙÙ
mobile.search.nav.clearSelections  = ÙØ³Ø­ Ø¬ÙÙØ¹ Ø§ÙØ§Ø®ØªÙØ§Ø±Ø§Øª
mobile.search.nav.facetValueCount  =  {0}
mobile.storelocator.title  = ÙØ±ÙØ¹ÙØ§
order.free  = ÙØ¬Ø§ÙÙØ§
order.itemPrice  = Ø³Ø¹Ø± Ø§ÙÙÙØªØ¬
order.order.totals  = Ø§ÙÙØ¬ÙÙØ¹ Ø§ÙØ®Ø§Øµ Ø¨Ø§ÙØ·ÙØ¨
order.orderItems  = Ø§ÙÙÙØªØ¬Ø§Øª Ø§ÙØªÙ ÙØªØ¶ÙÙÙØ§ Ø§ÙØ·ÙØ¨
order.product  = Ø§ÙÙÙØªØ¬
order.productDetails  = ØªÙØ§ØµÙÙ Ø§ÙÙÙØªØ¬
order.quantity  = Ø§ÙÙÙÙØ©
order.total  = Ø§ÙÙØ¬ÙÙØ¹
order.totals.delivery  = Ø§ÙØªÙØµÙÙ:
order.totals.savings  = Ø§ÙØªÙÙÙØ±:
order.totals.subtotal  = Ø§ÙÙØ¬ÙÙØ¹ Ø§ÙÙØ±Ø¹Ù:
order.totals.total  = Ø§ÙÙØ¬ÙÙØ¹:
password.strength.medium  = ÙØªÙØ³Ø·Ø©
password.strength.minchartext  = %d Ø­Ø±ÙÙØ§ Ø¹ÙÙ Ø§ÙØ£ÙÙ
password.strength.strong  = ÙÙÙØ©
password.strength.tooshortpwd  = ÙØµÙØ±Ø© Ø¬Ø¯ÙØ§
password.strength.verystrong  = ÙÙÙØ© Ø¬Ø¯ÙØ§
password.strength.veryweak  = Ø¶Ø¹ÙÙØ© Ø¬Ø¯ÙØ§
password.strength.weak  = Ø¶Ø¹ÙÙØ©
pickup.back.to.product.page  = &lt; Ø§ÙØ¹ÙØ¯Ø© Ø¥ÙÙ ØµÙØ­Ø© Ø§ÙÙÙØªØ¬
pickup.buy.online.message  = ÙÙÙÙÙ Ø´Ø±Ø§Ø¡ Ø§ÙÙÙØªØ¬Ø§Øª Ø§ÙØ¢Ù ÙØ§Ø³ØªÙØ§ÙÙØ§ ÙÙ ÙÙØªÙ ÙØ§Ø­Ù ÙÙ Ø§ÙÙØªØ¬Ø±.
pickup.force.in.stock  = ÙØªÙÙØ± ÙÙ Ø§ÙÙØ®Ø²ÙÙ
pickup.here.button  = Ø§Ø³ØªÙÙÙ ÙÙ ÙÙØ§
pickup.in.stock  = {0} ÙØ·Ø¹Ø© ÙÙ Ø§ÙÙØ®Ø²ÙÙ
pickup.in.store  = Ø§ÙØ§Ø³ØªÙØ§Ù ÙÙ Ø§ÙÙØªØ¬Ø±
pickup.in.store.back.to.results  = Ø§ÙØ±Ø¬ÙØ¹
pickup.location.required  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù ÙÙÙØ¹Ù ÙÙØ§Ø·ÙØ§Ø¹ Ø¹ÙÙ Ø§ÙÙÙØªØ¬Ø§Øª Ø§ÙÙØªÙÙØ±Ø© ÙÙ ÙÙØ·ÙØªÙ.
pickup.mobile.back.to.cart.page  = Ø§ÙØ¹ÙØ¯Ø© Ø¥ÙÙ Ø³ÙØ© Ø§ÙØªØ³ÙÙ
pickup.mobile.back.to.product.page  = Ø§ÙØ¹ÙØ¯Ø© Ø¥ÙÙ ØµÙØ­Ø© Ø§ÙÙÙØªØ¬
pickup.out.of.stock  = ÙÙØ° ÙÙ Ø§ÙÙØ®Ø²ÙÙ
pickup.pagination.first  = Ø§ÙØ£ÙÙ
pickup.pagination.last  = Ø§ÙØ£Ø®ÙØ±
pickup.pagination.next  = Ø§ÙØªØ§ÙÙ
pickup.pagination.from  = ÙÙ &nbsp;
pickup.pagination.stores  = ØªÙØ¬Ø¯ &nbsp; ÙØªØ§Ø¬Ø±
pickup.pagination.page.details  = {0} Ø¥ÙÙ {1} ÙÙ {2}
pickup.pagination.previous  = Ø§ÙØ³Ø§Ø¨Ù
pickup.product.availability  = ØªÙØ§ÙØ± Ø§ÙÙÙØªØ¬ ÙÙÙÙØ§ ÙÙÙÙØ¹ Ø§ÙÙØªØ¬Ø±
pickup.product.by.store.location  = Ø§ÙÙÙØªØ¬Ø§Øª ÙÙÙÙØ§ ÙÙÙÙØ¹ Ø§ÙÙØªØ¬Ø±
pickup.results.button  = Ø§ÙØ§Ø³ØªÙØ§Ù
pickup.search.button  = Ø§ÙØ¨Ø­Ø« Ø¹Ù ÙØªØ§Ø¬Ø±
pickup.search.message  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø§Ø³Ù Ø§ÙÙØ¯ÙÙØ© Ø£Ù Ø§ÙØ¨ÙØ¯Ø© Ø£Ù Ø§ÙØ±ÙØ² Ø§ÙØ¨Ø±ÙØ¯Ù:
popup.cart.empty  = Ø³ÙØ© ÙØ§Ø±ØºØ©
popup.cart.pickup  = Ø§ÙØ§Ø³ØªÙØ§Ù
popup.cart.quantity  = Ø§ÙÙÙÙØ©
popup.cart.quantity.added  = Ø§ÙÙÙÙØ© Ø§ÙÙØ¶Ø§ÙØ©
popup.cart.showing  = Ø¹Ø±Ø¶ {0} ÙÙ {1} ÙØ·Ø¹
popup.cart.title  = Ø³ÙØ© Ø§ÙØªØ³ÙÙ Ø§ÙØ®Ø§ØµØ© Ø¨Ù
popup.cart.total  = Ø§ÙÙØ¬ÙÙØ¹
popup.close  = Ø¥ØºÙØ§Ù
popup.quick.view.select  = Ø­Ø¯Ø¯ Ø§ÙØ®ÙØ§Ø±Ø§Øª
popup.cart.showall  = Ø¥Ø¸ÙØ§Ø± Ø§ÙÙÙ
product.average.review.rating  = ÙØ¹Ø¯Ù Ø§ÙØªÙÙÙÙ ÙÙÙÙØªØ¬Ø§Øª {0} / 5
product.bookmark.and.share  = Ø­ÙØ¸ Ø§ÙØ±Ø§Ø¨Ø· ÙÙØ´Ø§Ø±ÙØªÙ
product.close  = Ø¥ØºÙØ§Ù
product.image.zoom.in  = Ø§ÙÙØ± ÙØªÙØ¨ÙØ± Ø§ÙØµÙØ±Ø©
product.image.zoom.out  = ØªØµØºÙØ±
product.overview  = Ø§ÙØªÙØ§ØµÙÙ
product.price.from  = ÙÙ {0}
product.product.details  = ØªÙØ§ØµÙÙ Ø§ÙÙÙØªØ¬
product.product.details.more  = Ø§ÙÙØ²ÙØ¯ ÙÙ ØªÙØ§ØµÙÙ Ø§ÙÙÙØªØ¬
product.product.spec  = Ø§ÙÙÙØ§ØµÙØ§Øª
product.related.products  = ÙÙØªØ¬Ø§Øª Ø°Ø§Øª ØµÙØ©
product.share.email  = Ø§ÙØ¨Ø±ÙØ¯ Ø§ÙØ¥ÙÙØªØ±ÙÙÙ
product.share.print  = Ø§ÙÙÙØ´ Ø§ÙÙØ·Ø¨ÙØ¹
product.share.sendToFacebook  = Ø¥Ø±Ø³Ø§Ù Ø¥ÙÙ ÙÙØ³Ø¨ÙÙ
product.share.share  = ÙØ´Ø§Ø±ÙØ©
product.share.tweet  = Ø¥Ø±Ø³Ø§Ù Ø¥ÙÙ ØªÙÙØªØ±
product.share.viewMoreServices  = Ø¥Ø¸ÙØ§Ø± Ø§ÙÙØ²ÙØ¯ ÙÙ Ø§ÙØ®Ø¯ÙØ§Øª
product.variants.available  = ÙØªÙÙØ± Ø¹ÙÙ Ø§ÙØ¥ÙØªØ±ÙØª
product.variants.colour  = Ø§ÙÙÙÙ
product.variants.in.stock  = ÙØªÙÙØ± ÙÙ Ø§ÙÙØ®Ø²ÙÙ Ø£ÙÙÙØ§ÙÙ
product.variants.only.left  = Ø¨ÙÙ {0} ÙØ·Ø¹ ÙÙØ· Ø¹Ø¨Ø± Ø§ÙÙØªØ¬Ø± Ø§ÙØ¥ÙÙØªØ±ÙÙÙ
product.variants.out.of.stock  = ØºÙØ± ÙØªÙÙØ± ÙÙ Ø§ÙÙØ®Ø²ÙÙ Ø£ÙÙÙØ§ÙÙ
product.variants.select.size  = Ø§Ø®ØªÙØ§Ø± Ø§ÙÙÙØ§Ø³
product.variants.select.style  = ÙØ±Ø¬Ù ØªØ­Ø¯ÙØ¯ Ø§ÙØªØµÙÙÙ Ø£ÙÙØ§Ù
product.variants.select.variant  = ÙØ±Ø¬Ù Ø§Ø®ØªÙØ§Ø± Ø§ÙØ¨Ø¯ÙÙ
product.variants.size  = Ø§ÙÙÙØ§Ø³
product.variants.size.guide  = Ø¯ÙÙÙ Ø§ÙÙÙØ§Ø³Ø§Øª
product.variants.type  = Ø§ÙÙÙØ¹
product.variants.update  = ØªØ­Ø¯ÙØ«
product.volumePrices.column.price  = Ø³Ø¹Ø± ÙÙ ÙØ·Ø¹Ø©
product.volumePrices.column.qa  = Ø§ÙÙÙÙØ©
product.facebook.share.success  = ØªÙØª Ø§ÙÙØ´Ø§Ø±ÙØ© Ø¨ÙØ¬Ø§Ø­
profile.checkEmail  = Ø£Ø¹Ø¯ Ø¥Ø¯Ø®Ø§Ù Ø§ÙØ¨Ø±ÙØ¯ Ø§ÙØ¥ÙÙØªØ±ÙÙÙ
profile.checkEmail.invalid  = ÙØ±Ø¬Ù ØªØ£ÙÙØ¯ Ø¹ÙÙØ§Ù Ø§ÙØ¨Ø±ÙØ¯ Ø§ÙØ¥ÙÙØªØ±ÙÙÙ
profile.checkNewPassword  = ØªØ£ÙÙØ¯ ÙÙÙØ© Ø§ÙÙØ±ÙØ± Ø§ÙØ¬Ø¯ÙØ¯Ø©
profile.checkNewPassword.invalid  = ÙØ±Ø¬Ù ØªØ£ÙÙØ¯ ÙÙÙØ© Ø§ÙÙØ±ÙØ± Ø§ÙØ¬Ø¯ÙØ¯Ø©
profile.checkPwd  = ØªØ£ÙÙØ¯ ÙÙÙØ© Ø§ÙÙØ±ÙØ±
profile.checkPwd.invalid  = ÙØ±Ø¬Ù ØªØ£ÙÙØ¯ ÙÙÙØ© Ø§ÙÙØ±ÙØ±
profile.currentPassword  = ÙÙÙØ© Ø§ÙÙØ±ÙØ± Ø§ÙØ­Ø§ÙÙØ©
profile.currentPassword.invalid  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù ÙÙÙØ© Ø§ÙÙØ±ÙØ± Ø§ÙØ­Ø§ÙÙØ© Ø§ÙØµØ­ÙØ­Ø©
profile.email  = Ø§ÙØ¨Ø±ÙØ¯ Ø§ÙØ¥ÙÙØªØ±ÙÙÙ
profile.email.invalid  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø¹ÙÙØ§Ù Ø¨Ø±ÙØ¯ Ø¥ÙÙØªØ±ÙÙÙ ØµØ­ÙØ­
profile.email.unique  = Ø§ÙØ¨Ø±ÙØ¯ Ø§ÙØ¥ÙÙØªØ±ÙÙÙ Ø§ÙØ°Ù Ø£Ø¯Ø®ÙØªÙ ØºÙØ± ÙØªØ§Ø­
profile.firstName  = Ø§ÙØ§Ø³Ù Ø§ÙØ§ÙÙ
profile.firstName.invalid  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø§ÙØ§Ø³Ù Ø§ÙØ£ÙÙ
profile.lastName  = Ø§Ø³Ù Ø§ÙØ¹Ø§Ø¦ÙØ©
profile.lastName.invalid  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø§Ø³Ù Ø§ÙØ¹Ø§Ø¦ÙØ©
profile.newPassword  = ÙÙÙØ© Ø§ÙÙØ±ÙØ± Ø§ÙØ¬Ø¯ÙØ¯Ø©
profile.newPassword.invalid  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù ÙÙÙØ© Ø§ÙÙØ±ÙØ± Ø§ÙØ¬Ø¯ÙØ¯Ø©
profile.pwd  = ÙÙÙØ© Ø§ÙÙØ±ÙØ±
profile.pwd.invalid  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù ÙÙÙØ© Ø§ÙÙØ±ÙØ±
profile.submit  = ØªØ­Ø¯ÙØ«
profile.title  = Ø§ÙÙÙØ¨
profile.title.invalid  = ÙØ±Ø¬Ù Ø§Ø®ØªÙØ§Ø± Ø§ÙÙÙØ¨
register.back.login  = Ø§ÙØ¹ÙØ¯Ø© Ø¥ÙÙ ØªØ³Ø¬ÙÙ Ø§ÙØ¯Ø®ÙÙ
register.checkPwd  = ØªØ£ÙÙØ¯ ÙÙÙØ© Ø§ÙÙØ±ÙØ±
register.checkPwd.invalid  = ÙØ±Ø¬Ù ØªØ£ÙÙØ¯ ÙÙÙØ© Ø§ÙÙØ±ÙØ±
register.description  = ÙØªØ³ØªÙÙØ¯ ÙÙ Ø¥ÙÙØ§ÙÙØ© Ø¥Ø¬Ø±Ø§Ø¡ Ø¹ÙÙÙØ© Ø§ÙØ¯ÙØ¹ Ø¨Ø³Ø±Ø¹Ø©Ø ÙÙØ³ÙÙÙØ© Ø§ÙÙØµÙÙ Ø¥ÙÙ Ø·ÙØ¨Ø§ØªÙ Ø§ÙØ³Ø§Ø¨ÙØ©Ø ÙÙØªØªÙÙÙ ÙÙ Ø¥ÙØ´Ø§Ø¡ Ø¯ÙØªØ± ÙØ¹ÙØ§ÙÙÙÙ ÙØ­ÙØ¸ Ø§ÙØ¥Ø¹Ø¯Ø§Ø¯Ø§Øª Ø§ÙØ®Ø§ØµØ© Ø¨ØªÙØ¶ÙÙØ§ØªÙ ÙÙ Ø¨ØªØ³Ø¬ÙÙ Ø­Ø³Ø§Ø¨ ÙØ¯ÙÙØ§ Ø£Ø¯ÙØ§Ù.
recaptcha.required= ÙØ±Ø¬Ù Ø§ÙÙØ§Ù Ø§ÙØªØ­ÙÙ.
register.email  = Ø¹ÙÙØ§Ù Ø§ÙØ¨Ø±ÙØ¯ Ø§ÙØ¥ÙÙØªØ±ÙÙÙ
register.email.invalid  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø¹ÙÙØ§Ù Ø¨Ø±ÙØ¯ Ø¥ÙÙØªØ±ÙÙÙ ØµØ­ÙØ­
register.firstName  = Ø§ÙØ§Ø³Ù Ø§ÙØ§ÙÙ
register.firstName.invalid  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø§ÙØ§Ø³Ù Ø§ÙØ£ÙÙ
register.lastName  = Ø§Ø³Ù Ø§ÙØ¹Ø§Ø¦ÙØ©
register.lastName.invalid  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø§Ø³Ù Ø§ÙØ¹Ø§Ø¦ÙØ©
register.name.invalid  = ÙØ¬Ø¨ Ø£Ù ÙÙÙÙ ÙØ¬ÙÙØ¹ Ø·ÙÙ Ø§ÙØ§Ø³Ù Ø§ÙØ£ÙÙ ÙØ§Ø³Ù Ø§ÙØ¹Ø§Ø¦ÙØ© ÙØ¹ÙØ§ Ø£ÙÙ ÙÙ 255 Ø­Ø±ÙÙØ§
register.new.customer  = Ø¥ÙØ´Ø§Ø¡ Ø­Ø³Ø§Ø¨
register.phone  = Ø±ÙÙ Ø§ÙÙØ§ØªÙ (ÙÙØ¶Ù Ø§ÙÙØ§ØªÙ Ø§ÙÙØªØ­Ø±Ù)
register.pwd  = ÙÙÙØ© Ø§ÙÙØ±ÙØ±
register.pwd.invalid  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù ÙÙÙØ© ÙØ±ÙØ± ÙÙÙØ© (Ø¹ÙÙ Ø§ÙØ£ÙÙ 6 Ø®Ø§ÙØ§Øª)
register.remember  = ØªØ°ÙØ±ÙÙ Ø¹ÙÙ ÙØ°Ø§ Ø§ÙÙÙØ¨ÙÙØªØ±
register.submit  = ØªØ³Ø¬ÙÙ
register.title  = Ø§ÙÙÙØ¨
register.title.invalid  = ÙØ±Ø¬Ù Ø§Ø®ØªÙØ§Ø± Ø§ÙÙÙØ¨
registration.confirmation.message.subtitle  = ÙØ±Ø¬Ù ÙØ±Ø§Ø¬Ø¹Ø© Ø¨Ø±ÙØ¯Ù Ø§ÙØ¥ÙÙØªØ±ÙÙÙ Ø¨ØºØ±Ø¶ Ø¥ØªÙØ§Ù Ø¹ÙÙÙØ© Ø§ÙØªØ­ÙÙ ÙÙ Ø¹ÙÙØ§Ù Ø¨Ø±ÙØ¯Ù Ø§ÙØ¥ÙÙØªØ±ÙÙÙ.
registration.confirmation.message.title  = Ø´ÙØ±ÙØ§ ÙÙÙØ§ÙÙ Ø¨Ø§ÙØªØ³Ø¬ÙÙ.
registration.error.account.exists.subtitle  = ÙÙ Ø­Ø§Ù ÙØ³ÙØª ÙÙÙØ© Ø§ÙÙØ±ÙØ±Ø ÙØ±Ø¬Ù Ø§Ø³ØªØ®Ø¯Ø§Ù Ø±Ø§Ø¨Ø· "ÙØ³ÙØª ÙÙÙØ© Ø§ÙÙØ±ÙØ±".
registration.error.account.exists.title  = ÙÙØ¬Ø¯ Ø­Ø³Ø§Ø¨ ÙØ³Ø¬Ù ÙØ³Ø¨ÙÙØ§ Ø¨Ø¹ÙÙØ§Ù Ø§ÙØ¨Ø±ÙØ¯ Ø§ÙØ¥ÙÙØªØ±ÙÙÙ ÙØ°Ø§
review.alias  = Ø§Ø³ÙÙ
review.back  = Ø§ÙØ¹ÙØ¯Ø© Ø¥ÙÙ Ø§ÙØªÙÙÙÙØ§Øª
review.based.on  = ({0})
review.based.on.one  = Ø§Ø³ØªÙØ§Ø¯ÙØ§ Ø¥ÙÙ {0} ØªÙÙÙÙ
review.comment  = ÙØµÙ Ø§ÙØªÙÙÙÙ
review.comment.invalid  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù ÙØµÙ
review.confirmation.thank.you.subtitle  = ÙØ­Ø§ÙÙ Ø¯Ø§Ø¦ÙÙØ§ ÙØ¶Ø¹ Ø¬ÙÙØ¹ Ø§ÙØªÙÙÙÙØ§Øª Ø¹ÙÙ Ø§ÙÙÙÙØ¹ ÙÙ ØºØ¶ÙÙ 24 Ø³Ø§Ø¹Ø©.
review.confirmation.thank.you.title  = ÙØ´ÙØ±Ù Ø¹ÙÙ ÙÙØ§ÙÙ Ø¨Ø§ÙØªÙÙÙÙ.
review.general.error  = ÙØ±Ø¬Ù ØªØ¹Ø¨Ø¦Ø© Ø¬ÙÙØ¹ Ø§ÙØ­ÙÙÙ Ø§ÙØ¥ÙØ²Ø§ÙÙØ© Ø§ÙØ®Ø§ØµØ© Ø¨Ø§ÙØªÙÙÙÙ
review.headline  = Ø¹ÙÙØ§Ù Ø§ÙØªÙÙÙÙ
review.headline.invalid  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø¹ÙÙØ§Ù
review.no.reviews  = ÙÙ Ø£ÙÙ ÙÙ ÙÙÙÙ Ø¨Ø§ÙØªÙÙÙÙ.
review.number.of  = ÙÙ
review.number.reviews  = ØªÙÙÙÙØ§Øª
review.rating  = ØªÙÙÙÙÙ
review.rating.alt  = ÙØ¬ÙÙ
review.rating.invalid  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù ØªÙÙÙÙ
review.required  = ÙØ¬Ø¨ ÙÙØ¡ Ø§ÙØ­ÙÙÙ Ø§ÙØªÙ ØªØ­ÙÙ Ø¹ÙØ§ÙØ© "*"
review.reviews  = ØªÙÙÙÙØ§Øª
review.see.reviews  = Ø¹Ø±Ø¶ Ø§ÙØªÙÙÙÙØ§Øª
review.show.all  = Ø¥Ø¸ÙØ§Ø± Ø§ÙÙÙ
review.show.less  = Ø¥Ø¸ÙØ§Ø± Ø¹Ø¯Ø¯ Ø£ÙÙ
review.show.more  = Ø¥Ø¸ÙØ§Ø± Ø§ÙÙØ²ÙØ¯
review.submit  = Ø¥Ø±Ø³Ø§Ù Ø§ÙØªÙÙÙÙ
review.submitted.anonymous  = ÙØ¬ÙÙÙ
review.submitted.by  = Ø£Ø±Ø³ÙÙ
review.write.description  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù ØªÙÙÙÙÙ
review.write.review  = ÙØ±Ø¬Ù ÙØªØ§Ø¨Ø© ØªÙÙÙÙ
review.write.title  = ÙØ±Ø¬Ù ÙØªØ§Ø¨Ø© ØªÙÙÙÙ
review.write.title.product  = ÙØ±Ø¬Ù ÙØªØ§Ø¨Ø© ØªÙÙÙÙ ÙÙÙÙØªØ¬ {0}
search.back.to.product.list  = Ø§ÙØ¹ÙØ¯Ø© Ø¥ÙÙ ÙØ§Ø¦ÙØ© Ø§ÙÙÙØªØ¬Ø§Øª
search.meta.description.on  = Ø¹ÙÙ
search.meta.description.results  = ÙØªØ§Ø¦Ø¬ Ø§ÙØ¨Ø­Ø« Ø¹Ù
search.meta.title  = Ø¨Ø­Ø«
search.mobile.no.results  = ÙØ§ ØªÙØ¬Ø¯ ÙØªØ§Ø¦Ø¬
search.mobile.page.currentPage  = {0} - {1} ÙÙ {2} ÙÙØªØ¬
search.mobile.page.linkNextPage  = Ø§ÙØªØ§ÙÙ &raquo;
search.mobile.page.linkPreviousPage  = &laquo; Ø§ÙØ³Ø§Ø¨Ù
search.mobile.page.searchText  = ÙÙØ¯ Ø¨Ø­Ø«Øª Ø¹Ù "{0}"
search.mobile.page.showAllResults  = Ø¥Ø¸ÙØ§Ø± Ø§ÙÙÙ
search.mobile.page.sortTitle  = Ø§ÙØªØ±ØªÙØ¨ Ø¨Ø­Ø³Ø¨:
search.mobile.page.totalResults  = {0} ÙØ·Ø¹Ø© (ÙØ·Ø¹)
search.nav.appliedFilters  = Ø­Ø°Ù
search.nav.categoryNav  = Ø§ÙØªØ³ÙÙ Ø¨Ø­Ø³Ø¨ Ø§ÙÙØ¦Ø©
search.nav.changeLocation  = ØªØºÙÙØ± Ø§ÙÙÙÙØ¹
search.nav.done.button  = ØªÙ Ø§ÙØªÙÙÙØ°
search.nav.facetShowLess  = Ø£ÙÙ...
search.nav.facetShowLess_availableInStores  = ÙØªØ§Ø¬Ø± Ø£ÙÙ...
search.nav.facetShowLess_brand  = Ø¹ÙØ§ÙØ§Øª ØªØ¬Ø§Ø±ÙØ© Ø£ÙÙ...
search.nav.facetShowLess_category  = ÙØ¦Ø§Øª Ø£ÙÙ...
search.nav.facetShowLess_collection  = ØªØ´ÙÙÙØ§Øª Ø£ÙÙ...
search.nav.facetShowLess_colour  = Ø£ÙÙØ§Ù Ø£ÙÙ...
search.nav.facetShowLess_price  = Ø£Ø³Ø¹Ø§Ø± Ø£ÙÙ...
search.nav.facetShowLess_size  = ÙÙØ§Ø³Ø§Øª Ø£ÙÙ...
search.nav.facetShowLess_style  = ØªØµØ§ÙÙÙ Ø£ÙÙ...
search.nav.facetShowMore  = Ø§ÙÙØ²ÙØ¯...
search.nav.facetShowMore_availableInStores  = Ø§ÙÙØ²ÙØ¯ ÙÙ Ø§ÙÙØªØ§Ø¬Ø±...
search.nav.facetShowMore_brand  = Ø§ÙÙØ²ÙØ¯ ÙÙ Ø§ÙØ¹ÙØ§ÙØ§Øª Ø§ÙØªØ¬Ø§Ø±ÙØ©...
search.nav.facetShowMore_category  = Ø§ÙÙØ²ÙØ¯ ÙÙ Ø§ÙÙØ¦Ø§Øª...
search.nav.facetShowMore_collection  = Ø§ÙÙØ²ÙØ¯ ÙÙ Ø§ÙØªØ´ÙÙÙØ§Øª...
search.nav.facetShowMore_colour  = Ø§ÙÙØ²ÙØ¯ ÙÙ Ø§ÙØ£ÙÙØ§Ù...
search.nav.facetShowMore_price  = Ø§ÙÙØ²ÙØ¯ ÙÙ Ø§ÙØ£Ø³Ø¹Ø§Ø±...
search.nav.facetShowMore_size  = Ø§ÙÙØ²ÙØ¯ ÙÙ Ø§ÙÙÙØ§Ø³Ø§Øª...
search.nav.facetShowMore_stores  = Ø¥Ø¸ÙØ§Ø± Ø§ÙÙØ²ÙØ¯ ÙÙ Ø§ÙÙØªØ§Ø¬Ø±
search.nav.facetShowMore_style  = Ø§ÙÙØ²ÙØ¯ ÙÙ Ø§ÙØªØµØ§ÙÙÙ...
search.nav.facetTitle  = Ø§ÙØªØ³ÙÙ Ø¨Ø­Ø³Ø¨ {0}
search.nav.facetValueCount  = ({0})
search.nav.refinements  = ØªØµÙÙÙØ§Øª
search.nav.removeAttribute  = Ø­Ø°Ù Ø§ÙØ®Ø§ØµÙØ©
search.nav.resultsForStore  = ÙØªØ§Ø¦Ø¬ Ø§ÙØ¨Ø­Ø«  Ø¹Ù: {0}
search.no.results  = 0 ÙØ·Ø¹ ØªÙ Ø§ÙØ¹Ø«ÙØ± Ø¹ÙÙÙØ§ Ø¨Ø§Ø³ØªØ®Ø¯Ø§Ù ÙÙÙØ© Ø§ÙØ¨Ø­Ø«  <strong> {0} </strong>
search.page.currentPage  = ØµÙØ­Ø© {0} ÙÙ {1}
search.page.firstPage  = &laquo;
search.page.lastPage  = &raquo;
search.page.linkNextPage  = Ø§ÙØµÙØ­Ø© Ø§ÙØªØ§ÙÙØ©
search.page.linkPreviousPage  = Ø§ÙØµÙØ­Ø© Ø§ÙØ³Ø§Ø¨ÙØ©
search.page.nearbyStores  = ÙÙØ¯ Ø¨Ø­Ø«Øª Ø¹Ù Ø§ÙÙØªØ§Ø¬Ø± Ø§ÙÙØ±ÙØ¨Ø©
search.page.searchText  = ÙÙØ¯ Ø¨Ø­Ø«Øª Ø¹Ù "{0}"
search.page.showAllResults  = Ø¥Ø¸ÙØ§Ø± Ø§ÙÙÙ
search.page.showPageResults  = Ø§ÙØ¥Ø¸ÙØ§Ø± Ø¹ÙÙ ØµÙØ­Ø§Øª
search.page.sortTitle  = Ø§ÙØªØ±ØªÙØ¨ Ø¨Ø­Ø³Ø¨:
search.page.totalResults  = ØªÙ Ø§ÙØ¹Ø«ÙØ± Ø¹ÙÙ {0} ÙÙØªØ¬Ø§Øª
search.placeholder  = Ø£ÙØ§ Ø£Ø¨Ø­Ø« Ø¹Ù
search.spellingSuggestion.prompt  = ÙÙ ØªØ¹ÙÙ:
search.seeAll  = Ø§ÙØ§Ø·ÙØ§Ø¹ Ø¹ÙÙ Ø§ÙÙÙ
search.recentSearches  = Ø¹ÙÙÙØ§Øª Ø§ÙØ¨Ø­Ø« Ø§ÙØ£Ø®ÙØ±Ø©
search.in  = ÙÙ
search.productMatching  = ÙÙØªØ¬ ÙØ·Ø§Ø¨Ù
search.productsMatching  = ÙÙØªØ¬Ø§Øª ÙØ·Ø§Ø¨ÙØ©
search.noProductsMatching  = ÙØ§ ØªÙØ¬Ø¯ ÙÙØªØ¬Ø§Øª ÙØ·Ø§Ø¨ÙØ©
storeDetails.map.link  = Ø®Ø±ÙØ·Ø©
storeDetails.table.address  = Ø¹ÙÙØ§Ù
storeDetails.table.distance  = Ø§ÙÙØ³Ø§ÙØ©
storeDetails.table.distanceFromCurrentLocation  = {0} ÙÙ Ø§ÙÙÙÙØ¹ Ø§ÙØ­Ø§ÙÙ
storeDetails.table.distanceFromSource  = {0} ÙÙ {1}
storeDetails.table.email  = Ø§ÙØ¨Ø±ÙØ¯ Ø§ÙØ¥ÙÙØªØ±ÙÙÙ
storeDetails.table.features  = Ø§ÙÙØ²Ø§ÙØ§
storeDetails.table.from  = ÙÙ
storeDetails.table.opening  = Ø³Ø§Ø¹Ø§Øª
storeDetails.table.opening.closed  = ÙØºÙÙ
storeDetails.table.opening.opened  = ÙÙØªÙØ­
storeDetails.table.openingSpecialDays  = Ø£ÙÙØ§Øª Ø§ÙØ¹ÙÙ Ø§ÙØ®Ø§ØµØ©
storeDetails.table.telephone  = ÙØ§ØªÙ
storeDetails.title  = ØªÙØ§ØµÙÙ Ø§ÙÙØªØ¬Ø±
storeFinder.currentPosition  = Ø§ÙÙÙÙØ¹ Ø§ÙØ­Ø§ÙÙ
storeFinder.find.a.store  = Ø§ÙØ¨Ø­Ø« Ø¹Ù ÙØªØ¬Ø±
storeFinder.findStoresNearMe  = Ø§ÙØ¨Ø­Ø« Ø¹Ù ÙØªØ§Ø¬Ø± ÙØ±ÙØ¨Ø©Ù ÙÙÙ
storeFinder.line.text  = Ø£Ù
storeFinder.link  = ØªØ­Ø¯ÙØ¯ ÙÙØ§ÙØ¹ Ø§ÙÙØªØ§Ø¬Ø±
storeFinder.meta.description.results  = ÙØªØ§Ø¬Ø± ÙØ±ÙØ¨Ø©Ù ÙÙ
storeFinder.meta.title  = ÙØªØ§Ø¬Ø± ÙØ±ÙØ¨Ø©Ù ÙÙ
storeFinder.navigateTo  = Ø§ÙØ§ÙØªÙØ§Ù Ø¥ÙÙ
storeFinder.nearby.stores  = Ø§ÙÙØªØ§Ø¬Ø± Ø§ÙÙØ±ÙØ¨Ø©
storeFinder.orSearchBy  = Ø£Ù Ø§ÙØ¨Ø­Ø« Ø¨Ø­Ø³Ø¨:
storeFinder.pagination.from  = ÙÙ &nbsp;
storeFinder.pagination.next  = Ø§ÙØªØ§ÙÙ
storeFinder.pagination.previous  = Ø§ÙØ³Ø§Ø¨Ù
storeFinder.pagination.stores  = ØªÙ Ø§ÙØ¹Ø«ÙØ± Ø¹ÙÙ &nbsp; ÙØªØ§Ø¬Ø±
storeFinder.postcode.town  = Ø§ÙØ±ÙØ² Ø§ÙØ¨Ø±ÙØ¯Ù / Ø§ÙÙØ¯ÙÙØ©
storeFinder.search  = Ø¨Ø­Ø«
storeFinder.see.more  = ÙØ´Ø§ÙØ¯Ø© Ø§ÙÙØ²ÙØ¯...
storeFinder.store.locator  = ÙØ±ÙØ¹ÙØ§
storeFinder.stores.nearby  = Ø§ÙÙØªØ§Ø¬Ø± Ø§ÙÙØ±ÙØ¨Ø©
storeFinder.stores.nearto  = ÙØªØ§Ø¬Ø± ÙØ±ÙØ¨Ø© ÙÙ: {0}
storeFinder.table.address  = Ø§ÙØ¹ÙÙØ§Ù
storeFinder.table.distance  = Ø§ÙÙØ³Ø§ÙØ©
storeFinder.table.opening  = Ø³Ø§Ø¹Ø§Øª Ø§ÙØ¹ÙÙ
storeFinder.table.store  = ÙØªØ¬Ø±
storeFinder.table.view.map  = Ø¹Ø±Ø¶ Ø§ÙØ®Ø±ÙØ·Ø©
storeFinder.table.view.store  = Ø¹Ø±Ø¶ Ø§ÙÙØªØ¬Ø±
storeFinder.use.this.form  = Ø§Ø³ØªØ®Ø¯Ù ÙØ°Ø§ Ø§ÙÙÙÙØ°Ø¬ ÙÙØ¨Ø­Ø« Ø¹Ù ÙØªØ¬Ø±
storeFinder.viewMap  = Ø¹Ø±Ø¶ Ø§ÙØ®Ø±ÙØ·Ø©
storefinder.searchterm.invalid  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù ÙÙÙØ© Ø§ÙØ¨Ø­Ø« Ø£Ù Ø§ÙØ±ÙØ² Ø§ÙØ¨Ø±ÙØ¯Ù
storelocator.error.no.results.subtitle  = ØªØ£ÙØ¯ ÙÙ ØµØ­Ø© Ø§ÙØ±ÙØ² Ø§ÙØ¨Ø±ÙØ¯Ù Ø£Ù Ø§Ø³Ù Ø§ÙÙÙØ§Ù Ø§ÙØ°Ù ØªÙÙÙ Ø¨Ø¥Ø¯Ø®Ø§ÙÙ.
storelocator.error.no.results.title  = ÙÙ ÙØªÙ Ø§ÙØ¹Ø«ÙØ± Ø¹ÙÙ ÙØªØ§Ø¬Ø± Ø¨Ø§Ø³ØªØ®Ø¯Ø§Ù ÙØ¹Ø§ÙÙØ± Ø§ÙØ¨Ø­Ø« Ø§ÙØªÙ Ø§Ø®ØªØ±ØªÙØ§.
storelocator.postcode.city.search  = Ø§ÙØ¨Ø­Ø« Ø¨Ø§Ø³ØªØ®Ø¯Ø§Ù Ø§ÙØ±ÙØ² Ø§ÙØ¨Ø±ÙØ¯Ù / Ø§ÙÙØ¯ÙÙØ©
storelocator.query  = Ø§ÙØ±ÙØ² Ø§ÙØ¨Ø±ÙØ¯Ù / Ø§ÙÙØ¯ÙÙØ©
storelocator.search.results.go  = ØªÙÙÙØ°
storelocator.search.totalResults  = Ø¹Ø±Ø¶ {0} Ø£ÙØ±Ø¨ ÙØªØ§Ø¬Ø±
system.error.link.expired.subtitle  = ÙØ±Ø¬Ù Ø§Ø³ØªÙÙØ§Ù ÙÙÙØ°Ø¬ ÙØ³ÙØ§Ù ÙÙÙØ© Ø§ÙÙØ±ÙØ± ÙØ±Ø© Ø£Ø®Ø±Ù.
system.error.link.expired.title  = Ø¹Ø°Ø±ÙØ§Ø Ø§ÙØªÙØª ÙØ¯Ø© ØµÙØ§Ø­ÙØ© ÙØ°Ø§ Ø§ÙØ±Ø§Ø¨Ø·
system.error.page.not.found  = 404 Ø§ÙØµÙØ­Ø© ØºÙØ± ÙÙØ¬ÙØ¯Ø©
text.account.account  = Ø­Ø³Ø§Ø¨
text.account.addressBook  = Ø¯ÙØªØ± Ø¹ÙØ§ÙÙÙÙ
text.account.addressBook.addAddress  = Ø¥Ø¶Ø§ÙØ© Ø¹ÙÙØ§Ù Ø¬Ø¯ÙØ¯
text.account.addressBook.addEditAddress  = Ø¥Ø¶Ø§ÙØ© / ØªØ¹Ø¯ÙÙ Ø¹ÙÙØ§Ù
text.account.addressBook.addEditform  = ÙØ±Ø¬Ù Ø§Ø³ØªØ®Ø¯Ø§Ù ÙØ°Ø§ Ø§ÙÙÙÙØ°Ø¬ ÙØ¥Ø¶Ø§ÙØ© / ØªØ¹Ø¯ÙÙ Ø¹ÙÙØ§Ù
text.account.addressBook.addressDetails  = ØªÙØ§ØµÙÙ Ø§ÙØ¹ÙÙØ§Ù
text.account.addressBook.confirmationUpdated  = ØªÙ ØªØ­Ø¯ÙØ« Ø¹ÙÙØ§ÙÙ
text.account.addressBook.manageDeliveryAddresses  = ØªØ­Ø¯ÙØ¯ Ø¹ÙÙØ§Ù Ø§ÙØªØ³ÙÙÙ
text.account.addressBook.manageYourAddresses  = Ø¥Ø¯Ø§Ø±Ø© Ø¯ÙØªØ± Ø§ÙØ¹ÙØ§ÙÙÙ Ø§ÙØ®Ø§Øµ Ø¨Ù
text.account.addressBook.noSavedAddresses  = ÙØ§ ØªÙØ¬Ø¯ Ø¹ÙØ§ÙÙÙ ÙØ­ÙÙØ¸Ø©
text.account.addressBook.saveAddress  = Ø­ÙØ¸ Ø§ÙØ¹ÙÙØ§Ù
text.account.addressBook.setDefaultDeliveryAddress  = ØªØ¹ÙÙÙ Ø¹ÙÙØ§Ù Ø§ÙØªØ³ÙÙÙ Ø§ÙØ£Ø³Ø§Ø³Ù
text.account.addressBook.yourDefaultAddress  = ÙØ°Ø§ ÙÙ Ø¹ÙÙØ§ÙÙ Ø§ÙØ£Ø³Ø§Ø³Ù
text.account.change.email.address  = ØªØºÙÙØ± Ø§ÙØ¨Ø±ÙØ¯ Ø§ÙØ¥ÙÙØªØ±ÙÙÙ
text.account.confirmation.password.updated  = ØªÙ ØªØºÙÙØ± ÙÙÙØ© Ø§ÙÙØ±ÙØ± Ø§ÙØ®Ø§ØµØ© Ø¨Ù
text.account.order.consignment.status.cancelled  = ØªÙ Ø§ÙØ¥ÙØºØ§Ø¡
text.account.order.consignment.status.pickedup  = ØªÙ Ø§Ø³ØªÙØ§ÙÙ
text.account.order.consignment.status.readyforpickup  = ÙÙØ¨ØºÙ Ø§ÙØ§Ø³ØªÙØ§Ù ÙØ¨Ù
text.account.order.consignment.status.shipped  = ØªÙ Ø§ÙØ´Ø­Ù
text.account.order.consignment.status.delivered = ØªÙ Ø§ÙØªÙØµÙÙ
text.account.order.consignment.trackingID.notavailable  = ØºÙØ± ÙØªÙÙØ±.
text.account.order.delivery  = Ø§ÙØªÙØµÙÙ:
text.account.order.includesTax  = ÙØ´ÙÙ Ø·ÙØ¨Ù Ø¶Ø±ÙØ¨Ø© Ø¨ÙÙÙØ© {0}
text.account.order.netTax  = Ø§ÙØ¶Ø±ÙØ¨Ø©:
text.account.order.orderBreadcrumb  = Ø§ÙØ·ÙØ¨ {0}
text.account.order.orderNumber  = Ø±ÙÙ Ø§ÙØ·ÙØ¨ ÙÙ {0}
text.account.order.orderNumberShort  = Ø±ÙÙ Ø§ÙØ·ÙØ¨: {0}
text.account.order.orderPlaced  = ÙØ³Ø¬ÙÙ Ø¨ØªØ§Ø±ÙØ® {0}
text.account.order.orderStatus  = Ø§ÙØ·ÙØ¨ ÙÙ {0}
text.account.order.orderTotals  = Ø§ÙÙØ¬ÙÙØ¹ Ø§ÙØ®Ø§Øµ Ø¨Ø§ÙØ·ÙØ¨
text.account.order.pickup.location  = ÙÙÙØ¹ Ø§ÙØ§Ø³ØªÙØ§Ù:
text.account.order.receivedPromotions  = Ø§ÙØ¹Ø±ÙØ¶ Ø§ÙÙØªÙÙØ§Ø©
text.account.order.savings  = Ø§ÙØªÙÙÙØ±:
text.account.order.status  = Ø§ÙØ­Ø§ÙØ©: {0}
text.account.order.status.display.cancelled  = ØªÙ Ø§ÙØ¥ÙØºØ§Ø¡
text.account.order.status.display.cancelling  = Ø¬Ø§Ø±Ù Ø§ÙØ¥ÙØºØ§Ø¡
text.account.order.status.display.completed  = ØªÙ Ø¥ÙØ¬Ø§Ø²
text.account.order.status.display.created  = ØªÙ Ø§ÙØ¥ÙØ´Ø§Ø¡
text.account.order.status.display.error  = Ø­Ø¯Ø« Ø®Ø·Ø£
text.account.order.status.display.open  = ÙÙØªÙØ­
text.account.order.status.display.processing  = Ø¬Ø§Ø±Ù Ø§ÙØ¥ÙØ´Ø§Ø¡
text.account.order.status.display.submitted = ØªÙ Ø§ÙØªÙØ¯ÙÙ
text.account.order.subtotal  = Ø§ÙÙØ¬ÙÙØ¹ Ø§ÙÙØ±Ø¹Ù:
text.account.order.summary  = ÙÙØ®Øµ Ø§ÙØ·ÙØ¨ Ø§ÙØ®Ø§Øµ Ø¨Ù ÙØ¨ÙÙ Ø£Ø¯ÙØ§Ù:
text.account.order.title.deliveryItems  = Ø§ÙÙÙØªØ¬Ø§Øª Ø§ÙØªÙ Ø³ÙØªÙ ØªÙØµÙÙÙØ§
text.account.order.title.inProgressItems  = ÙØ·Ø¹ Ø§ÙØ·ÙØ¨ Ø§ÙØ¬Ø§Ø±Ù ØªØ¬ÙÙØ²ÙØ§
text.account.order.title.storePickUpItems  = Ø§ÙÙØ·Ø¹ Ø§ÙØªÙ Ø³ÙØªÙ Ø§Ø³ØªÙØ§ÙÙØ§ ÙÙ Ø§ÙÙØªØ¬Ø±
text.account.order.title.details  = ØªÙØ§ØµÙÙ Ø§ÙØ·ÙØ¨
text.account.order.total  = Ø§ÙÙØ¬ÙÙØ¹:
text.account.order.tracking  = Ø±ÙÙ Ø§ÙØªØªØ¨Ø¹:
text.account.order.warning.storePickUpItems  = ØªØ°ÙÙØ± - ÙØ±Ø¬Ù Ø§Ø³ØªÙØ§Ù Ø§ÙÙØ·Ø¹ Ø§ÙØ®Ø§ØµØ© Ø¨Ù ÙÙ Ø£ÙØ±Ø¨ ÙÙØª.
text.account.order.yourOrder  = Ø·ÙØ¨Ù
text.account.orderHistory  = Ø³ÙØ¬ÙÙÙ Ø·ÙØ¨Ø§ØªÙ
text.account.orderHistory.actions  = Ø§ÙØ¥Ø¬Ø±Ø§Ø¡Ø§Øª
text.account.orderHistory.datePlaced  = Ø³ÙØ¬Ù Ø¨ØªØ§Ø±ÙØ®
text.account.orderHistory.mobile.page.currentPage  = {0} ÙÙ {1}
text.account.orderHistory.mobile.page.currentResults  = {0} - {1} ÙÙ {2} Ø·ÙØ¨ (Ø·ÙØ¨Ø§Øª)
text.account.orderHistory.mobile.page.linkNextPage  = Ø§ÙØªØ§ÙÙ &raquo;
text.account.orderHistory.mobile.page.linkPreviousPage  = &laquo; Ø§ÙØ³Ø§Ø¨Ù
text.account.orderHistory.mobile.page.sort.byDate  = Ø§ÙØªØ§Ø±ÙØ®
text.account.orderHistory.mobile.page.sort.byOrderNumber  = Ø±ÙÙ Ø§ÙØ·ÙØ¨
text.account.orderHistory.mobile.page.sortTitle  = Ø§ÙØªØ±ØªÙØ¨ Ø¨Ø­Ø³Ø¨:
text.account.orderHistory.mobile.page.totalResults  = ÙÙØ§Ù {0} Ø·ÙØ¨ (Ø·ÙØ¨Ø§Øª)
text.account.orderHistory.noOrders  = ÙØ§ ØªÙØ¬Ø¯ Ø£Ù Ø·ÙØ¨Ø§Øª
text.account.orderHistory.orderNumber  = Ø±ÙÙ Ø§ÙØ·ÙØ¨
text.account.orderHistory.orderStatus  = Ø­Ø§ÙØ© Ø§ÙØ·ÙØ¨
text.account.orderHistory.status = Ø§ÙØ­Ø§ÙØ©
text.account.orderHistory.page.currentPage  = ØµÙØ­Ø© {0} ÙÙ {1}
text.account.orderHistory.page.firstPage  = &laquo;
text.account.orderHistory.page.lastPage  = &raquo;
text.account.orderHistory.page.linkNextPage  = Ø§ÙØµÙØ­Ø© Ø§ÙØªØ§ÙÙØ©
text.account.orderHistory.page.linkPreviousPage  = Ø§ÙØµÙØ­Ø© Ø§ÙØ³Ø§Ø¨ÙØ©
text.account.orderHistory.page.showAllResults  = Ø¥Ø¸ÙØ§Ø± Ø§ÙÙÙ
text.account.orderHistory.page.showPageResults  = Ø§ÙØ¥Ø¸ÙØ§Ø± Ø¹ÙÙ ØµÙØ­Ø§Øª
text.account.orderHistory.page.sort.byDate  = Ø§ÙØªØ§Ø±ÙØ®
text.account.orderHistory.page.sort.byOrderNumber  = Ø±ÙÙ Ø§ÙØ·ÙØ¨
text.account.orderHistory.page.sortTitle  = Ø§ÙØªØ±ØªÙØ¨ Ø¨Ø­Ø³Ø¨:
text.account.orderHistory.page.totalResults  = ÙÙØ§Ù {0} Ø·ÙØ¨ (Ø·ÙØ¨Ø§Øª)
text.account.orderHistory.total  = Ø§ÙÙØ¬ÙÙØ¹
text.account.orderHistory.viewOrders  = Ø¹Ø±Ø¶ Ø³Ø¬Ù Ø·ÙØ¨Ø§ØªÙ
text.account.paymentDetails  = Ø¨ÙØ§ÙØ§Øª Ø¹ÙÙÙØ© Ø§ÙØ¯ÙØ¹
text.account.paymentDetails.billingAddress  = Ø¹ÙÙØ§Ù Ø§Ø³ØªÙØ§Ù Ø§ÙÙØ§ØªÙØ±Ø©
text.account.paymentDetails.managePaymentDetails  = ØªØ­Ø¯ÙØ¯ Ø¨ÙØ§ÙØ§Øª Ø¹ÙÙÙØ© Ø§ÙØ¯ÙØ¹
text.account.paymentDetails.noPaymentInformation  = ÙØ§ ØªÙØ¬Ø¯ Ø¨ÙØ§ÙØ§Øª Ø¯ÙØ¹ ÙØ­ÙÙØ¸Ø©
text.account.paymentDetails.paymentCard  = Ø¨Ø·Ø§ÙØ© Ø§ÙØ¯ÙØ¹
text.account.paymentDetails.payment  = Ø§ÙØ¯ÙØ¹
text.account.paymentDetails.paymentCard.default  = Ø¨Ø·Ø§ÙØ© Ø§ÙØ¯ÙØ¹ Ø§ÙØ£Ø³Ø§Ø³ÙØ©
text.account.paymentDetails.setDefaultPaymentDetails  = ØªØ­Ø¯ÙØ¯ Ø¨ÙØ§ÙØ§Øª Ø§ÙØ¯ÙØ¹ Ø§ÙØ£Ø³Ø§Ø³ÙØ©
text.account.profile  = Ø§ÙÙÙÙ Ø§ÙØ´Ø®ØµÙ
text.account.profile.cancel  = Ø¥ÙØºØ§Ø¡
text.account.profile.changePassword  = ØªØºÙÙØ± ÙÙÙØ© Ø§ÙÙØ±ÙØ±
text.account.profile.changePassword.mobile  = ØªØºÙÙØ± ÙÙÙØ© Ø§ÙÙØ±ÙØ±
text.account.profile.confirmationUpdated  = ØªÙ ØªØ­Ø¯ÙØ« ÙÙÙÙ Ø§ÙØ´Ø®ØµÙ
text.account.profile.emailNotChanged  = ÙÙ ÙØªÙ ØªØ­Ø¯ÙØ« Ø¹ÙÙØ§Ù Ø¨Ø±ÙØ¯Ù Ø§ÙØ¥ÙÙØªØ±ÙÙÙ
text.account.profile.paymentCart.removed  = Ø£ÙØ²ÙÙØª Ø¨Ø·Ø§ÙØ© Ø§ÙØ¯ÙØ¹ Ø¨ÙØ¬Ø§Ø­
text.account.profile.saveUpdates  = Ø­ÙØ¸ Ø§ÙØªØ­Ø¯ÙØ«Ø§Øª
text.account.profile.updateEmail  = ØªØ­Ø¯ÙØ« Ø¹ÙÙØ§Ù Ø¨Ø±ÙØ¯Ù Ø§ÙØ¥ÙÙØªØ±ÙÙÙ
text.account.profile.updateEmail.mobile  = ØªØºÙÙØ± Ø¹ÙÙØ§Ù Ø§ÙØ¨Ø±ÙØ¯ Ø§ÙØ¥ÙÙØªØ±ÙÙÙ
text.account.profile.updateEmailAddress  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø¹ÙÙØ§Ù Ø¨Ø±ÙØ¯Ù Ø§ÙØ¥ÙÙØªØ±ÙÙÙ Ø§ÙØ¬Ø¯ÙØ¯ ÙØ§ÙØªØ£ÙÙØ¯ Ø¨Ø§Ø³ØªØ®Ø¯Ø§Ù ÙÙÙØ© Ø§ÙÙØ±ÙØ±
text.account.profile.updateForm  = ÙØ±Ø¬Ù Ø§Ø³ØªØ®Ø¯Ø§Ù ÙØ°Ø§ Ø§ÙÙÙÙØ°Ø¬ ÙØªØ­Ø¯ÙØ« Ø¨ÙØ§ÙØ§ØªÙ Ø§ÙØ´Ø®ØµÙØ©
text.account.profile.updatePassword  = ÙØ±Ø¬Ù Ø§Ø³ØªØ®Ø¯Ø§Ù ÙØ°Ø§ Ø§ÙÙÙÙØ°Ø¬ ÙØªØ­Ø¯ÙØ« ÙÙÙØ© Ø§ÙÙØ±ÙØ± Ø§ÙØ®Ø§ØµØ© Ø¨Ø­Ø³Ø§Ø¨Ù
text.account.profile.updatePasswordForm  = ØªØ­Ø¯ÙØ« ÙÙÙØ© Ø§ÙÙØ±ÙØ±
text.account.profile.updatePersonalDetails  = ØªØ­Ø¯ÙØ« Ø§ÙØ¨ÙØ§ÙØ§Øª Ø§ÙØ´Ø®ØµÙØ©
text.account.profile.updatePersonalDetails.mobile  = ØªØ¹Ø¯ÙÙ
text.account.profile.updateProfile.mobile  = ØªØ­Ø¯ÙØ« Ø§ÙÙÙÙ Ø§ÙØ´Ø®ØµÙ
text.account.trackOrders  = ØªØªØ¨Ø¹ Ø·ÙØ¨Ù
text.account.viewOrderHistory  = Ø¹Ø±Ø¶ Ø³Ø¬Ù Ø§ÙØ·ÙØ¨
text.account.yourAccount  = Ø­Ø³Ø§Ø¨Ù
text.address.remove.confirm  = ÙÙ ØªØ±ØºØ¨ ÙÙ ØªØ£ÙÙØ¯ Ø­Ø°Ù ÙØ°Ø§ Ø§ÙØ¹ÙÙØ§ÙØ
text.backToMobileStore  = Ø§ÙÙØ§ØªÙ Ø§ÙÙØªØ­Ø±Ù
text.button.cancel  = Ø¥ÙØºØ§Ø¡
text.button.menu  = Ø§ÙÙØ§Ø¦ÙØ©
text.button.new  = Ø¬Ø¯ÙØ¯
text.button.save  = Ø­ÙØ¸
text.button.showall  = Ø¥Ø¸ÙØ§Ø± Ø§ÙÙÙ
text.button.use  = Ø§Ø³ØªØ¹ÙØ§Ù
text.cart  = Ø³ÙØ© Ø§ÙØªØ³ÙÙ
text.connect  = Ø±Ø¨Ø·
text.copyright  = &copy; Ø¨Ø±ÙØ§ÙØ¬ hybris 2017
text.guest.customer  = Ø¶ÙÙ
text.header.connect  = Ø±Ø¨Ø·
text.header.language  = Ø§ÙÙØ± ÙÙØ§ ÙØªØºÙÙØ± Ø§ÙÙØºØ©
text.header.languageandcurrency  = Ø§ÙÙØºØ© ÙØ§ÙØ¹ÙÙØ©
text.header.loginandaccount  = ØªØ³Ø¬ÙÙ Ø§ÙØ¯Ø®ÙÙ ÙØ§ÙØ­Ø³Ø§Ø¨
text.header.menu  = Ø§ÙÙØ§Ø¦ÙØ©
text.header.storefinder  = ØªØ­Ø¯ÙØ¯ ÙÙØ§ÙØ¹ Ø§ÙÙØªØ§Ø¬Ø±
text.headertext  = ØªØ£ÙÙØ¯
text.headertext.conf  = ØªØ£ÙÙØ¯
text.headertext.error  = Ø§ÙØ®Ø·Ø£ (Ø§ÙØ£Ø®Ø·Ø§Ø¡)
text.headertext.info  = ÙØ¹ÙÙÙØ§Øª
text.headline.addaddress  = Ø§ÙÙØ± ÙØ¥Ø¶Ø§ÙØ© Ø¹ÙÙØ§Ù
text.headline.addtocart  = Ø§ÙÙØ± ÙØ¥Ø¶Ø§ÙØ© ÙØ°Ø§ Ø§ÙÙÙØªØ¬ Ø¥ÙÙ Ø³ÙØ© Ø§ÙØªØ³ÙÙ
text.headline.bottombanner  = Ø§ÙØ´Ø±ÙØ· Ø§ÙØ³ÙÙÙ
text.headline.breadcrumbs  = ÙØ³Ø§Ø± Ø§ÙØªØµÙØ­
text.headline.categories  = Ø§ÙÙØ± Ø¹ÙÙ Ø²Ø± Ø§ÙÙØ§Ø¦ÙØ© ÙÙÙØµÙÙ Ø¥ÙÙ Ø§ÙÙØ¦Ø§Øª
text.headline.findstore  = Ø§ÙØ¹Ø«ÙØ± Ø¹ÙÙ ÙØªØ¬Ø±
text.headline.footer.navigationbar  = ØªØµÙØ­ ÙÙ Ø®ÙØ§Ù Ø´Ø±ÙØ· Ø§ÙØªÙÙÙ Ø§ÙØ³ÙÙÙ
text.headline.homebanner  = Ø§ÙØ´Ø±ÙØ· Ø§ÙØ¹ÙÙÙ
text.headline.login  = Ø§Ø¶ØºØ· ÙÙØ§ ÙØªØ³Ø¬ÙÙ Ø§ÙØ¯Ø®ÙÙ
text.headline.myaccount  = Ø§Ø¶ØºØ· ÙÙØ§ ÙØªØ³Ø¬ÙÙ Ø§ÙØ¯Ø®ÙÙ Ø£Ù Ø§ÙÙØµÙÙ Ø¥ÙÙ Ø­Ø³Ø§Ø¨Ù
text.headline.navigationbar  = ØªØµÙØ­ ÙÙ Ø®ÙØ§Ù Ø´Ø±ÙØ· Ø§ÙØªÙÙÙ
text.headline.orderinfo  = Ø¬ÙÙØ¹ Ø§ÙÙØ¹ÙÙÙØ§Øª Ø§ÙÙØªØ¹ÙÙØ© Ø¨Ø¹ÙÙØ§Ù Ø§ÙØªÙØµÙÙ ÙØ·Ø±ÙÙØ© Ø§ÙØªÙØµÙÙ ÙØ·Ø±ÙÙØ© Ø§ÙØ¯ÙØ¹
text.headline.orderitems  = Ø¬ÙÙØ¹ Ø§ÙÙØ¹ÙÙÙØ§Øª Ø­ÙÙ Ø§ÙÙÙØªØ¬Ø§Øª Ø§ÙØªÙ ÙØªØ¶ÙÙÙØ§ Ø·ÙØ¨Ù
text.headline.orders  = Ø¹Ø±Ø¶ Ø·ÙØ¨Ø§ØªÙ
text.headline.productcategories  = ÙØ¦Ø§Øª Ø§ÙÙÙØªØ¬Ø§Øª
text.headline.productinfo  = Ø§ÙÙØ± ÙÙØ­ØµÙÙ Ø¹ÙÙ ÙØ²ÙØ¯ ÙÙ Ø§ÙÙØ¹ÙÙÙØ§Øª Ø­ÙÙ Ø§ÙÙÙØªØ¬ Ø£Ù ØªÙÙÙÙØ§Øª Ø§ÙÙÙØªØ¬ Ø£Ù Ø·Ø±ÙÙØ© Ø§ÙØªÙØµÙÙ
text.headline.profile  = ÙÙÙÙ Ø§ÙÙÙØ± ÙÙØ§ ÙÙØ§Ø·ÙØ§Ø¹ Ø¹ÙÙ Ø§ÙÙÙÙ Ø§ÙØ´Ø®ØµÙ Ø£Ù Ø¯ÙØªØ± Ø§ÙØ¹ÙØ§ÙÙÙ Ø£Ù Ø¨ÙØ§ÙØ§Øª Ø§ÙØ¯ÙØ¹ Ø£Ù Ø³ÙØ¬ÙÙÙ Ø§ÙØ·ÙØ¨Ø§Øª
text.headline.refinements  = Ø§Ø®ØªÙØ§Ø± ÙØ¯Ù Ø§ÙØµÙØ© Ø£Ù Ø¥Ø¶Ø§ÙØ© ØªØµÙÙÙØ§Øª
text.headline.register  = Ø§Ø¶ØºØ· ÙÙØ§ ÙØªØ³Ø¬ÙÙ Ø¹ÙÙÙ Ø¬Ø¯ÙØ¯
text.headline.search  = ÙÙÙÙÙ Ø§ÙØ¨Ø­Ø« ÙÙØ§ Ø¹Ù Ø§ÙÙÙØªØ¬Ø§Øª
text.headline.sortandrefine  = ØªØ±ØªÙØ¨ ÙØªØµÙÙÙ ÙØªØ§Ø¦Ø¬ Ø§ÙØ¨Ø­Ø«
text.headline.terms  = Ø£ÙØ§ÙÙ Ø¹ÙÙ Ø§ÙØ´Ø±ÙØ· ÙØ§ÙØ£Ø­ÙØ§Ù
text.help  = Ø§ÙÙØ³Ø§Ø¹Ø¯Ø©
text.javascript.disabled  = ÙØªØ·ÙØ¨ Ø§ÙÙØµÙÙ Ø¥ÙÙ Ø§ÙØµÙØ­Ø© Ø§Ø³ØªØ®Ø¯Ø§Ù Ø¬Ø§ÙØ§ Ø³ÙØ±ÙØ¨Øª. ÙØ±Ø¬Ù ØªÙÙÙÙ Ø¬Ø§ÙØ§ Ø³ÙØ±ÙØ¨Øª Ø¹ÙÙ Ø§ÙÙØªØµÙØ­.
text.label.loadingmoreresults  = ØªØ­ÙÙÙ Ø§ÙÙØ²ÙØ¯ ÙÙ Ø§ÙÙØªØ§Ø¦Ø¬...
text.label.showmoreresults  = Ø¥Ø¸ÙØ§Ø± Ø§ÙÙØ²ÙØ¯ ÙÙ Ø§ÙÙØªØ§Ø¦Ø¬
text.link.home.label  = Ø§ÙØµÙØ­Ø© Ø§ÙØ±Ø¦ÙØ³ÙØ©
text.loadingMessage  = Ø§ÙØªØ­ÙÙÙ Ø¬Ø§Ø±Ù...
text.logout  = ØªØ³Ø¬ÙÙ Ø®Ø±ÙØ¬
text.myaccount  = &laquo; Ø­Ø³Ø§Ø¨Ù
text.myaccount.orderHistory  = &laquo; Ø³Ø¬Ù Ø§ÙØ·ÙØ¨Ø§Øª
text.paymentcard.remove.confirm  = ÙØªØ£ÙØ¯ ÙÙ Ø±ØºØ¨ØªÙ Ø­Ø°Ù Ø¨Ø·Ø§ÙØ© Ø§ÙØ¯ÙØ¹ ÙØ°ÙØ
text.productreviews  = ØªÙÙÙÙØ§Øª
text.storefinder.desktop.page.currentPage  = {0} ÙÙ {1}
text.storefinder.desktop.page.firstPage  = &laquo;
text.storefinder.desktop.page.lastPage  = &raquo;
text.storefinder.desktop.page.linkNextPage  = Ø§ÙØªØ§ÙÙ
text.storefinder.desktop.page.linkPreviousPage  = Ø§ÙØ³Ø§Ø¨Ù
text.storefinder.desktop.page.showAllResults  = Ø¥Ø¸ÙØ§Ø± Ø§ÙÙÙ
text.storefinder.desktop.page.showPageResults  = Ø§ÙØ¥Ø¸ÙØ§Ø± Ø¹ÙÙ ØµÙØ­Ø§Øª
text.storefinder.desktop.page.sort.byName  = Ø¨Ø§ÙØ§Ø³Ù
text.storefinder.desktop.page.sortTitle  = Ø§ÙØªØ±ØªÙØ¨ Ø¨Ø­Ø³Ø¨:
text.storefinder.desktop.page.totalResults  = ØªÙ Ø§ÙØ¹Ø«ÙØ± Ø¹ÙÙ {0} ÙØªØ§Ø¬Ø±
text.storefinder.mobile.page.currentResults  = {0} - {1} ÙÙ  {2} ÙØªØ§Ø¬Ø±
text.storefinder.mobile.page.currentPage  = {0} ÙÙ {1}
text.storefinder.mobile.page.description  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù ÙÙÙØ¹Ù ÙÙØ¹Ø±ÙØ© Ø§ÙÙØªØ§Ø¬Ø± Ø§ÙØªÙ ØªØªÙØ§Ø¬Ø¯ ÙÙ ÙÙØ·ÙØªÙ Ø£Ù Ø§ÙØ³ÙØ§Ø­ ÙÙØ¬ÙØ§Ø² Ø¨Ø§ÙØ¨Ø­Ø« Ø¹Ù Ø§ÙÙØªØ§Ø¬Ø± Ø§ÙÙØ±ÙØ¨Ø© ÙÙÙ
text.storefinder.mobile.page.linkNextPage  = Ø§ÙØªØ§ÙÙ &raquo;
text.storefinder.mobile.page.linkPreviousPage  = &laquo; Ø§ÙØ³Ø§Ø¨Ù
text.storefinder.mobile.page.noResults  = ÙØ§ ØªÙØ¬Ø¯ ÙØªØ§Ø¦Ø¬ Ø¨Ø§ÙÙØ³Ø¨Ø© ÙÙÙØ·ÙØªÙ.
text.storefinder.mobile.page.totalResults  = ØªÙ Ø§ÙØ¹Ø«ÙØ± Ø¹ÙÙ {0} ÙØªØ§Ø¬Ø±
text.storefinder.responsive.page.currentPage  = {0} ÙÙ {1}
text.storefinder.responsive.page.firstPage  = &laquo;
text.storefinder.responsive.page.lastPage  = &raquo;
text.storefinder.responsive.page.linkNextPage  = Ø§ÙØªØ§ÙÙ
text.storefinder.responsive.page.linkPreviousPage  = Ø§ÙØ³Ø§Ø¨Ù
text.storefinder.responsive.page.showAllResults  = Ø¥Ø¸ÙØ§Ø± Ø§ÙÙÙ
text.storefinder.responsive.page.showPageResults  = Ø§ÙØ¥Ø¸ÙØ§Ø± Ø¹ÙÙ ØµÙØ­Ø§Øª
text.storefinder.responsive.page.sort.byName  = Ø¨Ø§ÙØ§Ø³Ù
text.storefinder.responsive.page.sortTitle  = Ø§ÙØªØ±ØªÙØ¨ Ø¨Ø­Ø³Ø¨:
text.storefinder.responsive.page.totalResults  = 1-10 ÙÙ {0} ÙØªØ¬Ø±ÙØ§
text.stores  = Ø§ÙÙØªØ§Ø¬Ø±
text.swithToMobileStore  = Ø§ÙØ¹ÙØ¯Ø© Ø¥ÙÙ Ø§ÙÙØ§ØªÙ Ø§ÙÙØªØ­Ø±Ù
text.viewfullsite  = Ø¹Ø±Ø¶ Ø§ÙÙÙÙØ¹ Ø§ÙÙØ§ÙÙ
text.expresscheckout.header  = Ø§ÙØ¯ÙØ¹ Ø§ÙØ³Ø±ÙØ¹ ÙØ§ÙØ®Ø±ÙØ¬
text.expresscheckout.title  = Ø§ÙØ§Ø³ØªÙØ§Ø¯Ø© ÙÙ Ø¥Ø¬Ø±Ø§Ø¡ Ø¹ÙÙÙØ© Ø§ÙØ¯ÙØ¹ Ø¨ØµÙØ±Ø© Ø£Ø³Ø±Ø¹ ÙÙ Ø®ÙØ§Ù:
text.expresscheckout.line1  = ØªØ¹ÙÙÙ Ø¹ÙÙØ§Ù Ø§ÙØªÙØµÙÙ Ø§ÙØ£Ø³Ø§Ø³Ù ÙÙ Ø­Ø³Ø§Ø¨Ù Ø£Ù Ø£Ø«ÙØ§Ø¡ Ø§ÙØ¯ÙØ¹ ÙØ§ÙØ®Ø±ÙØ¬
text.expresscheckout.line2  = ØªØ¹ÙÙÙ Ø¨ÙØ§ÙØ§Øª Ø§ÙØ¯ÙØ¹ Ø§ÙØ£Ø³Ø§Ø³ÙØ© Ø£Ø«ÙØ§Ø¡ Ø¥Ø¬Ø±Ø§Ø¡ Ø¹ÙÙÙØ© Ø§ÙØ¯ÙØ¹ ÙØ§ÙØ®Ø±ÙØ¬
text.expresscheckout.line3  = Ø§Ø³ØªØ®Ø¯Ø§Ù Ø·Ø±ÙÙØ© Ø§ÙØ´Ø­Ù Ø§ÙØ£Ø³Ø§Ø³ÙØ©
text.expresscheckout.info1  = Ø¶Ø¨Ø· Ø§ÙØ¥Ø¹Ø¯Ø§Ø¯Ø§Øª Ø§ÙØ®Ø§ØµØ© Ø¨Ù Ø¨ÙØ¬Ø§Ø­ Ø¹ÙÙ ÙØ°Ø§ Ø§ÙÙØ­ÙØ Ø³ÙØ³ÙØ­ ÙÙÙØ³ØªØ®Ø¯ÙÙÙ Ø§ÙÙØ³Ø¬ÙÙÙ Ø¨Ø§ÙØªÙØ¯Ù ÙØ¨Ø§Ø´Ø±Ø©Ù Ø¥ÙÙ Ø®Ø·ÙØ© Ø§ÙÙØ±Ø§Ø¬Ø¹Ø© Ø§ÙÙÙØ§Ø¦ÙØ© Ø¹ÙØ¯ ØªÙØ¬ÙÙÙ ÙØ¥Ø¬Ø±Ø§Ø¡ Ø¹ÙÙÙØ© Ø§ÙØ¯ÙØ¹
text.expresscheckout.info2  = ÙÙØ§ ÙÙÙÙ ÙÙÙØ³ØªØ®Ø¯ÙÙÙ Ø§ÙØ°ÙÙ ÙÙ ÙÙÙÙÙØ§ Ø¨Ø¹Ø¯ Ø¨ØªØ³Ø¬ÙÙ Ø§ÙØ¯Ø®ÙÙØ Ø§Ø®ØªÙØ§Ø± Ø·Ø±ÙÙØ© "Ø§ÙØ¯ÙØ¹ Ø§ÙØ³Ø±ÙØ¹" Ø¨Ø§ÙÙØ³Ø¨Ø© ÙØ³ÙØ© Ø§ÙØªØ³ÙÙ
text.expresscheckout.info3  = ÙÙØ²Ø© "Ø§ÙØ¯ÙØ¹ Ø§ÙØ³Ø±ÙØ¹" ØºÙØ± ÙØªØ§Ø­Ø© ÙÙÙØ³ØªØ®Ø¯ÙÙÙ Ø¨ØµÙØ© Ø¶ÙÙÙ
text.iconCartRemove  = Ø­Ø°Ù
text.payment.saved.card.removed  = Ø¨ÙØ§ÙØ§Øª Ø§ÙØ¨Ø·Ø§ÙØ© Ø§ÙÙØ­ÙÙØ¸Ø© Ø§ÙØªÙ Ø§Ø®ØªØ±ØªÙØ§ ØºÙØ± ÙÙØ¬ÙØ¯Ø©Ø ÙØ±Ø¬Ù ÙØ­Ø§ÙÙØ© Ø§Ø®ØªÙØ§Ø± Ø¨Ø·Ø§ÙØ© Ø£Ø®Ø±Ù
updatePwd.checkPwd  = ØªØ£ÙÙØ¯ ÙÙÙØ© Ø§ÙÙØ±ÙØ±
updatePwd.checkPwd.invalid  = ÙØ±Ø¬Ù ØªØ£ÙÙØ¯ ÙÙÙØ© Ø§ÙÙØ±ÙØ±
updatePwd.description  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù ÙÙÙØ© ÙØ±ÙØ± Ø¬Ø¯ÙØ¯Ø©.
updatePwd.pwd  = ÙÙÙØ© Ø§ÙÙØ±ÙØ±
updatePwd.pwd.invalid  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù ÙÙÙØ© ÙØ±ÙØ± ÙÙÙØ© (Ø¹ÙÙ Ø§ÙØ£ÙÙ 6 Ø®Ø§ÙØ§Øª)
updatePwd.submit  = ØªØ­Ø¯ÙØ« ÙÙÙØ© Ø§ÙÙØ±ÙØ±
updatePwd.title  = ØªØ­Ø¯ÙØ« ÙÙÙØ© Ø§ÙÙØ±ÙØ±
updatePwd.token.invalid  = Ø§ÙØ±Ø§Ø¨Ø· Ø§ÙÙØ³ØªØ®Ø¯Ù ÙÙÙØµÙÙ Ø¥ÙÙ ØµÙØ­Ø© Ø§ÙØªØ­Ø¯ÙØ« ØºÙØ± ØµØ­ÙØ­.
updatePwd.token.invalidated  = ØªÙ Ø¨Ø§ÙÙØ¹Ù ØªØ­Ø¯ÙØ« ÙÙÙØ© Ø§ÙÙØ±ÙØ± Ø§ÙØ®Ø§ØµØ© Ø¨Ù.
validation.checkEmail.equals  = Ø¹ÙÙØ§ÙÙ Ø§ÙØ¨Ø±ÙØ¯ Ø§ÙØ¥ÙÙØªØ±ÙÙÙØ Ø§ÙØ°Ù Ø£Ø¯Ø®ÙØªÙ Ø£ÙÙØ§Ù ÙØ§ÙØ¥Ø¹Ø§Ø¯Ø©Ø ÙÙØ³Ø§ ÙØªØ·Ø§Ø¨ÙÙÙ
validation.checkPwd.equals  = ÙÙÙØ© Ø§ÙØ³Ø± ÙØªØ£ÙÙØ¯ÙØ§ ÙÙØ³Ø§ ÙØªØ·Ø§Ø¨ÙÙÙ
paymentMethod.paymentDetails.expires  = -  ØªÙØªÙÙ Ø§ÙØµÙØ§Ø­ÙØ© ÙÙ {0} / {1}
text.payment.error.debit.card.optiob.disabled  = Ø®ÙØ§Ø± Ø§ÙØ¯ÙØ¹ Ø¨ÙØ§Ø³Ø·Ø© Ø¨Ø·Ø§ÙØ© Ø§ÙØ®ØµÙ ØºÙØ± ÙØ¯Ø¹ÙÙ Ø¶ÙÙ ÙØ°Ø§ Ø§ÙØ±ÙØ² Ø§ÙØ¨Ø±ÙØ¯ÙØ ÙØ±Ø¬Ù ÙØ­Ø§ÙÙØ© Ø§Ø³ØªØ®Ø¯Ø§Ù Ø·Ø±ÙÙØ© Ø¯ÙØ¹ ÙØ®ØªÙÙØ©
text.payment.error.card.optiob.disabled  = Ø®ÙØ§Ø± Ø§ÙØ¯ÙØ¹ Ø§ÙØ°Ù Ø§ÙØªÙÙØªÙ ØºÙØ± ÙØ¯Ø¹ÙÙ Ø¶ÙÙ ÙØ°Ø§ Ø§ÙØ±ÙØ² Ø§ÙØ¨Ø±ÙØ¯ÙØ ÙØ±Ø¬Ù ÙØ­Ø§ÙÙØ© Ø§Ø³ØªØ®Ø¯Ø§Ù Ø·Ø±ÙÙØ© Ø¯ÙØ¹ ÙØ®ØªÙÙØ©
text.payment.error.cod.optiob.disabled  = ÙØ£Ø³ÙØ Ø®ÙØ§Ø± Ø§ÙØ¯ÙØ¹ Ø¹ÙØ¯ Ø§ÙØ§Ø³ØªÙØ§Ù ØºÙØ± ÙØªØ§Ø­ Ø¶ÙÙ ÙØ°Ø§ Ø§ÙØ±ÙØ² Ø§ÙØ¨Ø±ÙØ¯ÙØ ÙØ±Ø¬Ù Ø§Ø®ØªÙØ§Ø± Ø·Ø±ÙÙØ© Ø¯ÙØ¹ ÙØ®ØªÙÙØ©.
text.payment.error.nbk.optiob.disabled  = Ø®ÙØ§Ø± Ø§ÙØµÙØ±ÙØ© Ø¹Ø¨Ø± Ø§ÙØ¥ÙØªØ±ÙØª ØºÙØ± ÙØ¯Ø¹ÙÙ Ø¶ÙÙ ÙØ°Ø§ Ø§ÙØ±ÙØ² Ø§ÙØ¨Ø±ÙØ¯ÙØ ÙØ±Ø¬Ù ÙØ­Ø§ÙÙØ© Ø§Ø³ØªØ®Ø¯Ø§Ù Ø·Ø±ÙÙØ© Ø¯ÙØ¹ ÙØ®ØªÙÙØ©
text.payment.error.cod.optiob.exceed.threshold  = ÙÙØ¯ ØªØ¬Ø§ÙØ²Øª Ø§ÙØ­Ø¯ Ø§ÙÙØ³ÙÙØ­ ÙØ®ÙØ§Ø± Ø§ÙØ¯ÙØ¹ Ø¹ÙØ¯ Ø§ÙØ§Ø³ØªÙØ§ÙØ Ø¥Ø° ÙØ¬Ø¨ Ø£Ù ÙÙÙÙ Ø£ÙÙ ÙÙ ÙØ¨ÙØº {0} {1}
text.page.message.underconstruction  = <strong> ÙØ¹ÙÙÙØ§Øª: </strong> Ø§ÙØµÙØ­Ø© ØªØ­Øª Ø§ÙØ¥ÙØ´Ø§Ø¡ - ÙÙØ§ ØªØ¹ÙÙ Ø¨Ø¬ÙÙØ¹ Ø§ÙÙÙØ²Ø§Øª Ø§ÙÙØ¸ÙÙÙØ©
text.payment.error.nbk.optiob.invalid.bank  = Ø§ÙØ¨ÙÙ Ø§ÙØ°Ù Ø§Ø®ØªØ±ØªÙ ÙØ¹ÙÙÙØ§Øª Ø§ÙØµÙØ±ÙØ© Ø¹Ø¨Ø± Ø§ÙØ¥ÙØªØ±ÙØª ØºÙØ± ÙØ¹ØªÙØ¯Ø ÙØ±Ø¬Ù Ø§Ø®ØªÙØ§Ø± Ø¨ÙÙ ÙØ®ØªÙÙ
landMarkOffersPage.filterby.department  = Ø§ÙØªØ±Ø´ÙØ­ ÙÙÙÙØ§ ÙÙÙØ³Ù
landMarkOffersPage.filterby.brand  = Ø§ÙØªØ±Ø´ÙØ­ ÙÙÙÙØ§ ÙÙØ¹ÙØ§ÙØ© Ø§ÙØªØ¬Ø§Ø±ÙØ©
landMarkOffersPage.filterby.all  = Ø§ÙÙÙ
landMarkOffersPage.offers.notfound  = ÙÙØ£Ø³ÙØ ÙÙØ³ ÙØ¯ÙÙØ§ Ø­Ø§ÙÙÙØ§ Ø£Ù Ø¹Ø±ÙØ¶.
invalid.card.number  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø±ÙÙ Ø¨Ø·Ø§ÙØ© ØµØ­ÙØ­
invalid.holder.name  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø§Ø³Ù ØµØ­ÙØ­ ÙØ­Ø§ÙÙ Ø§ÙØ¨Ø·Ø§ÙØ©
invalid.card.type  = ÙØ±Ø¬Ù Ø§Ø®ØªÙØ§Ø± ÙÙØ¹ Ø¨Ø·Ø§ÙØ© ØµØ­ÙØ­
payment.generic.failure  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù ÙØ¹ÙÙÙØ§Øª ØµØ­ÙØ­Ø©
landMarkHomePage.footer.newsletter.submit  = Ø§ÙØ§Ø´ØªØ±Ø§Ù
landMarkHomePage.footer.contactus  = Ø§ØªØµÙ Ø¨ÙØ§
landMarkHomePage.footer.storefinder  = ÙØ±ÙØ¹ÙØ§
landMarkHomePage.footer.Help  = Ø§ÙÙØ³Ø§Ø¹Ø¯Ø©
landMarkHomePage.footer.About				 = Ø­ÙÙ
landMarkHomePage.footer.landmarkOnline  = Ø±ÙØªÙÙ ÙØ§Ø±ÙØ¯ ÙÙÙØªØ¯
landMarkHomePage.footer.terms&condition		 = Ø§ÙØ´Ø±ÙØ· ÙØ§ÙØ£Ø­ÙØ§Ù
landMarkHomePage.footer.privacyPolicy		 = Ø³ÙØ§Ø³Ø© Ø§ÙØ®ØµÙØµÙØ©
landMarkHomePage.footer.EnterYourEmailHere        = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø¹ÙÙØ§Ù Ø§ÙØ¨Ø±ÙØ¯ Ø§ÙØ¥ÙÙØªØ±ÙÙÙ
landMarkHomePage.productcarousel.previouslink	 = Ø§ÙØ³Ø§Ø¨Ù
landMarkHomePage.productcarousel.nextlink	 = Ø§ÙØªØ§ÙÙ
landMarkHomePage.navigation.shopbydepartment  = Ø§ÙØªØ³ÙÙ Ø¨Ø­Ø³Ø¨ Ø§ÙÙØ³Ù
landMarkHomePage.navigation.shopbybrand  = Ø§ÙØªØ³ÙÙ Ø¨Ø­Ø³Ø¨ Ø§ÙØ¹ÙØ§ÙØ©
landMarkHomePage.header.user.greeting  = ÙØ±Ø­Ø¨ÙØ§Ø
landMarkHomePage.header.user.signup  = ØªØ³Ø¬ÙÙ Ø­Ø³Ø§Ø¨
landMarkHomePage.header.user.signin  = ØªØ³Ø¬ÙÙ Ø§ÙØ¯Ø®ÙÙ
landMarkHomePage.header.user.help = Ø§ÙÙØ³Ø§Ø¹Ø¯Ø©
landMarkHomePage.header.user.my.account  = Ø­Ø³Ø§Ø¨Ù
landMarkHomePage.header.user.order.history  = Ø³Ø¬Ù Ø§ÙØ·ÙØ¨Ø§Øª
landMarkHomePage.header.user.subscriptions  = Ø§ÙØªÙØ§ØµÙ
landMarkHomePage.header.user.rewards  = ÙÙØ§ÙØ¢Øª ÙØ§ÙØ¯ÙØ§Ø±Ù
landMarkHomePage.header.user.logout  = ØªØ³Ø¬ÙÙ Ø§ÙØ®Ø±ÙØ¬
landMarkHomePage.header.cart.count.message  = ÙØ¯ÙÙ 0 ÙÙØªØ¬ ÙÙ Ø³ÙØªÙ
landMarkHomePage.header.cart.subtotal  = Ø§ÙÙØ¬ÙÙØ¹ Ø§ÙÙØ±Ø¹Ù:
landMarkHomePage.header.cart.item.count  = 0
landMarkHomePage.header.cart.checkout.message  = Ø¥ØªÙØ§Ù Ø§ÙØ¯ÙØ¹
landMarkHomePage.header.mobile.menu  = Ø§ÙÙØ§Ø¦ÙØ©
landMarkHomePage.header.lifestyle.caption  = Ø¥Ø·ÙØ§ÙØªÙ Ø§ÙÙÙÙØ²Ø©. ÙØªØ¬Ø±Ù Ø§ÙÙÙØ¶Ù.
landMarkHomePage.header.max.caption  = Ø§ÙØ®Ø±ÙØ¬ Ø¨ÙØ¸ÙØ±Ù Ø±Ø§Ø¦Ø¹. Ø§ÙØ´Ø¹ÙØ± Ø§ÙØ±Ø§Ø¦Ø¹.
landMarkHomePage.header.babyshop.caption  = Ø¨ÙØ¨Ù Ø´ÙØ¨ Ø§ÙØ±Ø§Ø¦Ø¹. Ø´Ø¹ÙØ±Ù ÙØ¨ÙØ¬.
landMarkHomePage.header.splash.caption  = Ø³Ø¨ÙØ§Ø´ Ø§ÙØ±Ø§Ø¦Ø¹. Ø£ÙØ§ÙØ© ÙØ·ÙÙØ©.
landMarkHomePage.header.centerpoint.caption  = ÙÙØ·Ø© Ø§ÙÙØ±ÙØ². Ø§ÙØªØ³ÙÙ ÙÙØ·Ø©.
landMarkHomePage.header.shoemart.caption  = Ø´Ù ÙØ§Ø±Øª Ø§ÙØ±Ø§Ø¦Ø¹. Ø£ÙØ§ÙØ© ÙØ·ÙÙØ©.
landMarkHomePage.header.homecentre.caption  = ÙÙÙ Ø³ÙØªØ±. ÙØªØ¬Ø¹ÙÙ ÙÙØ²ÙÙ ÙØ§Ø­Ø©Ù ÙÙ Ø§ÙØ¬ÙØ§Ù.
landMarkHomePage.header.search.placeholder  = ÙØ§ Ø§ÙØ°Ù ØªØ¨Ø­Ø« Ø¹ÙÙØ
landMarkHomePage.header.minibasketempty  = Ø³ÙØ© Ø§ÙØªØ³ÙÙÙ ÙØ§Ø±ØºØ© <br> Ø¨Ø§ÙØªØ¸Ø§Ø± Ø§Ø®ØªÙØ§Ø±ØªÙ Ø§ÙÙÙÙÙØ²Ø©
pdp.size.guide  = Ø¯ÙÙÙ Ø§ÙÙÙØ§Ø³Ø§Øª
pdp.notify.email.required  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø¨Ø±ÙØ¯Ù Ø§ÙØ¥ÙÙØªØ±ÙÙÙ ÙÙØ§
pdp.webreserve.failed  = Ø¹Ø°Ø±ÙØ§! Ø­Ø¯Ø« Ø®Ø·Ø£ ÙØ§. ÙØ±Ø¬Ù Ø¥Ø¹Ø§Ø¯Ø© Ø§ÙÙØ­Ø§ÙÙØ© ÙØ§Ø­ÙÙØ§.
pdp.threshold.breach  = Ø§ÙØ­Ø¯ Ø§ÙØ£ÙØµÙ  Ø§ÙÙØ³ÙÙØ­ {0}
pdp.size.required  = ÙØ±Ø¬Ù Ø§Ø®ØªÙØ§Ø± Ø§ÙÙÙØ§Ø³.
pdp.color.required  = ÙØ±Ø¬Ù Ø§Ø®ØªÙØ§Ø± Ø§ÙÙÙÙ.
pdp.missed.offer  = Ø¹Ø±Ø¶ ÙÙ ÙØ³ØªÙØ§Ø¯ ÙÙÙ
pdp.bowser.promotion  = <a href="/search?q=allPromotions.ar:{0}"> Ø§ÙØ§Ø·ÙØ§Ø¹ Ø¹ÙÙ Ø§ÙØ¹Ø±Ø¶ Ø§ÙØªØ±ÙÙØ¬Ù </a>
pdp.color.label  = Ø§ÙÙÙÙ
pdp.next  = Ø§ÙØªØ§ÙÙ
pdp.offer  = Ø¹Ø±Ø¶ ØªØ±ÙÙØ¬Ù
pdp.previous  = Ø§ÙØ³Ø§Ø¨Ù
pdp.you.may.like.label  = ÙØ¯ ÙØ¹Ø¬Ø¨Ù Ø£ÙØ¶ÙØ§
pdp.customer.also.viewed.label  = ÙÙØªØ¬Ø§ØªÙ Ø£Ø®Ø±Ù Ø£Ø«Ø§Ø±Øª Ø§ÙØ§ÙØªÙØ§Ù
pdp.see.all.label  = Ø§ÙØ§Ø·ÙØ§Ø¹ Ø¹ÙÙ Ø§ÙÙÙ
pdp.please.select.size  = ÙØ±Ø¬Ù Ø§Ø®ØªÙØ§Ø± Ø§ÙÙÙØ§Ø³
pdp.recently.viewed.label  = ÙÙØªØ¬Ø§ØªÙ Ø§Ø³ØªØ¹Ø±Ø¶ØªÙØ§ ÙØ¤Ø®Ø±ÙØ§
pdp.rating.one  = ÙÙ ÙØ¹Ø¬Ø¨ÙÙ
pdp.rating.two  = ÙÙØ¨ÙÙ
pdp.rating.three  = Ø¬ÙØ¯
pdp.rating.four  = Ø±Ø§Ø¦Ø¹
pdp.rating.five  = ÙØªÙÙØ²
pdp.review.content.required  = ÙØ±Ø¬Ù ÙØªØ§Ø¨Ø© ØªÙÙÙÙ
pdp.review.rating.required  = ÙØ±Ø¬Ù ØªÙÙÙÙ Ø§ÙÙÙØªØ¬ Ø±ÙÙÙÙØ§
pdp.review.title.required  = ÙØ§ ØªÙØ³ Ø§ÙØ¹ÙÙØ§Ù
pdp.write.review.textarea.placeholder  = ÙØ±Ø¬Ù ÙØªØ§Ø¨Ø© Ø§ÙØªÙÙÙÙ Ø§ÙÙØµÙÙ ÙÙØ§
pdp.write.review.text.placeholder  = Ø£Ø¶Ù Ø¹ÙÙØ§ÙÙØ§ ÙØªÙÙÙÙÙ
pdp.thankyou  = Ø´ÙØ±ÙØ§ ÙÙ
pdp.suggestion.header  = ÙÙ Ø£ÙØª Ø¨ØµØ¯Ø¯ ÙØªØ§Ø¨Ø© ØªÙÙÙÙØ Ø¥ÙÙÙ Ø¨Ø¹Ø¶ Ø§ÙÙØµØ§Ø¦Ø­.
pdp.suggestion.line1  = ÙÙØ¨ØºÙ Ø´Ø±Ø­ Ø§ÙØ¬ÙØ§ÙØ¨ Ø§ÙØªÙ ØªØ¹Ø¬Ø¨Ù ÙØ§ÙØªÙ ÙÙ ØªØ¹Ø¬Ø¨Ù ÙÙ ÙØ°Ø§ Ø§ÙÙÙØªØ¬.
pdp.suggestion.line2  = Ø§ÙÙÙØ§Ø±ÙØ© ÙØ¹ ÙÙØªØ¬Ø§Øª ÙÙØ§Ø«ÙØ©.
pdp.suggestion.line3  = ÙÙ ÙÙÙÙ Ø£Ù ØªÙØµØ­Ù Ø¨Ø´Ø±Ø§Ø¡ Ø§ÙÙÙØªØ¬Ø
pdp.suggestion.line4  = ÙØ±Ø¬Ù Ø§ÙØªØ£ÙØ¯ ÙÙ Ø¹Ø¯Ù Ø¥Ø¹Ø·Ø§Ø¡ ÙØ¹ÙÙÙØ§Øª Ø´Ø®ØµÙØ© Ø¹ÙÙ ÙØ«Ù Ø¹ÙÙØ§ÙÙ Ø£Ù Ø±ÙÙ ÙØ§ØªÙÙ.
pdp.review.success.msg  = ÙÙØªØ§Ø²! ØªÙØª Ø¹ÙÙÙØ© Ø¥Ø±Ø³Ø§Ù ØªÙÙÙÙÙ Ø¨ÙØ¬Ø§Ø­Ø ÙÙÙ Ø§ÙØ¢Ù ÙÙØ¯ Ø§ÙÙØ±Ø§Ø¬Ø¹Ø© ÙØ³ÙÙ ÙØªÙ ÙØ´Ø±Ù ÙØ±ÙØ¨ÙØ§.
pdp.addreview  = ÙØ±Ø¬Ù ÙØªØ§Ø¨Ø© ØªÙÙÙÙ
pdp.review.submit  = Ø¥Ø±Ø³Ø§Ù
pdp.review.save  = Ø­ÙØ¸
pdp.reviews  = {0} ØªÙÙÙÙØ§Øª
pdp.review  = {0} ØªÙÙÙÙ
pdp.orderDeliveryQuestion  = ÙØªÙ Ø³Ø£Ø³ØªÙÙ Ø·ÙØ¨ÙØ
pdp.bulkOrderMsg  = ÙÙ Ø£ÙØª ÙÙØªÙ Ø¨Ø·ÙØ¨Ø§Øª Ø§ÙØ¬ÙÙØ©Ø ØªØ¹Ø±ÙÙ Ø¹ÙÙ ÙØ²ÙØ¯ ÙÙ Ø§ÙØªÙØ§ØµÙÙ
pdp.fbLike  = Ø¥Ø¹Ø¬Ø§Ø¨
pdp.tweet  = ØªØºØ±ÙØ¯
pdp.via  = Ø¨ÙØ§Ø³Ø·Ø©
pdp.share  = ÙØ´Ø§Ø±ÙØ©
pdp.pin  = ØªØ£Ø´ÙØ±
pdp.by  = Ø¨ÙØ§Ø³Ø·Ø©
pdp.addToBasket  = Ø£Ø¶Ù Ø¥ÙÙ Ø§ÙØ³ÙØ©
cta.addedToBasket = ØªÙØª Ø§ÙØ¥Ø¶Ø§ÙØ©
cta.addAllToBasket = Ø§Ø¶Ù ÙØ§ÙÙ Ø§ÙÙØ¬ÙÙØ¹Ø©
cta.addAllToBasketSuccess = ÙÙØ¯ ØªÙ Ø§Ø¶Ø§ÙØ© Ø§ÙÙØ¬ÙÙØ¹Ø© Ø¨ÙØ¬Ø§Ø­
preBasket.byThemTogether = ÙÙ Ø¨Ø´Ø±Ø§Ø¡ ÙØ§ÙÙ Ø§ÙÙØ¬ÙÙØ¹Ø©
preBasket.succesfullyAdded = ÙÙØ¯ ØªÙ Ø§Ø¶Ø§ÙØ© Ø§ÙÙÙØªØ¬ Ø¨ÙØ¬Ø§Ø­
preBasket.subtotal = Ø§ÙÙØ¬ÙÙØ¹ Ø§ÙÙØ±Ø¹Ù
preBasket.subtotal.item=&nbsp;ÙÙØªØ¬):
preBasket.subtotal.items=&nbsp;ÙÙØªØ¬Ø§Øª):
pdp.checkoutWithFastPay  = Ø§Ù Ø§Ø³ØªØ®Ø¯Ù Ø¹ÙÙÙØ© Ø§ÙØ¯ÙØ¹ Ø§ÙØ³Ø±ÙØ¹
pdp.shipTo  = Ø§ÙØ´Ø­Ù Ø¥ÙÙ
pdp.payNowWithFastPay  = Ø§ÙØ¯ÙØ¹ Ø§ÙØ³Ø±ÙØ¹
pdp.notifyMeWhenBack  = ÙØ±Ø¬Ù Ø¥Ø¨ÙØ§ØºÙ Ø¹ÙØ¯ ØªÙÙØ± Ø§ÙÙÙØªØ¬ ÙØ¬Ø¯Ø¯ÙØ§ ÙÙ Ø§ÙÙØ®Ø²ÙÙ
pdp.notifyMe  = ÙØ±Ø¬Ù Ø¥Ø®Ø·Ø§Ø±Ù
pdp.addToWishList  = Ø£Ø¶Ù Ø¥ÙÙ ÙØ§Ø¦ÙØªÙ Ø§ÙÙÙØ¶ÙÙØ©
pdp.save  = ÙÙÙØ± {1}<i>{0} Ø¨ÙØ§ ÙØ¹Ø§Ø¯Ù </i> (<i>  {2}  </i>%)
pdp.quantity  = Ø§ÙÙÙÙØ©
pdp.inStockOnline  = ÙØªÙÙØ± ÙÙ Ø§ÙÙØ®Ø²ÙÙ Ø£ÙÙÙØ§ÙÙ
pdp.soldOut  = Ø¨ÙØ¹Øª ÙÙ Ø§ÙÙÙÙØ©
pdp.overview  = ÙÙØ­Ø© Ø¹Ø§ÙØ©
pdp.tapToZoom  = Ø§Ø¶ØºØ· ÙØªÙØ¨ÙØ± Ø§ÙØµÙØ±Ø©
pdp.helpful  = ÙÙÙØ¯Ø
pdp.helpfulMsg  = {0} ÙÙ {1} ÙÙ Ø§ÙØ²ÙØ§Ø± ÙØ¬Ø¯ÙØ§  ÙØ°Ø§ Ø§ÙØªÙÙÙÙ ÙÙÙØ¯ÙØ§
pdp.reviews.label  = ØªÙÙÙÙØ§Øª
pdp.beFirstToReview  =ÙÙ Ø£ÙÙ ÙÙ ÙØ³Ø¬ÙÙ ØªÙÙÙÙÙØ§
pdp.writeReview  = ÙØ±Ø¬Ù ÙØªØ§Ø¨Ø© ØªÙÙÙÙ
pdp.averageRatings  = ÙØ¹Ø¯Ù Ø§ÙØªÙÙÙÙ
pdp.showAllReviews  = Ø¥Ø¸ÙØ§Ø± ÙØ§ÙØ© Ø§ÙØªÙÙÙÙØ§Øª
pdp.showLessReviews  = Ø¥Ø¸ÙØ§Ø± Ø¹Ø¯Ø¯ Ø£ÙÙ ÙÙ Ø§ÙØªÙÙÙÙØ§Øª
pdp.returnProcess  = Ø¹ÙÙÙØ© Ø§ÙØ¥Ø±Ø¬Ø§Ø¹
pdp.returnPolicy  = Ø³ÙØ§Ø³Ø© Ø§ÙØ¥Ø±Ø¬Ø§Ø¹
pdp.shipping  = Ø§ÙØ´Ø­Ù ÙØ§ÙØªÙØµÙÙ
pdp.sizeGuide  = Ø¯ÙÙÙ Ø§ÙÙÙØ§Ø³Ø§Øª
pdp.checkDelivery  = Ø§ÙØªØ­ÙÙ
pdp.color  = Ø§ÙÙÙÙ
pdp.color.name  = Ø§ÙÙÙÙ: {0}
pdp.outOf5Star  = ÙÙ Ø£ØµÙ 5 ÙØ¬ÙÙ
pdp.pincode.failMsg  = ÙØ¹ØªØ°Ø± Ø¹Ù Ø¹Ø¯Ù ØªÙÙÙØ± Ø®Ø¯ÙØ© Ø§ÙØªÙØµÙÙ Ø¥ÙÙ
pdp.pincode.emptyValMsg  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø§ÙØ±ÙØ² Ø§ÙØ¨Ø±ÙØ¯Ù Ø§ÙØ®Ø§Øµ Ø¨Ù
pdp.pincode.checkDiffPin  = Ø­Ø§ÙÙ Ø¥Ø¯Ø®Ø§Ù Ø±ÙØ² Ø¨Ø±ÙØ¯Ù ÙØ®ØªÙÙ
pdp.cvv.label  = CVV
pdp.cvv.required  = Ø£Ø¯Ø®Ù CVV
pdp.cvv.valid  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø±ÙÙ CVV ØµØ­ÙØ­
pdp.notify.subscribed  = Ø³ÙÙÙÙ Ø¨Ø¥Ø¨ÙØ§ØºÙ ÙÙØ± ØªÙÙÙØ±Ù ÙØ¬Ø¯Ø¯ÙØ§ ÙÙ Ø§ÙÙØ®Ø²ÙÙ.
pdp.email.id.required  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø¹ÙÙØ§Ù Ø¨Ø±ÙØ¯Ù Ø§ÙØ¥ÙÙØªØ±ÙÙÙ
pdp.outofstock  = Ø¹Ø°Ø±ÙØ§! Ø¨ÙØ¹Øª ÙÙ ÙÙÙØ© Ø§ÙÙÙØªØ¬
pdp.notify.subscribed  = Ø³ÙÙÙÙ Ø¨Ø¥Ø¨ÙØ§ØºÙ ÙÙØ± ØªÙÙÙØ±Ù ÙØ¬Ø¯Ø¯ÙØ§ ÙÙ Ø§ÙÙØ®Ø²ÙÙ.
pdp.enter.valid.email.id  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø¹ÙÙØ§Ù Ø¨Ø±ÙØ¯ Ø¥ÙÙØªØ±ÙÙÙ ØµØ­ÙØ­
pdp.email.id.required  = ÙØ¬Ø¨ Ø¥Ø¯Ø®Ø§Ù Ø¹ÙÙØ§Ù Ø¨Ø±ÙØ¯Ù Ø§ÙØ¥ÙÙØªØ±ÙÙÙ
pdp.outofstock  = Ø¹Ø°Ø±ÙØ§! Ø¨ÙØ¹Øª ÙÙ Ø§ÙÙÙÙØ© Ø§ÙØªÙ ÙØ§ÙØª ÙØ¯ÙÙØ§ ÙÙ ÙØ°Ø§ Ø§ÙÙÙØªØ¬
pdp.features  = Ø§ÙÙØ²Ø§ÙØ§
pdp.showAllFeatures  = Ø¥Ø¸ÙØ§Ø± ÙÙ Ø§ÙÙØ²Ø§ÙØ§
pdp.showLessFeatures  = Ø¥Ø¸ÙØ§Ø± Ø¹Ø¯Ø¯ Ø£ÙÙ ÙÙ Ø§ÙÙØ²Ø§ÙØ§
pdp.adjust.quantity  = ÙØ±Ø¬Ù ØªØ¹Ø¯ÙÙ Ø§ÙÙÙÙØ©
pdp.product.notfound  = Ø§ÙÙÙØªØ¬ Ø§ÙÙØ­Ø¯Ø¯ ØºÙØ± ÙØªÙÙØ±
pdp.let.us.help.you  = Ø¯Ø¹ÙØ§ ÙØ³Ø§Ø¹Ø¯Ù
pdp.tab.overview  = ÙÙØ­Ø© Ø¹Ø§ÙØ©
pdp.tab.dimensions  = Ø§ÙÙÙØ§Ø³Ø§Øª
basket.page.start.shopping  = Ø§ÙØ¨Ø¯Ø¡ Ø¨Ø§ÙØªØ³ÙÙ
basket.page.item  = Ø§ÙÙÙØªØ¬
basket.page.description  = Ø§ÙÙØµÙ
basket.page.totalPrice  = Ø§ÙØ³Ø¹Ø± Ø§ÙØ¥Ø¬ÙØ§ÙÙ
basket.page.appliedOffer  =Ø§ÙØ¹Ø±Ø¶ Ø§ÙÙØ³ØªØ­Ù
basket.page.salePrice  = Ø§ÙØ³Ø¹Ø± Ø§ÙÙØ¹ÙÙ
basket.page.yourSavings  = Ø§ÙØªÙÙÙØ± Ø§ÙØ°Ù Ø³ØªØ­ÙÙÙ
basket.page.missedOffer  = Ø§ÙØ­ØµÙÙ Ø¹ÙÙ Ø§ÙØ¹Ø±Ø¶
basket.page.empty.cart  = Ø³ÙØ© Ø§ÙØªØ³ÙÙ ÙØ§Ø±ØºØ©.
basket.page.empty.cart.add.something  = ÙÙØ§ ÙÙ Ø¨Ø¥Ø¶Ø§ÙØ© ÙÙØªØ¬Ø§ØªÙ Ø¥ÙÙÙØ§!
basket.page.checkout.now  = Ø§ÙØ¯ÙØ¹ Ø§ÙØ£Ù
basket.page.checkout.now = Ø§ÙØ¯ÙØ¹ Ø§ÙØ£Ù
basket.page.your.shopping.basket  = Ø³ÙØ© Ø§ÙØªØ³ÙÙ Ø§ÙØ®Ø§ØµØ© Ø¨Ù
basket.page.want.to.use  = Ø£ÙØ¯ Ø§Ø³ØªØ®Ø¯Ø§Ù
basket.page.i.will.save  = ÙØ¨Ø°ÙÙ Ø£ÙÙØ±
basket.page.cod.charges  = ÙØµØ§Ø±ÙÙ Ø®Ø¯ÙØ© Ø§ÙØ¯ÙØ¹ Ø¹ÙØ¯ Ø§ÙØ§Ø³ØªÙØ§Ù:
basket.page.shipping.standardGround  = Ø§ÙØ´Ø­Ù ÙØ§ÙØªÙØµÙÙ Ø§ÙØ¨Ø±Ù Ø§ÙØ¹Ø§Ø¯Ù:
basket.page.shipping.express  = Ø§ÙØªÙØµÙÙ Ø§ÙØ³Ø±ÙØ¹:
basket.page.shipping.homecentre.express=Ø§ÙØªÙØµÙÙ Ø§ÙØ³Ø±ÙØ¹ ÙÙ ÙÙÙ Ø³ÙØªØ±:
basket.page.shipping.babyshop.express=Ø¨Ø§Ø¨ÙØ´ÙØ¨ Ø§ÙØªØ³ÙÙÙ Ø§ÙØ³Ø±ÙØ¹:
basket.page.shipping.businessDays  = ({0} Ø£ÙØ§Ù Ø¹ÙÙ)
basket.page.capsTotal  = Ø§ÙÙØ¬ÙÙØ¹
basket.page.shipping.free  = ÙØ¬Ø§ÙÙØ§
basket.page.freeVoucherMsg  = ÙÙØ¯ Ø­ØµÙØª Ø¹ÙÙ {1} ÙØ³ÙÙØ© (ÙØ³Ø§Ø¦Ù) ÙØ¬Ø§ÙÙØ© Ø¨ÙÙÙØ© Ø¥Ø¬ÙØ§ÙÙØ© ØªØ¨ÙØº {0} {2}. Ø³ÙØ±Ø³Ù ÙÙ ÙØ³ÙÙØªÙ Ø¹Ù Ø·Ø±ÙÙ Ø§ÙØ¨Ø±ÙØ¯ Ø§ÙØ¥ÙÙØªØ±ÙÙÙ Ø¨Ø¹Ø¯ ØªÙØµÙÙ Ø§ÙØ·ÙØ¨
basket.page.singlefreeVoucherMsg  = ÙÙØ¯ Ø­ØµÙØª Ø¹ÙÙ ÙØ³ÙÙØ© ÙØ¬Ø§ÙÙØ© Ø¨ÙÙÙØ© Ø¥Ø¬ÙØ§ÙÙØ© ØªØ¨ÙØº {0} {1} . Ø³ÙØ±Ø³Ù ÙÙ ÙØ³ÙÙØªÙ Ø¹Ù Ø·Ø±ÙÙ Ø§ÙØ¨Ø±ÙØ¯ Ø§ÙØ¥ÙÙØªØ±ÙÙÙ Ø¨Ø¹Ø¯ ØªÙØµÙÙ Ø§ÙØ·ÙØ¨
basket.page.footer.promoCode  = Ø§ÙØ¹Ø±ÙØ¶ ÙØ§ÙÙØ³Ø§Ø¦Ù
basket.page.footer.promoCode.view.enter.voucher.code.text = Ø´Ø§ÙØ¯ Ø§ÙØ¹Ø±Ø¶/ Ø£Ø¯Ø®Ù Ø±ÙØ² Ø§ÙÙØ³ÙÙØ©
basket.page.footer.promoCode.got.promocode.text.popup = ÙÙ ÙØ¯ÙÙ Ø±ÙØ² ØªØ±ÙÙØ¬ÙØ
basket.page.footer.applyIt  = ÙØ±Ø¬Ù Ø¨Ø§Ø³ØªØ®Ø¯Ø§ÙÙ
basket.page.footer.fromHere  = ÙÙØ§
basket.page.footer.payment.payWays  = Ø·Ø±Ù Ø§ÙØ¯ÙØ¹:
basket.page.footer.contact.needHelp  = Ø¨Ø­Ø§Ø¬Ø© Ø¥ÙÙ ÙØ³Ø§Ø¹Ø¯Ø©Ø ÙØ­Ù ÙÙØ§ ÙØ£Ø¬ÙÙ.
basket.page.footer.contact.callCustomer  = Ø§ØªØµÙ Ø¨ÙØ³Ù Ø®Ø¯ÙØ© Ø§ÙØ¹ÙÙØ§Ø¡ Ø¹ÙÙ Ø§ÙØ±ÙÙ
basket.page.footer.contact.emailCustomer  = Ø§Ø±Ø³Ù Ø±Ø³Ø§ÙØ© Ø¥ÙÙØªØ±ÙÙÙØ© ÙÙØ³Ù Ø®Ø¯ÙØ© Ø§ÙØ¹ÙÙØ§Ø¡
basket.page.multiple.quantities  = ÙÙÙØ§Øª ÙØªØ¹Ø¯Ø¯Ø©
basket.page.undo.text  = ØªÙ Ø­Ø°ÙÙ ÙÙ Ø³ÙÙØ© Ø§ÙØªØ³ÙÙ
basket.page.undo  = Ø±Ø¬ÙØ¹
basket.page.still.need.free.shipping.first.string = ?ØªØ±ÙØ¯ Ø´Ø­Ù ÙØ¬Ø§ÙÙØ£Ø¶Ù Ø¬.Ù.\u0020
basket.page.still.need.free.shipping.last.string = \u0020ÙØ·ÙØ¨Ù
basket.page.free.shipping.checkout.now = ÙÙØ¯ Ø­ØµÙØª Ø¹ÙÙ Ø´Ø­Ù ÙØ¬Ø§ÙÙ! ØªØ§Ø¨Ø¹ Ø¹ÙÙÙØ© Ø§ÙØ´Ø±Ø§Ø¡
basket.page.free.shipping.charges.below139 = 139
basket.page.free.shipping.checkout.now.above200 = 200
basket.page.still.need.free.shipping.first.value= 140
basket.page.still.need.free.shipping.last.value= 199
basket.page.free.shipping.charges.over.egp200 = Ø´Ø­Ù ÙØ¬Ø§ÙÙØ§ÙØ·ÙØ¨Ø§Øª Ø¨Ø£ÙØ«Ø± 200 Ø¬.Ù.
apply.voucher.message.success  = ÙØ¨Ø±ÙÙ! ÙÙØ¯ ØªÙ ØªØ·Ø¨ÙÙ Ø±ÙØ² Ø§ÙÙØ³ÙÙØ© Ø§ÙØ®Ø§ØµØ© Ø¨Ù.
apply.voucher.message.failure  = Ø¹Ø°Ø±ÙØ§! ÙØ§ ÙÙÙÙ Ø§Ø³ØªØ¹ÙØ§Ù Ø±ÙØ² Ø§ÙÙØ³ÙÙØ© Ø§ÙØ°Ù ÙØ¯ÙØªÙ.
release.voucher.message.success  = ÙØ¨Ø±ÙÙ! ÙÙØ¯ ØªÙ Ø¥ØµØ¯Ø§Ø± Ø±ÙØ² Ø§ÙÙØ³ÙÙØ© Ø§ÙØ®Ø§ØµØ© Ø¨Ù.
release.voucher.message.failure  = Ø¹Ø°Ø±ÙØ§! ÙÙ ÙØªÙ Ø¥ØµØ¯Ø§Ø± Ø±ÙØ² Ø§ÙÙØ³ÙÙØ© Ø§ÙØ®Ø§ØµØ© Ø¨Ù.
apply.voucher.invalid.vouchercode  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø±ÙØ² ÙØ³ÙÙØ© ØµØ­ÙØ­
voucher.apply.limit.exceeded  = ÙÙØ¯ ØªØ¬Ø§ÙØ²Øª Ø§ÙØ­Ø¯ Ø§ÙØ£ÙØµÙ Ø§ÙÙØ³ÙÙØ­ ÙØ¹Ø¯Ø¯ Ø§ÙÙØ³Ø§Ø¦Ù ÙÙØ·ÙØ¨ Ø§ÙÙØ§Ø­Ø¯.
voucher.not.found  = Ø§ÙÙØ³ÙÙØ© ØºÙØ± ÙÙØ¬ÙØ¯Ø©
voucher.cannot.be.redeemed  = ÙØ§ ÙÙÙÙ Ø§Ø³ØªØ¨Ø¯Ø§Ù Ø§ÙÙØ³ÙÙØ©
orderEntry.qty  = Ø§ÙÙÙÙØ©:
orderEntry.price  = Ø§ÙØ³Ø¹Ø±:
smallcart.itemsInCart  = ÙØ¯ÙÙ {0} ÙÙØªØ¬Ø§Øª ÙÙ Ø³ÙØ© Ø§ÙØªØ³ÙÙ Ø§ÙØ®Ø§ØµØ© Ø¨Ù
smallcart.itemInCart  = ÙØ¯ÙÙ ÙÙØªØ¬ ÙØ§Ø­Ø¯ ÙÙ Ø³ÙØ© Ø§ÙØªØ³ÙÙ Ø§ÙØ®Ø§ØµØ© Ø¨Ù
smallcart.show.all.details  = Ø¹Ø±Ø¶ ÙÙ Ø§ÙØªÙØ§ØµÙÙ
smallcart.hide.all.details  = Ø¥Ø®ÙØ§Ø¡ Ø§ÙØªÙØ§ØµÙÙ
smallcart.viewAll  = Ø¹Ø±Ø¶ Ø¬ÙÙØ¹ Ø§ÙØªÙØ§ØµÙÙ
appliedVouchers.gotaPromocode  = ÙÙ ÙØ¯ÙÙ Ø±ÙØ² ØªØ±ÙÙØ¬ÙØ
appliedVouchers.fromhere  = Ø§Ø³ØªØ®Ø¯ÙÙ Ø§ÙØ¢Ù
appliedVouchers.gotanotherPromocode  = ÙÙ ÙØ¯ÙÙ Ø±ÙØ² ØªØ±ÙÙØ¬Ù Ø¢Ø®Ø±Ø
appliedVouchers.promocodeapplied  = ØªÙ Ø§Ø³ØªØ®Ø¯Ø§Ù Ø§ÙØ±ÙØ² Ø§ÙØªØ±ÙÙØ¬Ù
appliedVouchers.remove  = Ø­Ø°Ù
appliedVouchers.applyithere  = ØªØ·Ø¨ÙÙ
appliedVouchers.apply  = ØªØ·Ø¨ÙÙ
invalid.expired.promo.code  = Ø§ÙÙØ³ÙÙØ© {0} ØºÙØ± ØµØ­ÙØ­Ø© Ø£Ù Ø£ÙÙØ§ ÙÙØªÙÙØ© Ø§ÙØµÙØ§Ø­ÙØ©.
appliedVouchers.retry  =Ø£Ø¹Ø¯ Ø§ÙÙØ­Ø§ÙÙØ©
appliedVouchers.questionmark  =
cart.product.discountinued.failure  = ÙÙØ£Ø³Ù ÙÙ ÙØ¹Ø¯ ÙØ°Ø§ Ø§ÙÙÙØªØ¬ ÙØªÙÙØ±ÙØ§.
basket.enter.promo.code  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø§ÙØ±ÙØ² Ø§ÙØªØ±ÙÙØ¬Ù
basket.enter.apply.voucher= Ø§Ø³ØªØ®Ø¯Ù Ø§ÙÙØ³ÙÙØ©
cartItem.color  = Ø§ÙÙÙÙ:
cartItem.size  = Ø§ÙÙÙØ§Ø³:
multiproductpromo.plus  = +
multiproductpromo.more  = Ø§ÙÙØ²ÙØ¯
multiproductpromo.show.details  = <span> Ø¥Ø¸ÙØ§Ø± </span>Ø§ÙØªÙØ§ØµÙÙ
landmarkCategoryPage.filter.size  = Ø§ÙÙÙØ§Ø³
landmarkCategoryPage.filter.view.all  = Ø¥Ø¸ÙØ§Ø± Ø§ÙÙØ²ÙØ¯
landmarkCategoryPage.filter.Less  = Ø¥Ø¸ÙØ§Ø±
landmarkCategoryPage.filter.brands  = Ø£ÙÙ
landmarkCategoryPage.brand  = Ø¹ÙØ§ÙØ§Øª ØªØ¬Ø§Ø±ÙØ©
landmarkCategoryPage.filter.colour  = Ø§ÙÙÙÙ
landmarkCategoryPage.apply  = ØªØ·Ø¨ÙÙ
landmarkCategoryPage.sortby  = Ø§ÙØªØ±ØªÙØ¨ Ø¨Ø­Ø³Ø¨:
landmarkCategoryPage.sortbyM  = Ø§ÙØªØ±ØªÙØ¨ Ø¨Ø­Ø³Ø¨:
landmarkCategoryPage.index.relevance  = ÙØ¯Ù Ø§ÙØµÙØ©
landmarkCategoryPage.index.alphabetical  =  Ø§ÙØ­Ø±ÙÙ Ø§ÙØ£Ø¨Ø¬Ø¯ÙØ©
landmarkCategoryPage.index.low.to.high  = Ø§ÙØ³Ø¹Ø± - ÙÙ Ø§ÙØ£ÙÙ ÙÙØ£Ø¹ÙÙ
landmarkCategoryPage.index.high.to.low  = Ø§ÙØ³Ø¹Ø± - ÙÙ Ø§ÙØ£Ø¹ÙÙ Ø¥ÙÙ Ø§ÙØ£ÙÙ
landmarkCategoryPage.index.recent.products  =Ø§ÙØ£Ø­Ø¯Ø«
landmarkCategoryPage.index.discount = Ø§ÙØ®ØµÙ
landmarkSearchPage.index.numeric = Ø§ÙØ£Ø±ÙØ§Ù
landmarkSearchPage.index.alpha = Ø§ÙØ­Ø±ÙÙ
landmarkCategoryPage.narrowby  = Ø­ØµØ± ÙØªØ§Ø¦Ø¬ Ø§ÙØ¨Ø­Ø« Ø¨Ø­Ø³Ø¨
landmarkCategoryPage.filters  = ØªØµÙÙÙØ§Øª
landmarkCategoryPage.filter.clearall  = ÙØ³Ø­ Ø¬ÙÙØ¹ Ø§ÙØªØµÙÙÙØ§Øª
landmarkCategoryPage.filter.price  = Ø§ÙØ³Ø¹Ø±
landmarkCategoryPage.product  = Ø§ÙÙÙØªØ¬
landmarkCategoryPage.products  = Ø§ÙÙÙØªØ¬Ø§Øª
landmarkCategoryPage.categories  = Ø§ÙÙØ¦Ø§Øª
landmarkCategoryPage.close  = Ø¥ØºÙØ§Ù
landmarkCategoryPage.items3  = Ø«ÙØ§Ø« ÙØ·Ø¹
landmarkCategoryPage.items5  = Ø®ÙØ³ ÙØ·Ø¹
landmarkCategoryPage.title  = Ø§ÙØªØ³ÙÙ Ø£ÙÙÙØ§ÙÙ ÙÙ LandmarkShops.in
landmarkCategoryPage.text.clearance  = ØªØµÙÙØ§Øª
landmarkCategoryPage.quickview.notify.subscribed  = Ø³ÙÙÙÙ Ø¨Ø¥Ø¨ÙØ§ØºÙ ÙÙØ± ØªÙÙÙØ±Ù ÙØ¬Ø¯Ø¯ÙØ§ ÙÙ Ø§ÙÙØ®Ø²ÙÙ.
landmarkCategoryPage.show.more.colors  = + Ø§ÙÙØ²ÙØ¯ ÙÙ Ø§ÙØ£ÙÙØ§Ù...
landmarkCategoryPage.text.clearance.subheading  = Ø§ÙØ­ØµÙÙ Ø¹ÙÙ Ø®ØµÙÙØ§Øª ÙØ°ÙÙØ© ÙÙ ÙÙÙ ÙÙ Ø£ÙØ§Ù Ø§ÙØ³ÙØ©.
payment.zero.payment.message  = Ø±Ø§Ø¦Ø¹! ÙØ°Ø§ ÙØºØ·Ù Ø§ÙØªÙÙÙØ© Ø§ÙØ¥Ø¬ÙØ§ÙÙØ© ÙÙØ·ÙØ¨ Ø§ÙØ®Ø§Øµ Ø¨Ù.
paymentPage.year.format  = Ø§ÙØ³ÙØ©
paymentPage.month.format  = Ø§ÙØ´ÙØ±
paymentPage.comodoMessage  = Ø¨ÙØ§ÙØ§Øª Ø¨Ø·Ø§ÙØ© Ø§ÙØ§Ø¦ØªÙØ§Ù Ø§ÙØ®Ø§ØµØ© Ø¨Ù ÙØ´ÙØ±Ø© Ø¨ØµÙØ±Ø© Ø¢ÙÙØ© ØªÙØ§ÙÙØ§ ÙÙØªÙ ØªÙØ¬ÙÙÙØ§ ÙØ¨Ø§Ø´Ø±Ø©Ù ÙØ¨ÙØ§Ø¨Ø© Ø§ÙØ¯ÙØ¹ ÙØ¯ÙÙØ§ Ø§ÙÙØªÙØ§ÙÙØ© ÙØ¹ Ø§ÙÙØ¹ÙØ§Ø± PCI DSS ÙÙØªÙÙÙØ° ÙØ§ÙÙØ¹Ø§ÙØ¬Ø©. ÙÙØ§ Ø£ÙÙØ§ ÙØ§ ÙØ­ØªÙØ¸ Ø¶ÙÙ Ø³Ø¬ÙØ§ØªÙØ§ Ø³ÙÙ Ø¨Ø§ÙØ£Ø±ÙØ§Ù Ø§ÙØ£Ø±Ø¨Ø¹Ø© Ø§ÙØ£Ø®ÙØ±Ø© ÙÙ Ø±ÙÙ Ø¨Ø·Ø§ÙØ© Ø§ÙØ§Ø¦ØªÙØ§Ù Ø§ÙØ®Ø§ØµØ© Ø¨Ù ÙØ¨ØªØ§Ø±ÙØ® Ø§ÙØªÙØ§Ø¡ ØµÙØ§Ø­ÙØªÙØ§. ØªØ¹Ø§ÙÙØ§ØªÙ Ø¶ÙÙ ÙØ°Ù Ø§ÙØµÙØ­Ø© ÙØ¤ÙÙØ© ÙÙØ­ÙÙØ© Ø¨Ø§Ø³ØªØ®Ø¯Ø§Ù Ø´ÙØ§Ø¯Ø© SSL Ø§ÙÙØ¤ÙÙØ© Ø¥ÙØ§ ÙÙ 256-bit Ø£Ù 128-bit Ø¨Ø­Ø³Ø¨ Ø¥ØµØ¯Ø§Ø± Ø§ÙÙØªØµÙØ­ Ø§ÙØ°Ù ØªØ³ØªØ¹ÙÙÙ.
paymentPage.selectPayentMethod  = Ø§Ø®ØªØ± Ø·Ø±ÙÙØ© Ø§ÙØ¯ÙØ¹
paymentPage.selectPayentMethod.Credit.Debit.Card=Ø¨Ø·Ø§ÙØ© Ø§ÙØ§Ø¦ØªÙØ§Ù/Ø§ÙØ®ØµÙ Ø§ÙÙØ¨Ø§Ø´Ø±
paymentPage.selectPayentMethod.Payusing.Mastercard.Visacards=Ø§Ø¯ÙØ¹ Ø¨Ø§Ø³ØªØ®Ø¯Ø§Ù Ø¨Ø·Ø§ÙØ§Øª ÙØ§Ø³ØªØ±ÙØ§Ø±Ø¯ Ø£Ù ÙÙØ²Ø§
paymentPage.continueBtn  = Ø§ÙÙØªØ§Ø¨Ø¹Ø©
paymentPage.addNewCard  = Ø¥Ø¶Ø§ÙØ© Ø¨Ø·Ø§ÙØ© Ø¬Ø¯ÙØ¯Ø©
paymentPage.saveThisCard  = Ø­ÙØ¸ ÙØ°Ù Ø§ÙØ¨Ø·Ø§ÙØ©
paymentPage.cardNumber  = Ø±ÙÙ Ø§ÙØ¨Ø·Ø§ÙØ©
paymentPage.enter16DigNumber  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø±ÙÙ Ø¨Ø·Ø§ÙØªÙ Ø§ÙÙÙÙÙ ÙÙ 16 Ø±ÙÙÙØ§
paymentPage.nameOnCard  = Ø§ÙØ§Ø³Ù Ø¹ÙÙ Ø§ÙØ¨Ø·Ø§ÙØ©
paymentPage.expiryDate  = ØªØ§Ø±ÙØ® Ø§ÙØªÙØ§Ø¡ Ø§ÙØ¨Ø·Ø§ÙØ©
paymentPage.cvv  = Ø§ÙØ±ÙØ² Ø§ÙØ³Ø±Ù
paymentPage.saveCard  = Ø­ÙØ¸ ÙØ°Ù Ø§ÙØ¨Ø·Ø§ÙØ© ÙØ¥ÙØ¬Ø§Ø² Ø¹ÙÙÙØ§Øª Ø§ÙØ¯ÙØ¹ Ø¨ØµÙØ±Ø©Ù Ø£Ø³Ø±Ø¹
paymentPage.doNotSaveCVV  = ÙØ¹ØªÙØ¯ Ø£Ø³ÙÙØ¨ Ø§ÙØªØ´ÙÙØ± SSL 256-bitØ Ø§ÙØ£ÙØ¶Ù Ø¶ÙÙ Ø§ÙÙØ¦Ø© Ø°Ø§Øª Ø§ÙØµÙØ©Ø ÙØ°ÙÙ ÙÙ Ø£Ø¬Ù Ø¶ÙØ§Ù Ø­ÙØ§ÙØ© ÙØªØ£ÙÙÙ ØªØ¬Ø±Ø¨Ø© Ø§ÙØªØ³ÙÙ.
paymentPage.beSure  = ÙÙØ§ ÙÙØ¸Ù Ø¥Ø­Ø¯Ù Ø£ÙØ¶Ù Ø§ÙÙÙØ§Ø±Ø³Ø§Øª Ø§ÙÙØªØ¨Ø¹Ø© Ø¹ÙÙ Ø§ÙÙØ³ØªÙÙ Ø§ÙØ¹Ø§ÙÙÙ ÙØ§ÙÙØ¹Ø±ÙÙØ© Ø¨Ø§Ø³Ù "tokenization"Ø ÙØ§ÙØªÙ ÙÙÙÙ ÙÙÙØ§ Ø¨Ø­ÙØ¸ Ø¢Ø®Ø± Ø£Ø±Ø¨Ø¹Ø© Ø£Ø±ÙØ§Ù ÙÙ Ø¨Ø·Ø§ÙØªÙ ÙÙØ· Ø¥ÙÙ Ø¬Ø§ÙØ¨ Ø®Ø§ÙØªÙ Ø§ÙØ´ÙØ± ÙØ§ÙØ³ÙØ© ÙÙ ØªØ§Ø±ÙØ® Ø§ÙØ§ÙØªÙØ§Ø¡.
paymentPage.worldClassEncryption  = ÙÙØ§ ÙÙÙÙ Ø¨Ø­ÙØ¸ Ø±ÙÙ CVV Ø§ÙØ®Ø§Øµ Ø¨Ø¨Ø·Ø§ÙØªÙ
paymentPage.wishToDelete  = ÙÙÙÙÙ Ø­Ø°Ù Ø¨Ø·Ø§ÙØªÙØ ÙÙ Ø­Ø§Ù Ø±ØºØ¨Øª Ø¨Ø°ÙÙØ ÙÙ ÙØ³Ù "Ø§ÙØ¯ÙØ¹" Ø¶ÙÙ ÙØ§Ø¦ÙØ© "Ø­Ø³Ø§Ø¨Ù".
paymentPage.useAsDefaultCard  = ÙØ±Ø¬Ù Ø§Ø³ØªØ®Ø¯Ø§Ù ÙØ°Ù Ø§ÙØ¨Ø·Ø§ÙØ© ÙØ§Ø®ØªÙØ§Ø± Ø£Ø³Ø§Ø³Ù
paymentPage.billingAddress  = Ø¹ÙÙØ§Ù Ø§Ø³ØªÙØ§Ù Ø§ÙÙØ§ØªÙØ±Ø©
paymentPage.addNewAddressLink  = + Ø¥Ø¶Ø§ÙØ© Ø¹ÙÙØ§Ù Ø¬Ø¯ÙØ¯
paymentPage.chooseBillingAddress  = ÙØ±Ø¬Ù Ø§Ø®ØªÙØ§Ø± Ø¹ÙÙØ§Ù Ø§Ø³ØªÙØ§Ù Ø§ÙÙØ§ØªÙØ±Ø©
paymentPage.noSavedBillingAddress  = ÙØ¨Ø¯Ù Ø£Ù ÙÙØ³ ÙØ¯ÙÙ Ø¹ÙÙØ§Ù ÙØ­ÙÙØ¸ ÙØ§Ø³ØªÙØ§Ù Ø§ÙÙØ§ØªÙØ±Ø©
paymentPage.selectACard  = ÙØ±Ø¬Ù Ø§Ø®ØªÙØ§Ø± Ø§ÙØ¨Ø·Ø§ÙØ©
paymentPage.selectACard.error  = ÙØ±Ø¬Ù Ø§Ø®ØªÙØ§Ø± Ø§ÙØ¨Ø·Ø§ÙØ©
paymentPage.EnterCVV  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø±ÙÙ CVV
paymentPage.cvvNumber  = Ø±ÙÙ CVV
paymentPage.thanksForCOD  = <span> Ø·Ø±ÙÙØ© Ø¹ÙÙÙØ§: </span>
paymentPage.callToVerify  = ÙÙÙÙ ÙØ¨Ù Ø´Ø­Ù Ø·ÙØ¨Ù Ø¨Ø§ÙØ§ØªØµØ§Ù Ø¨Ù ÙØªØ£ÙÙØ¯Ù.
paymentPage.deliveryPartner  = Ø«Ù ÙÙÙÙ Ø´Ø±ÙÙÙØ§ ÙØ®Ø¯ÙØ§Øª Ø§ÙØªÙØµÙÙ Ø¨ØªÙØµÙÙ Ø§ÙØ·ÙØ¨ ÙØ§Ø³ØªÙØ§Ù Ø§ÙÙØ¨ÙØº Ø§ÙÙÙØ¯Ù.
paymentPage.remeberToKeepCash  = ÙØ±Ø¬Ù Ø§ÙØªØ£ÙØ¯ ÙÙ Ø¥Ø¨ÙØ§Ø¡ Ø§ÙÙØ¨ÙØº Ø§ÙÙØ·ÙÙØ¨ Ø¬Ø§ÙØ²ÙØ§.
paymentPage.ifYouWantToCall  = ÙÙÙ Ø­Ø§Ù Ø±ØºØ¨Øª Ø¨Ø¥Ø±Ø¬Ø§Ø¹ Ø·ÙØ¨Ù ÙØ£Ù Ø³Ø¨Ø¨Ù ÙØ§ÙØ ÙØ±Ø¬Ù Ø§ÙØ§ØªØµØ§Ù Ø¨ÙØ§ Ø¹ÙÙ Ø§ÙØ±ÙÙ <strong>  1555-123-1800 </strong> ÙØ³ÙØ³Ø¹Ø¯ÙØ§ ÙØ³Ø§Ø¹Ø¯ØªÙ.
paymentPage.chooseABank  = ÙØ±Ø¬Ù Ø§Ø®ØªÙØ§Ø± Ø§ÙØ¨ÙÙ
paymentPage.chooseYourBank  = ÙØ±Ø¬Ù Ø§Ø®ØªÙØ§Ø± Ø§ÙØ¨ÙÙ Ø§ÙØ°Ù ØªØªØ¹Ø§ÙÙ ÙØ¹Ù
paymentPage.showMyAddress  = Ø¥Ø¸ÙØ§Ø± Ø¹ÙÙØ§ÙÙ
paymentPage.addABillingAddress  = Ø¥Ø¶Ø§ÙØ© Ø¹ÙÙØ§Ù Ø§Ø³ØªÙØ§Ù ÙØ§ØªÙØ±Ø©
paymentPage.addNewBillingAddress  = Ø¥Ø¶Ø§ÙØ© Ø¹ÙÙØ§Ù Ø§Ø³ØªÙØ§Ù ÙØ§ØªÙØ±Ø© Ø¬Ø¯ÙØ¯
paymentPage.fullName  = Ø§ÙØ§Ø³Ù Ø¨Ø§ÙÙØ§ÙÙ
paymentPage.fullNamePlaceHolder  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø§Ø³ÙÙ Ø¨Ø§ÙÙØ§ÙÙ
paymentPage.addressLine1  = Ø¹ÙÙØ§Ù 1
paymentPage.addressLine1PlaceHolder  = Ø±ÙÙ Ø§ÙØ´ÙØ© Ø£Ù Ø§ÙÙÙØ²ÙØ ÙØ¹ Ø§ÙØ·Ø§Ø¨Ù ÙØ§ÙØ¨ÙØ§Ø¡
paymentPage.addressLine2  = Ø¹ÙÙØ§Ù 2 (Ø§Ø®ØªÙØ§Ø±Ù)
paymentPage.addressLine2PlaceHolder  = Ø§ÙÙØ­Ø§ÙØ¸Ø© ÙØ§ÙØ´Ø§Ø±Ø¹ ÙØ§ÙØ­Ù
paymentPage.town  = Ø§ÙÙØ¯ÙÙØ© Ø£Ù Ø§ÙØ¨ÙØ¯Ø©
paymentPage.state  = Ø§ÙÙØ­Ø§ÙØ¸Ø©
paymentPage.selectCity  = ÙØ±Ø¬Ù Ø§Ø®ØªÙØ§Ø± Ø§ÙÙØ¯ÙÙØ© Ø£Ù Ø§ÙØ¨ÙØ¯Ø©
paymentPage.selectState  = ÙØ±Ø¬Ù Ø§Ø®ØªÙØ§Ø± Ø§ÙÙØ­Ø§ÙØ¸Ø©
paymentPage.useAsDefaultShipping  = ÙØ±Ø¬Ù Ø§Ø³ØªØ®Ø¯Ø§ÙÙ Ø¹ÙÙ Ø£ÙÙ Ø§ÙØ¹ÙÙØ§Ù Ø§ÙØ£Ø³Ø§Ø³Ù ÙÙØ´Ø­Ù
paymentPage.addThisAddress  = Ø¥Ø¶Ø§ÙØ© Ø¹ÙÙØ§Ù Ø¬Ø¯ÙØ¯
paymentPage.select.bankname  = ÙØ±Ø¬Ù ØªØ­Ø¯ÙØ¯ Ø§Ø³Ù Ø§ÙØ¨ÙÙ
payment.generic.failure  = Ø¹Ø°Ø±ÙØ§! Ø­Ø¯Ø« Ø®Ø·Ø£ ÙØ§. ÙØ±Ø¬Ù Ø¥Ø¹Ø§Ø¯Ø© Ø§ÙÙØ­Ø§ÙÙØ©
checkoutPages.shippingStep  = Ø§ÙØ´Ø­Ù ÙØ§ÙØªÙØµÙÙ
checkoutPages.paymentStep  = Ø§ÙØ¯ÙØ¹
checkoutPages.orderConfirmation  = ØªØ³Ø¬ÙÙ Ø§ÙØ·ÙØ¨
payment.valid.card.error.data  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø±ÙÙ Ø¨Ø·Ø§ÙØ© Ø³Ø§Ø±ÙØ© Ø§ÙÙÙØ¹ÙÙ.
payment.enter.cvv.data  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù 3 Ø£Ø±ÙØ§Ù Ø¹ÙÙ Ø§ÙØ£ÙÙ.
payment.valid.card.error  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø±ÙÙ Ø¨Ø·Ø§ÙØªÙ Ø§ÙÙÙÙÙ ÙÙ 16 Ø±ÙÙÙØ§
payment.valid.card.error1 = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø±ÙÙ Ø¨Ø·Ø§ÙØªÙ Ø§ÙÙÙÙÙ ÙÙ true Ø±ÙÙÙØ§
payment.valid.card.required.length  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø±ÙÙ CVV ØµØ­ÙØ­
payment.enter.your.name  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø§Ø³ÙÙ
payment.select.month  = ÙØ±Ø¬Ù Ø§Ø®ØªÙØ§Ø± Ø´ÙØ± ØµØ­ÙØ­
payment.select.year  = ÙØ±Ø¬Ù Ø§Ø®ØªÙØ§Ø± Ø³ÙØ© ØµØ­ÙØ­Ø©
payment.enter.cvv  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø±ÙÙ Ø§ÙØ±ÙØ² Ø§ÙØ³Ø±Ù
payment.enter.fullname  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø§Ø³ÙÙ Ø¨Ø§ÙÙØ§ÙÙ
payment.enter.fullname1  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø§Ø³ÙÙ
payment.select.billing.address  = ÙØ±Ø¬Ù Ø§Ø®ØªÙØ§Ø± Ø¹ÙÙØ§Ù Ø§Ø³ØªÙØ§Ù Ø§ÙÙØ§ØªÙØ±Ø©
payment.card.expired  = ÙØ±Ø¬Ù Ø§Ø®ØªÙØ§Ø± ØªØ§Ø±ÙØ® Ø§ÙØªÙØ§Ø¡ ØµÙØ§Ø­ÙØ© ØµØ­ÙØ­
payment.mode.credit.card.not.supported  = ÙØ¹ØªØ°Ø±Ø ÙØ·Ø±ÙÙØ© Ø§ÙØ¯ÙØ¹ ÙØ°Ù ØºÙØ± ÙØ¯Ø¹ÙÙØ© ÙØ¯ÙÙØ§. ÙØ±Ø¬Ù Ø§Ø®ØªÙØ§Ø± Ø·Ø±ÙÙØ© Ø£Ø®Ø±Ù.
payment.mode.not.supported  = ÙØ¹ØªØ°Ø±Ø ÙØ·Ø±ÙÙØ© Ø§ÙØ¯ÙØ¹ ÙØ°Ù ØºÙØ± ÙØ¯Ø¹ÙÙØ© ÙØ¯ÙÙØ§. ÙØ±Ø¬Ù Ø§Ø®ØªÙØ§Ø± Ø·Ø±ÙÙØ© Ø£Ø®Ø±Ù.
cod.exceed.threshold.price  = Ø¹ÙÙÙØ§Ø ÙÙØ¯ ØªØ¬Ø§ÙØ² Ø³Ø¹Ø± Ø§ÙØ·ÙØ¨ Ø§ÙØ­Ø¯ Ø§ÙØ£ÙØµÙ Ø§ÙÙØ³ÙÙØ­ ÙØ®ÙØ§Ø± Ø§ÙØ¯ÙØ¹ Ø§ÙÙÙØ¯Ù Ø¹ÙØ¯ Ø§ÙØ§Ø³ØªÙØ§Ù. ÙØ±Ø¬Ù Ø­Ø°Ù ÙØ·Ø¹Ø© Ø£Ù Ø§Ø«ÙØªÙÙ ÙÙ Ø§ÙØ·ÙØ¨ ÙØ¥Ø¹Ø§Ø¯Ø© Ø§ÙÙØ­Ø§ÙÙØ©.
payment.address.enter.name  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø§Ø³ÙÙ
payment.address.enter.line1  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø§ÙØ¹ÙÙØ§Ù 1
payment.address.enter.line2  = ÙØ±Ø¬Ù Ø§ÙØ¹ÙÙØ§Ù 2
payment.address.enter.town  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø§ÙÙØ¯ÙÙØ© Ø£Ù Ø§ÙØ¨ÙØ¯Ø©
payment.address.select.address  = ÙØ±Ø¬Ù ØªØ­Ø¯ÙØ¯ Ø§ÙÙØ­Ø§ÙØ¸Ø©
paymentPage.expire  = ØªÙØªÙÙ Ø§ÙØµÙØ§Ø­ÙØ©
paymentPage.expires  = ØªÙØªÙÙ Ø§ÙØµÙØ§Ø­ÙØ© ÙÙ
payment.address.select.state  = ÙØ±Ø¬Ù ØªØ­Ø¯ÙØ¯ Ø§ÙÙØ­Ø§ÙØ¸Ø©
fastpay.signin.href.tag  = Ø§ÙØ¯ÙØ¹ Ø¨ÙØ§Ø³Ø·Ø© Ø®Ø¯ÙØ© Ø§ÙØ¯ÙØ¹ Ø§ÙØ³Ø±ÙØ¹
fastpay.quantity.minvalue  = ÙØ±Ø¬Ù ØªØ¹Ø¯ÙÙ Ø§ÙÙÙÙØ©
fastpay.quantity.maxvalue  = ÙØ±Ø¬Ù ØªØ¹Ø¯ÙÙ Ø§ÙÙÙÙØ©
fastpay.quantity.required  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø§ÙÙÙÙØ©
fastpay.quantity.validvalue  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø±ÙÙ ØµØ­ÙØ­ ÙÙÙÙÙØ©
fastpay.turnon.href  = ØªÙØ¹ÙÙ
fastpay.turnon.tag  = Ø§ÙØ¯ÙØ¹ Ø§ÙØ³Ø±ÙØ¹
fastpay.cart.threshold.breach  = ÙÙØ¯ ØªØ¬Ø§ÙØ²Øª Ø§ÙØ­Ø¯ Ø§ÙØ£ÙØµÙ ÙÙÙÙÙØ© Ø§ÙÙØ³ÙÙØ­ Ø¨ÙØ§
fastpay.cart.lowstock  = Ø§ÙÙÙÙØ© Ø§ÙØªÙ Ø·ÙØ¨ØªÙØ§ ØºÙØ± ÙØªÙÙØ±Ø©
fastpay.pageReloaded  = Ø¹ÙÙÙØ§Ø ÙØ¹ØªØ°Ø± ÙØ­Ø¯ÙØ« Ø®Ø·Ø£ ÙØ§ ÙÙ Ø¬ÙØªÙØ§Ø ÙØ±Ø¬Ù Ø¥Ø¹Ø§Ø¯Ø© Ø§ÙÙØ­Ø§ÙÙØ© ÙØ³ØªØªÙ Ø§ÙØ¹ÙÙÙØ© Ø¨Ø§ÙØµÙØ±Ø© Ø§ÙØµØ­ÙØ­Ø©.
fastpay.system.error  = Ø¹Ø°Ø±ÙØ§! Ø­Ø¯Ø« Ø®Ø·Ø£ ÙØ§. ÙØ±Ø¬Ù Ø¥Ø¹Ø§Ø¯Ø© Ø§ÙÙØ­Ø§ÙÙØ©.
fastpay.text.payment.error.cod.exceed.threshold  = Ø¹ÙÙÙØ§. ÙØ§ ÙÙÙÙ Ø§Ø³ØªØ®Ø¯Ø§Ù Ø§ÙØ¯ÙØ¹ Ø§ÙØ³Ø±ÙØ¹ ÙÙÙÙÙ ØªØ¬Ø§ÙØ²Øª Ø§ÙØ­Ø¯ Ø§ÙØ£ÙØµÙ ÙØ³Ø¹Ø± Ø§ÙØ·ÙØ¨ Ø§ÙÙØ³ÙÙØ­ ÙØ¹ Ø®ÙØ§Ø± Ø§ÙØ¯ÙØ¹ Ø¹ÙØ¯ Ø§ÙØ§Ø³ØªÙØ§Ù ÙØ§ÙØ°Ù ÙØ¨ÙØº {0} {1}. ÙØ§Ø³ØªÙÙØ§Ù Ø·ÙØ¨ÙØ   Ø§Ø³ØªØ®Ø¯Ù Ø²Ø± "Ø£Ø¶Ù Ø¥ÙÙ Ø§ÙØ³ÙØ©" Ø«Ù ÙÙ Ø¨Ø¥Ø¬Ø±Ø§Ø¡ Ø¹ÙÙÙØ© Ø§ÙØ¯ÙØ¹ ÙØ§ÙØ®Ø±ÙØ¬. Ø£Ù ÙÙÙÙÙ ØªØºÙÙØ± ØªÙØ¶ÙÙÙ ÙÙ Ø§ÙØ¯ÙØ¹ Ø¨ÙØ§Ø³Ø·Ø© Ø§ÙØ¯ÙØ¹ Ø§ÙØ³Ø±ÙØ¹ Ø¥ÙÙ Ø§ÙØ¯ÙØ¹ Ø¨Ø¨Ø·Ø§ÙØ§Øª Ø§ÙØ§Ø¦ØªÙØ§Ù Ø£Ù Ø¨Ø·Ø§ÙØ§Øª Ø§ÙØ®ØµÙ Ø§ÙÙØ¨Ø§Ø´Ø± ÙØ§Ø³ØªÙÙØ§Ù Ø·ÙØ¨Ù.
fastpay.freebundle.promotion.error  = ÙÙØ£Ø³Ù ÙÙ ÙØ¹Ø¯ ÙØ°Ø§ Ø§ÙØ¹Ø±Ø¶ ÙØªÙÙØ±ÙØ§.
pincode_notserved  = Ø¹Ø°Ø±ÙØ§Ø ÙØ§ ÙÙÙÙ Ø¨Ø§ÙØªÙØµÙÙ Ø¥ÙÙ ÙØ°Ø§ Ø§ÙØ¹ÙÙØ§Ù. ÙØ±Ø¬Ù Ø¥Ø¶Ø§ÙØ© Ø¹ÙÙØ§Ù Ø¬Ø¯ÙØ¯.
deliveryaddress_notfound  = Ø¹Ø°Ø±ÙØ§Ø Ø¹ÙÙØ§Ù Ø§ÙØªÙØµÙÙ ØºÙØ± ÙÙØ¬ÙØ¯.
paymentinfo_notfound  = Ø¹Ø°Ø±ÙØ§Ø Ø¨ÙØ§ÙØ§Øª Ø§ÙØ¯ÙØ¹ Ø§ÙØ®Ø§ØµØ© Ø¨Ù ØºÙØ± ÙÙØ¬ÙØ¯Ø©.
landmarkCategoryPage.pagination.previouspage  = Ø§ÙØµÙØ­Ø© Ø§ÙØ³Ø§Ø¨ÙØ©
landmarkCategoryPage.pagination.nextpage  = Ø§ÙØµÙØ­Ø© Ø§ÙØªØ§ÙÙØ©
landmarkCategoryPage.quickview  = ÙØ¸Ø±Ø© Ø³Ø±ÙØ¹Ø©
landmarkCategoryPage.quickview.viewfulldetails  = Ø¹Ø±Ø¶ Ø§ÙØªÙØ§ØµÙÙ Ø§ÙÙØ§ÙÙØ©
landmarkCategoryPage.filter.size				 = Ø§ÙÙÙØ§Ø³
landmarkCategoryPage.filter.view.all			 = Ø¹Ø±Ø¶ Ø§ÙØ¬ÙÙØ¹
landmarkCategoryPage.filter.Less				 = Ø¥Ø¸ÙØ§Ø± Ø¹Ø¯Ø¯ Ø£ÙÙ
landmarkCategoryPage.filter.brands				 = Ø§ÙØ¹ÙØ§ÙØ§Øª Ø§ÙØªØ¬Ø§Ø±ÙØ©
landmarkCategoryPage.brand						 = Ø§ÙØ¹ÙØ§ÙØ© Ø§ÙØªØ¬Ø§Ø±ÙØ©
landmarkCategoryPage.filter.color				 = Ø§ÙÙÙÙ
landmarkCategoryPage.apply						 = ØªØ·Ø¨ÙÙ
landmarkCategoryPage.sortby						 = Ø§ÙØªØ±ØªÙØ¨ Ø¨Ø­Ø³Ø¨
landmarkCategoryPage.sortbyM					 = Ø§ÙØªØ±ØªÙØ¨ Ø¨Ø­Ø³Ø¨
landmarkCategoryPage.index.relevance			 = ÙØ¯Ù Ø§ÙØµÙØ©
landmarkCategoryPage.index.alphabetical			 = ØªØ±ØªÙØ¨ Ø£Ø¨Ø¬Ø¯Ù
landmarkCategoryPage.index.low.to.high			 = Ø§ÙØ³Ø¹Ø± - ÙÙ Ø§ÙØ£ÙÙ Ø¥ÙÙ Ø§ÙØ£Ø¹ÙÙ
landmarkCategoryPage.index.high.to.low			 = Ø§ÙØ³Ø¹Ø± - ÙÙ Ø§ÙØ£Ø¹ÙÙ Ø¥ÙÙ Ø§ÙØ£ÙÙ
landmarkCategoryPage.narrowby					 = Ø­ØµØ± ÙØªØ§Ø¦Ø¬ Ø§ÙØ¨Ø­Ø« Ø¨Ø­Ø³Ø¨
landmarkCategoryPage.filters					 = Ø§ÙØªØµÙÙÙØ§Øª
landmarkCategoryPage.filter.clearall			 = ÙØ³Ø­ Ø¬ÙÙØ¹ Ø§ÙØªØµÙÙÙØ§Øª
landmarkCategoryPage.filter.price				 = Ø§ÙØ³Ø¹Ø±
landmarkCategoryPage.product					 = Ø§ÙÙÙØªØ¬
landmarkCategoryPage.products					 = ÙÙØªØ¬Ø§Øª
landmarkCategoryPage.categories					 = Ø§ÙÙØ¦Ø§Øª
landmarkCategoryPage.close						 = Ø¥ØºÙØ§Ù
landmarkCategoryPage.items3						 = Ø«ÙØ§Ø« ÙØ·Ø¹
landmarkCategoryPage.items5					     = Ø®ÙØ³ ÙØ·Ø¹
landmarkCategoryPage.title 						 = Ø§ÙØªØ³ÙÙ Ø£ÙÙØ§ÙÙ ÙÙ LandmarkShops.com
landMarkAddressBook.Nevermind.text = Ø±Ø¬ÙØ¹
landMarkPayment.header.user.payment.delete = Ø­Ø°Ù Ø¹ÙÙÙØ© Ø§ÙØ¯ÙØ¹
landMarkMyAccountPage.header.user.my.account  = Ø­Ø³Ø§Ø¨Ù
landMarkMyAccountPage.header.user.my.account.description  = Ø¥Ø¯Ø§Ø±Ø© ÙÙÙÙ Ø§ÙØ´Ø®ØµÙ ÙØ·ÙØ¨Ø§ØªÙ ÙÙØ§Ø¦ÙØªÙ Ø§ÙÙÙØ¶ÙÙØ© ÙØºÙØ± Ø°ÙÙ.
landMarkMyAccountPage.header.user.my.profile  = Ø§ÙÙÙÙ Ø§ÙØ´Ø®ØµÙ
landMarkMyAccountPage.header.user.my.profile.description  = Ø¥Ø¯Ø§Ø±Ø© Ø¨ÙØ§ÙØ§ØªÙ Ø§ÙØ´Ø®ØµÙØ©.
landMarkMyAccountPage.header.user.my.orders  = Ø·ÙØ¨Ø§ØªÙ
landMarkMyAccountPage.header.user.my.orders.description  = Ø¹Ø±Ø¶ ÙØªØªØ¨Ø¹ Ø·ÙØ¨Ø§ØªÙ
landMarkMyAccountPage.header.user.my.addressbook  = Ø¯ÙØªØ± Ø¹ÙØ§ÙÙÙÙ
landMarkMyAccountPage.header.user.my.addressbook.description  = ØªØ¹Ø¯ÙÙ Ø¹ÙØ§ÙÙÙ Ø§ÙØ´Ø­Ù ÙØ§Ø³ØªÙØ§Ù Ø§ÙÙÙØ§ØªÙØ±
landMarkMyAccountPage.header.user.my.payment  = Ø§ÙØ¯ÙØ¹
landMarkMyAccountPage.header.user.my.payment.description  = Ø¥Ø¯Ø§Ø±Ø© ØªÙØ¶ÙÙØ§Øª Ø¹ÙÙÙØ© Ø§ÙØ¯ÙØ¹.
landMarkMyAccountPage.header.user.my.payment.CreditOrDebit = Ø¨Ø·Ø§ÙØ§Øª Ø§ÙØ§Ø¦ØªÙØ§Ù Ø£Ù Ø§ÙØ®ØµÙ Ø§ÙÙØ¨Ø§Ø´Ø±
landMarkMyAccountPage.header.user.my.subscriptions  = Ø§ÙØªÙØ§ØµÙ
landMarkMyAccountPage.header.user.my.subscriptions.description  = ØªØ­Ø¯ÙØ¯ Ø§ÙØªÙØ¶ÙÙØ§Øª Ø§ÙØ®Ø§ØµØ© Ø¨Ø§ÙÙØ´Ø±Ø© Ø§ÙØ¥Ø®Ø¨Ø§Ø±ÙØ© ÙØ§ÙØ¨Ø±ÙØ¯ Ø§ÙØ¥ÙÙØªØ±ÙÙÙ.
landMarkMyAccountPage.header.user.my.reviews  = ØªÙÙÙÙØ§ØªÙ
landMarkMyAccountPage.header.user.my.reviews.description  = Ø¹Ø±Ø¶ ÙØ§ÙØ© ØªÙÙÙÙØ§ØªÙ.
landMarkMyAccountPage.header.user.my.fastPay  = Ø§ÙØ¯ÙØ¹ Ø§ÙØ³Ø±ÙØ¹
landMarkMyAccountPage.header.user.my.fastPay.description  = Ø§ÙØ§Ø³ØªÙØ§Ø¯Ø© ÙÙ ÙÙØ²Ø© Ø§ÙØ¯ÙØ¹ Ø¨Ø¶ØºØ·Ø© ÙØ§Ø­Ø¯Ø©
landMarkMyAccountPage.header.user.my.signout  = ØªØ³Ø¬ÙÙ Ø§ÙØ®Ø±ÙØ¬
landMarkMyAccountPage.header.user.my.signout.description  = ØªØ³Ø¬ÙÙ Ø§ÙØ®Ø±ÙØ¬ ÙÙ Ø­Ø³Ø§Ø¨Ù
landMarkMyAccountPage.header.user.my.wishlist  = ÙØ§Ø¦ÙØªÙ Ø§ÙÙÙØ¶ÙÙØ©
landMarkMyAccountPage.header.user.my.wishlist.description  = Ø¹Ø±Ø¶ Ø£ÙØ«Ø± Ø§ÙÙÙØªØ¬Ø§Øª Ø§ÙÙÙØ¶ÙÙØ© ÙÙ.
landMarkMyAccountPage.header.user.my.landmarkRewards.description  = Ø§ÙØ³Ø¨ ÙØ§ÙÙÙ ÙÙØ§Ø· Ø´ÙØ±ÙØ² ÙÙØªÙØªÙØ¹ Ø¨Ø®ØµÙÙØ§Øª ÙÙØ±ÙÙØ©
landMarkMyAccountPage.header.user.my.profile.fname  = Ø§ÙØ§Ø³Ù Ø§ÙØ§ÙÙ
landMarkMyAccountPage.header.user.my.profile.lname  = Ø§Ø³Ù Ø§ÙØ¹Ø§Ø¦ÙØ©
landMarkMyAccountPage.header.user.my.profile.email  = Ø§ÙØ¨Ø±ÙØ¯ Ø§ÙØ¥ÙÙØªØ±ÙÙÙ
landMarkMyAccountPage.header.user.my.profile.gender  = Ø§ÙÙÙØ¹
landMarkMyAccountPage.header.user.my.profile.gender.male  = Ø°ÙØ±
landMarkMyAccountPage.header.user.my.profile.gender.female  = Ø£ÙØ«Ù
landMarkMyAccountPage.header.user.my.profile.update.title  = ØªØ­Ø¯ÙØ« Ø§ÙÙÙÙ Ø§ÙØ´Ø®ØµÙ
lmgUpdateProfile.firstName.invalid  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø§ÙØ§Ø³Ù Ø§ÙØ£ÙÙ
lmgUpdateProfile.lastName.invalid  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø§Ø³Ù Ø§ÙØ¹Ø§Ø¦ÙØ©
lmgUpdateProfile.facebook.disconnet  = Ø¥ÙÙØ§Ù Ø§ÙØ±Ø¨Ø·
lmgUpdateProfile.facebook.connect  = &nbsp; Ø§ÙØ±Ø¨Ø· ÙØ¹ ÙÙØ³Ø¨ÙÙ
lmgUpdateProfile.facebook.connect.favourite.products  = Ø´Ø§Ø±Ù Ø§ÙÙÙØªØ¬Ø§Øª Ø§ÙÙÙØ¶ÙØ© ÙØ¯ÙÙ ÙØ¹ Ø§ÙØ£ÙÙ ÙØ§ÙØ£ØµØ¯ÙØ§Ø¡ Ø¹ÙÙ ÙÙØ³Ø¨ÙÙ. ÙÙ ÙÙÙÙ Ø¨ÙØ´Ø± Ø£Ù Ø´ÙØ¡ Ø¯ÙÙ Ø§ÙØ­ØµÙÙ Ø¹ÙÙ Ø¥Ø°Ù ÙØ³Ø¨Ù ÙÙÙ Ø¨Ø°ÙÙ.
lmgUpdateProfile.facebook.permission =
lmgUpdateProfile.facebook.connected.message = Ø´Ø§Ø±Ù Ø§ÙÙÙØªØ¬Ø§Øª Ø§ÙÙÙØ¶ÙØ© ÙØ¯ÙÙ ÙØ¹ Ø§ÙØ£ÙÙ ÙØ§ÙØ£ØµØ¯ÙØ§Ø¡ Ø¹ÙÙ ÙÙØ³Ø¨ÙÙ.
lmgUpdateProfile.profile.good  = Ø¬ÙØ¯
lmgUpdateProfile.email.invalid  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø¹ÙÙØ§Ù Ø¨Ø±ÙØ¯ Ø¥ÙÙØªØ±ÙÙÙ ØµØ­ÙØ­
lmgUpdateProfile.profile.genderType.required = ÙØ±Ø¬Ù Ø§Ø®ØªÙØ§Ø± Ø§ÙÙÙØ¹
landMarkMyAccountPage.header.user.my.profile.update.success  = ØªÙ Ø­ÙØ¸ Ø¨ÙØ§ÙØ§ØªÙ Ø§ÙØ¬Ø¯ÙØ¯Ø© Ø¨ÙØ¬Ø§Ø­
landMarkMyAccountPage.header.user.my.profile.changePassword  = ØªØºÙÙØ± ÙÙÙØ© Ø§ÙÙØ±ÙØ±
landMarkMyAccountPage.header.user.my.profile.oldPassword  = ÙÙÙØ© Ø§ÙÙØ±ÙØ± Ø§ÙÙØ¯ÙÙØ©
landMarkMyAccountPage.header.user.my.profile.newPassword  = ÙÙÙØ© Ø§ÙÙØ±ÙØ± Ø§ÙØ¬Ø¯ÙØ¯Ø©
landMarkMyAccountPage.header.user.my.profile.showPassword  = Ø¥Ø¸ÙØ§Ø±
landMarkMyAccountPage.header.user.my.profile.button.updatePassword  = ØªØºÙÙØ± ÙÙÙØ© Ø§ÙÙØ±ÙØ±
landMarkMyAccountPage.header.user.my.profile.currentPassword.mismatch  = ÙÙÙØ§Øª Ø§ÙÙØ±ÙØ± Ø§ÙØªÙ ÙÙØª Ø¨Ø¥Ø¯Ø®Ø§ÙÙØ§ ØºÙØ± ÙØªØ·Ø§Ø¨ÙØ©. ÙØ±Ø¬Ù Ø§ÙØªØ­ÙÙ ÙØ¥Ø¹Ø§Ø¯Ø© Ø§ÙÙØ­Ø§ÙÙØ©.
landMarkMyAccountPage.header.user.my.profile.picture  = ØµÙØ±Ø© ÙÙÙÙ Ø§ÙØ´Ø®ØµÙ
landMarkMyAccountPage.placeholder.user.my.profile.oldPassword  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù ÙÙÙØ© Ø§ÙÙØ±ÙØ± Ø§ÙØ­Ø§ÙÙØ©
landMarkMyAccountPage.placeholder.user.my.profile.newPassword  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù ÙÙÙØ© Ø§ÙÙØ±ÙØ± Ø§ÙØ¬Ø¯ÙØ¯Ø©
landMarkPayment.header.user.payment  = Ø§ÙØ¯ÙØ¹
landMarkPayment.header.user.manage.payment  =ØªØ­Ø¯ÙØ¯ Ø·Ø±ÙÙØ© Ø§ÙØ¯ÙØ¹ Ø§ÙØ£ÙØ³Ø¨ ÙÙ
landMarkPayment.header.user.add.card  = Ø¥Ø¶Ø§ÙØ© Ø¨Ø·Ø§ÙØ© Ø¬Ø¯ÙØ¯Ø©
landMarkPayment.header.user.payment.sure  = ÙÙ ØªØ±ØºØ¨ ÙÙ ØªØ£ÙÙØ¯ Ø°ÙÙØ
landMarkPayment.header.user.payment.address.delete  = Ø­Ø°Ù Ø§ÙØ¹ÙÙØ§Ù
landMarkPayment.header.user.payment.nevermind  = Ø±Ø¬ÙØ¹
landMarkPayment.header.user.payment.default  = Ø§ÙØ£Ø³Ø§Ø³Ù
landMarkPayment.header.user.payment.name  = Ø§ÙØ§Ø³Ù Ø¹ÙÙ Ø§ÙØ¨Ø·Ø§ÙØ©
landMarkPayment.header.user.payment.expiry  = ØªÙØªÙÙ Ø§ÙØµÙØ§Ø­ÙØ© ÙÙ
landMarkPayment.header.user.payment.billing.address  = Ø¹ÙÙØ§Ù Ø§Ø³ØªÙØ§Ù Ø§ÙÙØ§ØªÙØ±Ø©
landMarkPayment.header.user.payment.edit  = ØªØ¹Ø¯ÙÙ
landMarkPayment.header.user.payment.delete.text  = Ø­Ø°Ù
landMarkPayment.header.user.payment.set.default  = Ø­ÙØ¸ ÙØ£Ø³Ø§Ø³Ù
landMarkPayment.header.user.payment.cash.last = Ø¹ÙÙÙÙØ§ Ø§ÙØ¹Ø²ÙØ²! ÙÙØ§Ù Ø§ÙØ¹Ø¯ÙØ¯ ÙÙ ÙØ³Ø§Ø¦Ù Ø§ÙØ¯ÙØ¹ Ø§ÙØ­Ø§ÙÙØ© Ø£ÙØ¶Ù ÙØ£Ø­Ø¯Ø« ÙÙ Ø§ÙØ¯ÙØ¹ Ø§ÙÙÙØ¯Ù!
landMarkPayment.header.user.payment.add.card.faster = Ø£Ø¶Ù Ø¨Ø·Ø§ÙØªÙ ÙÙ Ø£Ø¬Ù Ø¥Ø¬Ø±Ø§Ø¡ Ø¹ÙÙÙØ© Ø§ÙØ¯ÙØ¹ Ø¨ØµÙØ±Ø© Ø£Ø³Ø±Ø¹
landMarkPayment.header.user.payment.no.card = ÙÙØ£Ø³Ù! ÙÙØ³ ÙØ¯ÙÙ Ø¨Ø·Ø§ÙØ© Ø§Ø¦ØªÙØ§ÙÙØ©
landMarkPayment.header.user.payment.shopping =  ÙÙÙØ§ ÙØ¨Ø¯Ø£ Ø§ÙØªØ³ÙÙÙÙ
landMarkPayment.header.user.payment.start.shopping = Ø§ÙØ¨Ø¯Ø¡ Ø¨Ø§ÙØªØ³ÙÙ
landMarkPayment.header.user.payment.card.number  = Ø±ÙÙ Ø§ÙØ¨Ø·Ø§ÙØ©
landMarkPayment.header.user.payment.expiry.date  = ØªØ§Ø±ÙØ® Ø§ÙØªÙØ§Ø¡ Ø§ÙØµÙØ§Ø­ÙØ©
landMarkPayment.header.user.payment.number  = Ø±ÙÙ
landMarkPayment.header.user.payment.add  = Ø¥Ø¶Ø§ÙØ©
landMarkPayment.header.user.payment.new  = Ø¥Ø¶Ø§ÙØ© Ø¹ÙÙØ§Ù Ø¬Ø¯ÙØ¯
landMarkPayment.header.user.payment.save  = Ø­ÙØ¸
landMarkPayment.header.user.payment.billing.address.text  = Ø¹ÙÙØ§Ù Ø§Ø³ØªÙØ§Ù Ø§ÙÙØ§ØªÙØ±Ø©
landMarkPayment.header.user.payment.save.card.faster  = Ø­ÙØ¸ Ø§ÙØ¨Ø·Ø§ÙØ© ÙØ¥ÙÙØ§Ø¡ Ø¹ÙÙÙØ© Ø§ÙØ¯ÙØ¹ Ø¨ØµÙØ±Ø© Ø£Ø³Ø±Ø¹
landMarkPayment.header.user.payment.experience  = ØªØ¬Ø±Ø¨Ø©
landMarkPayment.header.user.payment.default.card  = ÙØ±Ø¬Ù Ø§Ø³ØªØ®Ø¯Ø§ÙÙØ§ ÙØ§ÙØ¨Ø·Ø§ÙØ© Ø§ÙØ£Ø³Ø§Ø³ÙØ©
landMarkPayment.header.user.payment.show.address  = Ø¥Ø¸ÙØ§Ø± Ø¹ÙÙØ§ÙÙ
landMarkPayment.header.user.payment.newadress  = Ø¥Ø¶Ø§ÙØ© Ø¹ÙÙØ§Ù Ø§Ø³ØªÙØ§Ù ÙØ§ØªÙØ±Ø© Ø¬Ø¯ÙØ¯
landMarkPayment.header.user.payment.newadress.name  = Ø§ÙØ§Ø³Ù Ø¨Ø§ÙÙØ§ÙÙ
landMarkPayment.header.user.payment.newadress.adressline1  = Ø¹ÙÙØ§Ù 1
landMarkPayment.header.user.payment.newadress.adressline2  = Ø¹ÙÙØ§Ù 2
landMarkPayment.header.user.payment.newadress.town  = Ø§ÙÙØ¯ÙÙØ© Ø£Ù Ø§ÙØ¨ÙØ¯Ø©
landMarkPayment.header.user.payment.newadress.state  = Ø§ÙÙØ­Ø§ÙØ¸Ø©
landMarkPayment.header.user.payment.address.add  = Ø¥Ø¶Ø§ÙØ© Ø¹ÙÙØ§Ù Ø¬Ø¯ÙØ¯
landMarkPayment.header.user.payment.newadress.default.shipping  = ÙØ±Ø¬Ù Ø§Ø³ØªØ®Ø¯Ø§ÙÙ ÙØ¹ÙÙØ§Ù Ø£Ø³Ø§Ø³Ù Ø¹ÙØ¯ Ø§ÙØ´Ø­Ù
landMarkPayment.header.user.payment.this.address  = Ø¥Ø¶Ø§ÙØ© ÙØ°Ø§ Ø§ÙØ¹ÙÙØ§Ù
landMarkPayment.header.user.payment.continue  = Ø§ÙÙØªØ§Ø¨Ø¹Ø©
landMarkPayment.header.user.payment.default.billing.address  = ÙØ±Ø¬Ù Ø§Ø³ØªØ®Ø¯Ø§ÙÙ ÙØ¹ÙÙØ§Ù Ø£Ø³Ø§Ø³Ù ÙØ¥Ø±Ø³Ø§Ù Ø§ÙÙØ§ØªÙØ±Ø©
landmarkPayment.addcard.new  = Ø¥Ø¶Ø§ÙØ© Ø¨Ø·Ø§ÙØ© Ø¬Ø¯ÙØ¯Ø©
landMarkPayment.header.user.address.addressEmpty  = Ø¹ÙÙÙØ§! ÙØ§ ÙÙØ¬Ø¯ ÙØ¯ÙÙ Ø¹ÙÙØ§Ù ÙÙØ´Ø­Ù.
landMarkPayment.header.user.address.addressEmptyText  = ÙØ±Ø¬Ù Ø¥Ø¹ÙØ§ÙÙØ§ Ø¨Ø§ÙÙØ¬ÙØ© Ø§ÙØªÙ ÙÙØ¨ØºÙ Ø£Ù ÙØ±Ø³Ù ÙØ´ØªØ±ÙØ§ØªÙ Ø¥ÙÙÙØ§.
landMarkPayment.header.user.address.addressEmptyButton  = ÙØ±Ø¬Ù Ø¥Ø¶Ø§ÙØ© Ø¹ÙÙØ§ÙÙ Ø§ÙØ¢Ù
landMarkPayment.header.user.address.adrsForm.header1  = ÙØ±Ø¬Ù Ø¥Ø¶Ø§ÙØ© Ø¹ÙÙØ§Ù Ø§ÙØ´Ø­Ù ÙØ§ÙØªÙØµÙÙ Ø§ÙØ®Ø§Øµ Ø¨Ù
landMarkPayment.header.user.address.adrsForm.header2 =
landMarkPayment.header.user.address.optional = (Ø§Ø®ØªÙØ§Ø±Ù)
landMarkPayment.header.user.address.mobileno.message  = (ÙÙØªØ­Ø¯ÙØ«Ø§Øª Ø§ÙØ®Ø§ØµØ© Ø¨Ø§ÙØ·ÙØ¨Ø§Øª Ø£Ù Ø§ÙØªÙØµÙÙ)
landMarkPayment.header.user.address.savebox.mobile  = Ø§ÙÙØ§ØªÙ Ø§ÙÙØªØ­Ø±Ù:
landMarkPayment.header.user.orders.savebox.mobile  = Ø±ÙÙ Ø§ÙÙØ§ØªÙ Ø§ÙÙØªØ­Ø±Ù:
landMarkPayment.header.user.payment.cvv  = CVV
landMarkPayment.myaccount.user.payment.cardType  = ÙÙØ¹ Ø§ÙØ¨Ø·Ø§ÙØ©
landMarkPayment.myaccount.user.payment.creditCard  = Ø¨Ø·Ø§ÙØ© Ø§Ø¦ØªÙØ§Ù
landMarkPayment.myaccount.user.payment.debitCard  = Ø¨Ø·Ø§ÙØ© Ø®ØµÙ ÙØ¨Ø§Ø´Ø±
landMarkPayment.myaccount.user.payment.debitCardType  = DBCRD
landMarkPayment.myaccount.user.payment.creditCardType  = CRDC
landMarkPayment.myaccount.user.payment.cvv.required  = Ø£Ø¯Ø®Ù Ø±ÙÙ CVV Ø§ÙØ®Ø§Øµ Ø¨Ù
landMarkPayment.myaccount.user.payment.cardType.required  = ÙØ±Ø¬Ù Ø§Ø®ØªÙØ§Ø± ÙÙØ¹ Ø§ÙØ¨Ø·Ø§ÙØ©
landMarkPayment.header.user.payment.creditcards  = Ø¨Ø·Ø§ÙØ§Øª Ø§ÙØ§Ø¦ØªÙØ§Ù
landMarkPayment.header.user.payment.creditcard  = Ø¨Ø·Ø§ÙØ© Ø§Ø¦ØªÙØ§Ù
landMarkPayment.header.user.payment.debitcards  = Ø¨Ø·Ø§ÙØ§Øª Ø§ÙØ®ØµÙ
landMarkPayment.header.user.payment.debitcard  = Ø¨Ø·Ø§ÙØ© Ø®ØµÙ ÙØ¨Ø§Ø´Ø±
landMarkPayment.header.user.payment.addacard  = Ø¥Ø¶Ø§ÙØ© Ø¨Ø·Ø§ÙØ©
landMarkPayment.header.user.payment.netbanking  = Ø§ÙØ®Ø¯ÙØ§Øª Ø§ÙÙØµØ±ÙÙØ© Ø¹Ø¨Ø± Ø§ÙØ¥ÙØªØ±ÙØª
landMarkPayment.header.user.payment.cashondelivery  = Ø§ÙØ¯ÙØ¹ ÙÙØ¯ÙØ§ Ø¹ÙØ¯ Ø§ÙØ§Ø³ØªÙØ§Ù
landMarkPayment.header.user.payment.cod.charges  = ÙØµØ§Ø±ÙÙ Ø®Ø¯ÙØ© Ø§ÙØ¯ÙØ¹ Ø¹ÙØ¯ Ø§ÙØ§Ø³ØªÙØ§Ù:
landMarkPayment.header.user.payment.cod.notavailable = Ø¹Ø°Ø±ÙØ§Ø ÙØ°Ø§ Ø§ÙØ®ÙØ§Ø± ØºÙØ± ÙØªÙÙØ± ÙÙÙØ²Ø© Ø§Ø³ØªÙÙÙØ§ Ø¨ÙÙØ³Ù
landMarkPayment.header.user.payment.proudlyacceptfollowingmethods  = ÙÙØ¨Ù ÙØ¨ÙÙ ÙØ®Ø± ÙØ³Ø§Ø¦Ù Ø§ÙØ¯ÙØ¹ Ø§ÙØªØ§ÙÙØ©
landMarkPayment.header.user.payment.cardNumber.placeholder = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø±ÙÙ Ø¨Ø·Ø§ÙØªÙ ÙØ§ÙÙÙÙÙ ÙÙ 16 Ø£Ø±ÙØ§Ù
landMarkOrders.ordersPlacedIn.text  = Ø·ÙØ¨ ÙØ³Ø¬ÙÙ ÙÙ
landMarkOrders.oneOrdersPlacedIn.text  = Ø·ÙØ¨ ÙØ³Ø¬ÙÙ ÙÙ
landMarkOrders.orderRefine.one.month  = Ø´ÙØ±
landMarkOrders.orderRefine.three.months  = Ø«ÙØ§Ø«Ø© Ø£Ø´ÙØ±
landMarkOrders.orderRefine.six.months  = Ø³ØªØ© Ø£Ø´ÙØ±
landMarkOrders.orderRefine.one.year  = Ø³ÙØ©
landMarkOrders.orderId.text  = Ø±ÙÙ Ø§ÙØ·ÙØ¨
landMarkOrders.orderId.text.short  = Ø§ÙØ·ÙØ¨
landMarkOrders.totalAmount.text  = Ø§ÙÙØ¨ÙØº Ø§ÙØ¥Ø¬ÙØ§ÙÙ
landMarkOrders.status.text  = Ø§ÙØ­Ø§ÙØ©
landMarkOrders.payment.text  = Ø§ÙØ¯ÙØ¹
landMarkOrders.viewDetails.text  = Ø¹Ø±Ø¶ Ø§ÙØªÙØ§ØµÙÙ
landMarkOrders.shipto.text  = Ø§ÙØ´Ø­Ù Ø¥ÙÙ
landMarkOrders.header.orderDetails  = ØªÙØ§ØµÙÙ Ø§ÙØ·ÙØ¨
landMarkOrders.header.address  = Ø§ÙØ¹ÙÙØ§Ù
landMarkOrders.header.courierTrackingId.text  = Ø±ÙÙ ØªØªØ¨Ø¹ Ø§ÙØ´ÙØ­ÙØ©
landMarkOrders.header.shippedTo  = ØªÙ Ø§ÙØ´Ø­Ù Ø¥ÙÙ :
landMarkOrders.header.expires  = ØªÙØªÙÙ Ø§ÙØµÙØ§Ø­ÙØ© ÙÙ
landMarkOrders.header.subTotal  = Ø§ÙÙØ¬ÙÙØ¹ Ø§ÙÙØ±Ø¹Ù:
landMarkOrders.header.promoDiscount  = Ø§ÙØ®ØµÙ Ø¹ÙÙ Ø§ÙØ·ÙØ¨:
landMarkOrders.header.RefundVoucherDiscount  = Ø§ÙØ¥Ø´Ø¹Ø§Ø±Ø§Øª Ø§ÙØ¯Ø§Ø¦ÙØ©:
landMarkOrders.header.voucherDiscount  = {0}
landMarkOrders.header.shukranSaving  = Ø§ÙØªÙÙÙØ± Ø¨Ø§Ø³ØªØ®Ø¯Ø§Ù ÙÙØ§ÙØ¢Øª ÙØ§ÙØ¯ÙØ§Ø±Ù:
landMarkOrders.ordersPlacedIn.shukran.saving  = -456
landMarkOrders.header.Buy2Get1Free = Buy 2 Get 1 Free
landMarkOrders.header.pickupFrom = Pickup From
landMarkOrders.header.maxFashion = Max Fashion
landMarkOrders.header.standardGroundShipping  = Ø§ÙØ´Ø­Ù ÙØ§ÙØªÙØµÙÙ Ø§ÙØ¨Ø±Ù Ø§ÙØ¹Ø§Ø¯Ù:
landMarkOrders.header.standardGroundShipping.part2 = ÙØµØ§Ø±ÙÙ Ø§ÙØ´Ø­Ù
landMarkOrders.header.paymentCost  = ØªÙÙÙØ© Ø¹ÙÙÙØ© Ø§ÙØ¯ÙØ¹
landMarkOrders.header.total  = Ø§ÙÙØ¬ÙÙØ¹
landMarkOrders.header.orderSummary  = ÙÙØ®Øµ Ø§ÙØ·ÙØ¨
landMarkOrders.header.item  = Ø§ÙÙÙØªØ¬
landMarkOrders.header.description  = Ø§ÙÙØµÙ
landMarkOrders.header.deliveryOption  = Ø®ÙØ§Ø± Ø§ÙØªÙØµÙÙ
landMarkOrders.header.totalPrice  = Ø§ÙØ³Ø¹Ø± Ø§ÙØ¥Ø¬ÙØ§ÙÙ
landMarkOrders.header.quantity  = Ø§ÙÙÙÙØ©:
landMarkOrders.header.change  = ØªØºÙÙØ±
landMarkOrders.header.OMG  = ÙØ§ ØªÙØ¬Ø¯ Ø£Ù Ø·ÙØ¨Ø§Øª ÙØ³Ø¬ÙÙØ© ÙÙ Ø­Ø³Ø§Ø¨Ù.
landMarkOrders.header.start  = Ø§Ø¨Ø¯Ø£ Ø§ÙØªØ³ÙÙ
landMarkOrders.header.clothes = ÙÙÙØ§Ù! ÙÙØ§Ø¨Ø³Ù ÙØ¥Ø·ÙØ§ÙØªÙ ØªØ¨Ø¯Ù ÙØ¯ÙÙØ©!
landMarkOrders.header.newlook = ÙÙ ÙØ§ ØªÙÙØ­ ÙÙØ³Ù Ø¥Ø·ÙØ§ÙØ©Ù Ø¬Ø¯ÙØ¯Ø© ÙÙÙÙØ§Ø
landMarkOrders.deliveryOption.description = ÙÙÙØ§Ù Ø¥ÙÙ Ø«ÙØ§Ø«Ø© Ø£ÙØ§Ù Ø¹ÙÙ
landMarkOrders.standardGroundShipping.description = (ÙÙÙØ§Ù Ø¥ÙÙ Ø«ÙØ§Ø«Ø© Ø£ÙØ§Ù Ø¹ÙÙ)
landMarkOrders.standardGroundShipping.free = ÙØ¬Ø§ÙÙØ§
landMarkOrders.order.filter.last = Ø¢Ø®Ø±
landMarkOrders.order.filter.months = Ø£Ø´ÙØ±
landMarkOrders.order.filter.month = Ø´ÙØ±
landMarkOrders.order.filter.all = Ø¬ÙÙØ¹ Ø§ÙØ·ÙØ¨Ø§Øª
landMarkOrders.order.go.shopping  = Ø³ÙØ¹ÙÙ Ø¹ÙÙ ØªØºÙÙØ± Ø°ÙÙ Ø¹ÙÙ Ø§ÙÙÙØ±.
landMarkOrders.order.more = Ø§ÙÙØ²ÙØ¯
landMarkOrders.order.add = +
landMarkOrders.Order.expires = - ØªÙØªÙÙ Ø§ÙØµÙØ§Ø­ÙØ© ÙÙ {0} Ø {1}
landMarkOrders.orderDiscounts.symbol =&#45;&nbsp;
landMarkOrders.paymentMode.zeroCost.voucher  = ÙØ³ÙÙØ©
landMarkOrders.paymentMode.zeroCost.loyalty  = ÙÙØ§ÙØ¢Øª ÙØ§ÙØ¯ÙØ§Ø±Ù
landMarkOrders.delivery.number.text  = Ø§ÙØªÙØµÙÙ
landMarkOrders.courier.tracking.text  = ØªØªØ¨Ø¹ Ø§ÙØ¨Ø±ÙØ¯ Ø§ÙØ³Ø±ÙØ¹
landMarkOrders.delivery.count  =Ø³ÙÙ ØªØ³ØªÙÙ Ø·ÙØ¨Ù ÙÙ {0} {1}.
landMarkOrders.delivery.count.single  =  Ø´ÙØ­ÙØ© ØªÙØµÙÙ
landMarkOrders.delivery.count.multiple  =  Ø´ÙØ­ÙØ© ØªÙØµÙÙ
landMarkOrders.product.unavailable  = ÙÙØ£Ø³Ù ÙÙ ÙØ¹Ø¯ ÙØ°Ø§ Ø§ÙÙÙØªØ¬ ÙØªÙÙØ±ÙØ§.
landMarkAddressBook.mobileno.invalid.length  = ÙØ¬Ø¨ Ø£Ù ÙØªØ£ÙÙ Ø±ÙÙ ÙØ§ØªÙÙ Ø§ÙÙØªØ­Ø±Ù ÙÙ 10 Ø£Ø±ÙØ§Ù. ÙØ±Ø¬Ù Ø¥Ø¹Ø§Ø¯Ø© Ø§ÙÙØ­Ø§ÙÙØ©.
landMarkAddressBook.mobileno.invalid  = Ø§ÙØ±ÙÙ Ø§ÙØ°Ù ØªÙ Ø¥Ø¯Ø®Ø§ÙÙ ÙØ§ ÙØ¨Ø¯Ù ØµØ­ÙØ­ÙØ§ ÙØ±ÙÙ ÙØ§ØªÙ ÙØªØ­Ø±Ù. ÙØ±Ø¬Ù Ø§ÙØªØ­ÙÙ ÙØ¥Ø¹Ø§Ø¯Ø© Ø§ÙÙØ­Ø§ÙÙØ©.
landMarkAddressBook.Landmark  = Ø¹ÙØ§ÙÙ ÙÙÙØ²Ù
landMarkAddressBook.Lmgaddresstype  = ÙÙØ¹ Ø§ÙØ¹ÙÙØ§Ù
landMarkAddressBook.City  = Ø§ÙÙØ¯ÙÙØ© Ø£Ù Ø§ÙØ¨ÙØ¯Ø©
landMarkAddressBook.state  = Ø§ÙÙØ­Ø§ÙØ¸Ø©
landMarkAddressBook.area = Ø§ÙÙÙØ·ÙØ©
landMarkAddressBook.state.invalid  = ÙØ§ Ø§Ø³Ù Ø§ÙÙØ­Ø§ÙØ¸Ø© Ø§ÙØªÙ ØªÙØ·Ù Ø¨ÙØ§Ø
landMarkAddressBook.mobileno                 = Ø±ÙÙ Ø§ÙÙØ§ØªÙ Ø§ÙÙØªØ­Ø±Ù
landMarkAddressBook.submit                 = Ø­ÙØ¸
landMarkAddressBook.fullname            = Ø§ÙØ§Ø³Ù Ø¨Ø§ÙÙØ§ÙÙ
landMarkAddressBook.text.account.Address  = ØªØ¹Ø¯ÙÙ Ø¹ÙØ§ÙÙÙ Ø§ÙØ´Ø­Ù ÙØ§Ø³ØªÙØ§Ù Ø§ÙÙÙØ§ØªÙØ± Ø§ÙØ®Ø§ØµØ© Ø¨Ù.
landMarkAddressBook.text.account.NewAddress  = Ø¥Ø¶Ø§ÙØ© Ø¹ÙÙØ§Ù Ø¬Ø¯ÙØ¯
landMarkAddressBook.fullname.invalid  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø§Ø³ÙÙ Ø¨Ø§ÙÙØ§ÙÙ.
landMarkAddressBook.Edit  = ØªØ¹Ø¯ÙÙ
landMarkAddressBook.Delete  = Ø­Ø°Ù
landMarkAddressBook.default  = Ø­ÙØ¸ ÙØ£Ø³Ø§Ø³Ù
landMarkAddressBook.invalid.address.postcode  = Ø¥Ø¹Ø§Ø¯Ø© Ø¥Ø¯Ø®Ø§Ù
landMarkAddressBook.form.field.invalid.numeric  = ÙÙ ÙØªÙ Ø¥Ø¯Ø®Ø§Ù Ø§ÙØªÙØ§ØµÙÙ Ø§ÙØ®Ø§ØµØ© Ø¨Ù Ø¨Ø§ÙØµÙØ±Ø© Ø§ÙØµØ­ÙØ­Ø©. ÙØ±Ø¬Ù Ø¥Ø¹Ø§Ø¯Ø© Ø§ÙÙØ­Ø§ÙÙØ©.
landMarkAddressBook.text.account.yourAccount  = Ø­Ø³Ø§Ø¨Ù
landMarkAddressBook.line1  = Ø¹ÙÙØ§Ù 1
landMarkAddressBook.postcode  = Ø±ÙØ² Ø¨Ø±ÙØ¯Ù
landMarkAddressBook.line2  = Ø¹ÙÙØ§Ù 2
landMarkAddressBook.deletePopup.text  = ÙÙ ØªØ±ØºØ¨ ÙÙ ØªØ£ÙÙØ¯ Ø°ÙÙØ
landMarkAddressBook.deleteReview.text  = Ø­Ø°Ù Ø§ÙØªÙÙÙÙ
landMarkAddressBook.deleteAddress.text  = Ø­Ø°Ù Ø§ÙØ¹ÙÙØ§Ù
landMarkAddressBook.nevermind.text  = Ø±Ø¬ÙØ¹
landMarkAddressBook.default.text  = Ø§ÙØ£Ø³Ø§Ø³Ù
landMarkAddressBook.mobile  = Ø§ÙÙØ§ØªÙ Ø§ÙÙØªØ­Ø±Ù
landMarkAddressBook.line1.invalid  = ÙÙ ÙØªÙ Ø¥Ø¯Ø®Ø§Ù Ø¨ÙØ§ÙØ§ØªÙ Ø¨Ø§ÙØµÙØ±Ø© Ø§ÙØµØ­ÙØ­Ø©. ÙØ±Ø¬Ù Ø¥Ø¹Ø§Ø¯Ø© Ø§ÙÙØ­Ø§ÙÙØ©.
landMarkAddressBook.townCity.invalid  = ÙØ§ Ø§Ø³Ù Ø§ÙØ¨ÙØ¯Ø© Ø£Ù Ø§ÙÙØ¯ÙÙØ© Ø§ÙØªÙ ØªÙØ·ÙÙØ§Ø
landMarkAddressBook.postcode.invalid  = Ø®Ø¯ÙØ© Ø§ÙØ´Ø­Ù ÙØ§ÙØªÙØµÙÙ ØºÙØ± ÙØªÙÙØ±Ø© ÙÙØ°Ø§ Ø§ÙØ±ÙØ² Ø§ÙØ¨Ø±ÙØ¯Ù.
landMarkAddressBook.addressType  = ÙÙØ²ÙØ ÙÙØªØ¨Ø ØºÙØ± Ø°ÙÙ
landMarkAddressBook.fullNamePlaceHolder  = Ø£Ø¯Ø®Ù Ø§Ø³ÙÙ Ø¨Ø§ÙÙØ§ÙÙ
landMarkAddressBook.address.enter.name  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø§Ø³ÙÙ
landMarkAddressBook.placeholder.pincode = Ø£Ø¯Ø®Ù Ø§ÙØ±ÙØ² Ø§ÙØ¨Ø±ÙØ¯Ù
landMarkAddressBook.address.enter.pincode  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø§ÙØ±ÙØ² Ø§ÙØ¨Ø±ÙØ¯Ù
landMarkAddressBook.address.enter.line1  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø§ÙØ¹ÙÙØ§Ù 1
landMarkAddressBook.addressLine1PlaceHolder  = Ø±ÙÙ Ø§ÙØ´ÙØ© Ø£Ù Ø§ÙÙÙØ²ÙØ ÙØ¹ Ø§ÙØ·Ø§Ø¨Ù ÙØ§ÙØ¨ÙØ§Ø¡
landMarkAddressBook.address.enter.town  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø§ÙÙØ¯ÙÙØ© Ø£Ù Ø§ÙØ¨ÙØ¯Ø©
landMarkAddressBook.address.enter.mobile  = Ø­ÙÙ Ø±ÙÙ Ø§ÙÙØ§ØªÙ Ø§ÙÙØªØ­Ø±Ù Ø¥ÙØ²Ø§ÙÙ
landMarkAddressBook.address.enter.mobile1 = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø±ÙÙ Ø§ÙÙØ§ØªÙ Ø§ÙÙØªØ­Ø±Ù
landMarkAddressBook.address.enter.mobile2 = Ø­ÙÙ Ø±ÙÙ Ø§ÙÙØ§ØªÙ Ø§ÙÙØªØ­Ø±Ù Ø¥ÙØ²Ø§ÙÙ
landMarkAddressBook.emptyText  = ÙÙØ£Ø³Ù! ÙÙØ³ ÙØ¯ÙÙ Ø¹ÙÙØ§Ù ÙÙØ´Ø­Ù ÙØ§ÙØªÙØµÙÙ.
landMarkAddressBook.emptyText.description  = Ø£Ø¶Ù Ø¹ÙÙØ§ÙÙØ§ ÙÙØµÙ Ø¥ÙÙÙ
landMarkAddressBook.header.start  = ÙØ±Ø¬Ù Ø¥Ø¶Ø§ÙØ© Ø¹ÙÙØ§ÙÙ Ø§ÙØ¢Ù
landmarkFastPayAddress.shipping.address = Ø¹ÙØ§ÙÙÙ Ø§ÙØ´Ø­Ù ÙØ§ÙØªÙØµÙÙ
landmarkFastPayAddress.shipping.default.shipping = ÙØ±Ø¬Ù Ø¥Ø¶Ø§ÙØ© Ø¹ÙÙØ§Ù Ø§ÙØ´Ø­Ù Ø§ÙØ£Ø³Ø§Ø³Ù
landmarkFastPayAddress.shipping.address.add = Ø¥Ø¶Ø§ÙØ© Ø¹ÙÙØ§Ù Ø¬Ø¯ÙØ¯
landmarkFastPayAddress.shipping.address.add.new = ÙØ±Ø¬Ù Ø¥Ø¶Ø§ÙØ© Ø¹ÙÙØ§Ù Ø´Ø­Ù Ø¬Ø¯ÙØ¯
landmarkFastPayAddress.shipping.address.fullname = Ø§ÙØ§Ø³Ù Ø¨Ø§ÙÙØ§ÙÙ
landmarkFastPayAddress.shipping.address.addressline1 = Ø¹ÙÙØ§Ù 1
landmarkFastPayAddress.shipping.addressaddressline2 = Ø¹ÙÙØ§Ù 2
landmarkFastPayAddress.shipping.address.landmark = Ø¹ÙØ§ÙÙ ÙÙÙØ²Ù
landmarkFastPayAddress.shipping.address.type  = ÙÙØ¹ Ø§ÙØ¹ÙÙØ§Ù
landmarkFastPayAddress.shipping.address.town = Ø§ÙÙØ¯ÙÙØ© Ø£Ù Ø§ÙØ¨ÙØ¯Ø©
landmarkFastPayAddress.shipping.address.state = Ø§ÙÙØ­Ø§ÙØ¸Ø©
landmarkFastPayAddress.shipping.address.mobile = Ø±ÙÙ Ø§ÙÙØ§ØªÙ Ø§ÙÙØªØ­Ø±Ù
landmarkFastPayAddress.shipping.address.save = Ø­ÙØ¸
landmarkFastPayAddress.shipping.address.state  = ÙØ±Ø¬Ù Ø§Ø®ØªÙØ§Ø± Ø§ÙÙØ­Ø§ÙØ¸Ø©
landmarkFastPayLanding.fastpay  = Ø§ÙØ¯ÙØ¹ Ø§ÙØ³Ø±ÙØ¹
landmarkFastPayLanding.content  = Ø§ÙØ¯ÙØ¹ Ø§ÙØ³Ø±ÙØ¹ ÙÙ Ø®Ø¯ÙØ© ØªØªÙØ­ ÙÙ Ø§ÙØ¯ÙØ¹ Ø¨Ø¶ØºØ·Ø© ÙØ§Ø­Ø¯Ø© ÙØ¨ÙÙØªÙÙ Ø§ÙØ³Ø±Ø¹Ø©. ÙÙÙÙÙ Ø§ÙØªØ³ÙÙ ÙØ¥ÙÙØ§Ø¡ Ø¹ÙÙÙØ© Ø§ÙØ¯ÙØ¹ ÙÙ ØµÙØ­Ø© Ø§ÙÙÙØªØ¬ Ø°Ø§ØªÙØ§.
landmarkFastPayLanding.start  = ÙÙØ¨Ø¯Ø£ Ø§ÙØ¢Ù
landmarkFastPayLanding.work  = ÙØ§ ÙÙ Ø¢ÙÙØ© Ø¹ÙÙÙØ§Ø
landmarkFastPayLanding.paymentmethod  = ÙØ¬Ø¨ Ø¶Ø¨Ø· ÙØªØ¹Ø¨Ø¦Ø© Ø§ÙØ¨ÙØ§ÙØ§Øª Ø§ÙØ®Ø§ØµØ© Ø¨Ø·Ø±ÙÙØ© Ø§ÙØ¯ÙØ¹ Ø§ÙØ£Ø³Ø§Ø³ÙØ©
landmarkFastPayLanding.cardaccept  = ÙÙØ¨Ù Ø§ÙØ¯ÙØ¹ Ø¨ÙØ§Ø³Ø·Ø© Ø¨Ø·Ø§ÙØ§Øª ÙÙØ²Ø§ Ø£Ù ÙØ§Ø³ØªØ± ÙØ§Ø±Ø¯ Ø£Ù Ø¨Ø·Ø§ÙØ§Øª Ø§ÙØ§Ø¦ØªÙØ§Ù ÙØ§ÙØ®ØµÙ Ø§ÙÙØ¨Ø§Ø´Ø± ÙÙØ°ÙÙ Ø§ÙØ¯ÙØ¹ Ø§ÙÙÙØ¯Ù Ø¹ÙØ¯ Ø§ÙØ§Ø³ØªÙØ§Ù.
landmarkFastPayLanding.defaultship  = ÙØ¬Ø¨ Ø¶Ø¨Ø· ÙØªØ¹Ø¨Ø¦Ø© Ø§ÙØ¨ÙØ§ÙØ§Øª Ø§ÙØ®Ø§ØµØ© Ø¨Ø¹ÙÙØ§Ù Ø§ÙØ´Ø­Ù Ø§ÙØ£Ø³Ø§Ø³Ù
landmarkFastPayLanding.deliverytime  = Ø­ØªÙ ÙØ¹Ø±Ù Ø§ÙÙØ¬ÙØ© Ø§ÙØªÙ Ø³ÙÙÙÙ Ø¨ØªÙØµÙÙ Ø§ÙØ·ÙØ¨ Ø¥ÙÙÙØ§ ÙØ§ÙØ°Ù Ø³ÙØ¯Ø¯Øª ÙÙÙØªÙ Ø¨Ø§Ø³ØªØ®Ø¯Ø§Ù Ø®Ø¯ÙØ© Ø§ÙØ¯ÙØ¹ Ø§ÙØ³Ø±ÙØ¹ .
landmarkFastPayLanding.descr  = ÙØ°Ø§ ÙÙ ÙØ§ ÙÙØ§ÙÙ!
landmarkFastPayLanding.paynow  = Ø³ÙØªÙ ØªÙØ¹ÙÙ Ø²Ø± "Ø§Ø¯ÙØ¹ Ø§ÙØ¢Ù Ø¨Ø§Ø³ØªØ®Ø¯Ø§Ù Ø§ÙØ¯ÙØ¹ Ø§ÙØ³Ø±ÙØ¹".
landmarkFastPay.fastpay  = Ø§ÙØ¯ÙØ¹ Ø§ÙØ³Ø±ÙØ¹
landmarkFastPay.waytoshop  = Ø§Ø³ØªÙØªØ¹ Ø¨ÙÙØ²Ø© Ø§ÙØ¯ÙØ¹ Ø¨Ø¶ØºØ·Ø© ÙØ§Ø­Ø¯Ø© ÙØ¹ Ø®Ø¯ÙØ© Ø§ÙØ¯ÙØ¹ Ø§ÙØ³Ø±ÙØ¹
landmarkFastPay.paymentmethods  = Ø·Ø±Ù Ø§ÙØ¯ÙØ¹
landmarkFastPay.default  = Ø§ÙØ£Ø³Ø§Ø³Ù
landmarkFastPay.card.name  = Ø§ÙØ§Ø³Ù Ø¹ÙÙ Ø§ÙØ¨Ø·Ø§ÙØ©
landmarkFastPay.card.expires  = ØªÙØªÙÙ Ø§ÙØµÙØ§Ø­ÙØ© ÙÙ
landmarkFastPay.defaultpayment.method  = ÙØ±Ø¬Ù ØªØ­Ø¯ÙØ¯ Ø£Ù Ø¥Ø¶Ø§ÙØ© Ø·Ø±ÙÙØ© Ø§ÙØ¯ÙØ¹ Ø§ÙØ£Ø³Ø§Ø³ÙØ©
landmarkFastPay.carditem = Ø§ÙØ£Ø³Ø§Ø³Ù
landmarkFastPay.cashondelivery = Ø§ÙØ¯ÙØ¹ Ø¹ÙØ¯ Ø§ÙØ§Ø³ØªÙØ§Ù
landmarkFastPay.payment.mode.standardchagre  = ÙØªÙ Ø§ÙØ³Ø¯Ø§Ø¯ Ø¨Ø¹Ø¯ Ø§ÙØªÙØµÙÙ ÙØªÙØ·Ø¨ÙÙ Ø§ÙØ±Ø³ÙÙ Ø§ÙØªÙ ØªØ¨ÙØº {0}
landmarkFastPay.boxbar.item = Ø­ÙØ¸ ÙØ£Ø³Ø§Ø³Ù
landmarkFastPay.boxbar.item1 = Ø­ÙØ¸ ÙØ£Ø³Ø§Ø³Ù
landmarkFastPay.Shipping.address = Ø¹ÙØ§ÙÙÙ Ø§ÙØ´Ø­Ù
landmarkFastPay.adress.default =Ø§ÙØ£Ø³Ø§Ø³Ù
landmarkFastPay.defaultshippingaddress = ÙØ±Ø¬Ù Ø¥Ø¶Ø§ÙØ© Ø¹ÙÙØ§Ù Ø§ÙØ´Ø­Ù Ø§ÙØ£Ø³Ø§Ø³Ù
landmarkFastPay.myaction.about = Ø­ÙÙ Ø®Ø¯ÙØ© Ø§ÙØ¯ÙØ¹ Ø§ÙØ³Ø±ÙØ¹
landmarkFastPay.myaction.multisteporderprocess = ØªØªÙØ­ ÙÙ Ø®Ø¯ÙØ© Ø§ÙØ¯ÙØ¹ Ø§ÙØ³Ø±ÙØ¹ Ø¥ÙÙØ§ÙÙØ© Ø§ÙØ´Ø±Ø§Ø¡ Ø§ÙÙÙØ±Ù ÙÙØ°ÙÙ ØªØ®Ø·Ù Ø§ÙØ®Ø·ÙØ§Øª Ø§ÙÙØªØ¹Ø¯Ø¯Ø© Ø§ÙÙØ¹ØªØ§Ø¯Ø© ÙØ¹ÙÙÙØ© Ø§ÙØ·ÙØ¨.
landmarkFastPay.myaction.aboutfastpay = ÙØ±Ø¬Ù <a href="/ae/en/faq#faq-question-group-03"> Ø§ÙÙÙØ± ÙÙØ§ </a> ÙÙØ±Ø§Ø¡Ø© Ø§ÙÙØ²ÙØ¯ Ø¹Ù Ø®Ø¯ÙØ© ÙØ§Ø³Øª Ø¨Ø§Ù.
landmarkFastPay.info.secuirity = Ø¢ÙÙØ© Ø­ÙØ§ÙØ© ÙØ£ÙÙ 256-Ø¨Øª
landmarkFastPay.info.secuirity.industry = ØªØ¹Ø²Ø²ÙØ§ Ø¢ÙÙØ© ØªØ´ÙÙØ± Ø£ÙÙÙ ÙØ¤ÙÙØ© ÙÙ 256-Ø¨ØªØ ØªØ¹Ø¯ Ø§ÙØ±Ø§Ø¦Ø¯Ø© ÙÙ ÙØ°Ø§ Ø§ÙÙØ·Ø§Ø¹Ø Ø¨ØºØ±Ø¶ Ø­ÙØ§ÙØ© Ø¨ÙØ§ÙØ§ØªÙ.
landmarkFastPay.lighting.fast = Ø¨Ø³Ø±Ø¹Ø© Ø§ÙØ¨Ø±Ù
landmarkFastPay.shop.instantly = Ø§ÙØ´Ø±Ø§Ø¡ Ø§ÙÙÙØ±Ù Ø¨ÙØ¨Ø³Ø© Ø²Ø±.
landmarkFastPay.info.paymentmode = Ø§ÙØ¯ÙØ¹ ÙÙØ¯ÙØ§ Ø£Ù Ø¨ÙØ§Ø³Ø·Ø© Ø¨Ø·Ø§ÙØ©
landmarkFastPay.info.paymentmode.preferred  = ÙØ±Ø¬Ù Ø§Ø®ØªÙØ§Ø± Ø·Ø±ÙÙØ© Ø§ÙØ¯ÙØ¹ Ø§ÙÙÙØ¶ÙØ© Ø¨Ø§ÙÙØ³Ø¨Ø© ÙÙ - Ø§ÙØ¯ÙØ¹ Ø§ÙÙÙØ¯Ù Ø¹ÙØ¯ Ø§ÙØ§Ø³ØªÙØ§ÙØ Ø£Ù Ø¨Ø§Ø³ØªØ®Ø¯Ø§Ù Ø¨Ø·Ø§ÙØ© Ø§ÙØ§Ø¦ØªÙØ§Ù Ø£Ù Ø§ÙØ®ØµÙ.
landmarkFastPay.info.mobileshopping  = Ø§ÙØªØ³ÙÙ Ø¨ÙØ§Ø³Ø·Ø© Ø§ÙÙØ§ØªÙ Ø§ÙÙØªØ­Ø±Ù Ø¨ØµÙØ±Ø© Ø£Ø³Ø±Ø¹
landmarkFastPay.info.mobilefriendly  = Ø§Ø³ØªÙØªØ¹ Ø¨Ø®Ø¯ÙØ© ÙØ§Ø³Øª Ø¨Ø§Ù Ø¹ÙÙ ÙÙÙØ¹ÙØ§ Ø§ÙØ¥ÙÙØªØ±ÙÙÙ Ø§ÙÙØªÙØ§ÙÙ ÙØ¹ Ø§ÙÙÙØ§ØªÙ Ø§ÙÙØªØ­Ø±ÙØ©.
landmarkFastPay.addcard.new  = Ø¥Ø¶Ø§ÙØ© Ø¨Ø·Ø§ÙØ© Ø¬Ø¯ÙØ¯Ø©
landmarkFastPay.addcard.number  = Ø±ÙÙ Ø§ÙØ¨Ø·Ø§ÙØ©
landmarkFastPay.addcard.name  = Ø§ÙØ§Ø³Ù Ø¹ÙÙ Ø§ÙØ¨Ø·Ø§ÙØ©
landmarkFastPay.addcard.expiry  = ØªØ§Ø±ÙØ® Ø§ÙØªÙØ§Ø¡ Ø§ÙØµÙØ§Ø­ÙØ©
landmarkFastPay.addcard.cvv  = CVV
landmarkFastPay.billingaddress  = Ø¹ÙÙØ§Ù Ø§Ø³ØªÙØ§Ù Ø§ÙÙØ§ØªÙØ±Ø©
landmarkFastPay.billingaddress.new  = + Ø¥Ø¶Ø§ÙØ© Ø¹ÙÙØ§Ù Ø¬Ø¯ÙØ¯
landmarkFastPay.billingaddress.show  = Ø¥Ø¸ÙØ§Ø± Ø¹ÙÙØ§ÙÙ
landmarkFastPay.billingaddress.newadress  = Ø¥Ø¶Ø§ÙØ© Ø¹ÙÙØ§Ù Ø§Ø³ØªÙØ§Ù ÙØ§ØªÙØ±Ø© Ø¬Ø¯ÙØ¯
landmarkFastPay.billingaddress.newadress.name  = Ø§ÙØ§Ø³Ù Ø¨Ø§ÙÙØ§ÙÙ
landmarkFastPay.billingaddress.newadress.adressline1  = Ø¹ÙÙØ§Ù 1
landmarkFastPay.billingaddress.newadress.adressline2  = Ø¹ÙÙØ§Ù 2
landmarkFastPay.billingaddress.newadress.town  = Ø§ÙØ¨ÙØ¯Ø© Ø£Ù Ø§ÙÙØ¯ÙÙØ©
landmarkFastPay.billingaddress.newadress.state  = Ø§ÙÙØ­Ø§ÙØ¸Ø©
landmarkFastPay.save  = Ø­ÙØ¸
landmarkFastPay.addadress.new  = Ø¥Ø¶Ø§ÙØ© Ø¹ÙÙØ§Ù Ø´Ø­Ù Ø¬Ø¯ÙØ¯


landmarkFastPay.addadress.mandatoryfield =


landmarkFastPayAddress.shipping.address.type  = ÙØ±Ø¬Ù Ø§Ø®ØªÙØ§Ø± ÙÙØ¹ Ø¹ÙÙØ§ÙÙ
landmarkFastPayAddress.shipping.address.type.home  = Ø§ÙØµÙØ­Ø© Ø§ÙØ±Ø¦ÙØ³ÙØ©
landmarkFastPay.linkbox.addcard  = Ø¥Ø¶Ø§ÙØ© Ø¨Ø·Ø§ÙØ© Ø¬Ø¯ÙØ¯Ø©
landmarkFastPayadress.default  = Ø­ÙØ¸ ÙØ£Ø³Ø§Ø³Ù
landmarkFastPay.shippingaddress.add.new  = Ø¥Ø¶Ø§ÙØ© Ø¹ÙÙØ§Ù Ø¬Ø¯ÙØ¯
landmarkFastPayAddress.shipping.address.pincode  = Ø±ÙØ² Ø¨Ø±ÙØ¯Ù
landmarkFastPayAddress.shipping.address.city  = Ø§ÙÙØ¯ÙÙØ© Ø£Ù Ø§ÙØ¨ÙØ¯Ø©
landmarkFastPayAddress.shipping.address.state  = Ø§ÙÙØ­Ø§ÙØ¸Ø©
landmarkFastPayAddress.shipping.address.state.select  = ÙØ±Ø¬Ù Ø§Ø®ØªÙØ§Ø± Ø§ÙÙØ­Ø§ÙØ¸Ø©
landmark.shipping.address.area.select  = ÙØ±Ø¬Ù Ø§Ø®ØªÙØ§Ø± ÙÙØ·ÙØªÙ
landmarkFastPayAddress.shipping.address.type.office  = Ø§ÙÙÙØªØ¨
landmarkFastPayAddress.shipping.address.optional  = (Ø§Ø®ØªÙØ§Ø±Ù)
landmarkFastPayAddress.placeholder.fullname  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø§Ø³ÙÙ Ø¨Ø§ÙÙØ§ÙÙ
landmarkFastPayAddress.placeholder.pincode  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø§ÙØ±ÙØ² Ø§ÙØ¨Ø±ÙØ¯Ù Ø§ÙØ®Ø§Øµ Ø¨Ù
landmarkFastPayAddress.placeholder.address.line1  = Ø±ÙÙ Ø§ÙØ´ÙØ© Ø£Ù Ø§ÙÙÙØ²ÙØ ÙØ¹ Ø§ÙØ·Ø§Ø¨Ù ÙØ§ÙØ¨ÙØ§Ø¡
landmarkFastPayAddress.placeholder.address.line2  = Ø§ÙÙØ­Ø§ÙØ¸Ø© ÙØ§ÙØ´Ø§Ø±Ø¹ ÙØ§ÙØ­Ù
landmarkFastPayAddress.placeholder.address.landmark  = ØªØ³ÙÙØ© ÙÙØ¹ÙÙ ÙÙÙØ² Ø³ØªØ³Ø§Ø¹Ø¯ÙØ§ ÙÙ ØªØ­Ø¯ÙØ¯ Ø¹ÙÙØ§ÙÙ Ø¨ØµÙØ±Ø©Ù Ø£Ø³Ø±Ø¹
landmarkFastPayAddress.placeholder.city  = ÙØ±Ø¬Ù Ø§Ø®ØªÙØ§Ø± Ø§ÙÙØ¯ÙÙØ© Ø£Ù Ø§ÙØ¨ÙØ¯Ø©
landmarkFastPayAddress.placeholder.mobile  = Ø±ÙÙ Ø§ÙÙØ§ØªÙ Ø§ÙÙØªØ­Ø±Ù
landmarkFastPayAddress.required.for.order  = (ÙÙØªØ­Ø¯ÙØ«Ø§Øª
landmarkFastPayAddress.delivery  = Ø§ÙØ®Ø§ØµØ© Ø¨Ø§ÙØ·ÙØ¨Ø§Øª
landmarkFastPayAddress.related.update  = Ø£Ù Ø§ÙØªÙØµÙÙ)
landmarkFastPay.billingaddress.continue  = Ø§ÙÙØªØ§Ø¨Ø¹Ø©
landmarkFastPay.billingaddress.default.billing.address  = ÙØ±Ø¬Ù Ø§Ø³ØªØ®Ø¯Ø§ÙÙ ÙØ§ÙØ¹ÙÙØ§Ù Ø§ÙØ£Ø³Ø§Ø³Ù ÙØ¥Ø±Ø³Ø§Ù Ø§ÙÙØ§ØªÙØ±Ø©
landmarkFastPay.billingaddress.required  = (ÙÙØªØ­Ø¯ÙØ«Ø§Øª Ø§ÙØ®Ø§ØµØ© Ø¨Ø§ÙØ·ÙØ¨Ø§Øª Ø£Ù Ø§ÙØªÙØµÙÙ)
landmarFastPay.toggle.button.error.message  = Ø¹ÙÙÙØ§! ÙÙØ¯ Ø­Ø¯Ø« Ø®Ø·Ø£ ÙØ§. ÙØ±Ø¬Ù Ø§Ø®ØªÙØ§Ø± Ø·Ø±ÙÙØ© Ø§ÙØ¯ÙØ¹ ÙØ¹ÙÙØ§Ù Ø§ÙØ´Ø­Ù Ø§ÙØ£Ø³Ø§Ø³ÙÙÙ Ø«Ù Ø¥Ø¹Ø§Ø¯Ø© Ø§ÙÙØ­Ø§ÙÙØ©.
landmarkFastPay.toggle.button.error.message.address  = ÙØ±Ø¬Ù Ø§Ø®ØªÙØ§Ø± Ø£Ù Ø¥Ø¶Ø§ÙØ© Ø¹ÙÙØ§Ù Ø§ÙØ´Ø­Ù ÙØ§ÙØªÙØµÙÙ Ø§ÙØ£Ø³Ø§Ø³Ù.
landmarkFastPay.toggle.button.error.message.payment  = ÙØ±Ø¬Ù Ø§Ø®ØªÙØ§Ø± Ø·Ø±ÙÙØ© Ø§ÙØ¯ÙØ¹ Ø§ÙØ£Ø³Ø§Ø³ÙØ©
landmarkFastPay.toggle.button.error.message  = Ø¹ÙÙÙØ§! ÙÙØ¯ Ø­Ø¯Ø« Ø®Ø·Ø£ ÙØ§. ÙØ±Ø¬Ù Ø§Ø®ØªÙØ§Ø± Ø·Ø±ÙÙØ© Ø§ÙØ¯ÙØ¹ ÙØ¹ÙÙØ§Ù Ø§ÙØ´Ø­Ù Ø§ÙØ£Ø³Ø§Ø³ÙÙÙ Ø«Ù Ø¥Ø¹Ø§Ø¯Ø© Ø§ÙÙØ­Ø§ÙÙØ©.
landmarkFastPay.default.address.error.message  = ÙØ±Ø¬Ù ØªØ­Ø¯ÙØ¯ Ø¹ÙÙØ§Ù Ø£Ø³Ø§Ø³Ù ÙØ®ØªÙÙ ÙØ·Ø±ÙÙØ© Ø§ÙØ¯ÙØ¹ ÙØ°Ù.
landmarkFastPay.default.payment.error.message  = ÙØ§ ÙÙÙÙ ØªØ¹ÙÙÙ ÙØ°Ù Ø¹ÙÙ Ø£ÙÙØ§ Ø·Ø±ÙÙØ© Ø§ÙØ¯ÙØ¹ Ø§ÙØ£Ø³Ø§Ø³ÙØ© ÙØ¹ Ø§ÙØ¹ÙÙØ§Ù Ø§ÙØ°Ù ÙÙØª Ø¨Ø§Ø®ØªÙØ§Ø±Ù.
landmarkFastPayAddress.off  = Ø¥ÙÙØ§Ù
landmarkFastPayAddress.on  = ØªØ´ØºÙÙ
landmarkFastPayAddress.pincode.available  = Ø§ÙØ±ÙØ² Ø§ÙØ¨Ø±ÙØ¯Ù Ø§ÙÙÙØ¯ÙÙ ØºÙØ± ÙØªÙÙØ±
landmarkFastPayAddress.optional  = (Ø§Ø®ØªÙØ§Ø±Ù)
landmarkFastPay.mobile  = Ø§ÙÙØ§ØªÙ Ø§ÙÙØªØ­Ø±Ù:
landmarkFastPay.cardName.required  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø§Ø³ÙÙ
landmarkFastPay.cardNumber.required  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø±ÙÙ Ø¨Ø·Ø§ÙØªÙ
landmarkFastPay.address.fullName.required  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø§Ø³ÙÙ Ø¨Ø§ÙÙØ§ÙÙ
landmarkFastPay.address.addressLine1.required  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø§ÙØ¹ÙÙØ§Ù 1
landmarkFastPay.address.town.required  = ÙØ±Ø¬Ù Ø§Ø®ØªÙØ§Ø± Ø§ÙÙØ¯ÙÙØ© Ø£Ù Ø§ÙØ¨ÙØ¯Ø©
landmarkFastPay.address.state.required  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø§ÙÙØ­Ø§ÙØ¸Ø©
landmarkFastPay.address.pincode.required  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø§ÙØ±ÙØ² Ø§ÙØ¨Ø±ÙØ¯Ù
landmarkFastPay.address.mobile.required  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø±ÙÙ ÙØ§ØªÙÙ Ø§ÙÙØªØ­Ø±Ù
landmark.address.area.required  = ÙØ±Ø¬Ù Ø§Ø®ØªÙØ§Ø± ÙÙØ·ÙØªÙ
landmarkSubscriptions.Newsletter.Preferences  = ØªÙØ¶ÙÙØ§Øª Ø§ÙÙØ´Ø±Ø© Ø§ÙØ¥Ø®Ø¨Ø§Ø±ÙØ©
landmarkSubscriptions.Language.Preferences = ÙØ±Ø¬Ù Ø§Ø®ØªÙØ§Ø± ÙØºØªÙ Ø§ÙÙÙØ¶ÙØ©:
landmarkSubscriptions.hear  = ÙÙ ÙØ±Ø© ØªØ­Ø¨ Ø£Ù ÙØªÙØ§ØµÙ ÙØ¹ÙØ
landmarkSubscriptions.Daily  = ÙÙÙÙÙØ§
landmarkSubscriptions.Weekly  = Ø£Ø³Ø¨ÙØ¹ÙÙØ§
landmarkSubscriptions.Never  = ÙØ·ÙÙÙØ§
landmarkSubscriptions.Women.Fashion  = Ø£Ø²ÙØ§Ø¡ ÙØ³Ø§Ø¦ÙØ©
landmarkSubscriptions.Baby.Girl  = ÙÙÙÙØ¯Ø§Øª Ø¨ÙØ§Øª
landmarkSubscriptions.Electronics  = Ø¥ÙÙØªØ±ÙÙÙØ§Øª
landmarkSubscriptions.Beauty  = ÙØ³ØªØ­Ø¶Ø±Ø§Øª ØªØ¬ÙÙÙ
landmarkSubscriptions.Men.Fashion  = Ø£Ø²ÙØ§Ø¡ Ø±Ø¬Ø§ÙÙØ©
landmarkSubscriptions.Baby.Boy  = ÙÙØ§ÙÙØ¯ Ø£ÙÙØ§Ø¯
landmarkSubscriptions.Home  = Ø§ÙØµÙØ­Ø© Ø§ÙØ±Ø¦ÙØ³ÙØ©
landmarkSubscriptions.Shoes  = Ø£Ø­Ø°ÙØ©
landmarkSubscriptions.Update.Subscrptions  = ØªØ­Ø¯ÙØ« Ø§ÙØ§Ø´ØªØ±Ø§ÙØ§Øª
landmarkSubscriptions.departments  = ÙØ§ Ø§ÙØ£ÙØ³Ø§Ù Ø§ÙØªÙ ØªÙÙÙØ
landmarkSubscriptions.departments.description  = Ø³ÙØ³Ø§Ø¹Ø¯ÙØ§ Ø°ÙÙ Ø¹ÙÙ ØªÙØ¯ÙÙ ØªØ¬Ø±Ø¨Ø© ØªØ³ÙÙ ÙØ®ØµØµØ© ÙÙ Ø¨ØµÙØ±Ø©Ù Ø£ÙØ¶Ù Ø¨ÙØ«ÙØ±.
landmarkSubscriptions.Subscription  = Ø§ÙØªÙØ§ØµÙ
landmarkSubscriptions.Stock.Availability  = Ø¥Ø®Ø·Ø§Ø±Ø§Øª Ø§ÙÙÙØ§Ø° ÙÙ Ø§ÙÙØ®Ø²ÙÙ
landmarkSubscriptions.Stock.Availability.BackInStock  = Ø³ÙÙÙÙ Ø¨Ø¥Ø¨ÙØ§ØºÙ ÙÙØ± ØªÙÙÙØ±Ù ÙØ¬Ø¯Ø¯ÙØ§ ÙÙ Ø§ÙÙØ®Ø²ÙÙ.
landmarkSubscriptions.Unsubscribe  = Ø¥ÙØºØ§Ø¡ Ø§ÙØ§Ø´ØªØ±Ø§Ù
landMarkAddressBook.unsubscribe.text  = Ø¥ÙØºØ§Ø¡ Ø§ÙØ§Ø´ØªØ±Ø§Ù
inStockNotification.product.color  = Ø§ÙÙÙÙ:
inStockNotification.product.size  = Ø§ÙÙÙØ§Ø³:
multiproductpromo.show.details  = <span>Ø¹Ø±Ø¶ ÙØ§ÙØ©</span> Ø§ÙØªÙØ§ØµÙÙ
landMarkMyAccountPage.header.user.my.subcrption.update.success  = ØªÙ Ø­ÙØ¸ ØªÙØ¶ÙÙØ§ØªÙ.
landmarkSubscriptions.subscribe.fashion.newsletter  = Ø­Ø°Ù
landmarkSubscriptions.error.never  = &nbsp; ÙØ±Ø¬Ù Ø§ÙØªØ³Ø¬ÙÙ ÙÙ Ø§ÙØ´Ø±ÙØ· Ø§ÙØ³ÙÙÙ
landmarkSubscriptions.subscribe.various.news  = Ø§Ø´ØªØ±Ù ÙÙ ÙØ´Ø±ØªÙØ§ Ø§ÙØ¥Ø®Ø¨Ø§Ø±ÙØ© ÙÙØªØ¹Ø±Ù Ø¹ÙÙ Ø¢Ø®Ø± Ø§ÙØ¹Ø±ÙØ¶ ÙØ§ÙØªØ­Ø¯ÙØ«Ø§Øª.
landmarkSubscriptions.subscribe.start  = ÙÙÙØ¨Ø¯Ø£ Ø§ÙØ¢Ù
landMarkReviews.reviewsPlacedIn.text  = Ø§ÙØªÙÙÙÙØ§Øª Ø§ÙØªÙ ØªÙ ÙØ´Ø±ÙØ§ ÙÙ
landMarkReviews.starsCount  = ÙÙ Ø£ØµÙ Ø®ÙØ³ ÙØ¬ÙÙ
landMarkReviews.statusText  = Ø§ÙØ­Ø§ÙØ©
landMarkReviews.editLink  = ØªØ¹Ø¯ÙÙ
landMarkReviews.deleteLink  = Ø­Ø°Ù
landMarkReviews.emptyText  = ÙÙ ØªÙØ¯ÙÙ Ø£Ù ØªÙÙÙÙØ§Øª Ø­ØªÙ Ø§ÙØ¢Ù.
landMarkReviews.emptyText.description  = ØªØ³ÙÙ Ø§ÙÙÙØªØ¬Ø§Øª Ø§ÙÙÙØ¶ÙØ© ÙØ¯ÙÙØ ÙØ£Ø®Ø¨Ø±ÙØ§ Ø¨Ø±Ø£ÙÙ.
landMarkReviews.comment.readMore  = ÙØ±Ø§Ø¡Ø© Ø§ÙÙØ²ÙØ¯
landMarkWishList.emptyText  = ÙØ§ ØªÙØ¬Ø¯ ÙØ·Ø¹ Ø¶ÙÙ Ø§ÙÙÙØªØ¬Ø§Øª Ø§ÙÙÙØ¶ÙØ© Ø­ØªÙ Ø§ÙØ¢Ù.
landMarkWishList.emptyText.description  = Ø§Ø®ØªØ± Ø§ÙÙÙØªØ¬Ø§Øª Ø§ÙÙÙØ¶ÙØ© Ø§ÙØªÙ Ø£Ø¹Ø¬Ø¨ØªÙ Ø§ÙØ¢Ù ÙØªØ´ØªØ±ÙÙØ§ ÙØ§Ø­ÙÙØ§ ÙÙØªÙØ§ ØªØ´Ø§Ø¡!
landMarkWishList.removeText  = Ø­Ø°Ù Ø§ÙÙÙ
landMarkWishList.notAvailable  = Ø§ÙÙÙØªØ¬ ÙÙ ÙØ¹Ø¯ ÙØªØ§Ø­ÙØ§
landMarkWishList.outOfStock  = ÙÙØ° ÙÙ Ø§ÙÙØ®Ø²ÙÙ
landMarkWishList.undoText  = ØªÙØª Ø¥Ø²Ø§ÙØªÙ ÙÙ ÙØ§Ø¦ÙØ© Ø§ÙÙÙØªØ¬Ø§Øª Ø§ÙÙÙØ¶ÙØ©.
landmarkDepartmentPage.showMore  = Ø¥Ø¸ÙØ§Ø± Ø§ÙÙØ²ÙØ¯
landmarkDepartmentPage.showLess  = Ø¥Ø¸ÙØ§Ø± Ø¹Ø¯Ø¯ Ø£ÙÙ
landmarkDepartmentPage.showAllBrands  = Ø¥Ø¸ÙØ§Ø± Ø¬ÙÙØ¹ Ø§ÙØ¹ÙØ§ÙØ§Øª Ø§ÙØªØ¬Ø§Ø±ÙØ©
landmarkDepartmentPage.brand  = Ø¹ÙØ§ÙØ© ØªØ¬Ø§Ø±ÙØ©
landmarkDepartmentPage.browse  = ØªØµÙØ­
landmarkDepartmentPage.backto = Ø§ÙØ±Ø¬ÙØ¹ Ø§ÙÙ
landmarkDepartmentPage.gallery.previouslink  = Ø§ÙØ³Ø§Ø¨Ù
landmarkDepartmentPage.gallery.nextlink  = Ø§ÙØªØ§ÙÙ
landMarkLifestylePage.footer.heading  = ÙÙ ÙØ§ ÙØ®Øµ ÙÙØ· Ø§ÙØ­ÙØ§Ø©
landMarkBrandPage.banner.next  = Ø§ÙØªØ§ÙÙ
landMarkBrandPage.banner.previous  = Ø§ÙØ³Ø§Ø¨Ù
landMarkMaxPage.footer.heading  = ÙÙ ÙØ§ ÙØ®Øµ ÙØ§ÙØ³
landMarkBrandPage.categorycarousel.previouslink  = Ø§ÙØ³Ø§Ø¨Ù
landMarkBrandPage.categorycarousel.nextlink  = Ø§ÙØªØ§ÙÙ
landMark.pagetitle.lifestyle  = ÙÙØ· Ø§ÙØ­ÙØ§Ø©
landMark.pagetitle.max  = ÙØ§ÙØ³
landMarkBrandPage.storelocator  = ÙØ±ÙØ¹ÙØ§
landMarkBrandPage.locate  = Ø­Ø¯Ø¯ Ø§ÙÙÙÙØ¹
landMarkBrandPage.viewall  = Ø¹Ø±Ø¶ Ø¬ÙÙØ¹  Ø§ÙÙÙØ§ÙØ¹ ÙØ¹Ø¯Ø¯ÙØ§ {0}
landMarkBrandPage.subscribe  = Ø§ÙØ§Ø´ØªØ±Ø§Ù
landMarkBrandPage.enter.email  = Ø£Ø¯Ø®Ù Ø¨Ø±ÙØ¯Ù Ø§ÙØ¥ÙÙØªØ±ÙÙÙ
landmarkClearancePage.categories  = Ø§ÙÙØ¦Ø§Øª
landmarkClearancePage.top.seller  = Ø§ÙØ£Ø¹ÙÙ ÙØ¨ÙØ¹ÙØ§
landmarkClearancePage.text.shop  = ØªØ³ÙÙÙ
storelocator.type.city  = Ø§ÙØªØ¨ Ø£Ù Ø§Ø®ØªØ± Ø§ÙÙØ¯ÙÙØ©
storelocator.address  = Ø§ÙØ¹ÙÙØ§Ù:
storelocator.phone  = Ø§ÙÙØ§ØªÙ
storelocator.timing  = Ø§ÙØªÙÙÙØª
storelocator.getdirections  = Ø§Ø­ØµÙ Ø¹ÙÙ Ø§ÙØªÙØ¬ÙÙØ§Øª
storelocator.close  = Ø¥ØºÙØ§Ù
landmarkSearchPage.results  = Ø§ÙÙØªØ§Ø¦Ø¬
landmarkSearchPage.search  = Ø¨Ø­Ø«
landmarkSearchPage.filter.size  = Ø§ÙÙÙØ§Ø³
landmarkSearchPage.filter.view.all  = Ø¥Ø¸ÙØ§Ø± Ø§ÙÙØ²ÙØ¯
landmarkSearchPage.filter.Less  = Ø¥Ø¸ÙØ§Ø± Ø¹Ø¯Ø¯ Ø£ÙÙ
landmarkSearchPage.filter.brands  = ØºÙØ± Ø¥ÙØ²Ø§ÙÙ
landmarkSearchPage.brand  = Ø¹ÙØ§ÙØ© ØªØ¬Ø§Ø±ÙØ©
landmarkSearchPage.filter.color  = Ø§ÙÙÙÙ
landmarkSearchPage.apply  = ØªØ·Ø¨ÙÙ
landmarkSearchPage.sortBy  = Ø§ÙØªØ±ØªÙØ¨ Ø¨Ø­Ø³Ø¨:
landmarkSearchPage.sortByM  = Ø§ÙØªØ±ØªÙØ¨ Ø¨Ø­Ø³Ø¨:
landmarkSearchPage.index.relevance  = ÙØ¯Ù Ø§ÙØµÙØ©
landmarkSearchPage.index.alphabetical  = ØªØ±ØªÙØ¨ Ø£Ø¨Ø¬Ø¯Ù
landmarkSearchPage.index.low.to.high  = Ø§ÙØ³Ø¹Ø± - ÙÙ Ø§ÙØ£ÙÙ Ø¥ÙÙ Ø§ÙØ£Ø¹ÙÙ
landmarkSearchPage.index.high.to.low  = Ø§ÙØ³Ø¹Ø± - ÙÙ Ø§ÙØ£Ø¹ÙÙ Ø¥ÙÙ Ø§ÙØ£ÙÙ
landmarkSearchPage.index.recent.products  = ÙØ·Ø¹ Ø¬Ø¯ÙØ¯Ø©
landmarkSearchPage.narrowby  = Ø­ØµØ± ÙØªØ§Ø¦Ø¬ Ø§ÙØ¨Ø­Ø« Ø¨Ø­Ø³Ø¨
landmarkSearchPage.filters  = ÙØ±Ø´Ø­Ø§Øª
landmarkSearchPage.filter.clearall  = ÙØ³Ø­ Ø¬ÙÙØ¹ Ø§ÙØªØµÙÙÙØ§Øª
landmarkSearchPage.filter.price  = Ø§ÙØ³Ø¹Ø±
landmarkSearchPage.product  = Ø§ÙÙÙØªØ¬
landmarkSearchPage.products  = ÙÙØªØ¬Ø§Øª
landmarkSearchPage.categories  = Ø§ÙÙØ¦Ø§Øª
landmarkSearchPage.close  = Ø¥ØºÙØ§Ù
landmarkSearchPage.items3  = Ø«ÙØ§Ø« ÙØ·Ø¹
landmarkSearchPage.items5  = Ø®ÙØ³ ÙØ·Ø¹
landmarkSearchPage.title  = Ø§ÙØªØ³ÙÙ Ø£ÙÙÙØ§ÙÙ ÙÙ LandmarkShops.com
landmarkSearchPage.pagination.previouspage  = Ø§ÙØµÙØ­Ø© Ø§ÙØ³Ø§Ø¨ÙØ©
landmarkSearchPage.pagination.nextpage  = Ø§ÙØµÙØ­Ø© Ø§ÙØªØ§ÙÙØ©
landmarkSearchPage.youSearchedFor  = ÙÙØ¯ Ø¨Ø­Ø«Øª Ø¹Ù
landmarkSearchPage.noResult  = ÙØ§ ÙØªØ§Ø¦Ø¬
landmarkCheckoutThankYouPage.thankyou.msg  = Ø´ÙØ±ÙØ§ ÙÙ
landmarkCheckoutThankYouPage.order.placed  = ÙØ´ÙØ±Ù Ø¹ÙÙ ØªÙØ¯ÙÙ Ø§ÙØ·ÙØ¨.
landmarkCheckoutThankYouPage.order.number  = Ø±ÙÙ Ø·ÙØ¨Ù ÙÙ
landmarkCheckoutThankYouPage.cod.msg = Ø³ÙØªØµÙ Ø¨Ù ÙØ±ÙØ¨ÙØ§ ÙØªØ£ÙÙØ¯ Ø·ÙØ¨ÙØ ÙØ¨ÙØ¬Ø±Ø¯ ØªØ£ÙÙØ¯Ù Ø³ÙÙ ØªØ­ØµÙ Ø¹ÙÙÙ
landmarkCheckoutThankYouPage.cod.hc.msg  = Ø³ÙØªØµÙ Ø¨Ù ÙØ±ÙØ¨ÙØ§ ÙØªØ£ÙÙØ¯ Ø·ÙØ¨Ù.
landmarkCheckoutThankYouPage.area.extradays  = ÙÙÙ Ø­Ø§Ù ÙØ§Ù Ø·ÙØ¨Ù ÙÙ ÙØ§Ø­Ø¯Ø© ÙÙ <a href="#" data-toggle="modal" class="_link-grey" data-target="#arealist-modal">ÙØ°Ù Ø§ÙÙÙØ§Ø·Ù</a> Ø ÙØ¯ ØªØ³ØªØºØ±Ù Ø¹ÙÙÙØ© ØªÙØµÙÙÙ Ø¨Ø¶Ø¹Ø© Ø£ÙØ§Ù Ø¥Ø¶Ø§ÙÙØ©.
landmarkCheckoutThankYouPage.delvery.msg  = Ø³ØªØ­ØµÙ Ø¹ÙÙ Ø·ÙØ¨Ù
landmarkCheckoutThankYouPage.view.account.before  = Ø¹Ø±Ø¶
landmarkCheckoutThankYouPage.view.account.link  = Ø­Ø³Ø§Ø¨Ù
landmarkCheckoutThankYouPage.view.account.after  = ÙÙØ¹Ø±ÙØ© Ø§ÙÙØ²ÙØ¯
landmarkCheckoutThankYouPage.you.may.enjoy  = ÙØ¯ ÙØ¹Ø¬Ø¨Ù Ø£ÙØ¶ÙØ§
landmarkCheckoutThankYouPage.create.account  = Ø¥ÙØ´Ø§Ø¡ Ø­Ø³Ø§Ø¨ Ø¬Ø¯ÙØ¯
landmarkCheckoutThankYouPage.create.account.benefit  = ÙØ¬ÙØ¯ Ø­Ø³Ø§Ø¨ Ø®Ø§Øµ Ø¨Ù ÙØ¬Ø¹Ù Ø¹ÙÙÙØ© Ø§ÙØªØ³ÙÙ ÙØ¹ÙØ§ Ø£Ø³Ø±Ø¹ ÙØ£ÙØ«Ø± Ø°ÙØ§Ø¡ ÙØ¨Ø³Ø§Ø·Ø© ÙÙÙØ¹ÙØ§ ÙÙ.
landmarkCheckoutThankYouPage.create.account.password  = ÙÙÙØ© Ø§ÙÙØ±ÙØ±
landmarkCheckoutThankYouPage.create.account.password.show  = Ø¥Ø¸ÙØ§Ø±
landmarkCheckoutThankYouPage.create.account.password.good  = Ø¬ÙØ¯
landmarkCheckoutThankYouPage.create.account.label  = Ø¥ÙØ´Ø§Ø¡ Ø­Ø³Ø§Ø¨
landmarkCheckoutThankYouPage.gift.voucher = Ø³ÙØ±Ø³Ù ÙÙ ÙØ³ÙÙØ© ÙØ¯ÙØ©!
landmarkCheckoutThankYouPage.gift.voucher.details = Ø³ÙØªÙ Ø¥Ø±Ø³Ø§Ù Ø±ÙØ² ÙØ³ÙÙØªÙ Ø¹Ù Ø·Ø±ÙÙ Ø§ÙØ¨Ø±ÙØ¯ Ø§ÙØ¥ÙÙØªØ±ÙÙÙ ÙÙØ± ÙÙØ§ÙÙ Ø¨Ø§Ø³ØªÙÙØ§Ù Ø§ÙØ·ÙØ¨.
landmarkCheckoutThankYouPage.extradays.title  = Ø§ÙÙÙØ§Ø·Ù Ø§ÙØªÙ ÙØ³ØªØºØ±Ù Ø§ÙØªÙØµÙÙ Ø¥ÙÙÙØ§ Ø¨Ø¶Ø¹Ø© Ø£ÙØ§Ù Ø¥Ø¶Ø§ÙÙØ©
thankyou.delivery.nextday  = ÙÙ ÙÙÙ Ø§ÙØ¹ÙÙ Ø§ÙØªØ§ÙÙ.
thankyou.delivery.estimate  = Ø®ÙØ§Ù 4-5 Ø£ÙØ§Ù Ø¹ÙÙ.
thankyou.delivery.pincode.estimate.pre  = Ø®ÙØ§Ù
thankyou.delivery.pincode.estimate.post  = Ø£ÙØ§Ù Ø¹ÙÙ
landmarkForgotPassword.request.email.ajax.failed.message  = Ø¹Ø°Ø±ÙØ§Ø ÙØ§Ø¬ÙØªÙØ§ ÙØ´ÙÙØ© ÙÙÙØ©. ÙØ±Ø¬Ù Ø¥Ø¹Ø§Ø¯Ø© Ø§ÙÙØ­Ø§ÙÙØ© ÙØ§Ø­ÙÙØ§.
landmarkForgotPassword.header  = ÙØ§ Ø¯Ø§Ø¹Ù ÙÙÙÙÙ. Ø£Ø¯Ø®Ù Ø¹ÙÙØ§Ù Ø¨Ø±ÙØ¯Ù Ø§ÙØ¥ÙÙØªØ±ÙÙÙØ ÙØ³ÙØ³Ø§Ø¹Ø¯Ù Ø¹ÙÙ Ø¥Ø¹Ø§Ø¯Ø© ØªØ¹ÙÙÙÙØ§.
landmarkForgotPassword.label.email  = Ø§ÙØ¨Ø±ÙØ¯ Ø§ÙØ¥ÙÙØªØ±ÙÙÙ
landmarkForgotPassword.label.submit  = Ø¥Ø±Ø³Ø§Ù
landmarkForgotPassword.label.rest.pwd  = Ø±Ø§Ø¨Ø· Ø¥Ø¹Ø§Ø¯Ø© ØªØ¹ÙÙÙ ÙÙÙØ© Ø§ÙÙØ±ÙØ± ÙÙ Ø·Ø±ÙÙÙ Ø¥ÙÙ ØµÙØ¯ÙÙ Ø§ÙØ¨Ø±ÙØ¯ Ø§ÙÙØ§Ø±Ø¯.
landmarkForgotPassword.label.thankyou  = Ø´ÙØ±ÙØ§ ÙÙØ
landmarkForgotPassword.label.resend  = Ø¥Ø¹Ø§Ø¯Ø© Ø¥Ø±Ø³Ø§ÙØ
landmarkForgotPassword.required.email  = Ø£Ø¹ÙÙÙØ§ Ø¨Ø§ÙÙØ¬ÙØ© Ø§ÙÙÙØ§Ø³Ø¨Ø© Ø¨Ø§ÙÙØ³Ø¨Ø© ÙÙ ÙØ¥Ø±Ø³Ø§Ù Ø±Ø§Ø¨Ø· Ø¥Ø¹Ø§Ø¯Ø© ØªØ¹ÙÙÙ ÙÙÙØ© Ø§ÙÙØ±ÙØ±Ø ÙØ³ÙÙÙÙ Ø¨Ø°ÙÙ Ø¹ÙÙ Ø§ÙÙÙØ±.
landmarkForgotPassword.invalid.email  = Ø¹ÙÙÙØ§! Ø¹ÙÙØ§Ù Ø§ÙØ¨Ø±ÙØ¯ Ø§ÙØ¥ÙÙØªØ±ÙÙÙ ÙØ°Ø§ ÙØ§ ÙØ¨Ø¯Ù ØµØ­ÙØ­ÙØ§. ÙØ±Ø¬Ù Ø¥Ø¹Ø§Ø¯Ø© Ø§ÙÙØ­Ø§ÙÙØ©.
landmarkForgotPassword.email.sent.success.message  = ÙØ±Ø¬Ù Ø§ØªØ¨Ø§Ø¹ Ø§ÙØªØ¹ÙÙÙØ§Øª Ø§ÙÙØ±Ø³ÙØ© Ø¥ÙÙ Ø¨Ø±ÙØ¯Ù Ø§ÙØ¥ÙÙØªØ±ÙÙÙ ÙÙ Ø£Ø¬Ù ØªØ¹ÙÙÙ ÙÙÙØ© Ø§ÙÙØ±ÙØ± Ø§ÙØ®Ø§ØµØ© Ø¨Ù.
landmarkForgotPassword.email.sent.fail.message  = Ø¹ÙÙØ§Ù Ø§ÙØ¨Ø±ÙØ¯ Ø§ÙØ¥ÙÙØªØ±ÙÙÙ ÙØ°Ø§ ØºÙØ± ÙØ­ÙÙØ¸ ÙØ¯ÙÙØ§. ÙØ±Ø¬Ù Ø§ÙØªØ­ÙÙ ÙØ¥Ø¹Ø§Ø¯Ø© Ø§ÙÙØ­Ø§ÙÙØ©.
landmarkResetPassword.title  = Ø¥Ø¹Ø§Ø¯Ø© ØªØ¹ÙÙÙ ÙÙÙØ© Ø§ÙÙØ±ÙØ±
landmarkResetPassword.label.newpwd  = ÙÙÙØ© Ø§ÙÙØ±ÙØ± Ø§ÙØ¬Ø¯ÙØ¯Ø©
landmarkResetPassword.label.confirmpwd  = ØªØ£ÙÙØ¯ ÙÙÙØ© Ø§ÙÙØ±ÙØ±
landmarkResetPassword.label.show  = Ø¥Ø¸ÙØ§Ø±
landmarkResetPassword.label.submit  = Ø¥Ø±Ø³Ø§Ù
landmarkResetPassword.placeholder.newpwd  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù ÙÙÙØ© Ø§ÙÙØ±ÙØ± Ø§ÙØ¬Ø¯ÙØ¯Ø©
landmarkResetPassword.placeholder.confirmpwd  = ÙØ±Ø¬Ù ØªØ£ÙÙØ¯ ÙÙÙØ© Ø§ÙÙØ±ÙØ±
landmarkResetPassword.unmatchedpwd.message  = ÙÙÙØ§Øª Ø§ÙÙØ±ÙØ± Ø§ÙØªÙ ÙÙØª Ø¨Ø¥Ø¯Ø®Ø§ÙÙØ§ ØºÙØ± ÙØªØ·Ø§Ø¨ÙØ©. ÙØ±Ø¬Ù Ø§ÙØªØ­ÙÙ ÙØ¥Ø¹Ø§Ø¯Ø© Ø§ÙÙØ­Ø§ÙÙØ©.
landmarkResetPassword.required.newpwd  = ÙØ¬Ø¨ Ø¥Ø¯Ø®Ø§Ù ÙÙÙØ© ÙØ±ÙØ± Ø¬Ø¯ÙØ¯Ø©
landmarkResetPassword.required.confirmpwd  = ÙØ±Ø¬Ù ØªØ£ÙÙØ¯ ÙÙÙØ© Ø§ÙÙØ±ÙØ±
landmarkResetPassword.minlength.message  = ÙÙØ§Ø Ø¨Ø¥ÙÙØ§ÙÙ Ø§Ø®ØªÙØ§Ø± ÙØ§ ÙÙ Ø£ÙØ¶Ù ÙÙ ÙØ°Ù. ÙØ¬Ø¨ Ø£Ù ØªØªØ£ÙÙ ÙÙÙØ© Ø§ÙÙØ±ÙØ± ÙÙ 6 Ø®Ø§ÙØ§ØªÙ Ø¹ÙÙ Ø§ÙØ£ÙÙ.
landmarkResetPassword.newpasswordtips.title  = ÙØµØ§Ø¦Ø­ Ø­ÙÙ ØªÙÙÙÙ ÙÙÙØ© ÙØ±ÙØ± Ø¬Ø¯ÙØ¯Ø©
landmarkResetPassword.newpasswordtips.one  = ÙØ¬Ø¨ Ø§ÙØªØ£ÙØ¯ ÙÙ Ø£Ù ÙÙÙØ© Ø§ÙÙØ±ÙØ± ØªØªØ£ÙÙ ÙÙ Ø³ØªØ© Ø®Ø§ÙØ§ØªÙ Ø¹ÙÙ Ø§ÙØ£ÙÙ ÙØªØªØ¶ÙÙ ÙØ²ÙØ¬ÙØ§ ÙÙ Ø§ÙØ­Ø±ÙÙ Ø§ÙØµØºÙØ±Ø© Ù/Ø£Ù Ø§ÙÙØ¨ÙØ±Ø© ÙØ¹ Ø±ÙÙÙ Ø£Ù Ø­Ø±Ù Ø®Ø§Øµ ÙØ§Ø­Ø¯ Ø¹ÙÙ Ø§ÙØ£ÙÙ. ÙØªØ¹Ø¯ Ø§ÙØ­Ø±ÙÙ Ø§ÙÙØ¨ÙØ±Ø© ÙØ®ØªÙÙØ© Ø¹Ù Ø§ÙØ­Ø±ÙÙ Ø§ÙØµØºÙØ±Ø© Ø¥ÙØ§ Ø£ÙÙØ§ ØºÙØ± Ø¥ÙØ²Ø§ÙÙØ©.
landmarkResetPassword.newpasswordtips.two  = ÙØ§ ØªØ³ØªØ®Ø¯Ù ÙØ¹ÙÙÙØ§ØªÙ Ø§ÙØ´Ø®ØµÙØ©.
landmarkForgotPassword.updated.success.message  = ÙÙØªØ§Ø²! ØªÙ ØªØºÙÙØ± ÙÙÙØ© Ø§ÙÙØ±ÙØ± Ø§ÙØ®Ø§ØµØ© Ø¨Ù Ø¨ÙØ¬Ø§Ø­. ÙØ±Ø¬Ù ØªØ³Ø¬ÙÙ Ø§ÙØ¯Ø®ÙÙ ÙÙÙØµÙÙ Ø¥ÙÙ Ø­Ø³Ø§Ø¨Ù.
landmarkForgotPassword.update.token.invalidated.message  = ÙØ¨Ø¯Ù Ø£Ù Ø±Ø§Ø¨Ø· Ø¥Ø¹Ø§Ø¯Ø© ØªØ¹ÙÙÙ ÙÙÙØ© Ø§ÙÙØ±ÙØ± ÙÙ ÙØ¹Ø¯ ØµØ§ÙØ­ÙØ§. ÙØ±Ø¬Ù Ø§ÙÙÙØ± Ø¹ÙÙ Ø²Ø± "ÙØ³ÙØª ÙÙÙØ© Ø§ÙÙØ±ÙØ±Ø" ÙØ¥Ø¹Ø§Ø¯Ø© Ø§ÙÙØ­Ø§ÙÙØ©.
landmarkForgotPassword.update.token.invalid.message  = ÙÙØ£Ø³Ù ÙÙ ÙØ¹Ø¯ ÙØ°Ø§ Ø§ÙØ±Ø§Ø¨Ø· ØµØ§ÙØ­ÙØ§.
landmarkRegister.invalid.form.attribute  = ÙÙ ÙØªÙ Ø¥Ø¯Ø®Ø§Ù Ø§ÙØªÙØ§ØµÙÙ Ø§ÙØ®Ø§ØµØ© Ø¨Ù Ø¨Ø§ÙØµÙØ±Ø© Ø§ÙØµØ­ÙØ­Ø©. ÙØ±Ø¬Ù Ø¥Ø¹Ø§Ø¯Ø© Ø§ÙÙØ­Ø§ÙÙØ©.
landmarkHelp.stillHaveQuestions.desktop.header.title  = ÙØ§ ØªØ²Ø§Ù ÙØ¯ÙÙ Ø£Ø³Ø¦ÙØ©Ø
landmarkHelp.stillHaveQuestions.mobile.header.title  = Ø§ØªØµÙ Ø¨ÙØ§
landmarkHelp.stillHaveQuestions.desktop.header.desc  = ÙØ±Ø­Ø¨ÙØ§ Ø¨Ù ÙÙ ÙØ³Ù Ø®Ø¯ÙØ© Ø§ÙØ¹ÙÙØ§Ø¡ ÙØ¯ÙÙØ§! ÙØ­Ù ÙÙØ§ ÙÙØ³Ø§Ø¹Ø¯ØªÙ.
landmarkHelp.stillHaveQuestions.mobile.header.desc  = ÙØ­Ù ÙÙØ¬ÙØ¯ÙÙ ÙÙØ§ ÙÙØ³Ø§Ø¹Ø¯ØªÙØ ÙØ¨Ø¥ÙÙØ§ÙÙ Ø§ÙØªÙØ§ØµÙ ÙØ¹ÙØ§ Ø¹Ø¨Ø± Ø£ÙÙ ÙÙ ÙØ°Ù Ø§ÙØ·Ø±Ù:
landmarkHelp.stillHaveQuestions.email.title  = Ø±Ø§Ø³ÙÙØ§
landmarkHelp.stillHaveQuestions.email.desc  = Ø§ÙØ§ØªØµØ§Ù Ø¨ÙØ§ ÙØ§ØªÙÙÙØ§Ø ÙØ³ÙÙÙÙ Ø¨ÙØ¹Ø§Ø¯Ø© Ø§ÙØ§ØªØµØ§Ù Ø¨Ù Ø¨Ø£Ø³Ø±Ø¹ ÙØ§ ÙØ³ØªØ·ÙØ¹.
landmarkHelp.stillHaveQuestions.phone.title  = ØªØ­Ø¯Ø« Ø¥ÙÙÙØ§
landmarkHelp.stillHaveQuestions.phone.desc  = Ø§ØªØµÙ Ø¨ÙØ§ Ø¹ÙÙ Ø§ÙØ±ÙÙ 1555-123-1800 </br> ÙÙ Ø§ÙØ§Ø«ÙÙÙ Ø¥ÙÙ Ø§ÙØ³Ø¨ØªØ ÙÙÙ 9:00 ØµØ¨Ø§Ø­ÙØ§ Ø­ØªÙ 9:00 ÙØ³Ø§Ø¡Ù
landmarkHelp.stillHaveQuestions.twitter.title  = Ø£Ø±Ø³Ù ÙÙØ§ ØªØºØ±ÙØ¯Ø©
landmarkHelp.stillHaveQuestions.twitter.desc  = ØªÙØ§ØµÙ ÙØ¹ÙØ§ Ø¨Ø±Ø³Ø§ÙØ© ÙÙ 140 Ø­Ø±ÙÙØ§!
landmarkHelp.stillHaveQuestions.facebook.title  = Ø£Ø±Ø³Ù Ø±Ø³Ø§ÙØ© Ø¹Ø¨Ø± ÙÙØ³Ø¨ÙÙ
landmarkHelp.stillHaveQuestions.facebook.desc  = ØªÙØ§ØµÙ ÙØ¹ÙØ§ Ø¹Ø¨Ø± Ø§ÙØ´Ø¨ÙØ© Ø§ÙÙÙØ¶ÙØ© ÙØ¯ÙÙ ÙÙØªÙØ§ØµÙ Ø§ÙØ§Ø¬ØªÙØ§Ø¹Ù.
landmarkHelp.stillHaveQuestions.chat.title  = ØªØ­Ø¯ÙØ« Ø¥ÙÙÙØ§
landmarkHelp.stillHaveQuestions.chat.online.desc  = Ø§Ø­ØµÙ Ø¹ÙÙ Ø§ÙØ¥Ø¬Ø§Ø¨Ø§Øª Ø§ÙØªÙ ØªØ­ØªØ§Ø¬ÙØ§ Ø¨ØµÙØ±Ø©Ù Ø¢ÙÙØ©Ø ÙÙØ­Ù ÙÙØ¬ÙØ¯ÙÙ ÙÙØ§ ÙÙÙØ³Ø§Ø¹Ø¯Ø©.
landmarkHelp.stillHaveQuestions.chat.online.text  = Ø¯Ø±Ø¯Ø´ ÙØ¹ÙØ§ Ø§ÙØ¢Ù!
landmarkHelp.stillHaveQuestions.chat.offline.desc  = Ø³ÙØ¹ÙØ¯ ÙÙØªÙØ§Ø¬Ø¯ Ø£ÙÙÙØ§ÙÙ ÙØ±ÙØ¨ÙØ§.
landmarkStoreLocator.search.text  = Ø§ÙØ¨Ø­Ø« Ø¹Ù ÙØªØ­Ø± Ø¨Ø­Ø³Ø¨ Ø§ÙÙØ¯ÙÙØ© Ø£Ù Ø§ÙÙÙØ·ÙØ©
landmarkHelp.stillHaveQuestions.chat.msg  = Ø£ÙÙØ§Øª ØªÙØ§Ø¬Ø¯ÙØ§ ÙØªÙÙÙ Ø§ÙØ§ØªØµØ§ÙØ§Øª ÙØ§ÙØ¯Ø±Ø¯Ø´Ø©Ø
landmarkHelp.stillHaveQuestions.email.desc.contactus  = Ø£Ø±Ø³Ù ÙÙØ§ Ø±Ø³Ø§ÙØ© Ø¥ÙÙØªØ±ÙÙÙØ© Ø§ÙØ¢Ù!
homecentreae.landmarkHelp.stillHaveQuestions.phone.desc.contactus  = ÙÙ Ø§ÙØ³Ø¨Øª Ø¥ÙÙ Ø§ÙØ®ÙÙØ³Ø 9:00 ØµØ¨Ø§Ø­ÙØ§ ÙØ­ØªÙ 10:00 ÙØ³Ø§Ø¡Ù
lifestyleae.landmarkHelp.stillHaveQuestions.phone.desc.contactus  = Ø¹ÙÙ ÙØ¯Ø§Ø± Ø§ÙÙØ§Ù Ø§ÙØ£Ø³Ø¨ÙØ¹Ø ÙÙ 9:00 ØµØ¨Ø§Ø­ÙØ§ ÙØ­ØªÙ 9:00 ÙØ³Ø§Ø¡Ù
babyshopae.landmarkHelp.stillHaveQuestions.phone.desc.contactus  = Ø¹ÙÙ ÙØ¯Ø§Ø± Ø§ÙÙØ§Ù Ø§ÙØ£Ø³Ø¨ÙØ¹Ø ÙÙ 9:00 ØµØ¨Ø§Ø­ÙØ§ ÙØ­ØªÙ 9:00 ÙØ³Ø§Ø¡Ù
centrepointae.landmarkHelp.stillHaveQuestions.phone.desc.contactus  = Ø¹ÙÙ ÙØ¯Ø§Ø± Ø§ÙÙØ§Ù Ø§ÙØ£Ø³Ø¨ÙØ¹Ø ÙÙ 9:00 ØµØ¨Ø§Ø­ÙØ§ ÙØ­ØªÙ 9:00 ÙØ³Ø§Ø¡Ù
maxae.landmarkHelp.stillHaveQuestions.phone.desc.contactus  = Ø¹ÙÙ ÙØ¯Ø§Ø± Ø§ÙÙØ§Ù Ø§ÙØ£Ø³Ø¨ÙØ¹Ø ÙÙ 9:00 ØµØ¨Ø§Ø­ÙØ§ ÙØ­ØªÙ 9:00 ÙØ³Ø§Ø¡Ù
shoemartae.landmarkHelp.stillHaveQuestions.phone.desc.contactus  = Ø¹ÙÙ ÙØ¯Ø§Ø± Ø§ÙÙØ§Ù Ø§ÙØ£Ø³Ø¨ÙØ¹Ø ÙÙ 9:00 ØµØ¨Ø§Ø­ÙØ§ ÙØ­ØªÙ 9:00 ÙØ³Ø§Ø¡Ù
splashae.landmarkHelp.stillHaveQuestions.phone.desc.contactus  = Ø¹ÙÙ ÙØ¯Ø§Ø± Ø§ÙÙØ§Ù Ø§ÙØ£Ø³Ø¨ÙØ¹Ø ÙÙ 9:00 ØµØ¨Ø§Ø­ÙØ§ ÙØ­ØªÙ 9:00 ÙØ³Ø§Ø¡Ù
homeboxae.landmarkHelp.stillHaveQuestions.phone.desc.contactus = Ø¹ÙÙ ÙØ¯Ø§Ø± Ø§ÙÙØ§Ù Ø§ÙØ£Ø³Ø¨ÙØ¹Ø ÙÙ 9:00 ØµØ¨Ø§Ø­ÙØ§ ÙØ­ØªÙ 9:00 ÙØ³Ø§Ø¡Ù
landmarkHelp.stillHaveQuestions.phone.tel.contactus  = {0}
landmarkHelp.stillHaveQuestions.phone.tel.contactus.text  = Ø§ØªØµÙ Ø¨ÙØ§ Ø¹ÙÙ Ø§ÙØ±ÙÙ
landmarkHelp.stillHaveQuestions.twitter.desc.contactus  = ØªÙØ§ØµÙ ÙØ¹ÙØ§ Ø¨Ø±Ø³Ø§ÙØ© ÙÙ 140 Ø­Ø±ÙÙØ§!
landmarkHelp.stillHaveQuestions.twitter.desc2.contactus  = Ø³ÙØ£ØªÙÙÙ ÙØ±ÙØ¨ÙØ§!
landmarkHelp.stillHaveQuestions.facebook.title.contactus  = Ø¯Ø±Ø¯Ø´ ÙØ¹ÙØ§
landmarkHelp.stillHaveQuestions.facebook.desc.contactus  = Ø§Ø­ØµÙ Ø¹ÙÙ Ø§ÙØ¥Ø¬Ø§Ø¨Ø§Øª Ø§ÙØªÙ ØªØ­ØªØ§Ø¬ÙØ§ Ø¨ØµÙØ±Ø©Ù Ø¢ÙÙØ©Ø ÙÙØ­Ù ÙÙØ¬ÙØ¯ÙÙ ÙÙØ§ ÙÙÙØ³Ø§Ø¹Ø¯Ø©.
landmarkHelp.stillHaveQuestions.chat.online  = ÙØªÙØ§Ø¬Ø¯ÙÙ
landmarkHelp.stillHaveQuestions.heading.title.contactus  = Ø§ØªØµÙ Ø¨ÙØ§
lifestyleae.landmarkHelp.stillHaveQuestions.heading.desc.contactus  = Ø¨Ø­Ø§Ø¬Ø© Ø¥ÙÙ Ø§ÙÙØ³Ø§Ø¹Ø¯Ø© ÙØ§ÙØ¯Ø¹Ù ÙØ§ÙÙØ¹ÙÙÙØ§Øª Ø¹Ù ÙØ§ÙÙ Ø³ØªØ§ÙÙ Ø´ÙØ¨Ø³Ø Ø³ØªØ¬Ø¯ÙØ§ ÙÙØ§.
babyshopae.landmarkHelp.stillHaveQuestions.heading.desc.contactus  = Ø¨Ø­Ø§Ø¬Ø© Ø¥ÙÙ Ø§ÙÙØ³Ø§Ø¹Ø¯Ø© ÙØ§ÙØ¯Ø¹Ù ÙØ§ÙÙØ¹ÙÙÙØ§Øª Ø¹Ù Ø¨ÙØ¨Ù Ø´ÙØ¨Ø Ø³ØªØ¬Ø¯ÙØ§ ÙÙØ§.
centrepointae.landmarkHelp.stillHaveQuestions.heading.desc.contactus  = Ø¨Ø­Ø§Ø¬Ø© Ø¥ÙÙ Ø§ÙÙØ³Ø§Ø¹Ø¯Ø© ÙØ§ÙØ¯Ø¹Ù ÙØ§ÙÙØ¹ÙÙÙØ§Øª Ø¹Ù Ø³ÙØªØ±Ø¨ÙÙÙØªØ Ø³ØªØ¬Ø¯ÙØ§ ÙÙØ§.
homecentreae.landmarkHelp.stillHaveQuestions.heading.desc.contactus  = Ø¨Ø­Ø§Ø¬Ø© Ø¥ÙÙ Ø§ÙÙØ³Ø§Ø¹Ø¯Ø© ÙØ§ÙØ¯Ø¹Ù ÙØ§ÙÙØ¹ÙÙÙØ§Øª Ø¹Ù ÙÙÙ Ø³ÙØªØ±Ø Ø³ØªØ¬Ø¯ÙØ§ ÙÙØ§.
maxae.landmarkHelp.stillHaveQuestions.heading.desc.contactus  = Ø¨Ø­Ø§Ø¬Ø© Ø¥ÙÙ Ø§ÙÙØ³Ø§Ø¹Ø¯Ø© ÙØ§ÙØ¯Ø¹Ù ÙØ§ÙÙØ¹ÙÙÙØ§Øª Ø¹Ù ÙØ§ÙØ³ ÙØ§Ø´ÙØ Ø³ØªØ¬Ø¯ÙØ§ ÙÙØ§.
shoemartae.landmarkHelp.stillHaveQuestions.heading.desc.contactus  = Ø¨Ø­Ø§Ø¬Ø© Ø¥ÙÙ Ø§ÙÙØ³Ø§Ø¹Ø¯Ø© ÙØ§ÙØ¯Ø¹Ù ÙØ§ÙÙØ¹ÙÙÙØ§Øª Ø¹Ù Ø´Ù ÙØ§Ø±ØªØ Ø³ØªØ¬Ø¯ÙØ§ ÙÙØ§.
splashae.landmarkHelp.stillHaveQuestions.heading.desc.contactus  = Ø§ÙÙØ³Ø§Ø¹Ø¯Ø© ÙØ§ÙØ¯Ø¹Ù ÙØ§ÙÙØ¹ÙÙÙØ§Øª Ø¹Ù Ø³Ø¨ÙØ§Ø´ ÙØ§Ø´ÙØ²Ø Ø³ØªØ¬Ø¯ÙØ§ ÙÙØ§.
landmarkHelp.stillHaveQuestions.heading.para.desc.contactus  = ÙØ­Ù ÙÙØ¬ÙØ¯ÙÙ ÙÙØ§ ÙÙØ³Ø§Ø¹Ø¯ØªÙØ ÙØ¨Ø¥ÙÙØ§ÙÙ Ø§ÙØªÙØ§ØµÙ ÙØ¹ÙØ§ Ø¹Ø¨Ø± Ø£ÙÙ ÙÙ ÙØ°Ù Ø§ÙØ·Ø±Ù:

landmarkFeedback.heading.title.feedback.message.text = Feedback
landmarkFeedback.heading.title.feedback  = Ø§ÙØ¢Ø±Ø§Ø¡ ÙØ§ÙÙÙØ§Ø­Ø¸Ø§Øª
landmarkFeedback.heading.title.feedback.message.text = Ø§ÙØ¢Ø±Ø§Ø¡ ÙØ§ÙÙÙØ§Ø­Ø¸Ø§Øª
landmarkFeedback.heading.title.feedback.text  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø¢Ø±Ø§Ø¦Ù ÙÙÙØ§Ø­Ø¸Ø§ØªÙ ÙÙØ§
landmarkFeedback.heading.title.feedback.description  = ÙØ¯ÙÙ Ø§ÙØªØ±Ø§Ø­Ø§Øª Ø£Ù ØªØ­Ø³ÙÙØ§Øª Ø£Ù ØªØ¹ÙÙÙØ§ØªØ ÙØ³Ø¹Ø¯ÙØ§ Ø³ÙØ§Ø¹ ÙÙ Ø°ÙÙ.
landmarkFeedback.heading.title.feedback.name  = Ø§ÙØ§Ø³Ù
landmarkFeedback.heading.title.feedback.name.invalid  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø§Ø³ÙÙ
landmarkFeedback.heading.title.feedback.name.valid  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø§Ø³Ù ØµØ­ÙØ­
landmarkFeedback.heading.title.feedback.name.text  = Ø§Ø³ÙÙ
landmarkFeedback.heading.title.feedback.email.invalid  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø¹ÙÙØ§Ù Ø¨Ø±ÙØ¯ Ø¥ÙÙØªØ±ÙÙÙ ØµØ­ÙØ­
landmarkFeedback.heading.title.feedback.email.valid  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø¹ÙÙØ§Ù Ø¨Ø±ÙØ¯Ù Ø§ÙØ¥ÙÙØªØ±ÙÙÙ
landmarkFeedback.heading.title.feedback.email.text  = Ø¹ÙÙØ§Ù Ø¨Ø±ÙØ¯Ù Ø§ÙØ¥ÙÙØªØ±ÙÙÙ
landmarkFeedback.heading.title.feedback.button.title  = Ø£Ø±Ø³Ù Ø±Ø£ÙÙ ÙÙÙØ§Ø­Ø¸Ø§ØªÙ
landmarkFeedback.heading.title.feedback.name.maxlength.limit = Ø§ÙØ±Ø¬Ø§Ø¡ Ø¥Ø¯Ø®Ø§Ù ÙØ§ ÙØ²ÙØ¯ Ø¹Ù {0} Ø­Ø±ÙØ§Ù
landmarkFeedback.heading.title.feedback.contactnumber.required = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø±ÙÙ Ø§ÙÙØ§ØªÙ Ø§ÙÙØªØ­Ø±Ù
landmarkFeedback.success.msg  = ØªÙ Ø¥Ø±Ø³Ø§Ù Ø¢Ø±Ø§Ø¦Ù ÙÙÙØ§Ø­Ø¸Ø§ØªÙ Ø¨ÙØ¬Ø§Ø­. ÙØ´ÙØ±Ù ÙØ«ÙØ± Ø§ÙØ´ÙØ± ÙÙÙØ¯Ø± ÙÙ ÙØ´Ø§Ø±ÙØªÙØ§ Ø¥ÙØ§ÙØ§.
cart.delivery.address.missing  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø¹ÙÙØ§ÙÙ Ø§ÙØ®Ø§Øµ Ø¨Ø¹ÙÙÙØ© Ø§ÙØªÙØµÙÙ.
cart.paymentmode.missing  = Ø·Ø±ÙÙØ© Ø§ÙØ¯ÙØ¹ ØºÙØ± ÙÙØ¬ÙØ¯Ø©
cart.paymentinfo.missing  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø§ÙÙØ¹ÙÙÙØ§Øª Ø§ÙØ®Ø§ØµØ© Ø¨Ø¹ÙÙÙØ© Ø§ÙØ¯ÙØ¹.
cart.voucher.expired  = Ø§ÙÙØ³ÙÙØ© Ø§ÙÙØ³ØªØ¹ÙÙØ© ØºÙØ± ØµØ­ÙØ­Ø© Ø£Ù ÙÙØªÙÙØ© Ø§ÙØµÙØ§Ø­ÙØ©.
product.discontinued.error.msg  = Ø§ÙÙÙØªØ¬ ÙÙ ÙØ¹Ø¯ ÙØªÙÙØ±ÙØ§
product.outofstock.error.msg  = Ø§ÙÙÙØªØ¬ ÙÙØ° ÙÙ Ø§ÙÙØ®Ø²ÙÙ
product.threshold.error.msg  = ÙØµÙ Ø§ÙÙÙØªØ¬ Ø¥ÙÙ Ø­Ø¯Ù Ø§ÙØ£Ø¹ÙÙ.
product.mimimumprice.error.msg  = Ø³Ø¹Ø± Ø§ÙÙÙØªØ¬ Ø£ÙÙ ÙÙ Ø§ÙØ­Ø¯ Ø§ÙØ£Ø¯ÙÙ ÙÙØ³Ø¹Ø±.
landmarkAddressTypes.availableTypes  = Ø§ÙÙÙØ²ÙØ Ø§ÙÙÙØªØ¨
landmark.basket.validation.cart.generic.global.error  = Ø¹ÙÙÙØ§Ø ÙÙØ¯ Ø±ØµØ¯ÙØ§ Ø¨Ø¹Ø¶ Ø§ÙØ£Ø®Ø·Ø§Ø¡Ø ÙØ±Ø¬Ù Ø¥ØµÙØ§Ø­ÙØ§ Ø­ØªÙ ÙØªÙÙÙ ÙÙ Ø§ÙÙØªØ§Ø¨Ø¹Ø©.
landmark.basket.validation.cart.delivery.address.missing  = ÙØ¬Ø¨ Ø¥Ø¹Ø·Ø§Ø¡ Ø¹ÙÙØ§Ù Ø§ÙØªÙØµÙÙ ÙÙØªÙÙÙ ÙÙ Ø§ÙÙØªØ§Ø¨Ø¹Ø© Ø¥ÙÙ Ø§ÙØ®Ø·ÙØ© Ø§ÙØªØ§ÙÙØ©.
landmark.basket.validation.cart.delivery.mode.missing  = Ø·Ø±ÙÙØ© Ø§ÙØªÙØµÙÙ ØºÙØ± ÙÙØ¬ÙØ¯Ø©.
landmark.basket.validation.cart.paymentmode.missing  = Ø·Ø±ÙÙØ© Ø§ÙØ¯ÙØ¹ ØºÙØ± ÙÙØ¬ÙØ¯Ø©. ÙØ±Ø¬Ù Ø§Ø®ØªÙØ§Ø± ÙØ³ÙÙØ© Ø§ÙØ¯ÙØ¹.
landmark.basket.validation.cart.paymentinfo.missing  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø§ÙÙØ¹ÙÙÙØ§Øª Ø§ÙØ®Ø§ØµØ© Ø¨Ø¹ÙÙÙØ© Ø§ÙØ¯ÙØ¹.
landmark.basket.validation.cart.voucher.expired  = ÙØ¹ØªØ°Ø± Ø¹Ù ÙØ¨ÙÙ ÙØ°Ù Ø§ÙÙØ³ÙÙØ© ÙÙÙ ØºÙØ± ØµØ§ÙØ­Ø©. ÙØ±Ø¬Ù Ø¥Ø²Ø§ÙØªÙØ§ ÙÙÙØªØ§Ø¨Ø¹Ø©.
landmark.basket.validation.cart.entries.missing  = ÙØ§ ØªÙØ¬Ø¯ ÙØ·Ø¹ ÙÙ Ø³ÙØ© Ø§ÙØªØ³ÙÙ.
landmark.basket.validation.cart.calculation.error  = Ø¹ÙÙÙØ§! ÙØ¨Ø¯Ù Ø£ÙÙ ØªÙØ§Ø¬Ù ÙØ´ÙÙØ© ÙØ§. ÙØ±Ø¬Ù Ø¥ØµÙØ§Ø­ Ø§ÙØ£ÙØ± ÙØªØªÙÙÙ ÙÙ ÙØªØ§Ø¨Ø¹Ø© Ø§ÙØªØ³ÙÙ.
landmark.basket.validation.cart.validation.failed  = ÙÙ ØªÙØ¬Ø­ Ø¹ÙÙÙØ© Ø§ÙØªØ­ÙÙ ÙÙ Ø§ÙØ¨Ø·Ø§ÙØ©.
landmark.basket.validation.product.discontinued.error.msg  = Ø¹ÙÙÙØ§! ÙÙ ÙØ¹Ø¯ ÙØ°Ø§ Ø§ÙÙÙØªØ¬ ÙØªØ§Ø­ÙØ§. ÙØ±Ø¬Ù Ø¥Ø²Ø§ÙØªÙ ÙÙ Ø³ÙØ© Ø§ÙØªØ³ÙÙ ÙÙÙØªØ§Ø¨Ø¹Ø©
landmark.basket.validation.product.outofstock.error.msg  = Ø¹ÙÙÙØ§! ÙÙØ¯ ÙÙØ° ÙØ°Ø§ Ø§ÙÙÙØªØ¬ ÙÙ Ø§ÙÙØ®Ø²ÙÙ. ÙØ±Ø¬Ù Ø¥Ø²Ø§ÙØªÙ ÙÙ Ø³ÙØ© Ø§ÙØªØ³ÙÙ ÙÙÙØªØ§Ø¨Ø¹Ø©
landmark.basket.validation.product.lowstock.error.msg  = Ø¹ÙÙÙØ§! ÙÙ ÙØ¹Ø¯ ÙØ°Ø§ Ø§ÙÙÙØªØ¬ ÙØªÙÙØ±ÙØ§ ÙÙ Ø§ÙÙØ®Ø²ÙÙ Ø¨ÙÙÙØ© ÙØ§ÙÙØ©. ÙØ±Ø¬Ù ØªØºÙÙØ± Ø§ÙÙÙÙØ© Ø§ÙÙØ·ÙÙØ¨Ø© ÙÙÙ ÙÙÙØªØ§Ø¨Ø¹Ø©
landmark.basket.validation.product.threshold.error.msg  = Ø¹Ø°Ø±ÙØ§Ø ÙÙØ¯ ØªØ¬Ø§ÙØ²Øª Ø§ÙØ­Ø¯ Ø§ÙØ£ÙØµÙ Ø§ÙÙØ³ÙÙØ­ Ø¨Ù ÙÙ Ø§ÙÙÙÙØ© ÙØ§ÙØ¨Ø§ÙØº {2}. ÙØ±Ø¬Ù ØªÙÙÙÙ Ø§ÙÙÙÙØ© Ø«Ù Ø¥Ø¹Ø§Ø¯Ø© Ø§ÙÙØ­Ø§ÙÙØ©.
landmark.basket.validation.product.minimum.price.display  = ÙÙØ¯ Ø§ÙØ®ÙØ¶ Ø³Ø¹Ø± Ø§ÙÙÙØªØ¬ Ø§ÙÙØ¹ÙÙ Ø¹Ù Ø§ÙØ³Ø¹Ø± Ø§ÙÙØ¹Ø±ÙØ¶.
landmark.basket.validation.promotion.group.general.error  = Ø­Ø¯Ø« Ø®Ø·Ø£ ÙØ§ ÙÙ Ø§ÙØ¹Ø±Ø¶ Ø§ÙØªØ±ÙÙØ¬Ù ÙÙÙØ¬ÙÙØ¹Ø§Øª. ÙØ±Ø¬Ù Ø§ÙØªÙØ³ÙØ¹ ÙØ¥ØµÙØ§Ø­ Ø§ÙØ®Ø·Ø£.
landmark.basket.validation.text.payment.error.cod.optiob.exceed.threshold  = ÙÙØ¯ ØªØ¬Ø§ÙØ²Øª Ø§ÙØ­Ø¯ Ø§ÙØ£ÙØµÙ Ø§ÙÙØ³ÙÙØ­ ÙÙÙÙØ© Ø§ÙØ·ÙØ¨ Ø§ÙÙØ±ØªØ¨Ø· Ø¨Ø®ÙØ§Ø± Ø§ÙØ¯ÙØ¹ Ø¹ÙØ¯ Ø§ÙØ§Ø³ØªÙØ§ÙØ Ø¥Ø° ÙØ¬Ø¨ Ø£Ù ØªØ¨ÙÙ Ø£ÙÙ ÙÙ ÙØ¨ÙØº {0}{1}
landmark.basket.validation.payment.availability.missing  = Ø·Ø±ÙÙØ© Ø§ÙØ¯ÙØ¹ "{0}" ØºÙØ±  ÙØªÙÙØ±Ø© ÙÙØ±ÙØ² Ø§ÙØ¨Ø±ÙØ¯Ù {1}. ÙØ±Ø¬Ù  Ø§Ø®ØªÙØ§Ø± Ø·Ø±ÙÙØ© Ø¯ÙØ¹ ÙØ®ØªÙÙØ©.
landmark.basket.validation.cart.doublePromotion.error  = Ø¹ÙÙÙØ§! ÙØ¨Ø¯Ù Ø£Ù ÙÙØ§Ù ÙØ´ÙÙØ© ÙØ§. ÙØ±Ø¬Ù Ø¥Ø¹Ø§Ø¯Ø© Ø§ÙØªØ­ÙÙ ÙÙ Ø§ÙÙØ¨ÙØº Ø§ÙØ¥Ø¬ÙØ§ÙÙ ÙÙÙ Ø«ÙÙ ØªØ³Ø¬ÙÙ Ø·ÙØ¨Ù.
landmark.basket.validation.loyalty.partner.unreachable = Ø¹ÙÙÙØ§! ÙØ§ ÙÙÙÙ Ø§Ø³ØªØ±Ø¯Ø§Ø¯ ÙÙØ§Ø· Ø§ÙÙÙØ§ÙØ¢Øª Ø§ÙØ®Ø§ØµØ© Ø¨Ù Ø­Ø§ÙÙÙØ§ ÙØ¸Ø±ÙØ§ ÙÙØ¬ÙØ¯ Ø¨Ø¹Ø¶ Ø§ÙÙØ´Ø§ÙÙ Ø§ÙØªÙÙÙØ©.
landmark.basket.validation.loyalty.not.enough.points = Ø¹ÙÙÙØ§! ÙØ§ ÙÙÙÙ Ø§Ø³ØªØ±Ø¯Ø§Ø¯ ÙÙØ§Ø· Ø§ÙÙÙØ§ÙØ¢Øª Ø§ÙØ®Ø§ØµØ© Ø¨Ù Ø­Ø§ÙÙÙØ§ Ø¥Ø° ÙÙØ³ ÙØ¯ÙÙ Ø³ÙÙ {0} ÙÙØ·Ø© (ÙÙØ§Ø·) ÙØªØ¨ÙÙØ© ÙÙ Ø¨Ø·Ø§ÙØ© Ø§ÙÙÙØ§ÙØ¢Øª.
landmark.basket.validation.loyalty.redeem.failed = Ø¹ÙÙÙØ§! ÙØ§ ÙÙÙÙ Ø§Ø³ØªØ±Ø¯Ø§Ø¯ ÙÙØ§Ø· Ø§ÙÙÙØ§ÙØ¢Øª Ø§ÙØ®Ø§ØµØ© Ø¨Ù Ø­Ø§ÙÙÙØ§ Ø¨Ø³Ø¨Ø¨ {0}
landmark.basket.validation.loyalty.minimum.points.to.redeem = Ø¹ÙÙÙØ§! Ø§ÙØ­Ø¯ Ø§ÙØ£Ø¯ÙÙ Ø§ÙØ°Ù ÙÙÙÙ Ø§Ø³ØªØ±Ø¯Ø§Ø¯Ù ÙÙ Ø§ÙÙÙØ§Ø· ÙÙ {0}
landmark.basket.validation.loyalty.card.is.not.ok.to.redeem = Ø¹ÙÙÙØ§! Ø¨Ø·Ø§ÙØªÙ ÙÙØ³Øª ÙØ¹Ø¯Ø© Ø¨Ø¹Ø¯ ÙØ§Ø³ØªØ±Ø¯Ø§Ø¯ Ø§ÙÙÙØ§Ø·. ÙØ±Ø¬Ù Ø§ÙØ§ØªØµØ§Ù Ø¨Ø®Ø¯ÙØ© Ø§ÙØ¹ÙÙØ§Ø¡.
landmark.loyalty.point  = ÙÙØ·Ø©
landmark.loyalty.points  = ÙÙØ§Ø·

landmark.basket.validation.clickcollect.cart.ineligible= Ø³ÙÙØ© Ø§ÙØªØ³ÙÙ Ø§ÙØ®Ø§ØµØ© Ø¨Ù ØºÙØ± ÙØ¤ÙÙØ© ÙÙØ§Ø³ØªÙØ§Ø¯Ø© Ø¨Ø®Ø¯ÙØ© Ø§Ø³ØªÙÙÙØ§ ÙÙ Ø§ÙÙØªØ¬Ø±


myaccount.error.lettersonly  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø­Ø±ÙÙ Ø£Ø¨Ø¬Ø¯ÙØ© ÙÙØ·
myaccount.error.numbersonly  = ÙØ¬Ø¨ Ø£Ù ÙÙÙÙ Ø§ÙØ±ÙØ² Ø§ÙØ¨Ø±ÙØ¯Ù ÙØ¤ÙÙÙØ§ ÙÙ Ø£Ø±ÙØ§Ù ÙÙØ·
myaccount.error.alphanumericsOnly  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø­Ø±ÙÙ Ø£Ø¨Ø¬Ø¯ÙØ© ÙØ£Ø±ÙØ§Ù ÙÙØ·
myaccount.error.emailonly  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø¹ÙÙØ§Ù Ø¨Ø±ÙØ¯ Ø¥ÙÙØªØ±ÙÙÙ ØµØ­ÙØ­
myaccount.select.state  = ÙØ±Ø¬Ù Ø§Ø®ØªÙØ§Ø± Ø§ÙÙØ­Ø§ÙØ¸Ø©
myaccount.state.required  = Ø­ÙÙ Ø§ÙÙØ­Ø§ÙØ¸Ø© Ø¥ÙØ²Ø§ÙÙ
myaccount.select.address  = ÙØ±Ø¬Ù Ø§Ø®ØªÙØ§Ø± Ø¹ÙÙØ§ÙÙ
myaccount.select.expiryMonth  = ÙØ±Ø¬Ù Ø§Ø®ØªÙØ§Ø± Ø´ÙØ± Ø§ÙØªÙØ§Ø¡ Ø§ÙØµÙØ§Ø­ÙØ©
myaccount.select.expiryYear  = ÙØ±Ø¬Ù Ø§Ø®ØªÙØ§Ø± Ø³ÙØ© Ø§ÙØªÙØ§Ø¡ Ø§ÙØµÙØ§Ø­ÙØ©
myaccount.address.required  = Ø­ÙÙ Ø§ÙØ¹ÙÙØ§Ù Ø¥ÙØ²Ø§ÙÙ
myaccount.payment.nameOnCard  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø§Ø³ÙÙ
landmark.article.helpful.question  = ÙÙ ÙØ§Ù Ø§ÙÙÙØ§Ù ÙÙÙØ¯ÙØ§Ø
landmark.article.helpful.yes  = ÙØ¹Ù
landmark.article.helpful.no  = ÙØ§
landmark.article.helpful.msg  = % Ø§ÙÙØ³ØªØ®Ø¯ÙÙÙ Ø§ÙØ°ÙÙ ÙØ¬Ø¯ÙØ§ ÙØ°Ø§ Ø§ÙÙÙØ§Ù ÙÙÙØ¯ÙØ§.
landmark.label.refine  = ØªØµÙÙÙ
landmark.label.cancel  = Ø¥ÙØºØ§Ø¡
landmarkNewsletter.submit.processing.message = ÙØ±Ø¬Ù Ø§ÙØ§ÙØªØ¸Ø§Ø± Ø¨ÙÙÙØ§ ÙØ¹ÙÙ Ø¹ÙÙ ØªÙÙÙØ° Ø·ÙØ¨ Ø§ÙØ§Ø´ØªØ±Ø§Ù Ø§ÙØ®Ø§Øµ Ø¨Ù ...
landmarkNewsletter.submit.new.signup.message = ÙØ´ÙØ±Ù Ø¹ÙÙ Ø§ÙØ§Ø´ØªØ±Ø§Ù ÙÙ ÙØ´Ø±ØªÙØ§ Ø§ÙØ¥Ø®Ø¨Ø§Ø±ÙØ©.
landmarkNewsletter.submit.already.signup.message = Ø´ÙØ±ÙØ§Ø Ø£ÙØª ÙØ´ØªØ±Ù Ø¨Ø§ÙÙØ¹Ù ÙÙ ÙØ´Ø±ØªÙØ§ Ø§ÙØ¥Ø®Ø¨Ø§Ø±ÙØ©.
landmarkNewsletter.submit.unable.signup.message = Ø¹ÙÙÙØ§! ÙÙ ÙØ³ØªØ·Ø¹ Ø§ÙØªØ¹Ø±Ù Ø¹ÙÙ Ø¹ÙÙØ§Ù Ø§ÙØ¨Ø±ÙØ¯ Ø§ÙØ¥ÙÙØªØ±ÙÙÙ ÙØ°Ø§. ÙØ±Ø¬Ù Ø¥Ø¹Ø§Ø¯Ø© Ø§ÙÙØ­Ø§ÙÙØ©.
landmarkNewsletter.submit.server.error.message = Ø­Ø¯Ø« Ø®Ø·Ø£ ÙÙ Ø§ÙÙØ®Ø¯ÙØ ÙØ±Ø¬Ù Ø¥Ø¹Ø§Ø¯Ø© Ø§ÙÙØ­Ø§ÙÙØ© ÙØ§Ø­ÙÙØ§.
landmarkHelpPage.title  = Ø§ÙØ¨Ø­Ø« Ø¹Ù ÙØ³Ø§Ø¹Ø¯Ø©
landmarkHelpPage.no.faq  = ÙÙ ÙØªÙ Ø§ÙØ¹Ø«ÙØ± Ø¶ÙÙ Ø§ÙØ£Ø³Ø¦ÙØ© Ø§ÙÙØªÙØ±Ø±Ø© Ø¹ÙÙ Ø³Ø¤Ø§ÙÙ
landmarkHelpPage.faqs.text  = ÙØ±ÙØ² Ø§ÙÙØ³Ø§Ø¹Ø¯Ø©
landmarkHelpPage.faq.text  = Ø§ÙØ£Ø³Ø¦ÙØ© Ø§ÙÙØªÙØ±Ø±Ø©
landmarkHelpPage.faq.found.for  = ØªÙ Ø§ÙØ¹Ø«ÙØ± Ø¹ÙÙÙ ÙÙ
landmarkHelpPage.faq.notFoundFor  = ÙÙ ÙØªÙ Ø§ÙØ¹Ø«ÙØ± Ø¶ÙÙ Ø§ÙØ£Ø³Ø¦ÙØ© Ø§ÙÙØªÙØ±Ø±Ø© Ø¹ÙÙ Ø³Ø¤Ø§ÙÙ
landmarkHelpPage.help.text  = ÙØ§ Ø§ÙØ°Ù ØªØ¨Ø­Ø« Ø¹ÙÙØ
landmark.brands.morebrands  = Ø§ÙÙØ²ÙØ¯ ÙÙ Ø§ÙØ¹ÙØ§ÙØ§Øª Ø§ÙØªØ¬Ø§Ø±ÙØ©
landmark.brands.comingsoon  = ÙØ±ÙØ¨ÙØ§...
lmgcheckout.progresbar.shipping = Ø§ÙØ´Ø­Ù ÙØ§ÙØªÙØµÙÙ
lmgcheckout.progresbar.payment = Ø§ÙØ¯ÙØ¹
lmgcheckout.progresbar.place = ØªØ³Ø¬ÙÙ
lmgcheckout.progresbar.your = Ø·ÙØ¨Ù
lmgcheckout.progresbar.order = Ø·ÙØ¨
lmgcheckout.progresbar.step.one = 1
lmgcheckout.progresbar.step.two = 2
lmgcheckout.progresbar.step.three = 3
lmgcheckout.shippingaddress.selection.title = ÙØ±Ø¬Ø© Ø§Ø®ØªÙØ§Ø± Ø¹ÙÙØ§Ù Ø§ÙØ´Ø­Ù
lmgaddaddress.link = Ø¥Ø¶Ø§ÙØ© Ø¹ÙÙØ§Ù Ø¬Ø¯ÙØ¯
lmgshiptoaddress.text = Ø£Ø±Ø³Ù Ø§ÙØ´Ø­ÙØ© Ø¥ÙÙ ÙØ°Ø§ Ø§ÙØ¹ÙÙØ§Ù
lmgaddyouraddress.title = ÙØ±Ø¬Ù Ø¥Ø¶Ø§ÙØ© Ø¹ÙÙØ§Ù Ø§ÙØ´Ø­Ù ÙØ§ÙØªÙØµÙÙ Ø§ÙØ®Ø§Øµ Ø¨Ù
lmgaddaddress.title = Ø¥Ø¶Ø§ÙØ© Ø¹ÙÙØ§Ù Ø¬Ø¯ÙØ¯
lmgaddaddress.showalladdress = Ø¹Ø±Ø¶ Ø¬ÙÙØ¹ Ø§ÙØ¹ÙØ§ÙÙÙ
lmgaddaddress.hidealladdress=Ø§Ø®ÙØ§Ø¡ Ø¬ÙÙØ¹ Ø§ÙØ¹ÙØ§ÙÙÙ
lmgaddaddress.label.fullname = Ø§ÙØ§Ø³Ù Ø¨Ø§ÙÙØ§ÙÙ
lmgaddaddress.label.email = Ø§ÙØ¨Ø±ÙØ¯ Ø§ÙØ¥ÙÙØªØ±ÙÙÙ
lmgaddaddress.label.pincode = Ø§ÙØ±ÙØ² Ø§ÙØ¨Ø±ÙØ¯Ù
lmgaddaddress.label.addressline1 = Ø¹ÙÙØ§Ù 1
lmgaddaddress.label.addressline2 = Ø¹ÙÙØ§Ù 2
lmgaddaddress.label.landmark = Ø¹ÙØ§ÙÙ ÙÙÙØ²Ù
lmgaddaddress.label.addresstype = ÙÙØ¹ Ø§ÙØ¹ÙÙØ§Ù
lmgaddaddress.label.townorcity = Ø§ÙÙØ¯ÙÙØ© Ø£Ù Ø§ÙØ¨ÙØ¯Ø©
lmgaddaddress.label.state = Ø§ÙÙØ­Ø§ÙØ¸Ø©
lmgaddaddress.label.mobilenumber = Ø±ÙÙ Ø§ÙÙØ§ØªÙ Ø§ÙÙØªØ­Ø±Ù
lmgaddaddress.label.defaultaddress = ÙØ±Ø¬Ù Ø§Ø³ØªØ®Ø¯Ø§ÙÙ Ø¹ÙÙ Ø£ÙÙ Ø§ÙØ¹ÙÙØ§Ù Ø§ÙØ£Ø³Ø§Ø³Ù ÙÙØ´Ø­Ù
lmgaddaddress.termsandcondition.part1 = ÙØªØ§Ø¨Ø¹ØªÙ ÙØ¹ÙÙÙØ© Ø§ÙØ¯ÙØ¹ ØªØ¹ÙÙ ÙÙØ§ÙÙØªÙ Ø¹ÙÙ
lmgaddaddress.termsandcondition.part2 = Ø§ÙØ´Ø±ÙØ· ÙØ§ÙØ£Ø­ÙØ§Ù
lmgaddaddress.placeholder.fullname = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø§ÙØ§Ø³Ù Ø§ÙØ£ÙÙ ÙØ§Ø³Ù Ø§ÙØ¹Ø§Ø¦ÙØ©
lmgaddaddress.placeholder.email = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø¹ÙÙØ§Ù Ø¨Ø±ÙØ¯Ù Ø§ÙØ¥ÙÙØªØ±ÙÙÙ
lmgaddaddress.placeholder.pincode = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø§ÙØ±ÙØ² Ø§ÙØ¨Ø±ÙØ¯Ù
lmgaddaddress.placeholder.addressline1 = Ø±ÙÙ Ø§ÙØ´ÙØ© Ø£Ù Ø§ÙÙÙØ²ÙØ ÙØ¹ Ø§ÙØ·Ø§Ø¨Ù ÙØ§ÙØ¨ÙØ§Ø¡
lmgaddaddress.placeholder.addressline2 = Ø§ÙÙØ­Ø§ÙØ¸Ø©/Ø§ÙÙØ¬ØªÙØ¹ ÙØ§ÙØ´Ø§Ø±Ø¹ ÙØ§ÙØ­Ù/Ø§ÙÙÙØ·ÙØ©
lmgaddaddress.placeholder.landmark = ØªØ³ÙÙØ© ÙÙØ¹ÙÙ ÙØ¹Ø±ÙÙ Ø³ØªØ³Ø§Ø¹Ø¯ÙØ§ ÙÙ ØªØ­Ø¯ÙØ¯ Ø¹ÙÙØ§ÙÙ Ø¨ØµÙØ±Ø©Ù Ø£ÙØ¶Ù
lmgaddaddress.placeholder.addresstype = ÙØ±Ø¬Ù Ø§Ø®ØªÙØ§Ø± ÙÙØ¹ Ø¹ÙÙØ§ÙÙ
lmgaddaddress.placeholder.city = ÙØ±Ø¬Ù Ø§Ø®ØªÙØ§Ø± Ø§ÙÙØ¯ÙÙØ© Ø£Ù Ø§ÙØ¨ÙØ¯Ø©
lmgaddaddress.placeholder.state = ÙØ±Ø¬Ù Ø§Ø®ØªÙØ§Ø± Ø§ÙÙØ­Ø§ÙØ¸Ø©
lmgaddaddress.optional = (Ø§Ø®ØªÙØ§Ø±Ù)
lmgaddaddress.placeholder.countrycode = 91
lmgaddaddress.defaultaddress.text = Ø§ÙØ£Ø³Ø§Ø³Ù
lmgform.field.required.firstname = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø§ÙØ§Ø³Ù Ø¨Ø§ÙÙØ§ÙÙ
lmgform.field.invalid.firstname = ÙÙØ¨ØºÙ Ø£Ù ÙØ§ ÙØªØ¶ÙÙ Ø§ÙØ§Ø³Ù Ø§ÙÙØ§ÙÙ Ø³ÙÙ Ø­Ø±ÙÙ Ø£Ø¨Ø¬Ø¯ÙØ©
lmgform.field.required.fullname = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø§ÙØ§Ø³Ù Ø¨Ø§ÙÙØ§ÙÙ
lmgform.field.required.pickupPoint=ÙØ±Ø¬Ù Ø§Ø®ØªÙØ§Ø± ÙÙØ·Ø© Ø§Ø³ØªÙØ§Ù
lmgform.required.fullname  = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø§Ø³ÙÙ Ø¨Ø§ÙÙØ§ÙÙ
lmgform.required.fullname1 = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø§Ø³ÙÙ
lmgform.field.outofrange.firstname = ÙÙØ¨ØºÙ Ø£Ù ÙØ§ ÙØ²ÙØ¯ Ø·ÙÙ Ø§ÙØ§Ø³Ù Ø§ÙÙØ§ÙÙ Ø¹Ù 35 Ø­Ø±ÙÙØ§
lmgform.field.required.email = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø¹ÙÙØ§Ù Ø¨Ø±ÙØ¯Ù Ø§ÙØ¥ÙÙØªØ±ÙÙÙ
lmgform.field.invalid.email = Ø¹ÙÙØ§Ù Ø§ÙØ¨Ø±ÙØ¯ Ø§ÙØ¥ÙÙØªØ±ÙÙÙ ÙØ°Ø§ ØºÙØ± ØµØ­ÙØ­
lmgform.field.required.postcode = Ø§ÙØ±ÙØ² Ø§ÙØ¨Ø±ÙØ¯Ù Ø¥ÙØ²Ø§ÙÙ
lmgform.field.invalid.postcode = ÙØ¬Ø¨ Ø£Ù ÙÙÙÙ Ø§ÙØ±ÙØ² Ø§ÙØ¨Ø±ÙØ¯Ù ÙØ¤ÙÙÙØ§ ÙÙ Ø£Ø±ÙØ§Ù ÙÙØ·
lmgform.field.required.line1 = ÙØ¬Ø¨ ÙÙØ¡ Ø­ÙÙ Ø¹ÙÙØ§Ù 1
lmgform.field.invalid.line1 = ÙÙØ¨ØºÙ Ø£Ù ÙØ§ ÙØªØ¶ÙÙ Ø­ÙÙ Ø¹ÙÙØ§Ù 1 Ø³ÙÙ Ø­Ø±ÙÙ Ø£Ø¨Ø¬Ø¯ÙØ© ÙØ£Ø±ÙØ§Ù
lmgform.field.invalid.line2 = ÙÙØ¨ØºÙ Ø£Ù ÙØ§ ÙØªØ¶ÙÙ Ø­ÙÙ Ø¹ÙÙØ§Ù 2 Ø³ÙÙ Ø­Ø±ÙÙ Ø£Ø¨Ø¬Ø¯ÙØ© ÙØ£Ø±ÙØ§Ù
lmgform.field.invalid.landmark = ÙÙØ¨ØºÙ Ø£Ù ÙØ§ ÙØªØ¶ÙÙ Ø­ÙÙ Ø§ÙÙÙØ¹ÙÙÙ Ø§ÙÙØ¹Ø±ÙÙ Ø³ÙÙ Ø­Ø±ÙÙ Ø£Ø¨Ø¬Ø¯ÙØ© ÙØ£Ø±ÙØ§Ù
lmgform.field.required.town = Ø­ÙÙ Ø§ÙÙØ¯ÙÙØ© Ø£Ù Ø§ÙØ¨ÙØ¯Ø© Ø¥ÙØ²Ø§ÙÙ
lmgform.field.invalid.town = ÙÙØ¨ØºÙ Ø£Ù ÙØ§ ÙØªØ¶ÙÙ Ø­ÙÙ Ø§ÙÙØ¯ÙÙØ© Ø£Ù Ø§ÙØ¨ÙØ¯Ø© Ø³ÙÙ Ø­Ø±ÙÙ Ø£Ø¨Ø¬Ø¯ÙØ© ÙØ£Ø±ÙØ§Ù
lmgform.field.required.region = Ø­ÙÙ Ø§ÙÙØ­Ø§ÙØ¸Ø© Ø¥ÙØ²Ø§ÙÙ
lmgform.field.required.mobilenumber = Ø­ÙÙ Ø±ÙÙ Ø§ÙÙØ§ØªÙ Ø§ÙÙØªØ­Ø±Ù Ø¥ÙØ²Ø§ÙÙ
unavailable.selected.delivery.address.mssg = Ø¹ÙÙØ§Ù Ø§ÙØªÙØµÙÙ Ø§ÙØ°Ù Ø§Ø®ØªØ±ØªÙ ØºÙØ± ÙÙØ¬ÙØ¯
selected.delivery.address.area.notvalid.msg = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø±ÙØ² ÙÙØ·ÙØ© ØµØ­ÙØ­
lmg.location.map.label  = <strong> Ø­Ø¯ÙØ¯ ÙÙÙØ¹Ù </strong> (Ø§Ø®ØªÙØ§Ø±Ù - ÙÙØ°Ø§ ÙØ³Ø§Ø¹Ø¯ÙØ§ Ø¹ÙÙ Ø§ÙØªÙØµÙÙ Ø¨ØµÙØ±Ø©Ù Ø£Ø³Ø±Ø¹)
lmg.location.map.location=Ø­Ø¯ÙØ¯ ÙÙÙØ¹Ù
lmgcheckout.order.review.summary = ÙÙØ®Øµ Ø§ÙØ·ÙØ¨
lmgcheckout.order.review.palce.button = ØªØ³Ø¬ÙÙ Ø§ÙØ·ÙØ¨
lmgcheckout.order.review.bank.message = Ø³ÙØªÙ ØªÙØ¬ÙÙÙ Ø¨ØµÙØ±Ø©Ù Ø¢ÙÙØ© Ø¥ÙÙ {0} ÙØªØ³Ø¬ÙÙ  Ø§ÙØ¯Ø®ÙÙ ÙØ§ÙÙÙØ§ÙÙØ© Ø¹ÙÙ Ø¹ÙÙÙØ© Ø§ÙØ¯ÙØ¹ ÙØ°Ù.
lmgcheckout.order.review.shipto = Ø§ÙØ´Ø­Ù Ø¥ÙÙ:
lmgcheckout.order.review.change.link = ØªØºÙÙØ±
lmgcheckout.order.review.zerocost.label = ÙØ³ÙÙØ©
lmgcheckout.order.review.Expires.label = ØªÙØªÙÙ Ø§ÙØµÙØ§Ø­ÙØ© ÙÙ
lmgcheckout.order.review.payment.method = Ø·Ø±ÙÙØ© Ø§ÙØ¯ÙØ¹ Ø§Ù Ø§ÙØ³Ø¯Ø§Ø¯:
lmgcheckout.order.review.color = Ø§ÙÙÙÙ:
lmgcheckout.order.review.size = Ø§ÙÙÙØ§Ø³:
lmgcheckout.order.review.quantity = Ø§ÙÙÙÙØ©:
lmgcheckout.order.review.points.spent = ÙÙØ§Ø· ÙÙÙÙØ©
lmgcheckout.order.review.earned.points = ÙÙØ§Ø· ÙÙØªØ³Ø¨Ø©
lmgcheckout.order.review.total = Ø§ÙÙØ¬ÙÙØ¹
lmgcheckout.order.review.subtotal = Ø§ÙÙØ¬ÙÙØ¹ Ø§ÙÙØ±Ø¹Ù:
lmgcheckout.order.review.promo.discount = Ø§ÙØ®ØµÙ Ø§ÙØªØ±ÙÙØ¬Ù
lmgcheckout.order.review.rewards.savings = Ø§ÙØªÙÙÙØ± Ø¨Ø§Ø³ØªØ¹ÙØ§Ù ÙÙØ§ÙØ¢Øª ÙØ§ÙØ¯ÙØ§Ø±Ù:
lmgcheckout.order.review.your.order = ÙØ±Ø§Ø¬Ø¹Ø© Ø§ÙØ·ÙØ¨
lmgcheckout.order.review.item = Ø§ÙÙÙØªØ¬
lmgcheckout.order.review.description = Ø§ÙÙØµÙ
lmgcheckout.order.review.total.price = Ø§ÙØ³Ø¹Ø± Ø§ÙØ¥Ø¬ÙØ§ÙÙ
lmgcheckout.order.review.multiproduct.more = Ø§ÙÙØ²ÙØ¯
lmgcheckout.order.review.show.details = <span> Ø¹Ø±Ø¶ ÙØ§ÙØ© </span> Ø§ÙØªÙØ§ØµÙÙ
lmgcheckout.order.review.shipping.businessDays = ({0} Ø£ÙØ§Ù Ø¹ÙÙ)
lmgcheckout.order.review.address.mobile = Ø§ÙÙØ§ØªÙ Ø§ÙÙØªØ­Ø±Ù:
lmgcheckout.express  = Ø³Ø±ÙØ¹
lmgcheckout.free  =
lmgcheckout.today  = Ø§ÙÙÙÙ
checkout.error.authorization.failed                                                        = Ø¹Ø°Ø±ÙØ§Ø ÙÙ ØªÙØ¬Ø­ Ø¹ÙÙÙØ© Ø§ÙØ¯ÙØ¹. ÙØ±Ø¬Ù Ø§ÙØªØ­ÙÙ ÙÙ Ø¨ÙØ§ÙØ§ØªÙ ÙØ¥Ø¹Ø§Ø¯Ø© Ø§ÙÙØ­Ø§ÙÙØ©.
checkout.error.cart.notcalculated                                                          = Ø¹ÙÙÙØ§! ÙØ¨Ø¯Ù Ø£ÙÙ ØªÙØ§Ø¬Ù ÙØ´ÙÙØ© ÙØ§. ÙØ±Ø¬Ù Ø¥ØµÙØ§Ø­ Ø§ÙØ£ÙØ± ÙØªØªÙÙÙ ÙÙ ÙØªØ§Ø¨Ø¹Ø© Ø§ÙØªØ³ÙÙ.
pdp.tellWorld  = ÙØ§ Ø±Ø£ÙÙ ÙÙ ÙØ°Ø§ Ø§ÙÙÙØªØ¬Ø
cod.exceed.threshold.price = Ø§ÙØ¯ÙØ¹ Ø¹ÙØ¯ Ø§ÙØ§Ø³ØªÙØ§ÙØ ÙØªØ¬Ø§ÙØ² Ø§ÙØ­Ø¯ Ø§ÙÙØ³ÙÙØ­ ÙÙØ³Ø¹Ø±.
undeliverable.postcode.mssg = Ø®Ø¯ÙØ© Ø§ÙØ´Ø­Ù ÙØ§ÙØªÙØµÙÙ ØºÙØ± ÙØªÙÙØ±Ø© ÙÙØ°Ø§ Ø§ÙØ±ÙØ² Ø§ÙØ¨Ø±ÙØ¯Ù.
gdms.slot.notavailable.error.message = Ø¹ÙÙÙØ§! Ø§ÙÙØªØ±Ø© Ø§ÙØ²ÙÙÙØ© Ø§ÙØªÙ Ø§Ø®ØªØ±ØªÙØ§ ÙØªÙØµÙÙ Ø·ÙØ¨Ù ÙÙ ØªØ¹Ø¯ ÙØªÙÙØ±Ø©. ÙØ±Ø¬Ù ØªØ­Ø¯ÙØ¯ ØªØ§Ø±ÙØ® ÙÙÙØª Ø¬Ø¯ÙØ¯ÙÙ ÙÙØªÙØµÙÙ.
landmark.homecentre.express.delivery.legend.message  = Ø§ÙØªÙØµÙÙ Ø§ÙØ³Ø±ÙØ¹ ÙØ¶Ø§Ù <b>{0}</b>
landmark.homecentre.normal.delivery.legend.message  =  ØªØ­Ø¯ÙØ¯ ÙÙØ¹Ø¯ Ø§ÙØ§Ø³ØªÙØ§Ù
landmark.homecentre.express.delivery.slot.message  = Ø§ÙØ±Ø³Ø§ÙØ© Ø§ÙØ¢ÙÙØ© Ø§ÙØ®Ø§ØµØ© Ø¨Ø§ÙØªÙØµÙÙ Ø§ÙØ³Ø±ÙØ¹
landmark.homecentre.delivery.error.message = ÙØ¹ØªØ°Ø± Ø¹Ù Ø¹Ø¯Ù ÙØ¯Ø±ØªÙØ§ ØªÙØµÙÙ ÙØ´ØªØ±ÙØ§ØªÙ ÙÙ ÙÙØªØ¬Ø§Øª ÙÙÙ Ø³ÙØªØ± ÙÙ Ø§ÙÙÙØª Ø§ÙØ­Ø§ÙÙ. ÙØ±Ø¬Ù Ø¥Ø¹Ø§Ø¯Ø© Ø§ÙØªØ­ÙÙ ÙØ§Ø­ÙÙØ§ Ø£Ù ÙØªØ§Ø¨Ø¹Ø© Ø§ÙØªØ³ÙÙ ÙÙ Ø¹ÙØ§ÙØ§ØªÙØ§ Ø§ÙØ£Ø®Ø±Ù.
landmark.homecentre.delivery.items.remove.error.message = ÙØ¹ØªØ°Ø± Ø¹Ù Ø¹Ø¯Ù ÙØ¯Ø±ØªÙØ§ ØªÙØµÙÙ ÙØ´ØªØ±ÙØ§ØªÙ ÙÙ ÙÙØªØ¬Ø§Øª ÙÙÙ Ø³ÙØªØ± ÙÙ Ø§ÙÙÙØª Ø§ÙØ­Ø§ÙÙ. ÙØ±Ø¬Ù Ø¥Ø²Ø§ÙØªÙØ§ ÙÙ Ø³ÙØ© Ø§ÙØªØ³ÙÙ ÙÙÙ Ø«ÙÙ Ø§ÙØªÙØ¬Ù ÙØ¥Ø¬Ø±Ø§Ø¡ Ø¹ÙÙÙØ© Ø§ÙØ¯ÙØ¹.
landmark.homebox.delivery.items.remove.error.message = ÙØ¹ØªØ°Ø± Ø¹Ù Ø¹Ø¯Ù ÙØ¯Ø±ØªÙØ§ ØªÙØµÙÙ ÙØ´ØªØ±ÙØ§ØªÙ ÙÙ ÙÙØªØ¬Ø§Øª ÙÙÙ Ø¨ÙÙØ³ ÙÙ Ø§ÙÙÙØª Ø§ÙØ­Ø§ÙÙ. ÙØ±Ø¬Ù Ø¥Ø²Ø§ÙØªÙØ§ ÙÙ Ø³ÙØ© Ø§ÙØªØ³ÙÙ ÙÙÙ Ø«ÙÙ Ø§ÙØªÙØ¬Ù ÙØ¥Ø¬Ø±Ø§Ø¡ Ø¹ÙÙÙØ© Ø§ÙØ¯ÙØ¹.
landmark.homecentre.delivery.restricted.slot.error.message = ÙØ¹ØªØ°Ø± Ø¹Ù Ø¹Ø¯Ù ÙØ¯Ø±ØªÙØ§ ØªÙØµÙÙ ÙØ´ØªØ±ÙØ§ØªÙ ÙÙ ÙÙØªØ¬Ø§Øª ÙÙÙ Ø³ÙØªØ± ÙÙ Ø§ÙÙÙØª Ø§ÙØ­Ø§ÙÙ. ÙØ±Ø¬Ù Ø¥Ø¹Ø§Ø¯Ø© Ø§ÙØªØ­ÙÙ ÙØ§Ø­ÙÙØ§ Ø£Ù ÙØªØ§Ø¨Ø¹Ø© Ø§ÙØªØ³ÙÙ ÙÙ Ø¹ÙØ§ÙØ§ØªÙØ§ Ø§ÙØ£Ø®Ø±Ù.
landmark.babyshop.delivery.error.message=ÙØ£Ø³ÙØ ÙØªØ¹Ø°ÙØ± Ø¹ÙÙÙØ§ ØªÙØµÙÙ ÙÙØªØ¬Ø§Øª Ø¨ÙØ¨Ù Ø´ÙØ¨ ÙÙ Ø§ÙÙÙØª Ø§ÙØ­Ø§ÙÙ. ØªØ­ÙÙÙ ÙØ±Ø© Ø£Ø®Ø±Ù ÙÙ ÙÙØª ÙØ§Ø­Ù Ø£Ù ØªØ³ÙÙÙ ÙÙ Ø§ÙØ¹ÙØ§ÙØ§Øª Ø§ÙØªØ¬Ø§Ø±ÙØ© Ø§ÙØ£Ø®Ø±Ù.
landmark.homebox.delivery.error.message=ÙØ¹ØªØ°Ø± Ø¹Ù Ø¹Ø¯Ù ÙØ¯Ø±ØªÙØ§ ØªÙØµÙÙ ÙØ´ØªØ±ÙØ§ØªÙ ÙÙ ÙÙØªØ¬Ø§Øª ÙÙÙ Ø¨ÙÙØ³ ÙÙ Ø§ÙÙÙØª Ø§ÙØ­Ø§ÙÙ. ÙØ±Ø¬Ù Ø¥Ø¹Ø§Ø¯Ø© Ø§ÙØªØ­ÙÙ ÙØ§Ø­ÙÙØ§ Ø£Ù ÙØªØ§Ø¨Ø¹Ø© Ø§ÙØªØ³ÙÙ ÙÙ Ø¹ÙØ§ÙØ§ØªÙØ§ Ø§ÙØ£Ø®Ø±Ù.
landmark.babyshop.delivery.items.remove.error.message=ÙØ£Ø³ÙØ ÙØªØ¹Ø°ÙØ± Ø¹ÙÙÙØ§ ØªÙØµÙÙ ÙÙØªØ¬Ø§Øª Ø¨ÙØ¨Ù Ø´ÙØ¨ ÙÙ Ø§ÙÙÙØª Ø§ÙØ­Ø§ÙÙ. ÙØ±Ø¬Ù Ø¥Ø²Ø§ÙØªÙØ§ ÙÙ Ø³ÙÙØ© Ø§ÙÙØ´ØªØ±ÙØ§Øª ÙØ¥ÙÙØ§Ù Ø¹ÙÙÙØ© Ø§ÙØ¯ÙØ¹.
landmark.babyshop.delivery.restricted.slot.error.message=ÙØ£Ø³ÙØ ÙØªØ¹Ø°ÙØ± Ø¹ÙÙÙØ§ ØªÙØµÙÙ ÙÙØªØ¬Ø§Øª Ø¨ÙØ¨Ù Ø´ÙØ¨ ÙÙ Ø§ÙÙÙØª Ø§ÙØ­Ø§ÙÙ. ØªØ­ÙÙÙ ÙØ±Ø© Ø£Ø®Ø±Ù ÙÙ ÙÙØª ÙØ§Ø­Ù Ø£Ù ØªØ³ÙÙÙ ÙÙ Ø§ÙØ¹ÙØ§ÙØ§Øª Ø§ÙØªØ¬Ø§Ø±ÙØ© Ø§ÙØ£Ø®Ø±Ù.
landmark.homebox.delivery.restricted.slot.error.message=ÙØ¹ØªØ°Ø± Ø¹Ù Ø¹Ø¯Ù ÙØ¯Ø±ØªÙØ§ ØªÙØµÙÙ ÙØ´ØªØ±ÙØ§ØªÙ ÙÙ ÙÙØªØ¬Ø§Øª ÙÙÙ Ø¨ÙÙØ³ ÙÙ Ø§ÙÙÙØª Ø§ÙØ­Ø§ÙÙ. ÙØ±Ø¬Ù Ø¥Ø¹Ø§Ø¯Ø© Ø§ÙØªØ­ÙÙ ÙØ§Ø­ÙÙØ§ Ø£Ù ÙØªØ§Ø¨Ø¹Ø© Ø§ÙØªØ³ÙÙ ÙÙ Ø¹ÙØ§ÙØ§ØªÙØ§ Ø§ÙØ£Ø®Ø±Ù.
landmark.common.delivery.error.message=ÙØ£Ø³ÙØ ÙØªØ¹Ø°ÙØ± Ø¹ÙÙÙØ§ ØªÙØµÙÙ ÙÙØªØ¬Ø§Øª Ø¨ÙØ¨Ù Ø´ÙØ¨ ÙÙÙÙ Ø³ÙØªØ± ÙÙ Ø§ÙÙÙØª Ø§ÙØ­Ø§ÙÙ. ÙØ±Ø¬Ù Ø¥Ø²Ø§ÙØªÙØ§ ÙÙ Ø³ÙÙØ© Ø§ÙÙØ´ØªØ±ÙØ§Øª ÙØ¥ÙÙØ§Ù Ø¹ÙÙÙØ© Ø§ÙØ¯ÙØ¹.
landmark.babyshop.homecentre.delivery.error.message=Ø¹Ø°Ø±ÙØ§Ø ÙØ­Ù ØºÙØ± ÙØ§Ø¯Ø±ÙÙ Ø¹ÙÙ ØªÙØµÙÙ ÙØ´ØªØ±ÙØ§ØªÙ ÙÙ ÙÙÙ Ø³ÙØªØ± ÙØ¨ÙØ¨Ù Ø´ÙØ¨ Ø¥ÙÙ ÙØ°Ø§ Ø§ÙØ¹ÙÙØ§ÙØ ÙØ±Ø¬Ù Ø§Ø®ØªÙØ§Ø± Ø¹ÙÙØ§ÙÙØ§ ÙØ®ØªÙÙÙØ§ ÙÙØ´Ø­ÙØ Ø£Ù ÙØªØ§Ø¨Ø¹Ø© Ø§ÙØªØ³ÙÙÙ ÙÙ Ø¹ÙØ§ÙØ§ØªÙØ§ Ø§ÙØªØ¬Ø§Ø±ÙØ© Ø§ÙØ£Ø®Ø±Ù.
landmark.babyshop.homebox.delivery.error.message=Ø¹Ø°Ø±ÙØ§Ø ÙØ­Ù ØºÙØ± ÙØ§Ø¯Ø±ÙÙ Ø¹ÙÙ ØªÙØµÙÙ ÙØ´ØªØ±ÙØ§ØªÙ ÙÙ ÙÙÙ Ø¨ÙÙØ³ ÙØ¨ÙØ¨Ù Ø´ÙØ¨ Ø¥ÙÙ ÙØ°Ø§ Ø§ÙØ¹ÙÙØ§ÙØ ÙØ±Ø¬Ù Ø§Ø®ØªÙØ§Ø± Ø¹ÙÙØ§ÙÙØ§ ÙØ®ØªÙÙÙØ§ ÙÙØ´Ø­ÙØ Ø£Ù ÙØªØ§Ø¨Ø¹Ø© Ø§ÙØªØ³ÙÙÙ ÙÙ Ø¹ÙØ§ÙØ§ØªÙØ§ Ø§ÙØªØ¬Ø§Ø±ÙØ© Ø§ÙØ£Ø®Ø±Ù.
landmark.babyshop.homebox.homecentre.delivery.error.message=Ø¹Ø°Ø±ÙØ§Ø ÙØ­Ù ØºÙØ± ÙØ§Ø¯Ø±ÙÙ Ø¹ÙÙ ØªÙØµÙÙ ÙØ´ØªØ±ÙØ§ØªÙ ÙÙ ÙÙÙ Ø³ÙØªØ± ÙÙÙÙ Ø¨ÙÙØ³ ÙØ¨ÙØ¨Ù Ø´ÙØ¨ Ø¥ÙÙ ÙØ°Ø§ Ø§ÙØ¹ÙÙØ§ÙØ ÙØ±Ø¬Ù Ø§Ø®ØªÙØ§Ø± Ø¹ÙÙØ§ÙÙØ§ ÙØ®ØªÙÙÙØ§ ÙÙØ´Ø­ÙØ Ø£Ù ÙØªØ§Ø¨Ø¹Ø© Ø§ÙØªØ³ÙÙÙ ÙÙ Ø¹ÙØ§ÙØªÙØ§ Ø§ÙØªØ¬Ø§Ø±ÙØ© Ø§ÙØ£Ø®Ø±Ù.
landmark.homebox.homecentre.delivery.error.message=Ø¹Ø°Ø±ÙØ§Ø ÙØ­Ù ØºÙØ± ÙØ§Ø¯Ø±ÙÙ Ø¹ÙÙ ØªÙØµÙÙ ÙØ´ØªØ±ÙØ§ØªÙ ÙÙ ÙÙÙ Ø¨ÙÙØ³ ÙÙÙÙ Ø³ÙØªØ± Ø¥ÙÙ ÙØ°Ø§ Ø§ÙØ¹ÙÙØ§ÙØ ÙØ±Ø¬Ù Ø§Ø®ØªÙØ§Ø± Ø¹ÙÙØ§ÙÙØ§ ÙØ®ØªÙÙÙØ§ ÙÙØ´Ø­ÙØ Ø£Ù ÙØªØ§Ø¨Ø¹Ø© Ø§ÙØªØ³ÙÙÙ ÙÙ Ø¹ÙØ§ÙØ§ØªÙØ§ Ø§ÙØªØ¬Ø§Ø±ÙØ© Ø§ÙØ£Ø®Ø±Ù.
landmark.babyshop.homecentre.nonGdms.delivery.error.message=Ø¹Ø°Ø±ÙØ§Ø ÙØ­Ù ØºÙØ± ÙØ§Ø¯Ø±ÙÙ Ø¹ÙÙ ØªÙØµÙÙ ÙØ´ØªØ±ÙØ§ØªÙ Ø§ÙÙÙØ¶ÙØ­Ø© Ø£Ø¯ÙØ§Ù ÙÙ ÙÙÙ Ø³ÙØªØ± ÙØ¨ÙØ¨Ù Ø´ÙØ¨ Ø¥ÙÙ ÙØ°Ø§ Ø§ÙØ¹ÙÙØ§ÙØ ÙØ±Ø¬Ù Ø­Ø°Ù ÙØ°Ù Ø§ÙÙÙØªØ¬Ø§Øª Ø£Ù Ø§Ø®ØªÙØ§Ø± Ø¹ÙÙØ§ÙÙØ§ ÙØ®ØªÙÙÙØ§ ÙÙØ´Ø­Ù ÙØ§ÙÙØªØ§Ø¨Ø¹Ø©.
landmark.babyshop.homebox.nonGdms.delivery.error.message=Ø¹Ø°Ø±ÙØ§Ø ÙØ­Ù ØºÙØ± ÙØ§Ø¯Ø±ÙÙ Ø¹ÙÙ ØªÙØµÙÙ ÙØ´ØªØ±ÙØ§ØªÙ Ø§ÙÙÙØ¶ÙØ­Ø© Ø£Ø¯ÙØ§Ù ÙÙ ÙÙÙ Ø¨ÙÙØ³ ÙØ¨ÙØ¨Ù Ø´ÙØ¨ Ø¥ÙÙ ÙØ°Ø§ Ø§ÙØ¹ÙÙØ§ÙØ ÙØ±Ø¬Ù Ø­Ø°Ù ÙØ°Ù Ø§ÙÙÙØªØ¬Ø§Øª Ø£Ù Ø§Ø®ØªÙØ§Ø± Ø¹ÙÙØ§ÙÙØ§ ÙØ®ØªÙÙÙØ§ ÙÙØ´Ø­Ù ÙØ§ÙÙØªØ§Ø¨Ø¹Ø©.
landmark.babyshop.homebox.homecentre.nonGdms.delivery.error.message=Ø¹Ø°Ø±ÙØ§Ø ÙØ­Ù ØºÙØ± ÙØ§Ø¯Ø±ÙÙ Ø¹ÙÙ ØªÙØµÙÙ ÙØ´ØªØ±ÙØ§ØªÙ Ø§ÙÙÙØ¶ÙØ­Ø© Ø£Ø¯ÙØ§Ù ÙÙ ÙÙÙ Ø¨ÙÙØ³ ÙØ¨ÙØ¨Ù Ø´ÙØ¨Ø ÙÙÙÙ Ø³ÙØªØ± Ø¥ÙÙ ÙØ°Ø§ Ø§ÙØ¹ÙÙØ§ÙØ ÙØ±Ø¬Ù Ø­Ø°Ù ÙØ°Ù Ø§ÙÙÙØªØ¬Ø§Øª Ø£Ù Ø§Ø®ØªÙØ§Ø± Ø¹ÙÙØ§ÙÙØ§ ÙØ®ØªÙÙÙØ§ ÙÙØ´Ø­Ù ÙØ§ÙÙØªØ§Ø¨Ø¹Ø©.
landmark.homebox.homecentre.nonGdms.delivery.error.message=Ø¹Ø°Ø±ÙØ§Ø ÙØ­Ù ØºÙØ± ÙØ§Ø¯Ø±ÙÙ Ø¹ÙÙ ØªÙØµÙÙ ÙØ´ØªØ±ÙØ§ØªÙ Ø§ÙÙÙØ¶ÙØ­Ø© Ø£Ø¯ÙØ§Ù ÙÙ ÙÙÙ Ø³ÙØªØ± ÙÙÙÙ Ø¨ÙÙØ³ Ø¥ÙÙ ÙØ°Ø§ Ø§ÙØ¹ÙÙØ§ÙØ ÙØ±Ø¬Ù Ø­Ø°Ù ÙØ°Ù Ø§ÙÙÙØªØ¬Ø§Øª Ø£Ù Ø§Ø®ØªÙØ§Ø± Ø¹ÙÙØ§ÙÙØ§ ÙØ®ØªÙÙÙØ§ ÙÙØ´Ø­Ù ÙØ§ÙÙØªØ§Ø¨Ø¹Ø©.
landmark.homecentre.normal.delivery.message = Ø§ÙØ±Ø³Ø§ÙØ© Ø§ÙØ¢ÙÙØ© Ø§ÙØ®Ø§ØµØ© Ø¨Ø§ÙØªÙØµÙÙ Ø§ÙÙØ¬Ø§ÙÙ
landmark.homecentre.thankyou.page.express.delivery.legend.message = ØªÙØµÙÙ Ø³Ø±ÙØ¹

landmark.homecentre.thankyou.page.express.delivery.message = <strong>ÙÙÙ Ø³ÙØªØ± </strong><strong>{1} - {2}</strong> ÙÙ Ø§ÙÙÙØ±Ø± Ø£Ù ÙØµÙÙ Ø§ÙØ£Ø«Ø§Ø« ÙÙÙ <strong>{0}</strong> ÙØ§ Ø¨ÙÙ Ø§ÙØ³Ø§Ø¹Ù
landmark.homecentre.thankyou.page.free.delivery.message = <strong>ÙÙÙ Ø³ÙØªØ±</strong> ÙØ¬Ø¨ Ø£Ù ÙØµÙ Ø§ÙØ£Ø«Ø§Ø« ÙØ¬Ø§ÙØ§ ÙÙ <p class="delivery-time"><strong class="en-lang has-countryCode ltr-always">{0}</strong> ÙØ§ Ø¨ÙÙ <strong>{1} - {2}</strong> Ø³Ø§Ø¹Ø§Øª</p>
landmark.homecentre.thankyou.page.normal.delivery.legend.message = ÙÙØªØ¬Ø§Øª Ø§ÙØªÙØµÙÙ Ø§ÙÙØ¬ÙØ§ÙÙ Ø§ÙØ®Ø§ØµØ© Ø¨Ù
landmark.homecentre.thankyou.page.normal.delivery.message = <strong>ÙÙÙ Ø³ÙØªØ±</strong> ÙØ¬Ø¨ Ø£Ù ÙØµÙ Ø§ÙØ£Ø«Ø§Ø« Ø¹ÙÙ <p class="delivery-time"><strong>{0}</strong> ÙØ§ Ø¨ÙÙ <strong>{1} - {2}</strong> Ø³Ø§Ø¹Ø§Øª</p>
landmark.homecentre.thankyou.page.nonconcept.products.delivery.message = Ø£ÙØ§ Ø¨Ø§ÙÙ Ø§ÙÙÙØªØ¬Ø§Øª Ø§ÙØ®Ø§ØµØ© Ø¨Ù ÙÙØ¬Ø¨ Ø£Ù ØªØµÙ
landmark.homecentre.thankyou.delivery.title=Ø¥ÙÙÙ ÙÙÙ Ø³ÙØªÙ ØªÙØµÙÙ Ø·ÙØ¨Ù:
landmark.homecentre.customer.delivery.slot.error.message = ØªÙØ¯ Ø¥Ø¶Ø§ÙØ© Ø§ÙÙØ²ÙØ¯ ÙÙ ÙØ·Ø¹ Ø§ÙØ£Ø«Ø§Ø« ÙØ·ÙØ¨ÙØ ÙØ±Ø¬Ù ØªØ­Ø¯ÙØ¯ ØªØ§Ø±ÙØ® ÙÙÙØª ØªØ³ÙÙÙ Ø¬Ø¯ÙØ¯ÙÙ.
landmark.summary.express.slot  = ({0} {1} Ø®ÙØ§Ù {2} Ø³Ø§Ø¹Ø©)
orderreview.between  = ÙØ§ Ø¨ÙÙ
orderreview.hrs  = Ø³Ø§Ø¹Ø§Øª
schedule.delivery.title  = Ø­Ø¯Ø¯ ÙÙØ¹Ø¯ Ø§ÙØªÙØµÙÙ
schedule.delivery.letusknow = ÙØ±Ø¬Ù Ø§Ø¹ÙØ§ÙÙØ§ Ø¨ÙÙØ¹Ø¯ Ø§ÙØªÙØµÙÙ Ø§ÙÙÙØ§Ø³Ø¨.
schedule.delivery.description  = ÙØ±Ø¬Ù Ø¥Ø¹ÙØ§ÙÙØ§ Ø¨Ø§ÙÙÙØ¹Ø¯ Ø§ÙÙÙØ§Ø³Ø¨ ÙØªÙØµÙÙ ÙØ§ ÙÙÙ:
homecentre.schedule.delivery.title=
schedule.delivery.progress.label.one  = ÙØ±Ø¬Ù Ø§Ø®ØªÙØ§Ø± ØªØ§Ø±ÙØ® Ø§ÙØªÙØµÙÙ
schedule.delivery.progress.label.two  = Ø§Ø®ØªØ± ÙÙØªÙØ§ ÙÙØ§Ø³Ø¨ÙØ§
schedule.delivery.noslotsavailable  = Ø¹Ø°Ø±ÙØ§Ø ÙÙØ§Ø¹ÙØ¯ Ø§ÙØªÙØµÙÙ Ø¥ÙÙ ÙØ°Ù Ø§ÙÙÙØ·ÙØ© ØºÙØ± ÙØªÙÙØ±Ø© Ø­Ø§ÙÙÙØ§Ø ÙØ±Ø¬Ù ÙØ¹Ø§ÙØ¯Ø© Ø§ÙØªØ­ÙÙ ÙÙ ÙÙØªÙ ÙØ§Ø­Ù.
schedule.delivery.expressdelivery  = Ø§ÙØªÙØµÙÙ Ø§ÙØ³Ø±ÙØ¹
schedule.delivery.selected  = ØªÙ ØªØ­Ø¯ÙØ¯ ÙÙØ¹Ø¯ ØªÙØµÙÙ Ø·ÙØ¨Ù!
schedule.delivery.selected.title = ØªÙ ØªØ­Ø¯ÙØ¯ ÙÙØ¹Ø¯ Ø§ÙØªÙØµÙÙ <span class="status"> Ø§ÙØ³Ø±ÙØ¹ </span> ÙØ·ÙØ¨Ù!
schedule.deliveryexpress.selected  = ØªÙ ØªØ­Ø¯ÙØ¯ ÙÙØ¹Ø¯ Ø§ÙØªÙØµÙÙ Ø§ÙØ³Ø±ÙØ¹ ÙØ·ÙØ¨Ù!
schedule.delivery.datetime  =Ø³ÙÙ ÙÙÙÙ Ø¨Ø¥Ø±Ø³Ø§Ù Ø·ÙØ¨Ù ÙÙ <br/><time><strong class="date font-black"></strong> ÙØ§ Ø¨ÙÙ Ø§ÙØ³Ø§Ø¹Ø© <strong class="time font-black" ></strong></time>
schedule.delivery.date.error  = ÙØ±Ø¬Ù Ø§Ø®ØªÙØ§Ø± Ø§ÙØªØ§Ø±ÙØ®
schedule.delivery.time.error  = ÙØ±Ø¬Ù Ø§Ø®ØªÙØ§Ø± Ø§ÙÙÙØª
site.basket.qty  = Ø§ÙÙÙÙØ©
schedule.delivery.days = ["Ø§ÙØ§Ø­Ø¯", "Ø§ÙØ§Ø«ÙÙÙ", "Ø§ÙØ«ÙØ§Ø«Ø§Ø¡", "Ø§ÙØ§Ø±Ø¨Ø¹Ø§Ø¡", "Ø§ÙØ®ÙÙØ³", "Ø§ÙØ¬ÙØ¹Ø©", "Ø§ÙØ³Ø¨Øª"]
schedule.delivery.months = ["ÙÙØ§ÙØ±", "ÙØ¨Ø±Ø§ÙØ±", "ÙØ§Ø±Ø³", "Ø¥Ø¨Ø±ÙÙ", "ÙØ§ÙÙ", "ÙÙÙÙÙ","ÙÙÙÙÙ", "Ø£ØºØ³Ø·Ø³", "Ø³Ø¨ØªÙØ¨Ø±", "Ø£ÙØªÙØ¨Ø±", "ÙÙÙÙØ¨Ø±", "Ø¯ÙØ³ÙØ¨Ø±"]
site.months  = ["ÙÙØ§ÙØ±", "ÙØ¨Ø±Ø§ÙØ±", "ÙØ§Ø±Ø³", "Ø¥Ø¨Ø±ÙÙ", "ÙØ§ÙÙ", "ÙÙÙÙÙ","ÙÙÙÙÙ", "Ø£ØºØ³Ø·Ø³", "Ø³Ø¨ØªÙØ¨Ø±", "Ø£ÙØªÙØ¨Ø±", "ÙÙÙÙØ¨Ø±", "Ø¯ÙØ³ÙØ¨Ø±"]
schedule.delivery.today = Ø§ÙÙÙÙ
site.button.loading  = Ø§ÙØªØ­ÙÙÙ Ø¬Ø§Ø±Ù...
checkout.multi.deliveryAddress.notprovided = ÙØ¬Ø¨ Ø¥Ø¹Ø·Ø§Ø¡ Ø¹ÙÙØ§Ù Ø§ÙØªÙØµÙÙ ÙÙØªÙÙÙ ÙÙ Ø§ÙÙØªØ§Ø¨Ø¹Ø© Ø¥ÙÙ Ø§ÙØ®Ø·ÙØ© Ø§ÙØªØ§ÙÙØ©.
landmark.back.to.department = Ø§ÙØ¹ÙØ¯Ø© Ø¥ÙÙ Ø§ÙØ£ÙØ³Ø§Ù
landmark.back.to.menu = Ø§ÙØ±Ø¬ÙØ¹
account.order.details.normal.delivery.option = Ø§ÙØ´Ø­Ù ÙØ§ÙØªÙØµÙÙ Ø§ÙØ¹Ø§Ø¯Ù
landmark.non.homecentre.nextday.delivery.message  = Ø³Ø¬ÙÙ Ø·ÙØ¨Ù ÙØ¨Ù 3:30 Ø¹ØµØ±ÙØ§ ÙÙÙÙÙ Ø¨ØªÙØµÙÙÙ ÙÙ ÙÙ ÙÙÙ Ø§ÙØ¹ÙÙ Ø§ÙØªØ§ÙÙ
landmark.homecentre.express.delivery.message  = ÙØ±Ø¬Ù ØªØ­Ø¯ÙØ¯ ØªØ§Ø±ÙØ® ÙÙÙØª ØªÙØµÙÙ ÙÙØ§Ø¦Ù Ø¨Ø§ÙÙØ³Ø¨Ø© ÙÙ ÙÙ ØµÙØ­Ø© Ø§ÙØ´Ø­Ù ÙØ§ÙØªÙØµÙÙ
favourite.tooltip.title  = Ø£Ø¶Ù Ø¥ÙÙ ÙØ§Ø¦ÙØ© Ø§ÙÙÙØªØ¬Ø§Øª Ø§ÙÙÙØ¶ÙØ©!
favourite.tooltip.desc  = Ø¨Ø¥ÙÙØ§ÙÙ Ø´Ø±Ø§Ø¤Ù ÙÙØªÙØ§ ØªØ´Ø§Ø¡ ÙÙ ÙØ§Ø¦ÙØ© Ø§ÙÙÙØªØ¬Ø§Øª Ø§ÙÙÙØ¶ÙØ© ÙÙ Ø­Ø³Ø§Ø¨Ù.
favourite.signin.header.title.first  = ØªØ³Ø¬ÙÙ Ø§ÙØ¯Ø®ÙÙ Ø¥ÙÙ
favourite.signin.header.title.last   = ÙØ°Ø§ Ø§ÙÙÙØªØ¬
favourite.modal.header.text   = Ø§Ø®ØªØ± Ø§ÙÙÙØªØ¬Ø§Øª Ø§ÙÙÙØ¶ÙØ© Ø§ÙØªÙ ØªØ¹Ø¬Ø¨Ù Ø§ÙØ¢Ù ÙØ§Ø´ØªØ±ÙÙØ§ ÙØ§Ø­ÙÙØ§ ÙÙ Ø£Ù ÙÙØªÙ Ø´Ø¦Øª.
feedback.form.message.validation = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø¢Ø±Ø§Ø¦Ù ÙÙÙØ§Ø­Ø¸Ø§ØªÙ ÙÙØ§
apps.smart.banner.description = ÙØªÙÙØ± Ø§ÙØ¢Ù Ø¹ÙÙ ÙØªØ¬Ø± Ø§ÙØªØ·Ø¨ÙÙØ§Øª
apps.smart.banner.open.button.text = ÙÙØªÙØ­
apps.smart.banner.download.button.text = ØªØ­ÙÙÙ
favourite.wishlist.undo = ØªØ±Ø§Ø¬Ø¹
pdp.review.on = ÙÙ
dateformate = dd MMMMMM YYYY
landMarkHomePage.footer.About				= ÙÙ ÙØ­Ù
landMarkHomePage.footer.terms&condition		= Ø§ÙØ´Ø±ÙØ· ÙØ§ÙØ£Ø­ÙØ§Ù
landMarkHomePage.footer.privacyPolicy		= Ø³ÙØ§Ø³Ø© Ø§ÙØ®ØµÙØµÙØ©
paymentPage.allFieldsMandatory =
####=========last translated on 14 march 2017

homecentre.schedule.delivery.notification.title = ØªÙ ØªØ­Ø¯ÙØ¯ ÙÙØ¹Ø¯ Ø§ÙØªÙØµÙÙ <span class="status"> Ø§ÙØ³Ø±ÙØ¹ </span> ÙÙÙ Ø³ÙØªØ± ÙØ·ÙØ¨Ù!
homebox.schedule.delivery.notification.title = ØªÙ ØªØ­Ø¯ÙØ¯ ÙÙØ¹Ø¯ Ø§ÙØªÙØµÙÙ <span class="status"> Ø§ÙØ³Ø±ÙØ¹ </span> ÙÙÙ Ø¨ÙÙØ³ ÙØ·ÙØ¨Ù!
babyshop.schedule.delivery.notification.title = ØªÙ ØªØ­Ø¯ÙØ¯ ÙÙØ¹Ø¯ Ø§ÙØªÙØµÙÙ <span class="status"> Ø§ÙØ³Ø±ÙØ¹ </span> Ø¨ÙØ¨Ù Ø´ÙØ¨ ÙØ·ÙØ¨Ù!

#Address map properties
landmark.address.google.map.error.text = ÙØ±Ø¬Ù Ø§ÙØ³ÙØ§Ø­ Ø¨Ø®Ø¯ÙØ© ØªØ­Ø¯ÙØ¯ Ø§ÙÙÙÙØ¹ Ø¬ØºØ±Ø§ÙÙÙÙØ§ ÙØ¥Ø¹Ø§Ø¯Ø© Ø§ÙÙØ­Ø§ÙÙØ©.
landmark.address.google.map.response.error = ÙØ§ ÙÙÙÙ Ø§ÙØ¹Ø«ÙØ± Ø¹ÙÙ Ø§ÙÙÙÙØ¹ Ø­Ø³Ø¨ Ø§Ø®ØªÙØ§Ø± Ø§ÙÙÙØ·ÙØ©Ø Ø­Ø§ÙÙ ( Ø§ÙØ¨Ø­Ø« ÙÙ Ø®Ø±Ø§Ø¦Ø· Ø¬ÙØ¬Ù) Ø£Ù ( Ø§ÙØ­ØµÙÙ Ø¹ÙÙ ÙÙÙØ¹Ù)
landmark.address.google.map.text= Ø§Ø¨Ø­Ø« ÙÙ Ø®Ø±Ø§Ø¦Ø· Ø¬ÙØ¬Ù
landmark.address.google.map.location.text=Ø­Ø¯Ø¯ ÙÙÙØ¹Ù
landMarkPayment.header.user.address.map.message = ÙØ§Ø³ØªÙØ§Ù ÙØ´ØªØ±ÙØ§ØªÙ ÙÙ Ø£Ø³Ø±Ø¹ ÙÙØª ÙÙÙÙ.

## Language Switch
app.language.switch.text = English
landMarkHomePage.header.user.or = -
pdp.write.your.review.here=ÙØªØ§Ø¨Ø© ØªÙÙÙÙ ÙÙØ§
pdp.give.title.here=Ø¥Ø¹Ø·Ø§Ø¦ÙØ§ ÙÙØ¨

landmarkFastPayAddress.shipping.address.mandatory.fields =ÙÙ Ø§ÙØ­ÙÙÙ Ø¥ÙØ²Ø§ÙÙØ© ÙØ§ ÙÙ ÙØ°ÙØ± Ø®ÙØ§Ù Ø°ÙÙ
landMarkPayment.header.user.payment.address.mandatory.fields =
lmgaddaddress.message=
checkout.page.order.price=Ø§ÙØ³Ø¹Ø±:
babyshop.schedule.delivery.title = Ø­Ø¯ÙØ¯ ÙÙØ¹Ø¯ ÙØªÙØµÙÙ ÙØ°Ù Ø§ÙÙÙØªØ¬Ø§Øª ÙÙ Ø¨ÙØ¨Ù Ø´ÙØ¨.
basket.page.shipping.homecentre.express = Ø§ÙØªÙØµÙÙ Ø§ÙØ³Ø±ÙØ¹ ÙÙ ÙÙÙ Ø³ÙØªØ±
basket.page.shipping.babyshop.express =Ø§ÙØªÙØµÙÙ Ø§ÙØ³Ø±ÙØ¹ ÙÙ Ø¨ÙØ¨Ù Ø´ÙØ¨
basket.page.shipping.homebox.express = Ø§ÙØªÙØµÙÙ Ø§ÙØ³Ø±ÙØ¹ ÙÙ ÙÙÙ Ø¨ÙÙØ³
basket.page.shipping.homecentre.standardGround = Ø§ÙØªÙØµÙÙ Ø§ÙØ¹Ø§Ø¯Ù ÙÙ ÙÙÙ Ø³ÙØªØ±
basket.page.shipping.babyshop.standardGround = Ø§ÙØªÙØµÙÙ Ø§ÙØ¹Ø§Ø¯Ù ÙÙ Ø¨ÙØ¨Ù Ø´ÙØ¨
basket.page.shipping.homebox.standardGround = Ø§ÙØªÙØµÙÙ Ø§ÙØ¨Ø±Ù Ø§ÙØ¹Ø§Ø¯Ù ÙÙ ÙÙÙ Ø¨ÙÙØ³

addresstype.Home= ÙÙØ²Ù
addresstype.Office= ÙÙØªØ¨ Ø¹ÙÙ
common.language.english=English
common.language.englishInAr=Ø§ÙØ§ÙØ¬ÙÙØ²ÙØ©
common.language.arabic.display=Ø§ÙØ¹Ø±Ø¨ÙØ©

##Starts : Shipping page changes
lmgcheckout.shippingpage.select.shipping.title=Ø§Ø®ØªØ± Ø·Ø±ÙÙØ© Ø§ÙØ´Ø­Ù
lmgcheckout.shippingpage.clickcollect.title=Ø§Ø³ØªÙÙÙØ§ Ø¨ÙÙØ³Ù
lmgcheckout.shippingpage.clickcollect.label=Ø§Ø³ØªÙÙ Ø·ÙØ¨Ù ÙÙ Ø§ÙÙÙØ§Ù Ø§ÙØ§ÙØ±Ø¨ ÙÙ.
lmgcheckout.shippingpage.clickcollect.pickuppoint.text=Ø§ÙØ¹Ø«ÙØ± Ø¹ÙÙ ÙÙØ·Ø© Ø§Ø³ØªÙØ§Ù
lmgcheckout.shippingpage.clickcollect.pickuppoint.subtext = Search or use our map view to find a pickup point
lmgcheckout.shippingpage.clickcollect.selectstore.button =  Ø§Ø®ØªØ±Ø§ÙÙØªØ¬Ø±
lmgcheckout.shippingpage.homedelivery.title=ØªÙØµÙÙ ÙÙØ²ÙÙ
lmgcheckout.shippingpage.homedelivery.label=Ø·ÙØ¨Ù ÙØµÙÙ Ø¥ÙÙ Ø¨Ø§Ø¨ Ø§ÙÙÙØ²Ù
lmgcheckout.shippingpage.mapview.label=Ø¹Ø±Ø¶ Ø§ÙØ®Ø±ÙØ·Ø©
lmgcheckout.shippingpage.selectstore.label=Ø§Ø®ØªØ± Ø§ÙÙÙÙØ¹
lmgcheckout.shippingpage.searchstore.label=Ø§Ø¨Ø­Ø« Ø§Ù ÙÙ Ø¨Ø§Ø³ØªØ®Ø¯Ø§Ù Ø§ÙØ®Ø±ÙØ·Ø© ÙØ§ÙØ¬Ø§Ø¯ Ø§ÙØ±Ø¨ ÙØªØ¬Ø±
lmgcheckout.shippingpage.searchbymalloremirate.label=Ø§Ø¨Ø­Ø« ÙÙ ÙÙÙ Ø£Ù Ø¥ÙØ§Ø±Ø©
lmgcheckout.shippingpage.nostorefound.message= ÙØ¹ØªØ°Ø±Ø ÙØ§ ÙÙØ¬Ø¯ ÙØªØ§Ø¬Ø± ÙØªØ§Ø­Ø©
lmgcheckout.shippingpage.viewfullstorelist.label= Ø§Ø³ØªØ¹Ø±Ø¶ Ø¬ÙÙØ¹ Ø§ÙÙØªØ§Ø¬Ø±
lmgcheckout.shippingpage.aacontactdetails.label=Ø¥Ø¶Ø§ÙØ© ØªÙØ§ØµÙÙ Ø§ÙØ§ØªØµØ§Ù Ø§ÙØ®Ø§ØµØ© Ø¨Ù
lmgcheckout.shippingpage.Proceed.To.Payment=Ø§ÙÙØªØ§Ø¨Ø¹Ø© Ø¥ÙÙ Ø§ÙØ¯ÙØ¹

lmgcheckout.reviewpage.collectfrom.label=Ø§Ø³ØªÙÙ ÙÙ:
lmgcheckout.reviewpage.addresss.label=Ø§ÙØ¹ÙÙØ§Ù:
lmgcheckout.reviewpage.workinghours.label=Ø³Ø§Ø¹Ø§Øª Ø§ÙØ¹ÙÙ:
lmgcheckout.reviewpage.phonenumber.colon=Ø±ÙÙ Ø§ÙÙØ§ØªÙ:

lmgcheckout.shippingPage.selectShippingMethod=Ø­Ø¯Ø¯ Ø·Ø±ÙÙØ© Ø§ÙØ´Ø­Ù
lmgselectaddress.label= Ø£Ø®ØªØ§Ø± Ø¹ÙÙØ§Ù
lmgaddaddress.link=Ø¥Ø¶Ø§ÙØ© Ø¹ÙÙØ§Ù Ø¬Ø¯ÙØ¯
lmgshiptoaddress.text=Ø£Ø±Ø³Ù Ø§ÙØ·ÙØ¨ Ø¥ÙÙ ÙØ°Ø§ Ø§ÙØ¹ÙÙØ§Ù
lmgaddyouraddress.title=Ø£Ø¶Ù Ø¹ÙÙØ§Ù Ø§ÙØ´Ø­Ù
lmgaddaddress.title=Ø£Ø¶Ù Ø¹ÙÙØ§Ù Ø¬Ø¯ÙØ¯
lmgaddaddress.message=
checkout.page.order.price= Ø§ÙØ³Ø¹Ø±:
checkout.page.order.subtotal= Ø§ÙÙØ¬ÙÙØ¹ Ø§ÙÙØ±Ø¹Ù:
lmgaddaddress.showalladdress=Ø£Ø¹Ø±Ø¶ Ø¬ÙÙØ¹ Ø§ÙØ¹ÙÙØ§ÙÙ
lmgaddaddress.label.fullname=Ø§ÙØ§Ø³Ù ÙØ§ÙÙ
lmgaddaddress.label.mobile=Ø§ÙÙØ§ØªÙ Ø§ÙÙØªØ­Ø±Ù
lmgaddaddress.label.email = Ø§ÙØ¨Ø±ÙØ¯ Ø§ÙØ¥ÙÙØªØ±ÙÙÙ
lmgaddaddress.label.pincode=Pincode
lmgaddaddress.label.addressline1=Ø¹ÙÙØ§Ù 1
lmgaddaddress.label.addressline2=Ø¹ÙÙØ§Ù 2
lmgaddaddress.label.landmark=Ø¹ÙØ§ÙÙ ÙÙÙØ²Ù
lmgaddaddress.label.addresstype=ÙÙØ¹ Ø§ÙØ¹ÙÙØ§Ù
lmgaddaddress.label.townorcity= Ø§ÙÙØ¯ÙÙØ© Ø£Ù Ø§ÙØ¨ÙØ¯Ø©
lmgaddaddress.label.city=Ø§ÙÙØ¯ÙÙØ©
lmgaddaddress.label.area=Ø§ÙÙÙØ·ÙØ©
lmgaddaddress.label.street.name=Ø§Ø³Ù/Ø±ÙÙ Ø§ÙØ´Ø§Ø±Ø¹*
lmgaddaddress.label.building.name=Ø§Ø³Ù Ø§ÙØ¨ÙØ§Ø¡/Ø±ÙÙ Ø§ÙÙÙØ²ÙØ Ø§ÙØ·Ø§Ø¨ÙØ Ø±ÙÙ Ø§ÙØ´ÙØ©*
lmgaddaddress.label.Landmark.optional=Ø¹ÙØ§ÙØ© ÙÙÙØ²Ø©  (Ø§Ø®ØªÙØ§Ø±Ù)
lmgaddaddress.label.Address.Type.optional=Ø§ÙØ¹ÙÙØ§Ù (Ø§Ø®ØªÙØ§Ø±Ù)
lmgaddaddress.label.state=Ø§ÙÙØ­Ø§ÙØ¸Ø©
lmgaddaddress.label.mobilenumber= Ø±ÙÙ Ø§ÙÙØ§ØªÙ Ø§ÙÙØªØ­Ø±Ù
lmgaddaddress.label.defaultaddress= ÙØ±Ø¬Ù Ø§Ø³ØªØ®Ø¯Ø§ÙÙ Ø¹ÙÙ Ø£ÙÙ Ø§ÙØ¹ÙÙØ§Ù Ø§ÙØ£Ø³Ø§Ø³Ù ÙÙØ´Ø­Ù
lmgaddaddress.placeholder.fullname = Ø£Ø¯Ø®Ù Ø§ÙØ£Ø³Ù Ø¨Ø§ÙÙØ§ÙÙ
lmgaddaddress.placeholder.email= Ø£Ø¯Ø®Ù Ø§ÙØ¨Ø±ÙØ¯ Ø§ÙØ¥ÙÙØªØ±ÙÙÙ
lmgaddaddress.placeholder.pincode=Enter your Pincode
lmgaddaddress.placeholder.addressline1=Ø±ÙÙ Ø§ÙØ´ÙØ© Ø£Ù Ø§ÙÙÙØ²ÙØ ÙØ¹ Ø§ÙØ·Ø§Ø¨Ù ÙØ§ÙØ¨ÙØ§Ø¡
lmgaddaddress.placeholder.addressline2= Ø§ÙÙØ­Ø§ÙØ¸Ø© ÙØ§ÙØ´Ø§Ø±Ø¹ ÙØ§ÙØ­Ù
lmgaddaddress.placeholder.landmark=Ø§ÙØ¹ÙØ§ÙØ© Ø§ÙÙÙÙÙØ²Ø© Ø³ØªØ³Ø§Ø¹Ø¯ÙØ§ Ø¹ÙÙ ØªØ­Ø¯ÙØ¯ ÙÙÙØ¹Ù Ø¨ØµÙØ±Ø© Ø£ÙØ¶Ù
lmgaddaddress.placeholder.addresstype=Ø£Ø®ØªØ± ÙÙØ¹ Ø§ÙØ¹ÙÙØ§Ù
lmgaddaddress.placeholder.city=ÙØ±Ø¬Ù Ø§Ø®ØªÙØ§Ø± Ø§ÙÙØ¯ÙÙØ© Ø£Ù Ø§ÙØ¨ÙØ¯Ø©
lmgaddaddress.placeholder.state=ÙØ±Ø¬Ù Ø§Ø®ØªÙØ§Ø± Ø§ÙÙØ­Ø§ÙØ¸Ø©
lmgaddaddress.placeholder.mobilenumber.part1=
lmgaddaddress.placeholder.mobilenumber.part2=
lmgaddaddress.placeholder.mobilenumber.part3=
lmgaddaddress.optional= Ø£Ø®ØªÙØ§Ø±Ù
lmgaddaddress.placeholder.countrycode=+91
lmgaddaddress.defaultaddress.text=Ø§ÙØ£Ø³Ø§Ø³Ù
##Ends : Shipping page changes


# click and collect texts
landmarkCheckoutThankYouPage.clickcollect.paragraph.title = Ø¥ÙÙÙ ÙØ§ Ø³ÙØ­Ø¯Ø« Ø¨Ø¹Ø¯ Ø°ÙÙ:
landmarkCheckoutThankYouPage.clickcollect.paragraph.line1 = Ø³ØªØªÙÙÙ Ø±Ø³Ø§ÙØ© Ø¨Ø±ÙØ¯ Ø¥ÙÙØªØ±ÙÙÙ ÙØ±Ø³Ø§ÙØ© ÙØµÙØ© Ø¹ÙØ¯ÙØ§ ÙÙÙÙ Ø·ÙØ¨Ù Ø¬Ø§ÙØ²Ø§ ÙÙØ§Ø³ØªÙØ§Ù.
landmarkCheckoutThankYouPage.clickcollect.paragraph.line2 = ØªÙØ¬Ù Ø¥ÙÙ ÙÙØ·Ø© Ø§ÙØ§Ø³ØªÙØ§Ù Ø§ÙØªÙ Ø§Ø®ØªØ±ØªÙØ§ ÙØ¹ ÙØ³Ø®Ø© ÙÙ Ø§ÙØ±Ø³Ø§ÙØ© Ø§ÙØ¥ÙÙØªØ±ÙÙÙØ© Ø£Ù Ø±Ø³Ø§ÙØ© ÙØµÙØ©Ø Ø¥ÙÙ Ø¬Ø§ÙØ¨ Ø¨Ø·Ø§ÙØ© Ø§ÙÙÙÙØ© Ø£Ù Ø¬ÙØ§Ø² Ø§ÙØ³ÙØ±.


paymentPage.tlsnCODError=
landmarkCheckoutThankYouPage.cod.msg= Ø³ÙÙ ØªØ­ØµÙ Ø¹ÙÙÙ
paymentPage.callToVerify = Ø­Ø§ÙÙØ§ ØªÙØªÙÙ ÙÙ Ø·ÙØ¨ÙØ Ø³ÙØ¨Ø¯Ø£ Ø¹ÙÙÙØ© Ø§ÙØ´Ø­Ù.
paymentPage.tlsVersionError =

paymentPage.CashOnDeliveryForHomeDelivery = ÙÙØ· ÙÙØªÙØµÙÙ Ø§ÙÙÙØ²ÙÙ

landMarkOrders.cancel.items.select.text.value = ÙØ±Ø¬Ù Ø§Ø®ØªÙØ§Ø± Ø§ÙØ·ÙØ¨Ø§Øª Ø§ÙØªÙ ØªÙØ¯ Ø§ÙØºØ§Ø¦ÙØ§
landMarkOrders.return.items.select.text.value = ÙØ±Ø¬Ù Ø§Ø®ØªÙØ§Ø± Ø§ÙØ·ÙØ¨Ø§Øª Ø§ÙØªÙ ØªÙØ¯ Ø§Ø±Ø¬Ø§Ø¹ÙØ§
landMarkOrders.items.select.all = Ø§Ø®ØªÙØ§Ø± Ø§ÙÙÙ
landMarkOrders.cancel.items.button.text = Ø§ÙØºØ§Ø¡ Ø§ÙØ·ÙØ¨Ø§Øª
landMarkOrders.return.items.button.text = Ø§Ø±Ø¬Ø§Ø¹ Ø§ÙØ·ÙØ¨Ø§Øª
landMarkOrders.cancel.selected.items.button.text = Ø§ÙØºØ§Ø¡ Ø§ÙØ·ÙØ¨Ø§Øª Ø§ÙÙØ®ØªØ§Ø±Ø©
landMarkOrders.return.selected.items.button.text = Ø§Ø±Ø¬Ø§Ø¹
landMarkOrders.items.never.mind = ØªØ±Ø§Ø¬Ø¹
landMarkOrders.refund.text = Ø§ÙÙØ¨ÙØº Ø§ÙÙØ³ØªØ±Ø¯
landMarkOrders.return.reason.select.your.comments = ØªØ¹ÙÙÙÙ
landMarkOrders.return.reason.select.characters = Ø­Ø±ÙÙ ÙØªØ¨ÙÙØ©
landMarkOrders.return.reason.select.value = ÙØ±Ø¬Ù Ø§Ø®ØªÙØ§Ø± Ø§ÙØ³Ø¨Ø¨
landMarkOrders.return.reason.error.value = ÙÙÙØªØ§Ø¨Ø¹Ø©Ø ÙØ±Ø¬Ù Ø§Ø®ØªÙØ§Ø± Ø§ÙØ³Ø¨Ø¨
landMarkOrders.return.reason.other.error.value = ÙÙÙØªØ§Ø¨Ø¹Ø©Ø ÙÙ Ø¨Ø§Ø¯Ø®Ø§Ù Ø§ÙØªØ¹ÙÙÙ


#Order entry status for cancellation and return in order history
landMarkOrders.orderEntry.status.C = ØªÙ Ø§ÙØ§ÙØºØ§Ø¡
landMarkOrders.orderEntry.status.CI =  Ø¹ÙÙÙØ© Ø§ÙØ£ÙØºØ§Ø¡ ÙÙØ¯ Ø§ÙØªÙÙÙØ°
landMarkOrders.orderEntry.status.R = Ø£Ø³ØªØ±Ø¬Ø¹
landMarkOrders.orderEntry.status.RI = Ø¹ÙÙÙØ© Ø§ÙØ¥Ø±Ø¬Ø§Ø¹ ÙÙØ¯ Ø§ÙØªÙÙÙØ°
order.return.cancel.failed.IN = ÙØ­Ù Ø¢Ø³ÙÙÙ, ÙØªØ¹Ø°Ø± Ø¹ÙÙÙØ§ ÙØ¹Ø§ÙØ¬Ø© Ø·ÙØ¨Ù. ÙØ±Ø¬Ù Ø§ÙØ§ØªØµØ§Ù <span>{0}</span> Ø®Ø¯ÙØ© Ø¯Ø¹Ù Ø§ÙØ¹ÙÙØ§Ø¡ Ø¹ÙÙ 1800-123-1444.
order.return.cancel.failed.AE = ÙØ­Ù Ø¢Ø³ÙÙÙ, ÙØªØ¹Ø°Ø± Ø¹ÙÙÙØ§ ÙØ¹Ø§ÙØ¬Ø© Ø·ÙØ¨Ù. ÙØ±Ø¬Ù Ø§ÙØ§ØªØµØ§Ù <span>{0}</span> Ø®Ø¯ÙØ© Ø¯Ø¹Ù Ø§ÙØ¹ÙÙØ§Ø¡ Ø¹ÙÙ 800-{1}UAE (800-629823).

order.return.cancel.failed = ÙØ­Ù Ø¢Ø³ÙÙÙ, ÙØªØ¹Ø°Ø± Ø¹ÙÙÙØ§ ÙØ¹Ø§ÙØ¬Ø© Ø·ÙØ¨Ù. ÙØ±Ø¬Ù Ø§ÙØ§ØªØµØ§Ù <span>{0}</span> Ø®Ø¯ÙØ© Ø¯Ø¹Ù Ø§ÙØ¹ÙÙØ§Ø¡ Ø¹ÙÙ <span>{1}</span>.

landMarkOrders.cancel.success.message.caption = ØªÙ Ø¥ÙØºØ§Ø¡ Ø§ÙÙÙØªØ¬/Ø§ÙÙÙØªØ¬Ø§Øª Ø¨ÙØ¬Ø§Ø­.
landMarkOrders.cancel.success.message = {0} Ø³ÙØªÙ Ø±Ø¯ÙØ§ Ø¥ÙÙ
landMarkOrders.cancel.success.message.general = Ø£ÙØª.
landMarkOrders.cancel.success.message.NET_BANKING= Ø­Ø³Ø§Ø¨Ù Ø§ÙÙØµØ±ÙÙ.
landMarkOrders.cancel.success.message.WALLET= ÙØ­ÙØ¸ØªÙ.
landMarkOrders.return.success.message.caption = ØªÙ Ø¥Ø±Ø³Ø§Ù Ø·ÙØ¨Ù Ø¨ÙØ¬Ø§Ø­.
landMarkOrders.return.success.message.info =Ø³ÙÙ ÙØ±Ø³Ù Ø¥ÙÙÙ Ø±Ø³Ø§ÙØ© Ø¥ÙÙØªØ±ÙÙÙØ© ØªØªØ¶ÙÙ Ø§ÙØ®Ø·ÙØ§Øª Ø§ÙØªØ§ÙÙØ© ÙØ¨Ø§Ø´Ø±Ø©. <p class='_normal'> <a href="{0}">Ø§ÙØ±Ø£ Ø£ÙØ«Ø±</a> Ø­ÙÙ Ø§ÙÙØ±ØªØ¬Ø¹Ø§Øª.</p>
landMarkOrders.cancel.success.message.CREDIT_CARD= Ø¥ÙÙ Ø¨Ø·Ø§ÙØªÙ Ø§ÙØ£Ø¦ØªÙØ§ÙÙÙ
landMarkOrders.cancel.success.message.APPLE_PAY=Ø¥ÙÙ Ø¨Ø·Ø§ÙØªÙ Ø§ÙØ£Ø¦ØªÙØ§ÙÙÙ

landMarkOrders.returns.cancel.refund.your.WALLET.text=Ø¥ÙÙ ÙØ­ÙØ¸ØªÙ: {0}
landMarkOrders.returns.cancel.refund.your.NET_BANKING.text=Ø¥ÙÙ Ø­Ø³Ø§Ø¨Ù Ø§ÙÙØµØ±ÙÙ:{0}
landMarkOrders.returns.cancel.refund.cash.amount.text={0} : Ø§ÙÙØ¨ÙØº Ø§ÙÙÙØ¯Ù
landMarkOrders.returns.cancel.refund.your.CREDIT_CARD.text=Ø§ÙÙ Ø¨Ø·Ø§ÙØªÙ : {0}
landMarkOrders.returns.cancel.refund.your.DEBIT_CARD.text=Ø§ÙÙ Ø¨Ø·Ø§ÙØªÙ : {0}
landMarkOrders.returns.cancel.refund.your.APPLE_PAY.text=Ø§ÙÙ Ø¨Ø·Ø§ÙØªÙ : {0}
landMarkOrders.returns.cancel.refund.voucher =Ù <span>{0}</span> ÙØ³ÙÙØ©: {1}

landMarkOrders.returns.cancel.refund.voucher.amount= ÙØ³ÙÙØ©: {0}
landMarkOrders.returns.cancel.refund.shipping.charge =Ø±Ø³ÙÙ Ø§ÙØ´Ø­Ù:  {0}
landMarkOrders.returns.cancel.nonRefund.gift.voucher=ÙØ³ÙÙØ© ÙØ¯Ø§ÙØ§ ØºÙØ± ÙØ§Ø¨ÙØ© ÙÙØ§Ø³ØªØ±Ø¯Ø§Ø¯:  {0}
landMarkOrders.returns.cancel.refund.nonRefundable.voucher=ÙØ³ÙÙØ© ØºÙØ± ÙØ§Ø¨ÙØ© ÙÙØ§Ø³ØªØ±Ø¯Ø§Ø¯:  {0}
landMarkOrders.returns.cancel.cod.nonrefund.amount=ÙØ¨ÙØº Ø§ÙØ¯ÙØ¹ Ø¹ÙØ¯ Ø§ÙØ§Ø³ØªÙØ§Ù Ø§ÙØºÙØ± ÙØ§Ø¨Ù ÙÙØ¥Ø±Ø¬Ø§Ø¹ : <span>{0}</span>
landmarkCheckoutThankYouPage.getDirections.label = Ø§Ø­ØµÙ Ø¹ÙÙ Ø§ÙØ§ØªØ¬Ø§ÙØ§Øª

landmark.babyshop.thankyou.page.express.delivery.message=<strong>Ø¨ÙØ¨Ù Ø´ÙØ¨ </strong><strong>{1} - {2}</strong> ÙÙ Ø§ÙÙÙØ±Ø± Ø£Ù ÙØµÙÙ Ø§ÙØ£Ø«Ø§Ø« ÙÙÙ <strong>{0}</strong> ÙØ§ Ø¨ÙÙ Ø§ÙØ³Ø§Ø¹Ù
landmark.babyshop.thankyou.page.normal.delivery.message=<strong>Ø¨ÙØ¨Ù Ø´ÙØ¨</strong> ÙØ¬Ø¨ Ø£Ù ÙØµÙ Ø§ÙØ£Ø«Ø§Ø« Ø¹ÙÙ<p class="delivery-time"><strong>{0}</strong> ÙØ§ Ø¨ÙÙ <strong>{1} - {2}</strong> Ø³Ø§Ø¹Ø§Øª</p>
landmark.babyshop.thankyou.page.free.delivery.message=<strong>ÙÙÙ Ø¨ÙÙØ³</strong> Ø³ÙÙ ÙØµÙÙ Ø§ÙØ£Ø«Ø§Ø« ÙÙ <p class="delivery-time"><strong>{0}</strong> ÙØ§ Ø¨ÙÙ Ø§ÙØ³Ø§Ø¹Ø© <strong>{1} - {2}</strong> ØªÙØ±ÙØ¨ÙØ§.

# for homebox thank you page
landmark.homebox.thankyou.page.express.delivery.message=<strong>ÙÙÙ Ø¨ÙÙØ³ </strong><strong>{1} - {2}</strong> ÙÙ Ø§ÙÙÙØ±Ø± Ø£Ù ÙØµÙÙ Ø§ÙØ£Ø«Ø§Ø« ÙÙÙ <strong>{0}</strong> ÙØ§ Ø¨ÙÙ Ø§ÙØ³Ø§Ø¹Ù
landmark.homebox.thankyou.page.free.delivery.message=<strong>ÙÙÙ Ø¨ÙÙØ³</strong> Ø³ÙÙ ÙØµÙÙ Ø§ÙØ£Ø«Ø§Ø« ÙÙ <p class="delivery-time"><strong>{0}</strong> ÙØ§ Ø¨ÙÙ Ø§ÙØ³Ø§Ø¹Ø© <strong>{1} - {2}</strong> ØªÙØ±ÙØ¨ÙØ§.
landmark.homebox.thankyou.page.normal.delivery.message=<strong>ÙÙÙ Ø¨ÙÙØ³</strong> Ø³ÙÙ ÙØµÙÙ Ø§ÙØ£Ø«Ø§Ø« ÙÙ <p class="delivery-time"><strong>{0}</strong> ÙØ§ Ø¨ÙÙ Ø§ÙØ³Ø§Ø¹Ø© <strong>{1} - {2}</strong> ØªÙØ±ÙØ¨ÙØ§.


#click and collect error messages
clickcollect.cart.ineligible= Ø³ÙÙØ© Ø§ÙØªØ³ÙÙ Ø§ÙØ®Ø§ØµØ© Ø¨Ù ØºÙØ± ÙØ¤ÙÙØ© ÙÙØ§Ø³ØªÙØ§Ø¯Ø© Ø¨Ø®Ø¯ÙØ© Ø§Ø³ØªÙÙÙØ§ ÙÙ Ø§ÙÙØªØ¬Ø±

## ********* GLOBALS *********
# Its where these properties will be used globally (Ex: Form fields)
# Please, add carefully, remove any redundant, and if move parts to here if you think they are globally used.
# Naming conventions, PLEASE its a crucial thing.

#____________ Forms ____________

# > Labels and placeholders
form.name.first.label = Ø§ÙØ§Ø³Ù Ø§ÙØ§ÙÙ
form.name.last.label = Ø§Ø³Ù Ø§ÙØ¹Ø§Ø¦ÙØ©

#___________ Cart related ____________
site.vouchers.title = ÙÙØ¨ÙÙ

## ********* / GLOBALS ENDS *********

# > Errors
form.error.name.maxlength = {0} ÙØ¬Ø¨ Ø§Ù ÙØ§ ÙØ²ÙØ¯ Ø¹Ù  {1} Ø­Ø±ÙÙØ§!
form.error.name.required = ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù {0}

landmark.homecentre.thankyou.page.standard.delivery.legend.message= ØªÙØµÙÙ Ø¹Ø§Ø¯Ù

##NPS Section
nps.write.your.reason.here=Ø£Ø¯Ø®Ù Ø§ÙØ³Ø¨Ø¨ ÙÙØ§
nps.rating.field.requiredmsg=ÙØ°Ø§ Ø§ÙØ­ÙÙ Ø¥ÙØ²Ø§ÙÙ
nps.comment.field.requiredmsg=ÙØ°Ø§ Ø§ÙØ­ÙÙ Ø¥ÙØ²Ø§ÙÙ
nps.encdata.field.requiredmsg=ÙØ°Ø§ Ø§ÙØ­ÙÙ Ø¥ÙØ²Ø§ÙÙ
nps.comment.field.invalid.length=ÙØ³ÙÙØ­ Ø¨Ø­Ø¯ Ø£ÙØµÙ Ù¢Ù¥Ù  Ø­Ø±Ù
nps.page.tryagain.message=ÙØ±Ø¬Ù Ø§ÙÙØ­Ø§ÙÙØ© ÙØ¬Ø¯ÙØ¯ÙØ§
nps.page.thankyou.message=Ø´ÙØ±ÙØ§ ÙÙ
nps.page.appreciate.message=ÙÙØ¯ÙØ± ÙÙØªÙ Ø§ÙØ°Ù Ø®ØµÙØµØªÙ ÙØªÙÙÙÙÙØ§
nps.page.appreciate.feedback=Ø´ÙØ±ÙØ§ Ø¹ÙÙ Ø±Ø£ÙÙ
nps.page.score.message=ÙÙØ¯ Ø£Ø¹Ø·ÙØªÙØ§ ØªÙÙÙÙ:
nps.page.notlikely.message=ÙØ§ Ø£Ø±Ø¬Ø­ ÙØ·ÙÙÙØ§
nps.page.likely.message=Ø£Ø±Ø¬Ø­ ØªÙØ§ÙÙØ§
nps.page.important.reason.for.score.message= ÙØ§ ÙÙ Ø§ÙØ³Ø¨Ø¨ Ø§ÙØ£ÙÙ ÙØ±Ø§Ø¡ ØªÙÙÙÙÙØ (Ø§Ø®ØªÙØ§Ø±Ù)
nps.page.feedback.exist.message=ÙÙØ¯ Ø£Ø¹Ø·ÙØªÙØ§ Ø±Ø£ÙÙ Ø¨Ø§ÙÙØ¹Ù
nps.reason.submit=Ø¥Ø±Ø³Ø§Ù
nps.rating.invalid.digit=1Ø§ÙØªÙÙÙÙ ÙÙØ· ÙÙ 0 Ø¥ÙÙ 0
nps.rating.modified.message=ÙÙØ¯ Ø£Ø®ØªØ±Øª ØªÙÙÙÙÙØ§ Ø®Ø§Ø·Ø¦ÙØ§!Ø ÙØ±Ø¬Ù Ø§Ø®ØªÙØ§Ø± Ø§ÙØªÙÙÙÙ Ø§ÙØµØ­ÙØ­.
nps.order.not.exist=Ø¹Ø°Ø±ÙØ§Ø ÙÙ ÙØªÙÙÙÙ ÙÙ Ø¥ÙØ¬Ø§Ø¯ Ø·ÙØ¨Ù. ÙØ±Ø¬Ù Ø§ÙÙØ­Ø§ÙÙØ© ÙØ¬Ø¯ÙØ¯ÙØ§.
fastpay.quantity.valueexceed.error = Ø§ÙØ±Ø¬Ø§Ø¡ Ø¥Ø¯Ø®Ø§Ù ÙÙÙØ© Ø£ÙÙ ÙÙ Ø£Ù ØªØ³Ø§ÙÙ
landmark.homecentre.thankyou.page.standard.delivery.message=Ø³ØªØµÙÙ Ø¨Ø§ÙÙ Ø§ÙÙÙØªØ¬Ø§Øª ÙÙ 4-5 Ø£ÙØ§Ù Ø¹ÙÙ
homecentre.schedule.delivery.title =Ø­Ø¯ÙØ¯ ÙÙØ¹Ø¯ ØªÙØµÙÙ Ø§ÙØ£Ø«Ø§Ø« ÙÙ ÙÙÙ Ø³ÙØªØ±
homebox.schedule.delivery.title = Ø­Ø¯ÙØ¯ ÙÙØ¹Ø¯ ØªÙØµÙÙ Ø§ÙØ£Ø«Ø§Ø« ÙÙ ÙÙÙ Ø¨ÙÙØ³
clickcollect.invalid.payment.mode=.ÙØ£Ø³ÙØ Ø§ÙØ¯ÙØ¹ ÙÙØ¯Ø§ Ø¹ÙØ¯ Ø§ÙØªØ³ÙÙÙ ØºÙØ± ÙØªØ§Ø­ ÙÙØ°Ø§ Ø§ÙØ·ÙØ¨ . ÙØ±Ø¬Ù Ø§Ø³ØªØ®Ø¯Ø§Ù Ø¨Ø·Ø§ÙØ© Ø§ÙØ§Ø¦ØªÙØ§Ù Ø£Ù Ø§ÙØ®ØµÙ ÙØ¯ÙØ¹ Ø·ÙØ¨Ù
login.signup.captcha = .Ø§ÙØ¥Ø¬Ø§Ø¨Ø© Ø§ÙØªÙ Ø£Ø¯Ø®ÙØªÙØ§ ØºÙØ± ØµØ­ÙØ­Ø©Ø ÙØ±Ø¬Ù Ø§ÙØªØ­ÙÙ ÙØ§ÙÙØ­Ø§ÙÙØ© ÙØ±Ø© Ø£Ø®Ø±Ù
flyout.all = Ø£ÙØ³Ø§Ù


## ********* ï£¿ Pay *********
applepay.button.cta = Ø§ÙØ¯ÙØ¹ Ø¨Ø§Ø³ØªØ®Ø¯Ø§Ù Apple Pay
applepay.button.cta.mobile = Ø§ÙØ¯ÙØ¹ Ø¨ Apple Pay

#Write to us properties
payment.card.not.supported=.ÙØ£Ø³ÙØ ÙØ­Ù ÙÙØ¨Ù ÙÙØ· Ø¨Ø·Ø§ÙØ§Øª ÙÙØ²Ø§ ÙÙØ§Ø³ØªØ±ÙØ§Ø±Ø¯
landmarkWriteToUs.heading.title = Ø±Ø§Ø³ÙÙØ§
landmarkWriteToUs.heading.title.description = ÙØ³Ø¹Ø¯ÙØ§ Ø³ÙØ§Ø¹ Ø±Ø£ÙÙ ÙØªØ¹ÙÙÙØ§ØªÙ.
landmarkWriteToUs.heading.title.name = Ø§Ø³ÙÙ
landmarkWriteToUs.heading.title.email.text = Ø£Ø¯Ø®Ù Ø¨Ø±ÙØ¯Ù Ø§ÙØ¥ÙÙØªØ±ÙÙÙ
landmarkWriteToUs.heading.title.name.text = Ø£Ø¯Ø®Ù Ø§Ø³ÙÙ
landmarkWriteToUs.heading.title.message.text = Ø´Ø§Ø±ÙÙØ§ Ø±Ø£ÙÙ
landmarkWriteToUs.heading.title.text = Ø§ÙØªØ¨ ØªØ¹ÙÙÙØ§ØªÙ ÙÙØ§
landmarkWriteToUs.success.msg = Ø´ÙØ±ÙØ§ ÙÙØ±Ø§Ø³ÙØªÙ&#x21;</strong></br> Ø³ÙØ¹Ø§ÙØ¯ Ø§ÙØªÙØ§ØµÙ ÙØ¹Ù ÙÙ Ø£ÙØ±Ø¨ ÙÙØª.
landmarkWriteToUs.heading.title.button.title = Ø¥Ø±Ø³Ø§Ù
landMarkMyAccountPage.header.user.my.profile.write.to.us.email = Ø¨Ø±ÙØ¯Ù Ø§ÙØ¥ÙÙØªØ±ÙÙÙ
landmarkHelp.stillHaveQuestions.write.to.us.email.desc = Ø£Ø®Ø¨Ø±ÙØ§ Ø§Ø³ØªÙØ³Ø§Ø±Ù ÙØ³ÙÙÙÙ Ø¨Ø§ÙØ±Ø¯ Ø¹ÙÙÙ ÙÙ Ø£ÙØ±Ø¨ ÙÙØª
write.to.us.url = /write-to-us
write.to.us = Ø±Ø§Ø³ÙÙØ§
text.payment.error.sadad.not.supported=Ø®ÙØ§Ø± Ø§ÙØ¯ÙØ¹ Ø¹Ù Ø·Ø±ÙÙ Ø³Ø¯Ø§Ø¯ ØºÙØ± ÙØªÙÙÙØ±
call.to.us = Ø§ØªØµÙ Ø¨ÙØ§
payment.card.not.supported=.ÙØ£Ø³ÙØ ÙØ­Ù ÙÙØ¨Ù ÙÙØ· Ø¨Ø·Ø§ÙØ§Øª ÙÙØ²Ø§ ÙÙØ§Ø³ØªØ±ÙØ§Ø±Ø¯

## Verify order COD pin code section
landMarkOrders.cod.verification.header = Ø£ÙØª Ø¹ÙÙ ÙØ´Ù Ø§ÙØ§ÙØªÙØ§Ø¡! ÙØ±Ø¬Ù Ø§ÙØªØ­ÙÙ ÙÙ Ø·ÙØ¨Ù.
landMarkOrders.cod.verification.description.start = ÙÙ ÙØ§ Ø¹ÙÙÙ ÙØ¹ÙÙ ÙÙ Ø¥Ø¯Ø®Ø§Ù Ø±ÙØ² Ø§ÙØªØ­ÙÙ Ø§ÙØ°Ù ØªÙÙ Ø¥Ø±Ø³Ø§ÙÙ Ø¥ÙÙ
landMarkOrders.cod.verification.description.end = Ø£Ø¯ÙØ§Ù. &nbsp;
landMarkOrders.cod.verification.error = ÙÙØ¯ Ø£Ø¯Ø®ÙØª Ø±ÙØ² ØºÙØ± ØµØ­ÙØ­. ÙØ±Ø¬Ù Ø§ÙÙØ­Ø§ÙÙØ© ÙØ±Ø© Ø£Ø®Ø±Ù.
landMarkOrders.cod.verification.resend.description = ÙÙ ÙØµÙÙÙ Ø§ÙØ·ÙØ¨.
landMarkOrders.cod.verification.resend.link = Ø£Ø¹Ø¯ Ø¥Ø±Ø³Ø§ÙÙ Ø§ÙØ¢Ù
landMarkOrders.cod.verification.resend.description2 =  ÙÙØ¯ Ø£Ø¹Ø¯ÙØ§ Ø¥Ø±Ø³Ø§Ù Ø§ÙØ±ÙØ². ÙØ±Ø¬Ù Ø§ÙÙØ­Ø§ÙÙØ© ÙØ±Ø© Ø£Ø®Ø±Ù Ø®ÙØ§Ù
landMarkOrders.cod.verification.description = Ø¥Ø°Ø§ ÙÙ ØªØªÙÙÙ ÙÙ Ø§ÙØªØ­ÙÙ ÙÙ Ø·ÙØ¨Ù Ø§ÙØ¢ÙØ ÙÙØ§ Ø¯Ø§Ø¹Ù ÙÙÙÙÙ. Ø³ÙØªØµÙ Ø¨Ù ÙØ±ÙØ¨ÙØ§ ÙØªØ£ÙÙØ¯ Ø°ÙÙ.
landMarkOrders.cod.verification.confirmation = Ø¨Ø¹Ø¯ Ø§ÙØªØ£ÙÙØ¯Ø Ø³ÙØµÙÙ Ø·ÙØ¨Ù
landMarkOrders.cod.verification.confirmation.success = ÙÙØ¯ ØªÙ ØªØ£ÙÙØ¯ Ø·ÙØ¨Ù <strong>Ø³ÙØµÙÙ </strong>
landmarkOrders.cod.verification.gdms.confirmation.success = ÙÙØ¯ ØªÙ ØªØ£ÙÙØ¯ Ø·ÙØ¨Ù
landMarkOrders.cod.verification.button = Ø§ÙØªØ£ÙÙØ¯
#COD OTP Messages
wrong.otp.entered.for.cod.confirmation.error= ÙÙØ¯ Ø£Ø¯Ø®ÙØª Ø±ÙØ² ØºÙØ± ØµØ­ÙØ­. ÙØ±Ø¬Ù Ø§ÙÙØ­Ø§ÙÙØ© ÙØ±Ø© Ø£Ø®Ø±Ù.
cod.otp.resent.message= ÙÙØ¯ Ø£Ø¹Ø¯ÙØ§ Ø¥Ø±Ø³Ø§Ù Ø§ÙØ±ÙØ². ÙØ±Ø¬Ù Ø§ÙÙØ­Ø§ÙÙØ© ÙØ±Ø© Ø£Ø®Ø±Ù Ø®ÙØ§Ù
cod.order.confirmed.message=ÙÙØ¯ ØªÙ ØªØ£ÙÙØ¯ Ø·ÙØ¨Ù.
cod.otp.verify.error=.ÙØ£Ø³ÙØ ÙÙØ¯ Ø­ØµÙ Ø´ÙØ¡ Ø®Ø·Ø£. ÙØ±Ø¬Ù ÙØ¹Ø§ÙØ¯Ø© Ø§ÙÙØ­Ø§ÙÙØ© ÙÙ ÙÙØª ÙØ§Ø­Ù
cod.otp.verify.limit.exceede.error = ÙØ±Ø¬Ù Ø§ÙØ§ØªØµØ§Ù Ø¨ÙØ§ Ø¹ÙÙ Ø®Ø¯ÙØ© Ø¯Ø¹Ù Ø§ÙØ¹ÙÙØ§Ø¡ Ø¹ÙÙ {1}  Ø£Ù Ø§ÙØªØ¸Ø± Ø­ØªÙ ÙØªÙ Ø§ÙØªÙØ§ØµÙ ÙØ¹Ù ÙÙ Ø£ÙØ±Ø¨ ÙÙØª ÙØªØ£ÙÙØ¯ Ø·ÙØ¨Ù
form.field.invalid.char=Please enter valid characters
landmark.shipping.address.city.select = ÙØ±Ø¬Ù Ø§Ø®ØªÙØ§Ø± Ø§ÙÙØ¯ÙÙØ©
voucher.already.applied=Ø¹Ø°Ø±Ø§Ø ÙÙØ¯ Ø§Ø³ØªØ®Ø¯ÙØª ÙØ³ÙÙØ© Ø¨Ø§ÙÙØ¹Ù.
form.field.required=ÙØ°Ø§ Ø§ÙØ­ÙÙ Ø¥ÙØ²Ø§ÙÙ
form.field.invalid.max=ÙØ¬Ø¨ Ø£Ù ÙØªØ¹Ø¯Ù Ø¹Ø¯Ø¯ Ø§ÙØ£Ø­Ø±Ù Ø¯Ø§Ø®Ù ÙØ°Ø§ Ø§ÙØ­ÙÙ { 0 } Ø­Ø±ÙÙ
form.field.invalid.min=ÙØ¬Ø¨ Ø£Ù ÙÙÙÙ Ø¹Ø¯Ø¯ Ø§ÙØ£Ø­Ø±Ù Ø¯Ø§Ø®Ù ÙØ°Ø§ Ø§ÙØ­ÙÙ { 0 } Ø­Ø±ÙÙ Ø¹ÙÙ Ø§ÙØ£ÙÙ
form.field.invalid.email=ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø¨Ø±ÙØ¯ Ø¥ÙÙØªØ±ÙÙÙ ØµØ­ÙØ­
form.field.invalid.mobile=ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø±ÙÙ ÙØ§ØªÙ ÙØªØ­Ø±ÙÙ ØµØ­ÙØ­
form.field.invalid.value=ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù ÙÙÙØ© ØµØ­ÙØ­Ø©
landmark.or.pay.with = Ø§Ù ÙÙ Ø¨Ø§Ø³ØªØ®Ø¯Ø§Ù
lmgselectyouraddress.label=Ø§Ø®ØªØ± Ø¹ÙÙØ§Ù Ø§ÙØªÙØµÙÙ

#Fastpay toggle button
landmarkFastPay.toggle.button.on = Ø¹ÙÙ
landmarkFastPay.toggle.button.off = Ø¥ÙÙØ§Ù

schedule.delivery.confirm =  ØªØ£ÙÙØ¯ ÙÙØ¹Ø¯ Ø§ÙØªÙØµÙÙ

prebasket.page.global.size.error = <span class="visible-xs">ÙØ±Ø¬Ù Ø§Ø®ØªÙØ§Ø± ÙÙØ§Ø³ Ø§ÙÙÙØªØ¬Ø§Øª Ø£Ø¯ÙØ§Ù</span><span class="hidden-xs">ÙØ±Ø¬Ù Ø§Ø®ØªÙØ§Ø± ÙÙØ§Ø³ Ø§ÙÙÙØªØ¬Ø§Øª Ø£Ø¯ÙØ§Ù ÙÙØ§Ø³ØªÙØ±Ø§Ø± Ø¨Ø¹ÙÙÙØ© Ø§ÙØªØ³ÙÙ</span>
prebasket.page.add.all.to.basket = Ø£Ø¶Ù Ø§ÙÙÙ Ø¥ÙÙ Ø³ÙØ© Ø§ÙØªØ³ÙÙ
pp.page.view.the.collection = Ø§Ø³ØªØ¹Ø±Ø¶ Ø§ÙØªØ´ÙÙÙØ©
pp.page.complete.the.look = Ø£ÙÙÙ Ø§ÙÙØ¬ÙÙØ¹Ø© -
pp.page.view.all = <span class="visible-xs view">Ø¹Ø±Ø¶ Ø§ÙÙÙ</span><span class="hidden-xs">Ø§Ø³ØªØ¹Ø±Ø¶ Ø§ÙØªØ´ÙÙÙØ©</span>

landmarkFeedback.heading.title.feedback.contactnumber.text = Ø±ÙÙ Ø§ÙÙØ§ØªÙ Ø§ÙÙØªØ­Ø±Ù
landmarkFeedback.heading.title.feedback.about = Ø¥Ù ÙÙØ§Ø­Ø¸Ø§ØªÙ Ø­ÙÙ:
landmarkFeedback.heading.title.feedback.online = ØªØ¬Ø±Ø¨ØªÙ Ø£ÙÙÙØ§ÙÙ
landmarkFeedback.heading.title.feedback.instore = ØªØ¬Ø±Ø¨ØªÙ ÙÙ Ø§ÙÙØªØ¬Ø±
landmarkFeedback.heading.title.feedback.help.text = ÙÙÙ ÙÙÙÙÙØ§ ÙØ³Ø§Ø¹Ø¯ØªÙØ
landmarkFeedback.heading.title.feedback.help.required = ÙØ±Ø¬Ù Ø§Ø®ØªÙØ§Ø± Ø§ÙÙÙØ¶ÙØ¹
landmarkFeedback.heading.title.feedback.help.placeholder = ÙØ±Ø¬Ù Ø§Ø®ØªÙØ§Ø± Ø§ÙÙÙØ¶ÙØ¹
lmg.complete.collection=Ø£ÙÙÙ Ø§ÙØªØ´ÙÙÙØ©
lmg.complete.look=Ø£ÙÙÙ Ø§ÙÙØ¬ÙÙØ¹Ø©
vat.included.message=(ÙØ´ÙÙ Ø¶Ø±ÙØ¨Ø© Ø§ÙÙÙÙØ© Ø§ÙÙØ¶Ø§ÙØ© Ø§ÙÙØ·Ø¨Ù)


#click & Collect
landMarkOrders.clickAndCollect.addPickupAddress.title = Ø£Ø¶Ù Ø¹ÙÙØ§Ù Ø§ÙØ§Ø³ØªÙØ§Ù
landMarkOrders.clickAndCollect.selectPickupAddress.title = Ø§Ø®ØªØ± Ø¹ÙÙØ§Ù Ø§ÙØ§Ø³ØªÙØ§Ù
landMarkOrders.clickAndCollect.addPickupAddressModal.submit = ÙØªØ§Ø¨Ø¹Ø©



landMarkAddressBook.Emirate=Ø§ÙØ¥ÙØ§Ø±Ø©
landMarkAddressBook.Building.Error.Msg=ÙØ±Ø¬Ù Ø¥Ø¯Ø®Ø§Ù Ø§Ø³Ù Ø§ÙÙØ¨ÙÙ/Ø§ÙØ¹ÙØ§Ø±Ø©

#My Account shukran
loyalty.title = Ø´ÙØ±ÙØ§
landMarkMyAccountPage.header.user.my.landmarkRewards.description=Ø§ÙØ³Ø¨ ÙØ§Ø³ØªØ®Ø¯Ù ÙÙØ§Ø· Ø§ÙÙÙØ§Ø¡ Ø§ÙØ«ÙÙÙØ© ÙÙØªÙÙÙØ± Ø§ÙÙÙØ±Ù.

#Opc
Cnc.ChangeLink= ØªØºÙØ± Ø§ÙÙØªØ¬Ø±

#My Account MyCredit
landMarkMyAccountPage.header.user.my.credit = Ø±ØµÙØ¯Ù
landMarkMyAccountPage.header.user.my.credit.description = Ø¹Ø±Ø¶ Ø±ØµÙØ¯ Ø§ÙØ§Ø¦ØªÙØ§Ù Ø§ÙØ®Ø§Øµ Ø¨Ù.
landMarkMyCredit.emptyText = ÙÙØ³ ÙØ¯ÙÙ Ø±ØµÙØ¯ ÙÙ Ø§ÙÙÙØª Ø§ÙØ­Ø§ÙÙ.
landMarkMyCredit.emptyText.description =  Ø³ÙØªÙ ØªØ­Ø¯ÙØ« "Ø±ØµÙØ¯Ù" Ø¹ÙØ¯ÙØ§ ØªØ³ØªÙÙ Ø§ÙÙØ¨ÙØº Ø§ÙÙØ±Ø¬Ø¹.
landMarkMyCredit.uses.title = ÙØ§ ÙÙ ÙØ²Ø§ÙØ§ Ø§Ø³ØªØ®Ø¯Ø§Ù "Ø±ØµÙØ¯Ù"Ø
landMarkMyCredit.refunds.title = Ø§Ø­ØµÙ Ø¹ÙÙ Ø§Ø³ØªØ±Ø¯Ø§Ø¯ ÙÙØ±Ù ÙÙÙØ¨ÙØº
landMarkMyCredit.refunds.description = ÙÙØ· ÙÙ Ø¨Ø§Ø®ØªÙØ§Ø± "Ø±ØµÙØ¯Ù" ÙØ·Ø±ÙÙØ© Ø§Ø³ØªØ±Ø¯Ø§Ø¯ Ø§ÙÙØ¨ÙØº ÙÙ ØµÙØ­Ø© Ø³Ø¬Ù Ø§ÙØ·ÙØ¨Ø§Øª.
landMarkMyCredit.spend.title = Ø£ÙÙÙ Ø±ØµÙØ¯ Ø§ÙØ§Ø¦ØªÙØ§Ù Ø£Ø«ÙØ§Ø¡ Ø§ÙØªØ³ÙÙ
landMarkMyCredit.spend.description = Ø§Ø¶ØºØ· Ø¹ÙÙ Ø®ÙØ§Ø± Ø§ÙØ¯ÙØ¹ Ø¨Ø§Ø³ØªØ®Ø¯Ø§Ù "Ø±ØµÙØ¯Ù" ÙØ§Ø³ØªØ®Ø¯Ø§Ù Ø±ØµÙØ¯Ù Ø¨ÙÙ Ø³ÙÙÙØ©.
landMarkMyCredit.help =  ÙÙØ²ÙØ¯ ÙÙ Ø§ÙÙØ¹ÙÙÙØ§ØªØ ØªÙØ¬Ù 
landMarkMyCredit.help.link =  ÙØ±ÙØ² Ø§ÙÙØ³Ø§Ø¹Ø¯Ø©
landMarkMyCredit.balance.title = Ø±ØµÙØ¯ Ø§ÙØ§Ø¦ØªÙØ§Ù Ø§ÙØ®Ø§Øµ Ø¨Ù
landMarkMyCredit.transaction.filter.last=Ø¢Ø®Ø±
landMarkMyCredit.transaction.filter.months= Ø£Ø´ÙØ±
landMarkMyCredit.transaction.filter.month= Ø´ÙØ±
landMarkMyCredit.transaction.filter.all= All transactions
landMarkMyCredit.transaction.order= Ø±ÙÙ Ø§ÙØ·ÙØ¨
landMarkMyCredit.transaction.debit= Ø§ÙØ§Ø¦ØªÙØ§Ù Ø§ÙÙØ³ØªØ®Ø¯Ù
landMarkMyCredit.transaction.credit= Ø§ÙØ§Ø¦ØªÙØ§Ù Ø§ÙÙØ³ØªØ±Ø¯
landMarkMyCredit.transaction.dateformate = dd MMMMMM, YYYY
landMarkMyCredit.transaction.statement= ÙØ´Ù Ø­Ø³Ø§Ø¨Ù
landmark-ae.landMarkMyCredit.help.link = <a href="https://help.maxfashion.com/hc/ar/sections/115000726234-Returns-Policy" class="myCredit__help__link">ÙÙØ²ÙØ¯ ÙÙ Ø§ÙÙØ¹ÙÙÙØ§ØªØ ØªÙØ¬Ù</a>
landmark-bh.landMarkMyCredit.help.link = <a href="https://bh.help.maxfashion.com/hc/ar/sections/************-Returns-Policy" class="myCredit__help__link">ÙÙØ²ÙØ¯ ÙÙ Ø§ÙÙØ¹ÙÙÙØ§ØªØ ØªÙØ¬Ù</a>
landmark-sa.landMarkMyCredit.help.link = <a href="https://sa.help.maxfashion.com/hc/ar/sections/************-Returns-Policy" class="myCredit__help__link">ÙÙØ²ÙØ¯ ÙÙ Ø§ÙÙØ¹ÙÙÙØ§ØªØ ØªÙØ¬Ù</a>

#My Account DateOfBirth
login.signup.add.dob.title.optional = ØªØ§Ø±ÙØ® Ø§ÙÙÙØ§Ø¯Ø© (Ø§Ø®ØªÙØ§Ø±Ù)
login.signup.add.dob.birthdayText = ÙÙØ· Ø£Ø®Ø¨Ø±ÙÙØ§ ÙØ³ÙÙ ÙØ±Ø³Ù ÙÙÙ ÙØ¯ÙØ© Ø¹ÙØ¯ ÙÙÙØ§Ø¯Ù.
login.signup.select.date = Ø§ÙØªØ§Ø±ÙØ®
login.signup.select.month = Ø§ÙØ´ÙØ±
login.signup.incorrect.date = ÙØ±Ø¬Ù Ø§Ø®ØªÙØ§Ø± ØªØ§Ø±ÙØ® Ø§ÙØªÙØ§Ø¡ ØµÙØ§Ø­ÙØ© ØµØ­ÙØ­


pdp.priceVAT = Ø´Ø§ÙÙØ© Ø¶Ø±ÙØ¨Ø© Ø§ÙÙÙÙØ© Ø§ÙÙØ¶Ø§ÙØ©
pdp.freeShopping = Free shipping on EGP 399+ | Free returns
pdp.color.label = Ø§ÙÙÙÙ
pdp.size.label = Ø§ÙÙÙØ§Ø³
pdp.addToBasket = Ø¥ÙØªÙÙ ÙÙ Ø§ÙÙØ®Ø²Ù

plp.shopFor.label = ÙØªØ¬Ø± ÙÙ
plp.sortBy.label = Ø§ÙØªØ±ØªÙØ¨ Ø¨Ø­Ø³Ø¨
plp.sortBy.value.list = Ø§ÙØ£Ø­Ø¯Ø«:Ø§ÙØ®ØµÙ:Ø§ÙØ³Ø¹Ø± - ÙÙ Ø§ÙØ£ÙÙ ÙÙØ£Ø¹ÙÙ:Ø§ÙØ³Ø¹Ø± - ÙÙ Ø§ÙØ£Ø¹ÙÙ Ø¥ÙÙ Ø§ÙØ£ÙÙ:ÙØ¯Ù Ø§ÙØµÙØ©
plp.sortBy.value.newArrivals = Ø§ÙØ£Ø­Ø¯Ø«
plp.sortBy.value.discount = Ø§ÙØ®ØµÙ
plp.sortBy.value.priceLowToHigh = Ø§ÙØ³Ø¹Ø± - ÙÙ Ø§ÙØ£ÙÙ ÙÙØ£Ø¹ÙÙ
plp.sortBy.value.priceHighToLow = Ø§ÙØ³Ø¹Ø± - ÙÙ Ø§ÙØ£Ø¹ÙÙ Ø¥ÙÙ Ø§ÙØ£ÙÙ
plp.sortBy.value.relevance = ÙØ¯Ù Ø§ÙØµÙØ©

##Purchase - Instore

text.account.purchases.instore.viewdetails = Ø´Ø§ÙØ¯ Ø§ÙØªÙØ§ØµÙÙ
text.account.purchases.instore.location = Ø§ÙÙÙÙØ¹
text.account.purchases.instore.status = Ø§ÙØ­Ø§ÙØ©
text.account.purchases.instore.total = Ø¥Ø¬ÙØ§ÙÙ Ø§ÙÙØ¨ÙØº
text.account.purchases.instore.nonShukraninfoTitle = ÙØ§ Ø­Ø§Ø¬Ø© ÙÙØ§Ø­ØªÙØ§Ø¸ Ø¨ÙÙØ§ØªÙØ±Ù ÙÙ Ø§ÙÙØªØ¬Ø± Ø¨Ø¹Ø¯ Ø§ÙØ¢Ù!
text.account.purchases.instore.nonshukran.createOrLinkMessage = Ø£ÙØ´Ø¦ Ø­Ø³Ø§Ø¨ Ø´ÙØ±ÙØ§ Ø¬Ø¯ÙØ¯ Ø£Ù Ø§Ø±Ø¨Ø· Ø­Ø³Ø§Ø¨Ù Ø§ÙØ­Ø§ÙÙ
text.account.purchases.instore.nonShukran.viewAlltext = Ø§Ø·ÙÙØ¹ Ø¹ÙÙ ÙÙ ÙÙØ§ØªÙØ±Ù ÙÙ Ø§ÙÙØªØ¬Ø± ÙÙ Ø§ÙÙØªØµÙØ­
text.account.purchases.instore.linkShukranButton = Ø§Ø±Ø¨Ø· Ø­Ø³Ø§Ø¨ Ø´ÙØ±ÙØ§
text.account.purchases.instore.registerLinkButton = ÙØ³Øª Ø¹Ø¶ÙÙØ§ ÙÙ Ø´ÙØ±ÙØ§
text.account.purchases.instore.ShukranUserinfoTitle = You donÃ¢ÂÂt have any purchases yet!
text.account.purchases.instore.ShukranUserinfosubTitle = Looks like you havenÃ¢ÂÂt shopped from our
text.account.purchase.instore.completed = ÙÙØªÙÙ
text.account.purchase.instore.vat = (ØºÙØ± Ø´Ø§ÙÙ Ø§ÙØ¶Ø±ÙØ¨Ø©)
landMarkOrders.header.purchaseSummary = ÙÙØ®Øµ Ø¹ÙÙÙØ© Ø§ÙØ´Ø±Ø§Ø¡
# In store Order Receipts
text.orderdetails.instore.backToPurchases = Ø§ÙØ¹ÙØ¯Ø© Ø¥ÙÙ Ø§ÙÙØ´ØªØ±ÙØ§Øª
text.orderdetails.instore.completed = ÙÙØªÙÙ
text.orderdetails.instore.viewReceipt =  Ø¹Ø±Ø¶ Ø§ÙØ¥ÙØµØ§Ù
text.orderdetails.instore.purchaseSummary = ÙÙØ®Øµ Ø¹ÙÙÙØ© Ø§ÙØ´Ø±Ø§Ø¡
text.orderdetails.instore.returnsPolicy = Ø³ÙØ§Ø³Ø© Ø§ÙØ§Ø³ØªØ¨Ø¯Ø§Ù
text.orderdetails.instore.viewReturnsPolicy = Ø¹Ø±Ø¶ Ø³ÙØ§Ø³Ø© Ø§ÙØ¥Ø±Ø¬Ø§Ø¹
text.orderdetails.instore.paymentDetails = ØªÙØ§ØµÙÙ Ø¹ÙÙÙØ© Ø§ÙØ´Ø±Ø§Ø¡
text.orderdetails.instore.cash = ÙÙØ¯Ù
text.orderdetails.instore.Points = ÙÙØ§Ø·
text.orderdetails.instore.purchaseSummarytitle = ÙÙØ®Øµ Ø¹ÙÙÙØ© Ø§ÙØ´Ø±Ø§Ø¡
text.orderdetails.instore.quanity = Ø§ÙØ¹Ø¯Ø¯
text.orderdetails.instore.subtotal = Ø§ÙÙØ¬ÙÙØ¹ Ø§ÙÙØ±Ø¹Ù:
text.orderdetails.instore.VAT = Ø¶Ø±ÙØ¨Ø© Ø§ÙÙÙÙØ© Ø§ÙÙØ¶Ø§ÙØ©:
text.orderdetails.instore.Total= Ø§ÙØ¥Ø¬ÙØ§ÙÙ: