package com.landmarkshops.Web_Mweb.utilities;


import io.restassured.RestAssured;
import io.restassured.response.Response;

import java.security.SecureRandom;
import java.util.HashMap;
import java.util.Map;

public class MyCreditBalanceUpdate {

//    public static void main(String[] args) {



    public static Response responseMyCreditAccount() {

        String baseURI = "https://uat6.maxfashion.com";
        //String baseURI = "https://uat6.homecentre.com";
        // Set base URL
        //RestAssured.baseURI =baseURI;

        Response authResponse = AccessTokenForRegisterUserAPI.getAccessTokenForRegisterUser(baseURI);
        String access_token = authResponse.jsonPath().getString("access_token").trim();
        String customer_id = authResponse.jsonPath().getString("customer_id").trim();
        // Create headers
        Map<String, String> headers = createHeaders("Bearer " + access_token);
    /*  This "store-credit-accounts" using to fetch  my credit account number
     and   my credit balance
*/
        Response responseMyCreditAccount = getMyCreditAccountDetails("api/credit-account/customers/" + customer_id + "/store-credit-accounts", headers);
        responseMyCreditAccount.then().log().all();

        return responseMyCreditAccount;
    }

    public static String getMyCreditAccountBalance() {


         /* *//*  This "store-credit-accounts" using to fetch  my credit account number
             and   my credit balance
        */
//            Response responseMyCreditAccount = getMyCreditAccountDetails("api/credit-account/customers/" + customer_id + "/store-credit-accounts", headers);
//            responseMyCreditAccount.then().log().all();
            //String myCreditAccountNumber = responseMyCreditAccount.jsonPath().getString("content[0].accountNumber").trim();
            String myCreditBalanceAmount = responseMyCreditAccount().jsonPath().getString("content[0].balance.amount").trim();
            System.out.println("My credit balance for the given account"+myCreditBalanceAmount);
            return myCreditBalanceAmount;
        }


    public static String getMyCreditAccountNumber() {


//        /* *//*  This "store-credit-accounts" using to fetch  my credit account number
//     and   my credit balance
//*/
//            Response responseMyCreditAccount = getMyCreditAccountDetails("api/credit-account/customers/" + customer_id + "/store-credit-accounts", headers);
//            responseMyCreditAccount.then().log().all();
        String myCreditAccountNumber = responseMyCreditAccount().jsonPath().getString("content[0].accountNumber").trim();
        //String myCreditBalanceAmount = responseMyCreditAccount.jsonPath().getString("content[0].balance.amount").trim();
        //System.out.println("My credit balance for the given account"+myCreditBalanceAmount);
        return myCreditAccountNumber;
    }

    public static String addAmountToMyCreditAccount(int amount){
        String baseURI = "https://uat6.maxfashion.com";
        //String baseURI = "https://uat6.homecentre.com";
        Response authResponse = AccessTokenForRegisterUserAPI.getAccessTokenForRegisterUser(baseURI);
        String access_token = authResponse.jsonPath().getString("access_token").trim();
        String customer_id = authResponse.jsonPath().getString("customer_id").trim();
        Map<String, String> headers = createHeaders("Bearer " + access_token);
        // Create request body
        Map<String, Object> requestBody = addMyCreditPoints(amount, "EGP", randomStringGenerator(26));

          /*   "store-credit-accounts/account Number/credit"  using this end point we will add my credit points to account
     and   my credit balance
*/
        // Send the POST request
        Response myCreditAddResponse = addMyCreditAmount("/api/credit-account/store-credit-accounts/" + getMyCreditAccountNumber() + "/credit", headers, requestBody);
        myCreditAddResponse.then().log().all();
        // Print the response

        System.out.println("Response status code: " + myCreditAddResponse.getStatusCode());
        System.out.println("Response body: " + myCreditAddResponse.getBody().asString());
        Response responseMyCreditBalance = getMyCreditAccountDetails("api/credit-account/customers/" + customer_id + "/store-credit-accounts", headers);
        responseMyCreditBalance.then().log().all();
        String responseMyCreditBalanceUpdated = responseMyCreditBalance.jsonPath().getString("content[0].balance.amount").trim();

        System.out.println("My credit balance for the given account updated as"+responseMyCreditBalanceUpdated);
        return responseMyCreditBalanceUpdated;
    }

    // Create request headers
    private static Map<String, String> createHeaders(String authorizationToken) {
        Map<String, String> headers = new HashMap<>();
        headers.put("accept", "application/json");
        headers.put("X-Context-Request", "{   'applicationId': '102',   'tenantId': '5DF1363059675161A85F576D' }");
        //headers.put("X-Context-Request", "{   'applicationId': 'hc-eg',   'tenantId': '5DF1363059675161A85F576D' }");
        headers.put("Content-Type", "application/json");
        headers.put("Authorization", authorizationToken);
        return headers;
    }

    // Create request body
    private static Map<String, Object> addMyCreditPoints(int amount, String currency, String transactionReferenceId) {
        Map<String, Object> requestBody = new HashMap<>();
        Map<String, Object> transactionAmount = new HashMap<>();
        transactionAmount.put("amount", amount);
        transactionAmount.put("currency", currency);
        requestBody.put("transactionAmount", transactionAmount);
        requestBody.put("transactionReferenceId", transactionReferenceId);
        requestBody.put("requestId", randomStringGenerator(26));
        requestBody.put("source", randomStringGenerator(26));
        requestBody.put("sourceUserType", "BLC_CUSTOMER");
        requestBody.put("sourceUserId", "string");
        requestBody.put("sourceEntityType", "BLC_PAYMENT");
        requestBody.put("sourceEntityId", "string");
        requestBody.put("reason", "string");
        requestBody.put("reasonDescription", "string");
        requestBody.put("dateRecorded", "2023-05-15T09:39:59.549Z");

        return requestBody;
    }

    // Send POST request
    private static Response addMyCreditAmount(String endpoint, Map<String, String> headers, Map<String, Object> requestBody) {
        return RestAssured.given()
                .headers(headers)
                .body(requestBody)
                .when()
                .log()
                .all()
                .post(endpoint);
    }

    private static Response getMyCreditAccountDetails(String endpoint, Map<String, String> headers) {
        return RestAssured.given()
                .headers(headers)
                .when()
                .log()
                .all()
                .get(endpoint);
    }


    public static String randomStringGenerator(int length) {

        int desiredLength = length;
        String randomString = generateRandomString(desiredLength);
        System.out.println("Random String: " + randomString);
        return randomString;
    }

    private static String generateRandomString(int length) {
        final String characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        SecureRandom random = new SecureRandom();
        StringBuilder sb = new StringBuilder(length);
        for (int i = 0; i < length; i++) {
            int randomIndex = random.nextInt(characters.length());
            sb.append(characters.charAt(randomIndex));
        }
        return sb.toString();
    }
}
