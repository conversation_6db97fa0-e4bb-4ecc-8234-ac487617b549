/*
#  Web Utils
#  LandmarkGroup
#  Created by <PERSON><PERSON><PERSON><PERSON> on 01/2/16.
#  Copyright (c) 2016 LandmarkGroup . All rights reserved.
*/

package com.landmarkshops.Web_Mweb.utilities;

import com.landmarkshops.Web_Mweb.config.config;
import com.landmarkshops.Web_Mweb.launcher.TestHarness;
//import io.appium.java_client.MobileElement;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONArray;
import org.json.simple.JSONObject;
import org.json.simple.parser.JSONParser;
import org.json.simple.parser.ParseException;
import org.openqa.selenium.*;
import org.openqa.selenium.NoSuchElementException;
import org.openqa.selenium.interactions.Action;
import org.openqa.selenium.interactions.Actions;
import org.openqa.selenium.support.ui.*;

import javax.lang.model.element.Element;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class WebUtils {

    public WebDriver driver;
    public WebDriverWait ajaxWait;
    private int TIME_OUT;
    public Logger testLogger;
    public Wait<WebDriver> wait;
    String concept = config.getConcept();


    public WebUtils(WebDriver driver) {
        this.driver = driver;
        ajaxWait = new WebDriverWait(driver, Duration.ofSeconds(TIME_OUT));
        testLogger = LogManager.getLogger(Logger.class.getName());
    }


    public static int getRandomNumber() {

        Random rnd = new Random();
        int id = rnd.nextInt(5000);
        return id;

    }

    public static String generateRandomPhoneNumberString() {
        // It will generate 6 digit random Number.
        // from 0 to 999999
        Random rnd = new Random();
        int number = rnd.nextInt(999999);

        // this will convert any number sequence into 6 character.
        return "522" + String.format("%06d", number);
    }

    /**
     * This method will wait for Webelement to appear
     *
     * @param element
     * @param timeout
     */
    // Wait for element present for WebElement
    public void waitForWebElementPresent(WebElement element, long timeout) {


        new WebDriverWait(driver, Duration.ofSeconds(timeout)).until(ExpectedConditions.visibilityOf(element));

    }

    public void waitForWebElementPresent(WebElement element, WebDriver driver, long timeout) {
        new WebDriverWait(driver, Duration.ofSeconds(timeout)).until(ExpectedConditions.visibilityOf(element));

    }

    // Wait for visibilityOf of element
    public void waitForWebElementVisibility(WebElement element, long timeout) {

        new WebDriverWait(driver, Duration.ofSeconds(timeout)).until(ExpectedConditions.visibilityOf(element));
//		WebDriverWait ajaxWait = new WebDriverWait(driver, timeout);
//		ajaxWait.until(ExpectedConditions.visibilityOf(element));

    }


    public void waitForByElementPresent(By element, long timeout) {
        WebDriverWait ajaxWait = new WebDriverWait(driver, Duration.ofSeconds(timeout));
        ajaxWait.until(ExpectedConditions.presenceOfElementLocated(element));
    }


    /**
     * This method is used to execute a Java script
     *
     * @param we
     */
    public void javaScriptExecute(WebElement we) {
        final JavascriptExecutor js = (JavascriptExecutor) this.driver;
        js.executeScript("arguments[0].click();", we);
    }

    public void jsExeScrollTo(WebElement element) {
        final JavascriptExecutor js = (JavascriptExecutor) this.driver;
        js.executeScript("arguments[0].scrollIntoView();", element);
        js.executeScript("window.scrollTo(0, document.body.scrollHeight)");
    }

    public void Click(WebElement element) {
        element.click();
    }

    public String verify_element_Present(WebElement element) {
        boolean res = element.isDisplayed();
        //	log.info("User found  element is Displayed in screen is available : " + res);
        return null;
    }

    public void fluentWait(WebDriver driver, long withTimeoutDuration) {

        wait = new FluentWait<WebDriver>(driver)
                .withTimeout(Duration.ofSeconds(withTimeoutDuration))
                .pollingEvery(Duration.ofMillis(1000));
    }

    public boolean isAlertPresent(WebElement element, long withTimeoutDuration) {
        fluentWait(driver, withTimeoutDuration);
        if (wait.until(ExpectedConditions.alertIsPresent()) == null) {
            //log.info("Alert not present");
            return false;
        } else
            //log.info("Alert not present");
            return true;
    }

    public void visibilityOf(WebElement element, long withTimeoutDuration) {

        new WebDriverWait(driver, Duration.ofSeconds(withTimeoutDuration)).until(ExpectedConditions.visibilityOf(element));
    }

    public boolean textToBePresentInElementAttributeValue(WebElement element, String attributeValue) {

        return new WebDriverWait(driver, Duration.ofSeconds(15)).until(ExpectedConditions.textToBePresentInElementValue(element, attributeValue));

    }

    public boolean titleContains(String title) {

        return new WebDriverWait(driver, Duration.ofSeconds(15)).until(ExpectedConditions.titleContains(title));

    }
//
//	// MobileElement not required - WebElement is already existing
//	public void waitForElementToBeClickable(MobileElement element, long withTimeoutDuration) {
//		new WebDriverWait(driver,Duration.ofSeconds(withTimeoutDuration)).until(ExpectedConditions.elementToBeClickable(element));
//
//	}


//	public void waitForElement(MobileElement element, long withTimeoutDuration) {
//
//		new WebDriverWait(driver,Duration.ofSeconds(withTimeoutDuration)).until(ExpectedConditions.elementToBeClickable(element));
//
//	}
//
//	public boolean isElementDisplayed(MobileElement element) {
//		try {
//			element.isDisplayed();
//			return true;
//		} catch (org.openqa.selenium.NoSuchElementException e) {
//			return false;
//		}
//
//	}
//
//	public boolean isElementDisplayed(MobileElement element,
//									  long withTimeoutDuration) {
//		try {
//			element.isDisplayed();
//			return true;
//		} catch (org.openqa.selenium.NoSuchElementException e) {
//			return false;
//		}
//
//	}

    // Wait


    public void acceptAlerts() {
        try {
            Thread.sleep(2);
            driver.switchTo().alert().accept();
            //log.info("Alerts Presented and tap Accept");
        } catch (Exception ex) {
            //log.info("Alerts not Presented");
        }
    }

    public void dismissAlerts() {
        try {
            Thread.sleep(2);
            driver.switchTo().alert().dismiss();
            //log.info("Alerts Presented and tap Dismiss");
        } catch (Exception ex) {
            //log.info("Alerts not Presented");
        }
    }

    public void captureAlerts() {
        try {
            Thread.sleep(2);
            driver.switchTo().alert().getText();

        } catch (Exception ex) {
            //log.info("Alerts not Presented");
        }
    }

    /**
     * This method will wait for a web element present
     */

    // Wait for element present for lists WebElement
    public void waitForListOfWebElementsPresent(List<WebElement> elements, long timeout) {
        new WebDriverWait(driver, Duration.ofSeconds(timeout)).until(ExpectedConditions.visibilityOfAllElements(elements));

    }


    public void waitForElementToBeClickable(WebElement element, long withTimeoutDuration) {
        new WebDriverWait(driver, Duration.ofSeconds(withTimeoutDuration)).until(ExpectedConditions.elementToBeClickable(element));

    }


    public void waitForElementToBeClickable(WebElement element, WebDriver driver, long withTimeoutDuration) {
        new WebDriverWait(driver, Duration.ofSeconds(withTimeoutDuration)).until(ExpectedConditions.elementToBeClickable(element));

    }

    public void waitClick(WebElement element, WebDriver driver, long withTimeoutDuration) {
        new WebDriverWait(driver, Duration.ofSeconds(withTimeoutDuration)).until(ExpectedConditions.elementToBeClickable(element)).click();
    }

    public void jsWaitClick(WebElement element, WebDriver driver, long withTimeOutDuration) {
        WebDriverWait wait = new WebDriverWait(driver, Duration.ofSeconds(10));
        wait.until(ExpectedConditions.elementToBeClickable(element));
        ((JavascriptExecutor) driver).executeScript("arguments[0].click();", element);
    }


    public boolean isElementDisplayed(WebElement element, long withTimeoutDuration) {
        try {
            element.isDisplayed();
            return true;
        } catch (org.openqa.selenium.NoSuchElementException e) {
            return false;
        }

    }

    public void scrollDown(Object x, Object y) {
        JavascriptExecutor Scroller = (JavascriptExecutor) driver;
        Scroller.executeScript("scroll(x, y);");

    }

    public void dragAndDrop(WebElement element, int x, int y) {
        Actions price = new Actions(driver);
        price.dragAndDropBy(element, x, y).release().perform();

    }

    void waitForLoadJavaScript(WebDriver driver, int time) {
        new WebDriverWait(driver, Duration.ofSeconds(time)).until((ExpectedCondition<Boolean>) wd ->
                ((JavascriptExecutor) wd).executeScript("return document.readyState").equals("complete"));
    }

    public void waitForPageToLoad(int time) {
        driver.manage().timeouts().pageLoadTimeout(time, TimeUnit.SECONDS);
    }

    public void pageRefresh() {
        driver.navigate().refresh();
    }

    public void waitForPageToLoadJS() {
        JavascriptExecutor js = (JavascriptExecutor) driver;
        js.executeScript("return document.readyState").toString().equals("complete");
    }

    public void implicitWait(int time) {
        driver.manage().timeouts().implicitlyWait(time, TimeUnit.SECONDS);
    }

    public static org.openqa.selenium.By byLocator(final String locator) {
        By result = null;
        if (locator.startsWith("//")) {
            result = By.xpath(locator);
        } else if (locator.startsWith("css=")) {
            result = By.cssSelector(locator.replace("css=", ""));
        } else if (locator.startsWith("#")) {
            result = By.name(locator.replace("#", ""));
        } else if (locator.startsWith("Link=")) {
            result = By.linkText(locator.replace("Link=", ""));
        } else if (locator.startsWith("xpath=")) {
            result = By.xpath(locator);
        } else if (locator.startsWith("(//")) {
            result = By.xpath(locator);
        } else {
            result = By.id(locator);
        }
        return result;
    }

    // Assert element present
    public Boolean isElementPresent(final String locator) {
        Boolean result = false;
        try {
            driver.findElement(byLocator(locator));
            result = true;
        } catch (final Exception ex) {
        }
        return result;
    }

    public void scrollUp() {
        JavascriptExecutor Scroller = (JavascriptExecutor) driver;
        Scroller.executeScript("scroll(0, -250);");

    }

    public void scrollingUp() {
        JavascriptExecutor Scroller = (JavascriptExecutor) driver;
        Scroller.executeScript("scroll(0, -10);");
        Scroller.executeScript("scroll(0,-1)");

    }


    public void scrollElement(WebElement Element) {
        JavascriptExecutor jse = (JavascriptExecutor) driver;
        jse.executeScript("arguments[0].scrollIntoView();", Element);

    }

    public void scrolldown() {
        JavascriptExecutor Scroller = (JavascriptExecutor) driver;
        Scroller.executeScript("scroll(0, 250);");


    }

    public void scrollDown(int value) {

        JavascriptExecutor scroller = (JavascriptExecutor) driver;
        scroller.executeScript("scroll(0, 700);");


    }

    public void scrollPageEnd() {
        JavascriptExecutor scroller = (JavascriptExecutor) driver;
        scroller.executeScript("window.scrollTo(0, document.body.scrollHeight)");
    }

    public void scrolldownElement(WebElement Element) {
        JavascriptExecutor js = (JavascriptExecutor) driver;
        js.executeScript("arguments[0].scrollIntoView();", Element);

    }

    // Wait
    public void waitForElementPresent(int Seconds) {
        int miliseconds;
        try {
            miliseconds = Seconds * 1000;
            Thread.sleep(miliseconds);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public String mouseHoverGetText(WebElement element) {
        Actions action = new Actions(driver);
        return action.moveToElement(element).toString();
    }

    public void mouseHoverClick(WebElement element) {
        Actions action = new Actions(driver);
        action.moveToElement(element).click().build().perform();
    }

    public void mouseHoverClick(List<WebElement> element) {
        Actions action = new Actions(driver);
        action.moveToElement(element.get(0)).click().build().perform();
    }

    public void mouseHoverClick(List<WebElement> element, int elementNumber) {
        for (int productCount = 1; productCount < element.size(); productCount++) {
            if (elementNumber == productCount) {
                Actions action = new Actions(driver);
                action.moveToElement(element.get(productCount - 1)).click().build().perform();
            }
        }
    }

    public void mouseHover(WebElement element) {
        Actions action = new Actions(driver);
        action.moveToElement(element).build().perform();
    }

    public void mouseHover(List<WebElement> element, int elementNumber) {
        for (int productCount = 1; productCount < element.size(); productCount++) {
            if (elementNumber == productCount) {
                Actions action = new Actions(driver);
                action.moveToElement(element.get(productCount - 1)).build().perform();
            }
        }
    }

    public void mouseHover2(WebElement element, WebElement element1) {
        Actions action = new Actions(driver);
        action.moveToElement(element).moveToElement(element1).build().perform();
    }


    public void mouseHover_3(WebElement element, WebElement element1, WebElement element2) {
        Actions action = new Actions(driver);
        action.moveToElement(element).moveToElement(element1).moveToElement(element2).build().perform();
    }


    public void mouseHoverClick(WebElement element, WebElement element1) {
        Actions action = new Actions(driver);
        action.moveToElement(element).moveToElement(element1)
                .click().
                build().perform();
    }

    public void mouseHover3(WebElement element1, WebElement element2, WebElement element3) {
        Actions action = new Actions(driver);
        action.moveToElement(element1)
                .moveToElement(element2)
                .moveToElement(element3)
                .click()
                .build().perform();
    }

    public void mouseHover3_GetText(WebElement element1, WebElement element2, WebElement element3) {
        Actions action = new Actions(driver);
        action.moveToElement(element1)
                .moveToElement(element2)
                .moveToElement(element3)

                .build().perform();


    }

    public void slider(WebElement slider, int xOffset, int yOffset) {
        Actions act = new Actions(driver);
        act.dragAndDropBy(slider, xOffset, xOffset).perform();
    }

    public void elementMouseClick(WebElement element) {
        Actions action = new Actions(driver);
        action.moveToElement(element).click().build().perform();
    }

    public void moveToElementAndSendKeys(WebElement element, String send) {
        Actions actions = new Actions(driver);
        actions.moveToElement(element);
        actions.sendKeys(send);
        actions.build().perform();
    }

    public void moveToElementAndClickAndSendKeys(WebElement element, String send) {
        Actions actions = new Actions(driver);
        actions.moveToElement(element);
        actions.click();
        actions.sendKeys(send);
        actions.build().perform();
    }


//
//	public void hideAndroidKeyBoard() {
//		AndroidDriver<?> androidDriver = (AndroidDriver<?>) driver;
//		androidDriver.hideKeyboard();
//	}

    public void scroll(Object x, Object y) {
        JavascriptExecutor Scroller = (JavascriptExecutor) driver;
        String scr = "scroll(" + x + "," + y + ")";
        Scroller.executeScript(scr);

    }

    public void new_Chrome_Tab() throws Exception {

        /*
         * WebElement body = driver.findElement(By.cssSelector("body")); String
         * newTabAction = Keys.chord(Keys.COMMAND, "t"); body.sendKeys(newTabAction);
         *
         * String chooseTab = Keys.chord(Keys.COMMAND, "2"); body.sendKeys(chooseTab);
         */

        // WebDriver driver = new ChromeDriver();

        driver.findElement(By.cssSelector("body")).sendKeys(Keys.CONTROL + "t");
        ArrayList<String> tabs = new ArrayList<String>(driver.getWindowHandles());
        driver.switchTo().window(tabs.get(0)); // switches to new tab
        Thread.sleep(2);
        TestHarness accessTestMethod = new TestHarness();
        accessTestMethod.openUrl();

    }

    public void waitForElement(WebElement element, long withTimeoutDuration) throws InterruptedException {
        while (withTimeoutDuration > 0) {
            try {
                if (element.isDisplayed())
                    break;
                else
                    continue;
            } catch (Exception e) {
                Thread.sleep(1000);
                withTimeoutDuration--;
            }
        }

    }

    public void waitThread(long wait) {
        try {
            Thread.sleep(wait);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    public void navigateBack() {
        driver.navigate().back();
        waitForElementPresent(4);
        waitForPageToLoad(25);
    }

    public String extractIntegersInString(String str) {
        // Replacing every non-digit number
        // with a space(" ")
        str = str.replaceAll("[^\\d]", " ");

        // Remove extra spaces from the beginning
        // and the ending of the string
        str = str.trim();

        // Replace all the consecutive white
        // spaces with a single space
        return str = str.replaceAll(" +", " ");


    }


    public void scrollToParticularElement(WebElement element) {
        Actions a = new Actions(driver);
        a.moveToElement(element);
        a.perform();
    }

    public void scrollToElementBasedOnLocation(WebElement element) {
        //get position
        int x = element.getLocation().getX();
        int y = element.getLocation().getY();

        //scroll to x y
        JavascriptExecutor js = (JavascriptExecutor) driver;
        js.executeScript("window.scrollBy(" + x + ", " + y + ")");
    }

    public void waitForLoadJavaScripts(WebDriver driver, int time) {
        new WebDriverWait(driver, Duration.ofSeconds(time)).until((ExpectedCondition<Boolean>) wd ->
                ((JavascriptExecutor) wd).executeScript("return document.readyState").equals("complete"));
    }

    public void scrollToViewAndClick(WebElement element){
        ((JavascriptExecutor) driver).executeScript("arguments[0].scrollIntoView(true);", element);
        element.click();

    }



    public void waitForDomToLoad(int time) {
        waitForLoadJavaScripts(driver, time);
    }


    public void navigateToParticularPage(String url) {
        //implicitWait(45);
        //waitForElementPresent(20);
        waitForPageToLoad(40);
        driver.navigate().to(url);
        //driver.get(url);
        waitForPageToLoad(40);
        //waitForElementPresent(20);
        //implicitWait(50);
    }

    public String randomGenerateEmail() {
        String email;
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("ddMMyyyyHHmmss");
        String date = simpleDateFormat.format(new Date());
        email = "blc" + date + "@mailinator.com";
        System.out.println("Random Generated Email id with time stamp : " + email);
        return email;

    }

    public String generatePhoneNumberBasedOnCountry() {

        String phone = BuildConfigBean.getFirstDigit();
        String lengthOfPhone = BuildConfigBean.getPhoneLength();
        String dialCode = BuildConfigBean.getDialCode();
        System.out.println("****phone length = " + lengthOfPhone);
        int totalLength = Integer.parseInt(lengthOfPhone) - 4;
        System.out.println(" *******total length = " + totalLength);
        Random rand = new Random();
        int randomPhoneGeneration = 10000000 + rand.nextInt(20000000);
        System.out.println(" *******random string = " + randomPhoneGeneration);
        String phoneNumber = phone + randomPhoneGeneration;
        System.out.println("*******phone number = " + phoneNumber);
        return phoneNumber;
    }

    public void mobileNumberDetails(String country) throws IOException, ParseException {
        //return configproperties = loadProperties(System.getProperty("user.dir") + "/Resources/" + configFileName);

        System.out.println("======================GET MOBILE PHONE DETAILS" + country);
        String jsonFile = System.getProperty("user.dir") + "/src/test/java/com/landmarkshops/Web_Mweb/utilities/PhoneNumber.json";
        System.out.println("======================JSON File" + jsonFile);
        Object obj = new JSONParser().parse(new FileReader(jsonFile));
        JSONObject jsonObject = (JSONObject) obj;
        JSONObject childObject = (JSONObject) jsonObject.get(country);
        String firstDigit = (String) childObject.get("firstDigit");
        String phoneLength = (String) childObject.get("PhoneLength");
        String dialCode = (String) childObject.get("DialCode");
        BuildConfigBean.setFirstDigit(firstDigit);
        BuildConfigBean.setPhoneLength(phoneLength);
        BuildConfigBean.setDialCode(dialCode);
        System.out.println("firstDigit --" + firstDigit);
        System.out.println("phoneLength --" + phoneLength);
        System.out.println("dialCode  --" + dialCode);
    }

    public boolean VerifyElementsList(List<WebElement> elementList, String name) {
        boolean productNamePresent = false;
        for (WebElement element : elementList) {
            String elementName = element.getText().trim();
            elementName.replace("\n", "");
            if (elementName.equalsIgnoreCase(name)) {
                testLogger.info("Element name " + name + " is present");
                productNamePresent = true;
                break;
            }
        }
        if (!productNamePresent) {
            testLogger.error("Element name " + name + " is not displayed");
        }
        return productNamePresent;
    }

    public boolean clickElementsList(List<WebElement> elementList, String name) {
        boolean productNamePresent = false;
        for (WebElement element : elementList) {
            String elementName = element.getText().trim();
            elementName.replace("\n", "");
            if (elementName.equals(name)) {
                //waitThread(5000);
                //scrolldown();
               // scrolldownElement(element);
                element.click();
               // elementMouseClick(element);
                waitThread(5000);
                testLogger.info("Product " + name + " is clicked");
                productNamePresent = true;
                break;
            }
        }
        if (!productNamePresent) {
            testLogger.error("Product name " + name + " is not available");
        }
        return productNamePresent;
    }

    public boolean clickElementsListWithOutScrollDown(List<WebElement> elementList, String name) {
        boolean productNamePresent = false;
        for (WebElement element : elementList) {
            String elementName = element.getText().trim();
            elementName.replace("\n", "");
            if (elementName.equals(name)) {
                //waitThread(5000);
                //scrolldown();
                element.click();
                // elementMouseClick(element);
                waitThread(5000);
                testLogger.info("Product " + name + " is clicked");
                productNamePresent = true;
                break;
            }
        }
        if (!productNamePresent) {
            testLogger.error("Product name " + name + " is not available");
        }
        return productNamePresent;
    }

    public boolean clickElementTillDisplayedText(WebElement clickElement, WebElement labelElement, String expectedValue) {
        boolean productNamePresent = false;
        for(int i=0; i<10; i++){
            if (!labelElement.getText().trim().equalsIgnoreCase(expectedValue)){
                clickElement.click();

            }else{
                productNamePresent = true;
                testLogger.info("Product " + expectedValue + " is clicked");
                break;
            }
        }
        return productNamePresent;
    }
    public boolean clickElementsList(List<WebElement> elementList, String name, WebElement element) {

//        CardDataBean.getCardNumber().substring(Math.max(0, CardDataBean.getCardNumber().length() - 4);
        //driver.findElement(By.xpath("//*[@id='shippingForm']/div/div[1]/form/div[6]/div/div/div[3]/div[2]/div[2]/div[*]/div/div/div[1]")).getText().substring(Math.max(0, CardDataBean.getCardNumber().length() - 2);
        boolean productNamePresent1 = false;
        for (WebElement elementItem : elementList) {
            String elementName = elementItem.getText().substring(Math.max(0, CardDataBean.getCardNumber().length() - 2)).trim();
            elementName.replace("\n", "");
            if (elementName.equals(name)) {
                //waitThread(5000);
                //scrolldown();
                //scrolldownElement(elementItem);
                scrollToParticularElement(elementItem);
//                elementItem.click();
//                mouseHoverClick(elementItem);
//                mouseHoverClick(elementItem);
//                clickElement(elementItem,2);
//                elementMouseClick(elementItem);
                waitForElementPresent(2);
                javaScriptExecute(elementItem);
                waitForElementPresent(2);
                //waitThread(5000);
                testLogger.info("Product " + name + " is clicked");
                productNamePresent1 = true;
                break;
            }
        }
        if (!productNamePresent1) {
            testLogger.error("Product name " + name + " is not available");
        }
        return productNamePresent1;
    }

    public boolean clickElementsListContains(List<WebElement> elementList, String name) {
        boolean productNamePresent = false;
        for (WebElement element : elementList) {
            String elementName = element.getText().trim();
            elementName.replace("\n", "");
            if (elementName.contains(name)) {
                element.click();
                waitThread(3000);
                testLogger.info("Product " + name + " is clicked");
                productNamePresent = true;
                break;
            }
        }
        if (!productNamePresent) {
            testLogger.error("Product name " + name + " is not available");
        }
        return productNamePresent;
    }

    public boolean clickElementsListByAttributeValue(List<WebElement> elementList, String name) {
        boolean productNamePresent = false;
        for (WebElement element : elementList) {
            String elementName = element.getAttribute("name");
            elementName.replace("\n", "");
            if (elementName.equalsIgnoreCase(name)) {
                element.click();
                waitForElementPresent(3);
                testLogger.info("Product " + name + " is clicked");
                productNamePresent = true;
                break;
            }
        }
        if (!productNamePresent) {
            testLogger.error("Product name " + name + " is not available");
        }
        return productNamePresent;
    }

    public boolean clickElementsList(List<WebElement> elementList, WebElement elementText, String name) {
        boolean productNamePresent = false;
        for (WebElement element : elementList) {
            String elementName = element.getText().trim();
            elementName.replace("\n", "");
            if (elementName.equals(name)) {
                element.click();
                testLogger.info("Product " + name + " is clicked");
                productNamePresent = true;
                break;
            }
        }
        if (!productNamePresent) {
            testLogger.error("Product name " + name + " is not available");
        }
        return productNamePresent;
    }

    public void enterOTP(List<WebElement> elementList, String otp) {
        int count = 0;
        String[] otpInput = otp.trim().split("");
        for (WebElement element : elementList) {
            element.sendKeys(Keys.BACK_SPACE);
            element.sendKeys(otpInput[count]);
            count++;
        }
    }


    public ArrayList<String> findElementsList(List<WebElement> element) {
        ArrayList<String> arrlist = new ArrayList<String>();
        for (WebElement ele : element) {
            arrlist.add(ele.getText().trim());
        }
        testLogger.info("Items present : " + arrlist);
        return arrlist;
    }



    public boolean isElementPresent(WebElement element) {
        try {
            element.getText().isEmpty();
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    public boolean isElementDisplayed(WebElement element) {
        try {
            element.click();
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    public boolean isElementPresentCheckUsingJavaScriptExecutor(WebElement element) {
        JavascriptExecutor jse = (JavascriptExecutor) driver;
        try {

            Object obj = jse.executeScript("return typeof(arguments[0]) != 'undefined' && arguments[0] != null;", element);
            if (obj.toString().contains("true")) {
                System.out.println("isElementPresentCheckUsingJavaScriptExecutor: SUCCESS");
                return true;
            } else {
                System.out.println("isElementPresentCheckUsingJavaScriptExecutor: FAIL");
            }

        } catch (NoSuchElementException e) {
            System.out.println("isElementPresentCheckUsingJavaScriptExecutor: FAIL");
        }
        return false;
    }

    public void clearBrowserCache() {
        implicitWait(60);
        driver.manage().deleteAllCookies();
        implicitWait(60);

    }

    public static String generateRandomString(int length) {
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijk"
                + "lmnopqrstuvwxyz";
        Random rnd = new Random();
        StringBuilder sb = new StringBuilder(length);
        for (int i = 0; i < length; i++)
            sb.append(chars.charAt(rnd.nextInt(chars.length())));
        return sb.toString();
    }

    // Generates a random int with n digits
    public static int generateRandomDigits(int n) {
        int m = (int) Math.pow(10, n - 1);
        return m + new Random().nextInt(9 * m);
    }

    public int getRandomNumber(int min, int max) {
        return (int) ((Math.random() * (max - min)) + min);
    }

    public void switchToIFrame(WebDriver driver, String value) {
        driver.switchTo().frame(value);
    }
    public void switchToIFrameAndSendKey(WebElement element, String message) {
        WebElement input_element = (WebElement) driver.switchTo().frame(element);
        input_element.sendKeys(message);
    }

    public void switchToIFrame(WebElement element) {
        driver.switchTo().frame(element);
    }

    public void switchFromIFrame() {
        driver.switchTo().defaultContent();
    }

    public static void newWindowTabClose(WebDriver driver) {

        // It will return the parent window name as a String
        String parent = driver.getWindowHandle();
        Set<String> s = driver.getWindowHandles();
        // Now iterate using Iterator
        Iterator<String> I1 = s.iterator();
        while (I1.hasNext()) {
            String child_window = I1.next();
            if (!parent.equals(child_window)) {
                driver.switchTo().window(child_window);

                System.out.println(driver.switchTo().window(child_window).getTitle());

                driver.close();
            }

        }
        //switch to the parent window
        driver.switchTo().window(parent);


    }

    void closeAllTabsExcept(String windowHandle) {
        for (String handle : driver.getWindowHandles()) {
            if (!handle.equals(windowHandle)) {
                driver.switchTo().window(handle);
                driver.close();
            }
        }
        driver.switchTo().window(windowHandle);
    }

    public String switchToTab(String destinationWindowHandle) {
        String currentWindowHandle = driver.getWindowHandle();
        driver.switchTo().window(destinationWindowHandle);
        return currentWindowHandle;
    }

    String openLinkAndSwitchToNewTab(By link) {
        String windowHandle = driver.getWindowHandle();
        Set<String> windowHandlesBefore = driver.getWindowHandles();

        driver.findElement(link).click();
        Set<String> windowHandlesAfter = driver.getWindowHandles();
        windowHandlesAfter.removeAll(windowHandlesBefore);

        Optional<String> newWindowHandle = windowHandlesAfter.stream().findFirst();
        newWindowHandle.ifPresent(s -> driver.switchTo().window(s));

        return windowHandle;
    }

    public static void firstTabClose(WebDriver driver) {

        // It will return the parent window name as a String
        String parent = driver.getWindowHandle();
        System.out.println("main window name" + parent);
        Set<String> s = driver.getWindowHandles();
        // Now iterate using Iterator
        Iterator<String> I1 = s.iterator();
        while (I1.hasNext()) {
            String child_window = I1.next();
            System.out.println("window name" + child_window);
            if (driver.switchTo().window(child_window).getTitle().equals("Mailinator")) {
                driver.switchTo().window(child_window).close();
            } else if (driver.switchTo().window(child_window).getTitle().equals("Reset Password")) {
                driver.switchTo().window(child_window);
            }


        }
        String main = driver.getWindowHandle();
        System.out.println("main window name" + main);

    }

    public static String stringBuilder(WebDriver driver) {
        String curUrl = driver.getCurrentUrl();
        StringBuilder sb = new StringBuilder(curUrl);

        StringBuilder sb1 = sb.delete(8, 30);
        String curUrl1 = sb1.toString();
        return curUrl1;

    }

    public void scrollIntoView(WebElement Element) {
        JavascriptExecutor jse = (JavascriptExecutor) driver;
        try {
            jse.executeScript("arguments[0].scrollIntoView();", Element);
        } catch (NoSuchElementException nse) {
            System.out.println("Unable to scroll to the element : " + Element.toString() + " as element is not found.");
        }
    }


    /**
     * <AUTHOR>
     * This method will select the value in dropdown
     */
    public void selectDropdownValueswithActions(WebElement element, String text) {


        Actions builder = new Actions(driver);
        Action click = builder
                .moveToElement(element)
                .click()
                .sendKeys(text)
                .build();
        click.perform();
    }

    // Capturing Screenshot
    public void captureScreenshot(String Screenshotname) {
        try {
            File srcPath = ((TakesScreenshot) driver).getScreenshotAs(OutputType.FILE);

            FileUtils.copyFile(srcPath,
                    new File(System.getProperty("user.dir") + "/Screenshots/" + Screenshotname + ".jpg"));
        } catch (Exception e) {
            testLogger.error("Exception while taking a screenshot" + Screenshotname + e.getMessage());
        }
    }

    /**
     * @param el
     * @param text
     * <AUTHOR>
     * This method is used to input text in field.
     */
    public void enterText(WebElement el, String text) {
        el.clear();
        el.sendKeys(text);
        testLogger.info("Entered '" + text + "' text");
        el.sendKeys(Keys.TAB);
        waitForElementPresent(3);
    }

    /**
     * @param xpath
     * @param text
     * <AUTHOR>
     * This method is used to input text in field.
     */
    public void enterText(String xpath, String text) {
        driver.findElement(By.xpath(xpath)).clear();
        driver.findElement(By.xpath(xpath)).sendKeys(text);
        testLogger.info("Entered '" + text + "' text");
        driver.findElement(By.xpath(xpath)).sendKeys(Keys.TAB);
        waitForElementPresent(3);
    }

    /**
     * This method is used to select dropdown by index value
     *
     * @param element
     * @param index
     * <AUTHOR>
     */
    public void selectDropdownValuesByIndex(WebElement element, int index) {
        Select select = new Select(element);
        select.selectByIndex(index);
    }

    /**
     * This method returns By type for an WebElement
     *
     * @param
     * @return
     * <AUTHOR>
     */
    public By toByVal(WebElement el) {
        // By format = "[foundFrom] -> locator: term"
        // see RemoteWebElement toString() implementation
        String[] data = el.toString().split(" -> ")[1].replace("]", "").split(": ");
        String locator = data[0];
        String term = data[1];

        switch (locator) {
            case "xpath":
                return By.xpath(term);
            case "css selector":
                return By.cssSelector(term);
            case "id":
                return By.id(term);
            case "tag name":
                return By.tagName(term);
            case "name":
                return By.name(term);
            case "link text":
                return By.linkText(term);
            case "class name":
                return By.className(term);
        }
        return (By) el;
    }

    /**
     * This method is used to wait until the presence of element located by specified locator
     *
     * @param el
     * @param timeInSecs
     * <AUTHOR>
     */
    public void waitForVisibilityOfElementLocated(WebElement el, WebDriver driver, long timeInSecs) {

        new WebDriverWait(driver, Duration.ofSeconds(timeInSecs)).until(ExpectedConditions.visibilityOf(el));

    }


    public void waitForPresenceOfElementLocated(WebElement el, WebDriver driver, long timeInSecs) {

        new WebDriverWait(driver, Duration.ofSeconds(timeInSecs)).until(ExpectedConditions.visibilityOf(el));
//		WebDriverWait wait = new WebDriverWait(driver, timeInSecs);
//		try {
//			wait.until(ExpectedConditions.presenceOfElementLocated(toByVal(el)));
//		} catch (Exception e) {}
    }

    /**
     * This method is used to wait until the presence of element located by specified locator
     *
     * @param xpath
     * @param timeInSecs
     * <AUTHOR>
     */
    public void waitForPresenceOfElementLocated(String xpath, long timeInSecs) {
        WebDriverWait wait = new WebDriverWait(driver, Duration.ofSeconds(timeInSecs));
        try {
            wait.until(ExpectedConditions.presenceOfElementLocated(By.xpath(xpath)));
        } catch (Exception e) {
        }
    }

    /**
     * This method is used to click on selected WebElement
     *
     * @param el
     * @param seconds
     * <AUTHOR>
     */
    public void clickElement(WebElement el, long seconds) throws NoSuchElementException {
        waitForPresenceOfElementLocated(el, driver, seconds);
        //highlightWebElement(el);
        el.click();
        testLogger.info("Clicked on WebElement: " + el.toString());
    }

    /**
     * This method is used to click on selected WebElement
     *
     * @param
     * @param seconds
     * <AUTHOR>
     */
    public void clickElement(String xpath, long seconds) throws NoSuchElementException {
        waitForPresenceOfElementLocated(xpath, seconds);
        //highlightWebElement(el);
        driver.findElement(By.xpath(xpath)).click();
        testLogger.info("Clicked on WebElement: " + xpath);
    }

    /**
     * This method is used to highlight a webelement
     *
     * @param el
     * <AUTHOR>
     */
    public void highlightWebElement(WebElement el) {
        JavascriptExecutor js = (JavascriptExecutor) driver;
        js.executeScript("arguments[0].setAttribute('style', 'background: yellow; border: 2px solid red;');", el);
    }

    /**
     * This method is used to verify the presence of webelement
     *
     * @param el
     * @param seconds
     * @return
     * <AUTHOR>
     */
    public boolean isElementPresent(WebElement el, String elementName, int seconds) {
        waitForPresenceOfElementLocated(el, driver, seconds);
        try {
            if (el.isDisplayed()) {
                testLogger.info(elementName + " is displayed - " + el.toString());
                return true;
            }
        } catch (NoSuchElementException e) {
            testLogger.info(elementName + " is not displayed - " + el.toString());
            return false;

        } catch (Exception e) {
            testLogger.info(elementName + " is not displayed - " + el.toString());
            e.printStackTrace();
            return false;
        }
        return true;
    }

    /**
     * This method is used to verify text in the webelement
     *
     * @param el
     * @param seconds
     * @param expectedText
     * @return
     * <AUTHOR>
     */
    public boolean verifyText(WebElement el, String elementName, int seconds, String expectedText) {
        try {
            if (isElementPresent(el, elementName, seconds)
                    && (el.getText().trim().equalsIgnoreCase(expectedText) || el.getText().trim().contains(expectedText))) {
                testLogger.info("Text Matched; Actual text: " + el.getText() + "; Expected Text: " + expectedText);
                return true;
            } else {
                testLogger.error("Text NOT Matched; Actual text: " + el.getText() + "; Expected Text: " + expectedText);
                return false;
            }
        } catch (NoSuchElementException e) {
            return false;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * This method returns boolean based on element selection
     *
     * @param el
     * @param elementName
     * @param seconds
     * @return
     */
    public boolean isSelected(WebElement el, String elementName, int seconds) {
        if (isElementPresent(el, elementName, seconds) && (el.isSelected() || el.getAttribute("checked").equals("checked"))) {
            testLogger.info(el.toString() + " is checked.");
            return true;
        } else {
            testLogger.info(el.toString() + " is NOT checked.");
            return false;
        }

    }

    /**
     * This method generates random string based on usage
     *
     * @param count
     * @param useLetters
     * @param useNumbers
     * @return
     */
    public String generateRandomString(int count, boolean useLetters, boolean useNumbers) {
        return RandomStringUtils.random(count, useLetters, useNumbers);
    }

    /**
     * This method clears input from the field using keyboard actions
     *
     * @param el
     */
    public void clearInput(WebElement el) {
        Actions action = new Actions(driver);
        action.keyDown(Keys.CONTROL).sendKeys("A").keyUp(Keys.CONTROL).perform();
        action.keyDown(Keys.DELETE).perform();
    }

    /**
     * This method is used to execute a Java script
     *
     * @param
     */
    public void javaScriptExecute(String xpath) {
        WebElement we = driver.findElement(By.xpath(xpath));
        final JavascriptExecutor js = (JavascriptExecutor) this.driver;
        js.executeScript("arguments[0].click();", we);
    }

    public void javaScriptExecuteClick(WebElement element) {
        //WebElement we = driver.findElement(By.xpath(xpath));
        final JavascriptExecutor js = (JavascriptExecutor) this.driver;
        js.executeScript("arguments[0].click();", element);
    }

    /**
     * This method is used to read the Payment provider value from console
     *
     * <AUTHOR>
     */
    public String getPaymentProvider() {
        String paymentProvider = "";
        JavascriptExecutor jse = (JavascriptExecutor) driver;
        paymentProvider = (String) jse.executeScript("return LMS.pageData.paymentProvider");
        testLogger.info("Payment Provider: " + paymentProvider);
        return paymentProvider;
    }

    public void selectDropdownValues(WebElement element, String text) {
        Select select = new Select(element);
        select.selectByVisibleText(text);

    }



    public String removeText(String text) {
        String s = text.replaceAll("[^0-9.]", "");
        return s;
    }

    public void enterPromoCode(WebElement element, String name) {
        element.sendKeys(name);
    }

    /**
     * By using this method, read the json data w.r.t environment, country, concept localization path
     * Ex: uat5.egypt.max.en/ar
     */
    public static JSONObject loadJsonTestDataWithRelativePath(String filePath) {
        JSONObject languageObject = null;
        try {

            String jsonFileReader = System.getProperty("user.dir") + filePath;
            Object obj = new JSONParser().parse(new FileReader(jsonFileReader));
            JSONObject jsonObject = (JSONObject) obj;
            JSONObject envObject = (JSONObject) jsonObject.get(config.getEnvironment().trim().toLowerCase());
            JSONObject countryObject = (JSONObject) envObject.get(config.getCountry().trim().toLowerCase());
            JSONObject conceptObject = (JSONObject) countryObject.get(config.getConcept().trim().toLowerCase());
            languageObject = (JSONObject) conceptObject.get(config.getLanguage().toLowerCase());
        } catch (IOException e) {
            e.printStackTrace();
        } catch (ParseException p) {
            p.printStackTrace();
        }
        return languageObject;

    }

    /**
     * By using this method, read the json data w.r.t environment (GENERIC Env as UAT), country, concept localization path
     * Ex: uat5.egypt.max.en/ar
     */
    public static JSONObject loadJsonGenericTestDataWithRelativePath(String filePath) {
        JSONObject languageObject = null;
        try {
            String jsonFileReader = System.getProperty("user.dir") + filePath;
            Object obj = new JSONParser().parse(new FileReader(jsonFileReader));
            JSONObject jsonObject = (JSONObject) obj;
            JSONObject envObject = (JSONObject) jsonObject.get(config.getEnvironment().trim().toLowerCase().contains("uat") ? "uat" : config.getEnvironment().trim().toLowerCase());
            JSONObject countryObject = (JSONObject) envObject.get(config.getCountry().trim().toLowerCase());
            JSONObject conceptObject = (JSONObject) countryObject.get(config.getConcept().trim().toLowerCase());
            languageObject = (JSONObject) conceptObject.get(config.getLanguage().toLowerCase());
        } catch (IOException e) {
            e.printStackTrace();
        } catch (ParseException p) {
            p.printStackTrace();
        }
        return languageObject;

    }

    /**
     * By using this method, read the json data w.r.t visa/master/knet card data from the json
     * Ex: uat5.egypt.max.en/ar.visa/master/knet
     */

    public static void CreditDebitCardDetails(String cardType) {
        System.out.println("Card Type:" + cardType);
        String filepath = "/src/test/java/com/landmarkshops/Web_Mweb/utilities/creditCardTestData.json";
        JSONObject languageObject = loadJsonGenericTestDataWithRelativePath(filepath);
        JSONObject cardObject = (JSONObject) languageObject.get(cardType);
        String cardNumber = (String) cardObject.get("cardNumber");
        String expMonth = (String) cardObject.get("expMonth");
        String expYear = (String) cardObject.get("expYear");
        String cvv = (String) cardObject.get("cvv");
        String nameOnCard = (String) cardObject.get("nameOnCard");
        String otp = (String) cardObject.get("OTP");
        CardDataBean.setCardNumber(cardNumber);
        CardDataBean.setExpiryMonth(expMonth);
        CardDataBean.setExpiryYear(expYear);
        CardDataBean.setCvvNumber(cvv);
        CardDataBean.setNameOnCard(nameOnCard);
        CardDataBean.setOtp(otp);
        System.out.println("Card 16 Digit Number --" + cardNumber);
        System.out.println("Card Expire Month --" + expMonth);
        System.out.println("Card Expire Year --" + expYear);
        System.out.println("CVV Number  --" + cvv);
        System.out.println("Name On Card  --" + nameOnCard);
    }

    /**
     * By using this method, read the json data w.r.t email/facebook/facebookLogin/Invalid Credentials data from the json
     * Ex: uat5.egypt.max.en/ar.email/facebook/facebookLogin/Invalid Credentials
     */
    public static void signInSignUpFacebookLogin(String loginType, String email, String password) {
        String filepath = "/src/test/java/com/landmarkshops/Web_Mweb/utilities/LoginCredentials.json";
        JSONObject languageObject = loadJsonTestDataWithRelativePath(filepath);
        JSONObject login = (JSONObject) languageObject.get(loginType);
        String email1 = (String) login.get(email);
        System.out.println("emailid is" + email1);
        String password1 = (String) login.get(password);
        LoginDataBean.setEmail(email1);
        LoginDataBean.setPassword(password1);
    }

    /**
     * By using this method, read the json data w.r.t Promo Code data from the json
     * Ex: uat5.egypt.max.en/ar.Promo Code
     */
    public static void promoCode(String promoCodeText, String TestPromoCode) {
        String filepath = "/src/test/java/com/landmarkshops/Web_Mweb/utilities/PromoCode.json";
        JSONObject languageObject = loadJsonTestDataWithRelativePath(filepath);
        JSONObject promoObject = (JSONObject) languageObject.get(promoCodeText);
        String promoCode1 = (String) promoObject.get(TestPromoCode);
        CardDataBean.setPromoCode(promoCode1);
    }

    /**
     * By using this method, read the json data w.r.t Shukran test data from the json
     * Ex: uat5.egypt.max.shukranData
     */

    public static void shukranTestData(String shukran) {
        String filepath = "/src/test/java/com/landmarkshops/Web_Mweb/utilities/ShukranTestData.json";
        JSONObject languageObject = loadJsonTestDataWithRelativePath(filepath);
        JSONObject shukranData = (JSONObject) languageObject.get(shukran);
        String countryCode = (String) shukranData.get("countryCode");
        String mobileNumber = (String) shukranData.get("phoneNumber");
        String shukranCard = (String) shukranData.get("cardNumber");
        ShukranTestDataBean.setCountryData(countryCode);
        ShukranTestDataBean.setPhoneNumber(mobileNumber);
        ShukranTestDataBean.setCardNumber(shukranCard);

    }

    /**
     * By using this method, read the json data w.r.t Address data from the json
     * Ex: uat5.egypt.max.en/ar.Address
     */
    public static void address(String Address) {
        String filepath = "/src/test/java/com/landmarkshops/Web_Mweb/utilities/AddressData.json";
        JSONObject languageObject = loadJsonGenericTestDataWithRelativePath(filepath);
        JSONObject addressObject = (JSONObject) languageObject.get(Address);
        String countryCode = (String) addressObject.get("CountryCode");
        String mobileNum = (String) addressObject.get("MobileNumber");
        String city = (String) addressObject.get("City");
        String area = (String) addressObject.get("Area");
        String floorNo = (String) addressObject.get("FloorNo");
        String streetNo = (String) addressObject.get("StreetNo");
        String landmark = (String) addressObject.get("Landmark");
        String firstName = (String) addressObject.get("FirstName");
        AddressDataBean.setCity(city);
        AddressDataBean.setArea(area);
        AddressDataBean.setFloorNo(floorNo);
        AddressDataBean.setStreetNo(streetNo);
        AddressDataBean.setLandmark(landmark);
        AddressDataBean.setFirstName(firstName);
        AddressDataBean.setCountryCode(countryCode);
        AddressDataBean.setMobileNumber(mobileNum);
    }


    public static void storeDetails(String store) {
        String filepath = "/src/test/java/com/landmarkshops/Web_Mweb/utilities/storeDetails.json";
        JSONObject languageObject = loadJsonGenericTestDataWithRelativePath(filepath);
        JSONObject storeDetailsObject = (JSONObject) languageObject.get(store);
        String storeName = (String) storeDetailsObject.get("storeName");
        String storeAddress = (String) storeDetailsObject.get("storeAddress");
        String storeTime = (String) storeDetailsObject.get("storeTime");
        String storeKeySearch = (String) storeDetailsObject.get("storeKeySearch");
        StoreDetailsBean.setStoreName(storeName);
        StoreDetailsBean.setStoreAddress(storeAddress);
        StoreDetailsBean.setStoreTime(storeTime);
        StoreDetailsBean.setStoreKeySearch(storeKeySearch);
    }

    /**
     * By using this method, read the json data w.r.t Product URLS data from the json
     * Ex: uat5.egypt.max.en/ar.PDP
     */
    public static void productsURL(String products) {
        String filepath = "/src/test/java/com/landmarkshops/Web_Mweb/utilities/ProductURLs.json";
        JSONObject languageObject = loadJsonGenericTestDataWithRelativePath(filepath);
        JSONObject productObject = (JSONObject) languageObject.get(products);
        String URL = BaseURLBean.getUrl() + productObject.get("URL");
        ProductURLsDataBean.setURL(URL);
        ProductURLsDataBean.setColor((String) productObject.get("color"));
        ProductURLsDataBean.setSize((String) productObject.get("size"));
        ProductURLsDataBean.setPrice((String) productObject.get("price"));
        ProductURLsDataBean.setSku((String) productObject.get("sku"));
        ProductURLsDataBean.setProductId((String) productObject.get("productId"));
        ProductURLsDataBean.setVariantId((String) productObject.get("variantId"));
    }

    public static void baseURL() {
        String filepath = "/src/test/java/com/landmarkshops/Web_Mweb/utilities/BaseURL.json";
        JSONObject languageObject = loadJsonTestDataWithRelativePath(filepath);
        //JSONObject languageObject=loadJsonGenericTestDataWithRelativePath(filepath);
        BaseURLBean.setBaseURL((String) languageObject.get("URL"));
    }

    public static void userData(String user) {
        String filepath = "/src/test/java/com/landmarkshops/Web_Mweb/utilities/UserData.json";
        JSONObject languageObject = loadJsonGenericTestDataWithRelativePath(filepath);
        JSONObject userObject = (JSONObject) languageObject.get(user);
        UserDataBean.setEmail((String) userObject.get("email"));
        UserDataBean.setPassword((String) userObject.get("password"));
        UserDataBean.setShukranCardNumber((String) userObject.get("shukranCardNumber"));
        UserDataBean.setShukranBalance((String) userObject.get("shukranBalance"));
        UserDataBean.setMyCreditAccountNumber((String) userObject.get("myCreditAccountNumber"));
        UserDataBean.setMyCreditBalance((String) userObject.get("myCreditBalance"));
        UserDataBean.setFirstname((String) userObject.get("firstname"));
        UserDataBean.setLastName((String) userObject.get("lastName"));
        UserDataBean.setMobileNo((String) userObject.get("mobileNo"));
        UserDataBean.setGender((String) userObject.get("gender"));
        UserDataBean.setDob((String) userObject.get("dob"));
    }

    public static boolean verifyNoelement(WebElement element) {
        try {
            if (!element.isDisplayed()) {
                return false;
            } else {
                return true;
            }
        } catch (Exception e) {
            return false;
        }
    }

    public static boolean isClickable(WebDriver driver, WebElement element) {
        try {
            new WebDriverWait(driver, Duration.ofSeconds(5)).until(ExpectedConditions.elementToBeClickable(element));
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    public static void verifyLinks(String linkUrl) {
        try {

            URL url = new URL(linkUrl);
            //Now we will be creating url connection and getting the response code
            HttpURLConnection httpURLConnect = (HttpURLConnection) url.openConnection();
            httpURLConnect.setConnectTimeout(5000);
            httpURLConnect.connect();
            if (httpURLConnect.getResponseCode() >= 400) {
                System.out.println(linkUrl + " - " + httpURLConnect.getResponseMessage() + "is a broken link");
            }

            //Fetching and Printing the response code obtained
            else {
                System.out.println(linkUrl + " - " + httpURLConnect.getResponseMessage());
            }
        } catch (Exception e) {
        }
    }

    public String returnOnlyNumbersFromString(String input) {
        Pattern pattern = Pattern.compile("\\d+");
        Matcher matcher = pattern.matcher(input);
        if (matcher.find()) {
            String output = matcher.group();
            System.out.println(output);
        } else {
            System.out.println("No numerical part found in the input string.");
        }
        return input;
    }

    public JSONArray setListData(List<WebElement> elementList) {

        JSONArray outputArray = new JSONArray();
        int i = 0;
        for (WebElement ele : elementList) {
            outputArray.put(ele.getText());
            i++;
        }
        return outputArray;
    }

    static JSONObject giftCard = new JSONObject();

    public void updateGiftCardDetails(String giftCardNumber, String pin, String actualGiftCardAmount) {
        JSONObject addGiftCard = new JSONObject();
        addGiftCard.put("giftCardNumber", giftCardNumber);
        addGiftCard.put("pin", pin);
        addGiftCard.put("actualGiftCardAmount", actualGiftCardAmount);
        addGiftCard.put("remainingGiftCardAmount", actualGiftCardAmount);
        giftCard.put("gidtCard" + (giftCard.size() + 1), addGiftCard);
        getLastGiftCardNumber();
        System.out.println("wait");
    }

    public String getLastGiftCardNumber() {
        JSONObject giftCredNumber = (JSONObject) giftCard.get("gidtCard" + giftCard.size());
        return giftCredNumber.get("giftCardNumber").toString();
    }

    public String getLastGiftCardNumberPin() {
        JSONObject giftCredPin = (JSONObject) giftCard.get("gidtCard" + giftCard.size());
        return giftCredPin.get("pin").toString();
    }

//    public static void updateGiftCardBalanceAmount() {
//        JSONObject giftCard = new JSONObject();
//        giftCard.put("balanceGiftCardAmount", "123");
//        giftCard.put("gidtCard" + (giftCard.size()), giftCard);
//    }

//
//    public String[][] setTestCaseDataCart(List<WebElement> elementList, String name) {
//
//        String[][] productData;
//        for (WebElement element : elementList) {
//
//        }
//
//        String[][] productsData = {
//                {"1", "2", "3", "4", "5", "6", "7"},
//                {"8", "9", "10", "11", "12", "13", "14"}
//        };
//
//
//        System.out.println();
////        Map<String, JSONObject> productsMap = new HashMap<>();
////
////        for (int i = 0; i < numProducts; i++) {
////            System.out.println("Enter details for product " + (i + 1) + ":");
////            String productName = getStringInput("Product Name", scanner);
////            JSONObject product = new JSONObject();
////            product.put("size", getStringInput("Size", scanner));
////            product.put("color", getStringInput("Color", scanner));
////            product.put("price", getStringInput("Price", scanner));
////            product.put("quantity", getStringInput("Quantity", scanner));
////            product.put("actualPrice", getStringInput("Actual Price", scanner));
////            product.put("discountPrice", getStringInput("Discount Price", scanner));
////            productsMap.put(productName, product);
////        }
//
//    }
}

