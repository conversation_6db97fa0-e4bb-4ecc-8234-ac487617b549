{"uat": {"egypt": {"max": {"en": {"Address": {"City": "Al <PERSON>", "Area": "Atsa", "FloorNo": "Test 123", "StreetNo": "300523432", "Landmark": "Test 123", "FirstName": "WebAutomation Test", "CountryCode": "+20", "MobileNumber": "1000000000"}, "contact": {"FirstName": "WebAutomation Test", "MobileNumber": "1000000000"}, "Address1": {"City": "<PERSON><PERSON>", "Area": "Draw", "FloorNo": "Test 456", "StreetNo": "200123456", "Landmark": "Test 456", "FirstName": "WebAutomation TestTwo", "CountryCode": "+20", "MobileNumber": "1000000000"}, "contact1": {"FirstName": "WebAutomation TestTwo", "MobileNumber": "1000000000"}}, "ar": {"Address": {"City": "اسيوط", "Area": "ابو تيج", "FloorNo": "اختبار 123", "StreetNo": "300523432", "Landmark": "اختبار 123", "FirstName": "أتمتة الويب", "CountryCode": "+20", "MobileNumber": "1000000000"}, "contact": {"FirstName": "أتمتة الويب", "MobileNumber": "1000000000"}, "Address1": {"City": "اسيوط", "Area": "ابو تيج", "FloorNo": "اختبار 456", "StreetNo": "200123456", "Landmark": "اختبار 456", "FirstName": "أتمتة الويب", "CountryCode": "+20", "MobileNumber": "1000000000"}, "contact1": {"FirstName": "أتمتة الويب", "MobileNumber": "1000000000"}}}, "hc": {"en": {"Address": {"City": "Al <PERSON>", "Area": "Atsa", "FloorNo": "Test 123", "StreetNo": "300523432", "Landmark": "Test 123", "FirstName": "WebAutomation Test", "CountryCode": "+20", "MobileNumber": "1000000000"}, "Address1": {"City": "<PERSON><PERSON>", "Area": "Draw", "FloorNo": "Test 456", "StreetNo": "200123456", "Landmark": "Test 456", "FirstName": "WebAutomation TestTwo", "CountryCode": "+20", "MobileNumber": "1000000000"}, "contact1": {"FirstName": "WebAutomation TestTwo", "MobileNumber": "1000000000"}}, "ar": {"Address": {"City": "اسيوط", "Area": "ابو تيج", "FloorNo": "اختبار 123", "StreetNo": "300523432", "Landmark": "اختبار 123", "FirstName": "أتمتة الويب", "CountryCode": "+20", "MobileNumber": "1000000000"}, "contact": {"FirstName": "أتمتة الويب", "MobileNumber": "1000000000"}, "Address1": {"City": "اسيوط", "Area": "ابو تيج", "FloorNo": "اختبار 456", "StreetNo": "200123456", "Landmark": "اختبار 456", "FirstName": "أتمتة الويب", "CountryCode": "+20", "MobileNumber": "1000000000"}, "contact1": {"FirstName": "أتمتة الويب", "MobileNumber": "1000000000"}}}}, "oman": {"max": {"en": {"UserDetails1": {"City": "Al Buraimi", "Area": "MAHDHA", "FloorNo": "Test 15534", "StreetNo": "300523432", "Landmark": "Test 123", "FirstName": "WebAutomated TestOne", "CountryCode": "+20", "MobileNumber": "77012345"}, "UserDetails2": {"City": "Al Buraimi", "Area": "MAHDHA", "FloorNo": "Test 15534", "StreetNo": "300523432", "Landmark": "Test 123", "FirstName": "WebAutomated TestTwo", "CountryCode": "+20", "MobileNumber": "77012345"}, "UserDetails3": {"City": "Al Buraimi", "Area": "MAHDHA", "FloorNo": "Test 15534", "StreetNo": "300523432", "Landmark": "Test 123", "FirstName": "WebAutomated TestThree", "CountryCode": "+20", "MobileNumber": "77012345"}, "UserDetails4": {"City": "Al Buraimi", "Area": "MAHDHA", "FloorNo": "Test 15534", "StreetNo": "300523432", "Landmark": "Test 123", "FirstName": "WebAutomated TestFour", "CountryCode": "+20", "MobileNumber": "77012345"}, "UserDetails5": {"City": "Al Buraimi", "Area": "MAHDHA", "FloorNo": "Test 15534", "StreetNo": "300523432", "Landmark": "Test 123", "FirstName": "WebAutomated Testfive", "CountryCode": "+20", "MobileNumber": "77012345"}, "UserDetails6": {"City": "Al Buraimi", "Area": "MAHDHA", "FloorNo": "Test 15534", "StreetNo": "300523432", "Landmark": "Test 123", "FirstName": "WebAutomated Test", "CountryCode": "+20", "MobileNumber": "77012345"}, "UserDetails7": {"City": "Al Buraimi", "Area": "MAHDHA", "FloorNo": "Test 15534", "StreetNo": "300523432", "Landmark": "Test 123", "FirstName": "WebAutomated Test", "CountryCode": "+20", "MobileNumber": "77012345"}, "ar": {"Address": {"City": "اسيوط", "Area": "ابو تيج", "FloorNo": "اختبار 123", "StreetNo": "300523432", "Landmark": "اختبار 123", "FirstName": "أتمتة الويب", "CountryCode": "+20", "MobileNumber": "1000000000"}, "contact": {"FirstName": "أتمتة الويب", "MobileNumber": "1000000000"}, "Address1": {"City": "اسيوط", "Area": "ابو تيج", "FloorNo": "اختبار 456", "StreetNo": "200123456", "Landmark": "اختبار 456", "FirstName": "أتمتة الويب", "CountryCode": "+20", "MobileNumber": "1000000000"}, "contact1": {"FirstName": "أتمتة الويب", "MobileNumber": "1000000000"}}}, "hc": {"en": {"Address": {"City": "Al <PERSON>", "Area": "Atsa", "FloorNo": "Test 123", "StreetNo": "300523432", "Landmark": "Test 123", "FirstName": "WebAutomation Test", "CountryCode": "+20", "MobileNumber": "1000000000"}, "contact": {"FirstName": "WebAutomation Test", "MobileNumber": "1000000000"}, "Address1": {"City": "<PERSON><PERSON>", "Area": "Draw", "FloorNo": "Test 456", "StreetNo": "200123456", "Landmark": "Test 456", "FirstName": "WebAutomation TestTwo", "CountryCode": "+20", "MobileNumber": "1000000000"}, "contact1": {"FirstName": "WebAutomation TestTwo", "MobileNumber": "1000000000"}}, "ar": {"Address": {"City": "اسيوط", "Area": "ابو تيج", "FloorNo": "اختبار 123", "StreetNo": "300523432", "Landmark": "اختبار 123", "FirstName": "أتمتة الويب", "CountryCode": "+20", "MobileNumber": "1000000000"}, "contact": {"FirstName": "أتمتة الويب", "MobileNumber": "1000000000"}, "Address1": {"City": "اسيوط", "Area": "ابو تيج", "FloorNo": "اختبار 456", "StreetNo": "200123456", "Landmark": "اختبار 456", "FirstName": "أتمتة الويب", "CountryCode": "+20", "MobileNumber": "1000000000"}, "contact1": {"FirstName": "أتمتة الويب", "MobileNumber": "1000000000"}}}, "cp": {"en": {"Address": {"City": "Muscat", "Area": "AL HAIL", "FloorNo": "Test 123", "StreetNo": "300523432", "Landmark": "Test 123", "FirstName": "WebAutomation Test", "CountryCode": "+968", "MobileNumber": "77234567"}, "contact": {"FirstName": "WebAutomation Test", "MobileNumber": "77234567"}, "Address1": {"City": "<PERSON><PERSON>", "Area": "Draw", "FloorNo": "Test 456", "StreetNo": "200123456", "Landmark": "Test 456", "FirstName": "WebAutomation TestTwo", "CountryCode": "+20", "MobileNumber": "1000000000"}, "contact1": {"FirstName": "WebAutomation TestTwo", "MobileNumber": "1000000000"}}, "ar": {"Address": {"City": "اسيوط", "Area": "ابو تيج", "FloorNo": "اختبار 123", "StreetNo": "300523432", "Landmark": "اختبار 123", "FirstName": "أتمتة الويب", "CountryCode": "+20", "MobileNumber": "1000000000"}, "contact": {"FirstName": "أتمتة الويب", "MobileNumber": "1000000000"}, "Address1": {"City": "اسيوط", "Area": "ابو تيج", "FloorNo": "اختبار 456", "StreetNo": "200123456", "Landmark": "اختبار 456", "FirstName": "أتمتة الويب", "CountryCode": "+20", "MobileNumber": "1000000000"}, "contact1": {"FirstName": "أتمتة الويب", "MobileNumber": "1000000000"}}}}}, "ptegy": {"egypt": {"max": {"en": {"Address": {"City": "Al <PERSON>", "Area": "Atsa", "FloorNo": "Test 123", "StreetNo": "300523432", "Landmark": "Test 123", "FirstName": "WebAutomation Test", "CountryCode": "+20", "MobileNumber": "1000000000"}, "contact": {"FirstName": "WebAutomation Test", "MobileNumber": "1000000000"}, "Address1": {"City": "<PERSON><PERSON>", "Area": "Draw", "FloorNo": "Test 456", "StreetNo": "200123456", "Landmark": "Test 456", "FirstName": "WebAutomation TestTwo", "CountryCode": "+20", "MobileNumber": "1000000000"}, "contact1": {"FirstName": "WebAutomation TestTwo", "MobileNumber": "1000000000"}}, "ar": {"Address": {"City": "اسيوط", "Area": "ابو تيج", "FloorNo": "اختبار 123", "StreetNo": "300523432", "Landmark": "اختبار 123", "FirstName": "أتمتة الويب", "CountryCode": "+20", "MobileNumber": "1000000000"}, "contact": {"FirstName": "أتمتة الويب", "MobileNumber": "1000000000"}, "Address1": {"City": "اسيوط", "Area": "ابو تيج", "FloorNo": "اختبار 456", "StreetNo": "200123456", "Landmark": "اختبار 456", "FirstName": "أتمتة الويب", "CountryCode": "+20", "MobileNumber": "1000000000"}, "contact1": {"FirstName": "أتمتة الويب", "MobileNumber": "1000000000"}}}}}, "prod": {"egypt": {"max": {"en": {"Address": {"City": "Al <PERSON>", "Area": "Atsa", "FloorNo": "Test 123", "StreetNo": "300523432", "Landmark": "Test 123", "FirstName": "WebAutomation Test", "CountryCode": "+20", "MobileNumber": "1000000000"}, "contact": {"FirstName": "WebAutomation Test", "MobileNumber": "1000000000"}, "Address1": {"City": "Al <PERSON>", "Area": "Atsa", "FloorNo": "Test 123", "StreetNo": "300523432", "Landmark": "Test 123", "FirstName": "WebAutomation Test", "CountryCode": "+20", "MobileNumber": "1000000000"}, "contact1": {"FirstName": "WebAutomation Test", "MobileNumber": "1000000000"}}, "ar": {"Address": {"City": "اسيوط", "Area": "ابو تيج", "FloorNo": "اختبار 123", "StreetNo": "300523432", "Landmark": "اختبار 123", "FirstName": "أتمتة الويب", "CountryCode": "+20", "MobileNumber": "1000000000"}, "contact": {"FirstName": "أتمتة الويب", "MobileNumber": "1000000000"}, "Address1": {"City": "اسيوط", "Area": "ابو تيج", "FloorNo": "اختبار 123", "StreetNo": "300523432", "Landmark": "اختبار 123", "FirstName": "أتمتة الويب", "CountryCode": "+20", "MobileNumber": "1000000000"}, "contact1": {"FirstName": "أتمتة الويب", "MobileNumber": "1000000000"}}}}}}}