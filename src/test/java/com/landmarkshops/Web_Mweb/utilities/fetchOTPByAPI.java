package com.landmarkshops.Web_Mweb.utilities;

import io.restassured.RestAssured;
import io.restassured.response.Response;
import io.restassured.specification.RequestSpecification;
import java.util.HashMap;
import java.util.Map;

public class fetchOTPByAPI {
    static String baseURL = "https://uat5.centrepointstores.com";
    static String clientId = "customerclient";
    static String clientSecret = "customer_secret";
    static String  client_Id="centrepoint_om";

    // Set the base URI for your token endpoint

    public static String fetchOTPBasedOnMobileNumber(String concept,String country,String mobileNumber){
        RestAssured.baseURI = baseURL + "/auth/oauth/token"; // Replace with your actual token endpoint
        // Create a request specification
        RequestSpecification httpRequest = RestAssured.given();
        // Set the headers
        httpRequest.header("Content-Type", "application/x-www-form-urlencoded");
        httpRequest.header("X-Forwarded-For", "192.23.23.33");
        httpRequest.header("Authorization", "Basic " + encode(clientId, clientSecret));
        // Set the form parameters
        httpRequest.formParam("grant_type", "client_credentials");
        httpRequest.formParam("client_id", clientId);
        httpRequest.formParam("client_secret", clientSecret);
        // Make the POST request to obtain the token
        Response response = httpRequest.log().all().post();
        // Validate the response (you can add more assertions as needed)
        response.then().assertThat().statusCode(200); // Assuming a successful response
        // Extract the access token from the response (if needed)
        String accessToken = response.jsonPath().getString("access_token").trim();
        System.out.println("Access Token: " + accessToken);
        String otpResponse = fetchOTPBasedOnMobileNumber(accessToken,mobileNumber);
        System.out.println("Response body for the OTP--------------------->" + otpResponse);
        return otpResponse;
    }
    public static String fetchOTPBasedOnMobileNumber(String accessToken,String mobileNumber) {
        RestAssured.baseURI = baseURL+"/api/customer/gust-verification-otp-send/"+mobileNumber;
        // Create a request specification
        RequestSpecification httpRequest = RestAssured.given();
        // Set the headers
        httpRequest.header("accept", "*/*");
        httpRequest.header("accept-language", "en-US");
        httpRequest.header("content-type", "application/json");
        //httpRequest.header("X-Context-Request", "{'applicationId':'102','tenantId':'5DF1363059675161A85F576D','customerContextId':'102'}");
        httpRequest.header("Authorization", "Bearer " + accessToken);
        // Replace with your actual token
        httpRequest.queryParam("client_id",client_Id);
        httpRequest.queryParam("isQa",true);

        Response response = httpRequest.log().all().get();
        // Validate the response (you can add more assertions as needed)
        response.then().log().all().statusCode(200); // Assuming a successful response
        System.out.println("^^^^^^^^^^^RESPONSE^^^^^^^^^");
        String responseBody = response.getBody().asString();
        response.then().log().all();
        System.out.println("++++++++++++++++++++++++++++++++++++++++ RESPONSE  END - ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++");

        System.out.println("Response otp: " + response.jsonPath().getString("data"));
        return response.jsonPath().getString("data");
    }
    public static String encode(String str1, String str2) {
        return new String(java.util.Base64.getEncoder().encode((str1 + ":" + str2).getBytes()));
    }
    public static Map<String, Object> queryParam(String fields, String appId, String access_token) {
        Map<String, Object> map = new HashMap<>();
        map.put("fields", fields);
        map.put("access_token", access_token);
        map.put("appId", appId);
        return map;
    }
}
