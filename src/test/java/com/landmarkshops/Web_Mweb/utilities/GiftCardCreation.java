package com.landmarkshops.Web_Mweb.utilities;




import io.restassured.RestAssured;
import io.restassured.http.ContentType;
import io.restassured.response.Response;

import java.util.HashMap;
import java.util.Map;
import java.util.Random;

    public class GiftCardCreation {

        private static final String BASE_URI = "https://demo.ogloba.com/gc-restful-gateway/giftCardService";
        private static final String accessToken = "Basic SFlCUklTXzAxOjNaajhtbEExbk5rODdpdUdRT1dUSGx2cTZ0MjEwNjYxMjBramdzMEhOODlOeDZNcVRW";




        public static Response generateGiftCard(String gc_amount){
            RestAssured.baseURI = BASE_URI;
            Response activationResponse = giftCardActivation(gc_amount);
            String gcCardNumber = activationResponse.jsonPath().getString("barcodeNumber").trim();
            String pinNumber = activationResponse.jsonPath().getString("pinCode");
            String referenceNumber = activationResponse.jsonPath().getString("referenceNumber");
            System.out.println("GiftCardNumber      : " + gcCardNumber);
            System.out.println("GiftCard PIN Number         :" + pinNumber);

            Response   confirmTransactionResponse= giftCardGenerationConfirmTransaction(accessToken,referenceNumber);
            String successStatus = confirmTransactionResponse.jsonPath().getString("isSuccessful").trim();
            String pinCodeConfirm = confirmTransactionResponse.jsonPath().getString("pincode");
            String balanceConfirm = confirmTransactionResponse.jsonPath().getString("balance");
            String referenceConfirm = confirmTransactionResponse.jsonPath().getString("referenceNumber");

            System.out.println("Status message should be true   :" + successStatus);
            System.out.println("PIN Number confirmed    :" + pinCodeConfirm);
            System.out.println("Balance Confirm     :" + balanceConfirm);
            System.out.println("Reference confirm  :" + referenceConfirm);

            return activationResponse;


        }

        private static Response giftCardActivation(String gc_amount){

            String randomTransactionNumber = generateRandom18DigitNumber();
            Map<String, Object> requestBody = gcActivationBody(gc_amount,randomTransactionNumber);
            System.out.println(requestBody);
            Response response = sendPostRequest(accessToken,"/activation",requestBody );
            response.then().log().all().statusCode(200);
            return response;
        }

        private static Response giftCardGenerationConfirmTransaction(String accessToken,String referenceNumber){
            Map<String, Object> gcConfirmBody = gcConfirmTransactionBody("500",referenceNumber);
            Response confirmTransactionResponse = sendPostRequest(accessToken,"/confirmTransaction",gcConfirmBody );
            int statusCode1 = confirmTransactionResponse.getStatusCode();
            confirmTransactionResponse.then().log().all().statusCode(200);
            System.out.println("Status code should be 200: " + statusCode1);
            return confirmTransactionResponse;
        }

        private static Response sendPostRequest(String authorization,String endpoint, Map<String, Object> requestBody ) {


            return RestAssured.given()
                    .header("Authorization", authorization)
                    .header("Content-Type", ContentType.JSON)
                    .body(requestBody)
                    .log().all()
                    .post(endpoint);
        }

        private static Map<String, Object> gcConfirmTransactionBody(String gcAmount,String referenceNumber) {

            Map<String, Object> requestBody = new HashMap<String, Object>();
            requestBody.put("merchantId", "HYBRIS_01");
            requestBody.put("gencode", "160998123");
            requestBody.put("terminalId", "1");
            requestBody.put("cashierId", "1");
            requestBody.put("referenceNumber", referenceNumber);


            return requestBody;

        }

        private static Map<String, Object> gcActivationBody(String gcAmount,String randomTractionNum) {

            Map<String, Object> requestBody = new HashMap<String, Object>();
            requestBody.put("merchantId", "HYBRIS_01");
            requestBody.put("gencode", "160998123");
            requestBody.put("terminalId", "1");
            requestBody.put("cashierId", "1");
            requestBody.put("transactionNumber", randomTractionNum);
            requestBody.put("amount", gcAmount);

            return requestBody;

        }
        private static String generateRandom18DigitNumber() {
            Random random = new Random();
            StringBuilder sb = new StringBuilder();

            // Generate the first digit (1-9)
            sb.append(random.nextInt(9) + 1);

            // Generate the remaining 17 digits
            for (int i = 1; i < 18; i++) {
                sb.append(random.nextInt(10));
            }

            return sb.toString();
        }

    }

