package com.landmarkshops.Web_Mweb.utilities;

import io.restassured.RestAssured;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import io.restassured.specification.RequestSpecification;

import java.util.HashMap;
import java.util.Map;

    public class GiftCardBalanceCheckAPI {



//        public static void main(String[] args) {
//            final String BASE_URL = "https://uat6.maxfashion.com";
//            String giftCardNo = "1609981239911220993683799";
//            String pinCode = "7540";
//            String applicationID="102";
//            String tenantId = "5DF1363059675161A85F576D";
//            Response response = getGiftCardBalance(BASE_URL,giftCardNo,pinCode,applicationID,tenantId);
//            String giftCardNumberVerify = response.jsonPath().getString("cardNo");
//            String balanceAmount = response.jsonPath().getString("balance.amount");
//            String activeStatus = response.jsonPath().getString("active");
//            System.out.println("Gift Card Number Verify: " + giftCardNumberVerify);
//            System.out.println("Gift card Balance :  " + balanceAmount);
//            System.out.println("Gift card active status :  " + activeStatus);
//        }

        public static Response getGiftCardBalance(String giftCardNo, String pinCode){
//            final String BASE_URL = "https://uat6.maxfashion.com";
//            String giftCardNo = "1609981239911220993683799";
//            String pinCode = "7540";
//            String applicationID="102";
//            String tenantId = "5DF1363059675161A85F576D";
            return getGiftCardBalance("https://uat6.maxfashion.com",giftCardNo,pinCode,"102","5DF1363059675161A85F576D");

        }

        private static Response getGiftCardBalance(String BASE_URL,String giftCardNo,String pinCode,String applicationID,String tenantIDParam){



            // Create header map
            Map<String, String> headers = new HashMap<>();
            headers.put("accept", "*/*");
            headers.put("content-type", "application/json");
            headers.put("x-context-request", getContextRequestHeader(applicationID, tenantIDParam));

            // Create a JSON request body map
            Map<String, Object> requestBodyMap = new HashMap<>();
            requestBodyMap.put("giftCardNo", giftCardNo);
            requestBodyMap.put("pinCode", pinCode);

            // Create a request specification
            RequestSpecification requestSpec = RestAssured.given()
                    .baseUri(BASE_URL)
                    .headers(headers)
                    .contentType(ContentType.JSON)
                    .body(requestBodyMap);

            // Perform the POST request
            Response response = requestSpec.log().all().post("/api/giftcard/transactions/balance-enquiry");
            response.then().log().all().statusCode(200);
            return response;
        }

        // Create a generic method to generate the x-context-request header
        private static String getContextRequestHeader(String applicationId, String tenantId) {

            Map<String, String> contextRequestMap = new HashMap<>();
            contextRequestMap.put("applicationId", applicationId);
            contextRequestMap.put("tenantId", tenantId);
            return String.format("{'applicationId':'%s','tenantId':'%s'}", applicationId, tenantId);
        }

    }


