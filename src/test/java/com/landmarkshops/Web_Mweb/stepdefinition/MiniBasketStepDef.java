package com.landmarkshops.Web_Mweb.stepdefinition;

import com.landmarkshops.Web_Mweb.helper.LoggerHelper;
import com.landmarkshops.Web_Mweb.launcher.TestHarness;
import com.landmarkshops.Web_Mweb.pageobjects.MiniBasketScreen;
import cucumber.api.java.en.And;
import cucumber.api.java.en.Then;
import org.testng.Assert;

public class MiniBasketStepDef extends TestHarness {

    public MiniBasketScreen miniBasket;
    protected static org.apache.log4j.Logger log = LoggerHelper.getLogger(Category.class);

    @Then("^verify mini basket tool tip message$")
    public void VerifyMiniBasketToolTipMessage() {
        if(!(miniBasket instanceof MiniBasketScreen))
        miniBasket = new MiniBasketScreen(driver);
        String toolTipMessage = miniBasket.getMiniBasketToolTipMessage();
        System.out.println("Tool tip message"+toolTipMessage);
    }

    @And("^Verify User able to see mini basket count as Icon$")
    public void verifyUserAbleToSeeMiniBasketCountAsIcon() {
        if(!(miniBasket instanceof MiniBasketScreen))
        miniBasket = new MiniBasketScreen(driver);
        String qty = miniBasket.verifyMiniBasketIcon();
        Assert.assertEquals("1",qty,"verified all ui components");
    }

    @And("^Verify all UI components in mini basket page$")
    public void verifyAllUIComponentsInMiniBasketPage() {
        if(!(miniBasket instanceof MiniBasketScreen))
        miniBasket = new MiniBasketScreen(driver);
        miniBasket.verifyAllUIComponentsInMiniBasket();

    }

    @Then("^User click on view cart$")
    public void userClickOnViewCart() {
        if(!(miniBasket instanceof MiniBasketScreen))
            miniBasket = new MiniBasketScreen(driver);
        miniBasket.clickOnViewCartBtn();


    }

    @And("^User click on close mini basket icon$")
    public void userOnClickCloseMiniBasketIcon() {
        if(!(miniBasket instanceof MiniBasketScreen))
            miniBasket = new MiniBasketScreen(driver);
        miniBasket.closeMiniBasket();
    }

    @And("^Verify User able to see mini basket count as '([^']*)'$")
    public void verifyUserAbleToSeeMiniBasketCountAs(String count) throws Throwable {
        miniBasket = new MiniBasketScreen(driver);

        String qty = miniBasket.verifyMiniBasketIcon();
        Assert.assertEquals(count,qty,"verified all ui components");
    }

    @And("^user click on view cart from mini basket$")
    public void userClickOnViewCartFromMiniBasket() {
        miniBasket = new MiniBasketScreen(driver);
        miniBasket.clickOnViewCartBtn();
    }

    @Then("^User click on checkout from mini basket$")
    public void userClickOnCheckoutFromMiniBasket() {
        miniBasket = new MiniBasketScreen(driver);
        miniBasket.clickOnCheckOutBtn();
    }

    @And("^verify free gift added to mini basket as count '([^']*)'$")
    public void verifyFreeGiftAddedToMiniBasketAsCount(String count) throws Throwable {
        miniBasket = new MiniBasketScreen(driver);
        String qty = miniBasket.verifyMiniBasketIcon();
        Assert.assertEquals(count,qty,"verified all ui components");
    }

    @Then("^verify product subtotal price in mini basket$")
    public void verifyProductSubtotalPriceInMiniBasket() {
        miniBasket = new MiniBasketScreen(driver);
    }
}
