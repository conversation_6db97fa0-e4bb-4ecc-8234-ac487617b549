package com.landmarkshops.Web_Mweb.stepdefinition;

import com.landmarkshops.Web_Mweb.helper.LoggerHelper;
import com.landmarkshops.Web_Mweb.launcher.TestHarness;
import com.landmarkshops.Web_Mweb.pageobjects.FooterStaticScreen;
import cucumber.api.java.en.*;

public class FooterStaticStepDef extends TestHarness {
	
	protected static org.apache.log4j.Logger log = LoggerHelper.getLogger(AllCategoriesStepD.class);
	FooterStaticScreen fss=new FooterStaticScreen(driver);;
	@When("^User navigate to footer section$")
	public void user_navigate_to_footer_section() throws Throwable {
		
		fss.scrollToFooter();
	  
	}

	@Then("^Verify all UI components in  '([^']*)' first section$")
	public void verify_all_UI_components_in_first_section(String strVal) {
	    
	    fss.validateKnowUsText(strVal);
	    
	}
	@And("^Verify UI first Sub compenent '([^']*)' under first section$")
	public void verify_UI_first_Sub_compenent_under_first_section(String linkVal) {
	    fss.validateAboutLandmarkGroupStaticLink(linkVal);
	}
	@And("^Verify UI second Sub compenent '([^']*)' under first section$")
	public void verify_UI_second_Sub_compenent_under_first_section(String linkVal) throws InterruptedException {
		fss.validateAboutEmaxStaticLink(linkVal);
	   
	}
	@And("^Verify UI third Sub compenent '([^']*)' under first section$")
	public void verify_UI_third_Sub_compenent_under_first_section(String linkVal) {
		fss.validateFAQStaticLinks(linkVal);
	   
	}
	@And("^Verify UI forth Sub compenent '([^']*)' under first section$")
	public void verify_UI_forth_Sub_compenent_under_first_section(String linkVal)  {
		fss.validateJoinOutTeamStaticLink(linkVal);
	    
	}
	@And("^Verify UI fifth Sub compenent '([^']*)' under first section$")
	public void verify_UI_fifth_Sub_compenent_under_first_section(String linkVal) throws InterruptedException {
		fss.validateJoinSoteLocatorStaticLink(linkVal);
	}
	
	@Then("^Verify all UI components in '([^']*)' second section$")
	public void verify_all_UI_components_in_second_section(String strVal){
		fss.validateSupportServicesText(strVal);
	}
	
	@And("^Verify UI first Sub compenent '([^']*)' under second section$")
	public void verify_UI_first_Sub_compenent_under_second_section(String linkVal) {
		fss.validateWarrantyLink(linkVal);
	}

	@And("^Verify UI second Sub compenent '([^']*)' under second section$")
	public void verify_UI_second_Sub_compenent_under_second_section(String linkVal){
		fss.validateExtendedWarrantyLink(linkVal);
	}

	@And("^Verify UI third Sub compenent '([^']*)' under second section$")
	public void verify_UI_third_Sub_compenent_under_second_section(String linkVal){
		fss.validateExchangeLink(linkVal);
	}

	@And("^Verify UI forth Sub compenent '([^']*)' under second section$")
	public void verify_UI_forth_Sub_compenent_under_second_section(String linkVal){
		fss.validateSmartPayLink(linkVal);
	}

	@And("^Verify UI fifth Sub compenent '([^']*)' under second section$")
	public void verify_UI_fifth_Sub_compenent_under_second_section(String linkVal){
		fss.validateEmaxRepairLink(linkVal);
	}
	
	@And("^Verify UI sixth Sub compenent '([^']*)' under second section$")
	public void verify_UI_sixth_Sub_compenent_under_second_section(String linkVal) {
		fss.validateEmaxcareLink(linkVal);
	}
	
	@Then("^Verify all UI components in '([^']*)' third section$")
	public void verify_all_UI_components_in_third_section(String strVal){
		fss.validateLoyaltyText(strVal);
		
	}

	@And("^Verify UI first Sub compenent '([^']*)' under third section$")
	public void verify_UI_first_Sub_compenent_under_third_section(String linkVal){
		fss.validateEmaxPrideMembershipLink(linkVal);
	}

	@And("^Verify UI second Sub compenent '([^']*)' under third section$")
	public void verify_UI_second_Sub_compenent_under_third_section(String linkVal){
		fss.validateChairmansClubLink(linkVal);
		
	}

	@And("^Verify UI third Sub compenent '([^']*)' under third section$")
	public void verify_UI_third_Sub_compenent_under_third_section(String linkVal){
		fss.validateVVIPMembershipLink(linkVal);
		
	}

	@And("^Verify UI forth Sub compenent '([^']*)' under third section$")
	public void verify_UI_forth_Sub_compenent_under_third_section(String linkVal) throws InterruptedException {
		fss.validateVIPMembershipLink(linkVal);
	    
	}
	@Then("^Verify all UI components in '([^']*)' forth section$")
	public void verify_all_UI_components_in_forth_section(String strVal){
		fss.validateCommunicationText(strVal);
		
	}

	@And("^Verify UI first Sub compenent '([^']*)' under forth section$")
	public void verify_UI_first_Sub_compenent_under_forth_section(String linkVal) throws InterruptedException {
		fss.validateCommunicationLink(linkVal);
	
	}

	@Then("^Verify all UI components in '([^']*)' fifth section$")
	public void verify_all_UI_components_in_fifth_section(String strVal){
		fss.validateHelpText(strVal);
		
	}

	@And("^Verify UI first Sub compenent '([^']*)' under fifth section$")
	public void verify_UI_first_Sub_compenent_under_fifth_section(String linkVal){
		fss.validateContactLink(linkVal);
	}

	@And("^Verify UI second Sub compenent '([^']*)' under fifth section$")
	public void verify_UI_second_Sub_compenent_under_fifth_section(String linkVal){
		fss.validateCorporateEnquiriesLink(linkVal);
	}

	@And("^Verify UI third Sub compenent '([^']*)' under fifth section$")
	public void verify_UI_third_Sub_compenent_under_fifth_section(String linkVal){
		fss.validateCustomerFeedbackFormLink(linkVal);
		
	}
	

}
