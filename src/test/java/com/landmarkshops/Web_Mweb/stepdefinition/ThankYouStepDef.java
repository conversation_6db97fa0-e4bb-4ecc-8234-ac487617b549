package com.landmarkshops.Web_Mweb.stepdefinition;

import com.landmarkshops.Web_Mweb.helper.LoggerHelper;
import com.landmarkshops.Web_Mweb.launcher.TestHarness;
import com.landmarkshops.Web_Mweb.pageobjects.ThankYouScreen;
import com.landmarkshops.Web_Mweb.utilities.WebUtils;
import cucumber.api.java.en.And;
import cucumber.api.java.en.Then;
import cucumber.api.java.en.When;

public class ThankYouStepDef extends TestHarness {
    public ThankYouScreen th;
    protected static org.apache.log4j.Logger log = LoggerHelper.getLogger(Category.class);

    @When("^User navigate to thank you page as Guest user$")
    public void userNavigateToThankYouPageAsGuestUser() {
        th = new ThankYouScreen(driver);
        th.navigateToParticularPage("https://uat1emaxme.lmsin.net/ae/en/checkout/orderConfirmation/3259760844");
    }

    @When("^User navigate to thank you page as Registered user$")
    public void userNavigateToThankYouPageAsRegisteredUser() {
        th = new ThankYouScreen(driver);
        th.navigateToParticularPage("https://uat1emaxme.lmsin.net/ae/en/checkout/orderConfirmation/3259760844");

    }

    @Then("^verify all UI components in Guest thank you page$")
    public void verifyAllUIComponentsInGuestThankYouPage() {
        th = new ThankYouScreen(driver);
        th.verifyAllUIComponentsGuestThankYouPage();
    }

    @And("^enter password as '([^']*)' in Guest thank you page$")
    public void enterPasswordAsInGuestThankYouPage(String password) throws Throwable {
        th = new ThankYouScreen(driver);
        th.enterPassword(password);
    }

    @Then("^enter mobile number in guest thank you page$")
    public void enterMobileNumberInGuestThankYouPage() {
        th = new ThankYouScreen(driver);
        int num = WebUtils.generateRandomDigits(7);
        String mobile =String.valueOf(num);
      // String phone= WebUtils.generateRandomPhoneNumberString();
        th.enterPhoneNumber("50"+mobile);
    }

    @Then("^click on create your account button$")
    public void clickOnCreateYourAccountButton() {
        th = new ThankYouScreen(driver);
        th.clickOnCreateYourAccount();
    }

    @Then("^Verify all UI components in registered user Thank you page$")
    public void verifyAllUIComponentsInRegisteredUserThankYouPage() {
        th = new ThankYouScreen(driver);
        th.verifyAllUIComponentsThankYouPageAsRegisteredUser();
    }

    @And("^Verify UI components for CNC Guest user in Thank you page$")
    public void verifyUIComponentsForCNCGuestUserInThankYouPage() {
        th = new ThankYouScreen(driver);
        th.clickOnCNCGuest();
        th.verifyAllCNCGuestDetails();
    }

    @Then("^Verify UI Components for Pride Guest user in Thank you page$")
    public void verifyUIComponentsForPrideGuestUserInThankYouPage() {
        th = new ThankYouScreen(driver);
        th.clickOnPrideGuest();
        th.verifyAllComponentsForPrideGuestUserInThankYouPage();
    }
}
