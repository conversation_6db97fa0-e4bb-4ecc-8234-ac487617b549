package com.landmarkshops.Web_Mweb.stepdefinition;

import com.landmarkshops.Web_Mweb.helper.LoggerHelper;
import com.landmarkshops.Web_Mweb.launcher.TestHarness;
import com.landmarkshops.Web_Mweb.pageobjects.BrandsListingScreen;
import cucumber.api.java.en.And;
import cucumber.api.java.en.Then;
import cucumber.api.java.en.When;
import org.testng.asserts.SoftAssert;

public class BrandListingStepDef extends TestHarness {
    protected static org.apache.log4j.Logger log = LoggerHelper.getLogger(HeaderStepDef.class);
    public BrandsListingScreen bl;
    public SoftAssert sa;
    @Then("^Verify user landed to brands listing page$")
    public void verifyUserLandedToBrandsListingPage() {
        bl =new BrandsListingScreen(driver);
        bl.verifyUserLandedToBrandListingPage();



    }

    @And("^verify all ui components in brand listing page$")
    public void verifyAllUiComponentsInBrandListingPage() {
        bl =new BrandsListingScreen(driver);
        bl.verifyAllUIComponentsInBrandsListingPage();
    }

    @Then("^verify total brands '([^']*)' displayed in brand listing page$")
    public void verifyTotalBrandsDisplayedInBrandListingPage(String count) throws Throwable {
        bl =new BrandsListingScreen(driver);
        bl.verifyListOfBrandsInBrandsListingPage(count);
    }

    @When("^user click on '([^']*)' brand from brand listing page$")
    public void userClickOnBrandFromBrandListingPage(String brandName) throws Throwable {
        bl =new BrandsListingScreen(driver);
        bl.clickOnBrand(brandName);
    }

    @Then("^verify user landed to '([^']*)' brands page$")
    public void verifyUserLandedToBrandsPage(String brandName) throws Throwable {
        bl =new BrandsListingScreen(driver);
        bl.verifyUserLandedToListingPage(brandName);
    }
}
