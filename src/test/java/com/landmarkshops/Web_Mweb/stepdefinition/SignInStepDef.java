package com.landmarkshops.Web_Mweb.stepdefinition;

import com.landmarkshops.Web_Mweb.helper.LoggerHelper;
import com.landmarkshops.Web_Mweb.launcher.TestHarness;
import com.landmarkshops.Web_Mweb.pageobjects.SignInScreen;
import com.landmarkshops.Web_Mweb.utilities.LoginDataBean;
import com.landmarkshops.Web_Mweb.utilities.StepContext;
import cucumber.api.java.en.And;
import cucumber.api.java.en.Then;
import org.testng.Assert;

public class SignInStepDef extends TestHarness {

    SignInScreen signIn;
    private StepContext contex;
    public SignInStepDef(StepContext context){
        this.contex = context;
    }
    protected static org.apache.log4j.Logger log = LoggerHelper.getLogger(SignInStepDef.class);

    @Then("^User enter valid '([^']*)' and password '([^']*)'$")
    public void userEnterValidAndPassword(String email, String password) throws Throwable {
        signIn = new SignInScreen(driver);
        signIn.signInWithValidCredentials(email,password);
    }

    @Then("^User enter valid '([^']*)' and password as '([^']*)'$")
    public void userEnterValidAndPasswordAs(String email, String password) throws Throwable {
            signIn = new SignInScreen(driver);
            String emailId=LoginDataBean.getEmail();
            String pwd=LoginDataBean.getPassword();
             signIn.signInWithValidCredentials(emailId,pwd);
    }

    @Then("^Verify user not able to signIn$")
    public void verifyUserNotAbleToSignIn() {
        signIn = new SignInScreen(driver);
        String errorMessage = signIn. verifyInvalidSignInErrorMessage();
        Assert.assertEquals("Your Username or Password is incorrect.",errorMessage,"SignIn invalid copy text message not displayed ");
    }

    @Then("^verify all UI components in sign in page$")
    public void verifyAllUIComponentsInSignInPage() {
        signIn = new SignInScreen(driver);
        signIn.verifyUIComponentsInSignInPage();
    }

    @And("^validate error message in signIn page$")
    public void validateErrorMessageInSignInPage() {
        signIn = new SignInScreen(driver);
        signIn.validateErrorMessagesInSignIn();
    }

    @And("^user click on forgot password link$")
    public void userClickOnForgotPasswordLink() {
        signIn = new SignInScreen(driver);
        signIn.forgotPasswordClick();
    }

    @And("^User landed to sign in page$")
    public void userLandedToSignInPage() {
        signIn = new SignInScreen(driver);
        signIn.clearBrowserCache();
        signIn.navigateToParticularPage("https://uat1emaxme.lmsin.net/auth/login?client_id=E_MAX");
    }

    @Then("^Verify user sign in with registered email and password$")
    public void verifyUserSignInWithRegisteredEmailAndPassword() {
        signIn = new SignInScreen(driver);
        signIn.enterEmail(contex.signUpEmail);
        signIn.enterPassword("Test@1234");
        signIn.clickOnSignInButton();
    }
}
