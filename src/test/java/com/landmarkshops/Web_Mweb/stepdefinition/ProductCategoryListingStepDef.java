package com.landmarkshops.Web_Mweb.stepdefinition;

import com.landmarkshops.Web_Mweb.helper.LoggerHelper;
import com.landmarkshops.Web_Mweb.launcher.TestHarness;
import com.landmarkshops.Web_Mweb.pageobjects.ProductCategoryListingScreen;
import cucumber.api.java.en.And;
import cucumber.api.java.en.Then;
import cucumber.api.java.en.When;
import org.testng.Assert;

public class ProductCategoryListingStepDef extends TestHarness {

    ProductCategoryListingScreen ps;
    protected static org.apache.log4j.Logger log = LoggerHelper.getLogger(ProductCategoryListingStepDef.class);


    @When("^User can able to access all categories$")
    public void userCanAbleToAccessAllCategories() {
                ps = new ProductCategoryListingScreen(driver);
               ps.verifyUserLandedToCategoryListingPage();
    }

    @Then("^User access subcategories under categories section$")
    public void userAccessSubcategoriesUnderCategoriesSection() {
        ps = new ProductCategoryListingScreen(driver);
        String filterValue = ps.clickOnSubcategory();
        ps.facetsProductTitleValidation(filterValue);

    }

    @And("^Verify user can access '([^']*)' facets under section$")
    public void verifyUserCanAccessFacetsUnderSection(int count) throws Throwable {
        ps = new ProductCategoryListingScreen(driver);
        Assert.assertEquals(ps.getFacetsCount(),count,"category facets mismatch");
    }

    @Then("^verify view all link to displayed$")
    public void verifyViewAllLinkToDisplayed() {
    }

    @When("^User can able to access sort by section$")
    public void userCanAbleToAccessSortBySection() {
        ps = new ProductCategoryListingScreen(driver);
        ps.verifySortBySection();
    }

    @Then("^Verify default values as '([^']*)'$")
    public void verifyDefaultValuesAs(String sortValue) throws Throwable {
        ps = new ProductCategoryListingScreen(driver);
        ps.sortByDropDownDefaultValue(sortValue);
    }

    @And("^User can able to click on sort by drop down Icon$")
    public void userCanAbleToClickOnSortByDropDownIcon() {
        ps = new ProductCategoryListingScreen(driver);
        ps.clickOnSortByDropDownIcon();
    }

    @Then("^User can choose drop down value as '([^']*)'$")
    public void userCanChooseDropDownValueAs(String value) throws Throwable {
        ps = new ProductCategoryListingScreen(driver);
        ps.sortByValue(value);

    }

    @Then("^Verify result based on '([^']*)' sort value$")
    public void verifyResultBasedOnSortValue(String defaultValue) throws Throwable {
        ps = new ProductCategoryListingScreen(driver);
        ps.sortByDropDownDefaultValue(defaultValue);
        System.out.println("Break point");
    }



    @When("^User should be able to see the category title at the top of the category listing page$")
    public void userShouldBeAbleToSeeTheCategoryTitleAtTheTopOfTheCategoryListingPage() {
        ps = new ProductCategoryListingScreen(driver);
        ps.verifyCategoryTitle();
    }

    @Then("^Verify total number of products for category$")
    public void verifyTotalNumberOfProductsForCategory() {
        ps = new ProductCategoryListingScreen(driver);
        ps.verifyTotalProducts();

    }

    @And("^User get access product  attributes$")
    public void userGetAccessProductAttributes() {
        ps = new ProductCategoryListingScreen(driver);
        ps.verifyProductDetails();
    }

    @Then("^User able to see the badges for the applicable products$")
    public void userAbleToSeeTheBadgesForTheApplicableProducts() {
        //not yet implemented
    }

    @When("^User choose any category in product category listing page$")
    public void userChooseAnyCategoryInProductCategoryListingPage() {
        ps = new ProductCategoryListingScreen(driver);
        ps.clickOnSubcategory();

    }

    @Then("^Verify breadcrumbs in product category page$")
    public void verifyBreadcrumbsInProductCategoryPage() {
        ps = new ProductCategoryListingScreen(driver);
        ps.verifyBreadCrumb();
    }

    @And("^User navigate to respective page once user clicked on breadcrumb link$")
    public void userNavigateToRespectivePageOnceUserClickedOnBreadcrumbLink() {
        ps = new ProductCategoryListingScreen(driver);
        ps.clickOnRootBreadCrumb();
    }

    @Then("^user click on load more$")
    public void userClickOnLoadMore() {
        ps = new ProductCategoryListingScreen(driver);
        ps.clickOnLoadMore();
    }

    @Then("^verify number of products remaining to load$")
    public void verifyNumberOfProductsRemainingToLoad() {
        ps = new ProductCategoryListingScreen(driver);
        ps.numberOfProductsRemaining();
    }

    @And("^User apply filter related to '([^']*)' facets$")
    public void userApplyFilterRelatedToFacets(String colour) throws Throwable {
        ps = new ProductCategoryListingScreen(driver);
        ps.clickOnSubcategory();
    }

    @And("^Verify the price value in '([^']*)'$")
    public void verifyThePriceValueIn(String value) throws Throwable {
        ps = new ProductCategoryListingScreen(driver);
        ps.priceValueInProductList(value);
    }

    @And("^User can clear all applied filters$")
    public void userCanClearAllAppliedFilters() {
        ps = new ProductCategoryListingScreen(driver);
        ps.clickOnClearAll();
    }

    @Then("^Verify UI components in filter search$")
    public void verifyUIComponentsInFilterSearch() {
        ps = new ProductCategoryListingScreen(driver);
        ps.verifyAllUIComponentsInFilterSection();
    }

    @And("^apply multiple filters in facets and verify products in listing page$")
    public void applyMultipleFiltersInFacetsAndVerifyProductsInListingPage() {
        ps = new ProductCategoryListingScreen(driver);
        ps.applyMultipleFilterAndValidateProduct();
    }

    @Then("^choose price from slider and verify the range products displayed$")
    public void choosePriceFromSliderAndVerifyTheRangeProductsDisplayed() {
        ps = new ProductCategoryListingScreen(driver);
        String filter =ps.selectPriceRangAndValidate();
        ps.productPriceValidation(filter);

    }
}
