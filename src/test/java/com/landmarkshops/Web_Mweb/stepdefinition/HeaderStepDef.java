package com.landmarkshops.Web_Mweb.stepdefinition;

import com.landmarkshops.Web_Mweb.helper.LoggerHelper;
import com.landmarkshops.Web_Mweb.launcher.TestHarness;
import com.landmarkshops.Web_Mweb.pageobjects.BrandsListingScreen;
import com.landmarkshops.Web_Mweb.pageobjects.HeaderSection;
import cucumber.api.DataTable;
import cucumber.api.java.en.And;
import cucumber.api.java.en.Given;
import cucumber.api.java.en.Then;
import cucumber.api.java.en.When;
import org.testng.Assert;
import org.testng.asserts.SoftAssert;

import java.util.List;

/**
 * <AUTHOR>
 * This class defines all step definition methods
 */

public class HeaderStepDef extends TestHarness {

    protected static org.apache.log4j.Logger log = LoggerHelper.getLogger(HeaderStepDef.class);
    public HeaderSection hs;
    public BrandsListingScreen bl;
    public SoftAssert sa;

    @Given("^User landed to home page$")
    public void userLandedToHomePage() throws Exception {
        hs= new HeaderSection(driver);
        hs.userNavigateToHomePage();
        log.info("driver details" +driver);
    }

    @When("^Verify UI components in header section$")
    public void verifyUIComponentsInHeaderSection() {
        hs= new HeaderSection(driver);
        sa = new SoftAssert();
        sa.assertTrue(hs.verifyEMaxLogo(),"EMax logo is not displayed in Header section");
        sa.assertTrue(hs.verifyDeskTopWrapper(),"Header section having layout missed");
        sa.assertEquals(hs.verifyLoginText(),"Login","login link is not displayed");
        sa.assertEquals(hs.verifyRegisterText(),"Register","Register link is not displayed");
        sa.assertEquals(hs.verifyBasketText(),"Basket","Basket text is not displayed");
        sa.assertTrue(hs.isBasketIconDisplayed(),"Basket icon not displayed in header section");
        sa.assertTrue(hs.dropDownIcon(),"country switcher dropdown displayed");
        sa.assertEquals(hs.verifySearchPlaceholder(),"What are you looking for?","search place holder not yet displayed");
        sa.assertAll();

    }

    @When("^User can able to change country as '([^']*)'$")
    public void userCanAbleToChangeCountryAs(String country) throws Throwable {
        hs= new HeaderSection(driver);
        hs.clickOnCountrySwitcher();
        hs.changeCountry();
    }




    @And("^User click on login link and navigate to login page$")
    public void userClickOnLoginLinkAndNavigateToLoginPage() {
        hs= new HeaderSection(driver);
        hs.clickOnLogin();
    }

    @And("^User click on Register link and navigate to Register page$")
    public void userClickOnRegisterLinkAndNavigateToRegisterPage() throws InterruptedException {
        hs= new HeaderSection(driver);
        hs.clickOnRegister();
    }

    @Then("^Verify basket icon displayed$")
    public void verifyBasketIconDisplayed() {
        hs= new HeaderSection(driver);
        sa = new SoftAssert();
        sa.assertTrue(hs.isBasketIconDisplayed(),"Basket icon not displayed in header section");
        sa.assertAll();
    }

    @And("^User able to click on basket$")
    public void userAbleToClickOnBasket() {
        hs= new HeaderSection(driver);

        hs.basketClick();
    }

    @And("^User click on EMax logo$")
    public void userClickOnEMaxLogo() {
        hs= new HeaderSection(driver);
        hs.clickOnEmaxLogo();
    }

    @Then("^User can search '([^']*)' from search box$")
    public void userCanSearchFromSearchBox(String searchKey) throws Throwable {
        hs= new HeaderSection(driver);

        hs.searchInput(searchKey);


    }


    @When("^user can access department '([^']*)'$")
    public void userCanAccessDepartment(String name) throws Throwable {
        hs = new HeaderSection(driver);
        String actual = hs.verifyDepartment(name);
        Assert.assertEquals(actual,name,"Department name mismatch");
    }

    @Then("^user can check department name as '([^']*)'$")
    public void userCanCheckDepartmentNameAs(String name) throws Throwable {
        hs = new HeaderSection(driver);
        String actual = hs.verifyDepartment(name);
        Assert.assertEquals(actual,name,"Department name mismatch");
    }

    @When("^user check all departments details as$")
    public void userCheckAllDepartmentsDetailsAs() {

    }

    @When("^user check all departments details as '([^']*)'$")
    public void userCheckAllDepartmentsDetailsAs(String name) throws Throwable {
        hs = new HeaderSection(driver);
        String actual = hs.verifyDepartmentNames(name);
        Assert.assertEquals(actual,name,"both labels are "+actual+"not same +"+name);
    }

    @When("^user check all department labels as$")
    public void userCheckAllDepartmentLabelsAs(DataTable table) {
        hs = new HeaderSection(driver);

        List<List<String>> rows = table.cells(1); // skip the header
        for (List<String> row : rows) {
            String name = row.get(0);
            String actual = hs.verifyDepartmentNames(name);
            Assert.assertEquals(actual,name,"both labels are "+actual+"not same +"+name);
        }
    }

    @And("^Verify user logged account$")
    public void verifyUserLoggedAccount() {
        hs= new HeaderSection(driver);
        sa = new SoftAssert();
        sa.assertTrue(hs.verifyEMaxLogo(),"EMax logo is not displayed in Header section");
        sa.assertTrue(hs.verifyDeskTopWrapper(),"Header section having layout missed");
        sa.assertEquals(hs.verifyLoginText(),"Login","login link is not displayed");
        sa.assertEquals(hs.verifyRegisterText(),"Register","Register link is not displayed");
        sa.assertEquals(hs.verifyBasketText(),"Basket","Basket text is not displayed");
        sa.assertTrue(hs.isBasketIconDisplayed(),"Basket icon not displayed in header section");
        sa.assertAll();
    }

    @Then("^User click on brands link from header sections$")
    public void userClickOnBrandsLinkFromHeaderSections() {
        hs= new HeaderSection(driver);
        hs.userNavigateToBrandsListingPage();
        //hs.clickOnBrands();
    }

    @And("^user click on sign in link from header$")
    public void userClickOnSignInLinkFromHeader() {
        hs= new HeaderSection(driver);
        hs.clickOnLogin();
    }



}
