package com.landmarkshops.Web_Mweb.stepdefinition;

import com.landmarkshops.Web_Mweb.launcher.TestHarness;
import com.landmarkshops.Web_Mweb.pageobjects.PaymentsScreen;
import cucumber.api.java.en.And;
import cucumber.api.java.en.Then;

public class PaymentStepDef extends TestHarness {

     PaymentsScreen ps;

    @And("^Verify user landed to payment page$")
    public void verifyUserLandedToPaymentPage() {
        ps =new PaymentsScreen(driver);
        ps.verifyUserLandedToPaymentPage();

    }

    @Then("^verify payment page if user don't have added cards in payment section$")
    public void verifyPaymentPageIfUserDonTHaveAddedCardsInPaymentSection() {
        ps =new PaymentsScreen(driver);
        ps.verifyUIComponentsInpaymentPage();
    }

    @And("^user click on label add a new card from payment page$")
    public void userClickOnFirstAddANewCardFromPaymentPage() {
        ps =new PaymentsScreen(driver);
        ps.clickOnFirstAddACard();
    }


    @Then("^Verify user landed to add a new card dialog screen$")
    public void verifyUserLandedToAddANewCardDialogScreen() {
        ps =new PaymentsScreen(driver);
        ps.verifyUserLandedToDialogBoxToAddNewCard();
    }

    @And("^verify all ui components in add a new card screen$")
    public void verifyAllUiComponentsInAddANewCardScreen() {
        ps =new PaymentsScreen(driver);
        ps.verifyAllLabelsInAddANewCardDialogBox();
    }

    @Then("^User click on continue button on add a new card dialog$")
    public void userClickOnContinueButtonOnAddANewCardDialog() {
        ps =new PaymentsScreen(driver);
        ps.clickOnContinue();
    }

    @And("^verify validation messages in add a new card$")
    public void verifyValidationMessagesInAddANewCard() {
        ps =new PaymentsScreen(driver);
        ps.verifyAddaNewCardValidation();
    }

    @Then("^enter card details in add a new card$")
    public void enterCardDetailsInAddANewCard() {
        ps =new PaymentsScreen(driver);
        ps.enterAddNewCardDetails();
    }

    @Then("^verify card details in payment card list$")
    public void verifyCardDetailsInPaymentCardList() {
        ps =new PaymentsScreen(driver);
        ps.verifyAddedCardDetailsInPaymentList();
    }

    @And("^user click on delete address from payment list$")
    public void userClickOnDeleteAddressFromPaymentList() {
        ps =new PaymentsScreen(driver);
        ps.deletePaymentCardFromList();
    }

    @Then("^verify confirmation dialog box once user click on delete address$")
    public void verifyConfirmationDialogBoxOnceUserClickOnDeleteAddress() {
        ps =new PaymentsScreen(driver);
        ps.verifyUIComponentsInConfirmationDialogBox();

    }

    @And("^user click on delete from conformation dialog box$")
    public void userClickOnDeleteFromConformationDialogBox() {
        ps =new PaymentsScreen(driver);
        ps.clickOnDeleteConfirmation();
    }

    @Then("^user click on never mind from delete conformation dialog box$")
    public void userClickOnNeverMindFromDeleteConformationDialogBox() {
        ps =new PaymentsScreen(driver);
        ps.clickOnNeverMind();
    }

    @Then("^user click on close new card add dialog box$")
    public void userClickOnCloseNewCardAddDialogBox() {
        ps =new PaymentsScreen(driver);
        ps.closeAddNewCardDialogBox();
    }



    @And("^user click on add a new card link to add multiple cards$")
    public void userClickOnAddANewCardLinkToAddMultipleCards() {
        ps =new PaymentsScreen(driver);
        ps.clickOnAddaNewCardLink();
    }

    @And("^user click on add a new card$")
    public void userClickOnAddANewCard() {
        ps =new PaymentsScreen(driver);
        ps.clickOnAddaNewCardLink();
    }

    @And("^enter second card details in payment section from my account$")
    public void enterSecondCardDetailsInPaymentSectionFromMyAccount() {
        ps =new PaymentsScreen(driver);
        ps.enterSecondCardDetails();
    }

    @And("^Verify number of payment cards added as '([^']*)' in my account payment section$")
    public void verifyNumberOfPaymentCardsAddedAsInMyAccountPaymentSection(String arg0) throws Throwable {
        ps =new PaymentsScreen(driver);

        ps.verifyCardDetailsInList();
        ps.verifyNumberOfCardAddedInPayment("2");
    }

    @Then("^user click on second card set as default$")
    public void userClickOnSecondCardSetAsDefault() {
        ps =new PaymentsScreen(driver);
        ps.setSecondCardDetailsInBean();
        ps.clickOnSecondCardSetDefault();


    }

    @And("^verify card successfully updated as default card$")
    public void verifyCardSuccessfullyUpdatedAsDefaultCard() {
        ps =new PaymentsScreen(driver);
        ps.verifySetDefaultUpdatedSuccessfully();
    }
}
