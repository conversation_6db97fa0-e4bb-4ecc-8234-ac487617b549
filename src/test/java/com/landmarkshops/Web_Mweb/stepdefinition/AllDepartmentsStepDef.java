package com.landmarkshops.Web_Mweb.stepdefinition;

import com.landmarkshops.Web_Mweb.helper.LoggerHelper;
import com.landmarkshops.Web_Mweb.launcher.TestHarness;
import com.landmarkshops.Web_Mweb.pageobjects.AllDepartmentsSection;
import cucumber.api.java.en.*;

public class AllDepartmentsStepDef extends TestHarness {

    AllDepartmentsSection departmentSection;
    protected static org.apache.log4j.Logger log = LoggerHelper.getLogger(AllDepartmentsStepDef.class);

    @Then("^User Clicked on '([^']*)' first department$")
    public void user_Clicked_on_first_department(String deptVal) throws InterruptedException {
        departmentSection=new AllDepartmentsSection(driver);
        departmentSection.clickOnMobilesAccessoriesDept(deptVal);

    }

    @And("^Verify navigation link first department$")
    public void verify_navigation_link_first_department(){
    	departmentSection=new AllDepartmentsSection(driver);
    	departmentSection.verifyNavigationLink1stDept();
    }

    @Then("^User clicked on Banner under '([^']*)' of first department$")
    public void user_clicked_on_Banner_under_of_first_department(String deptVal) throws InterruptedException {
    	departmentSection=new AllDepartmentsSection(driver);
    	departmentSection.clickedOnBanner1stDept();
    }

    @And("^Verify navigation link of Banner under '([^']*)' first department$")
    public void verify_navigation_link_of_Banner_under_first_department(String deptVal) throws InterruptedException{
    	departmentSection=new AllDepartmentsSection(driver);
    	departmentSection.verifyNavigationLinkBanner1stDept();
    }
    @Then("^User Clicked on '([^']*)' second department$")
    public void user_Clicked_on_second_department(String deptVal) throws InterruptedException{
    	departmentSection=new AllDepartmentsSection(driver);
    	departmentSection.clickOnComputerNetworksDept(deptVal);
    	
    }

    @And("^Verify navigation link second department$")
    public void verify_navigation_link_second_department() throws InterruptedException{
    	departmentSection=new AllDepartmentsSection(driver);
    	departmentSection.verifyNavigationLink2ndtDept();
    }

    @Then("^User clicked on Banner under '([^']*)' second department$")
    public void user_clicked_on_Banner_under_second_department(String deptVal) throws InterruptedException{
    	departmentSection=new AllDepartmentsSection(driver);
    	departmentSection.clickedOnBanner2ndDept();
    }

    @And("^Verify navigation link of Banner under '([^']*)' second department$")
    public void verify_navigation_link_of_Banner_under_second_department(String deptVal) throws InterruptedException{
    	departmentSection=new AllDepartmentsSection(driver);
    	
    	departmentSection.verifyNavigationLinkBanner2ndDept();
    }

    @Then("^User Clicked on '([^']*)' third department$")
    public void user_Clicked_on_third_department(String deptVal) throws InterruptedException{
    	departmentSection=new AllDepartmentsSection(driver);
    	departmentSection.clickOnAppliancesDept(deptVal);
    }

    @And("^Verify navigation link third of department$")
    public void verify_navigation_link_third_of_department() throws InterruptedException{
    	departmentSection=new AllDepartmentsSection(driver);
    	departmentSection.clickedOnBanner3rddDept();
    }

    @Then("^User clicked on Banner under '([^']*)' third department$")
    public void user_clicked_on_Banner_under_third_department(String deptVal){
    	departmentSection=new AllDepartmentsSection(driver);
    	departmentSection.verifyNavigationLinkBanner3rdDept();
    }

    @And("^Verify navigation link of Banner under '([^']*)' third department$")
    public void verify_navigation_link_of_Banner_under_third_department(String deptVal){
    	departmentSection=new AllDepartmentsSection(driver);
    }

    @Then("^User Clicked on '([^']*)' fourth department$")
    public void user_Clicked_on_fourth_department(String deptVal) throws InterruptedException{
    	departmentSection=new AllDepartmentsSection(driver);
    	departmentSection.clickOnAccessoriesDept(deptVal);
    }

    @And("^Verify navigation link of fourth department$")
    public void verify_navigation_link_of_fourth_department(){
    	departmentSection=new AllDepartmentsSection(driver);
    	departmentSection.verifyNavigationLink4thdtDept();
    }

    @Then("^User clicked on Banner under '([^']*)' fourth department$")
    public void user_clicked_on_Banner_under_fourth_department(String deptVal) throws InterruptedException{
    	departmentSection=new AllDepartmentsSection(driver);
    	departmentSection.clickedOnBanner4thdDept();
    }

    @And("^Verify navigation link of Banner under '([^']*)' fourth department$")
    public void verify_navigation_link_of_Banner_under_fourth_department(String deptVal){
    	departmentSection=new AllDepartmentsSection(driver);
    	departmentSection.verifyNavigationLinkBanner4thDept();
    }

    @Then("^User Clicked on '([^']*)' fifth department$")
    public void user_Clicked_on_fifth_department(String deptVal) throws InterruptedException{
    	departmentSection=new AllDepartmentsSection(driver);
    	departmentSection.clickOnAudioVideoDept(deptVal);
    }

    @And("^Verify navigation link of fifth department$")
    public void verify_navigation_link_of_fifth_department(){
    	departmentSection=new AllDepartmentsSection(driver);
    	departmentSection.verifyNavigationLink5thdtDept();
    }

    @Then("^User clicked on Banner under '([^']*)' fifth department$")
    public void user_clicked_on_Banner_under_fifth_department(String deptVal) throws InterruptedException{
    	departmentSection=new AllDepartmentsSection(driver);
    	departmentSection.clickedOnBanner5thdDept();
    }

    @And("^Verify navigation link of Banner under '([^']*)' fifth department$")
    public void verify_navigation_link_of_Banner_under_fifth_department(String deptVal){
    	departmentSection=new AllDepartmentsSection(driver);
    	departmentSection.verifyNavigationLinkBanner5thDept();
    }

    @Then("^User Clicked on '([^']*)' sixth department$")
    public void user_Clicked_on_sixth_department(String deptVal) throws InterruptedException{
    	departmentSection=new AllDepartmentsSection(driver);
    	departmentSection.clickOnTabletDept(deptVal);
    }

    @And("^Verify navigation link of sixth department$")
    public void verify_navigation_link_of_sixth_department(){
    	departmentSection=new AllDepartmentsSection(driver);
    	departmentSection.verifyNavigationLink6thdtDept();
    }

    @Then("^User clicked on Banner under '([^']*)' sixth department$")
    public void user_clicked_on_Banner_under_sixth_department(String deptVal) throws InterruptedException{
    	departmentSection=new AllDepartmentsSection(driver);
    	departmentSection.clickedOnBanner6thdDept();
    }

    @And("^Verify navigation link of Banner under '([^']*)' sixth department$")
    public void verify_navigation_link_of_Banner_under_sixth_department(String deptVal){
    	departmentSection=new AllDepartmentsSection(driver);
    	departmentSection.verifyNavigationLinkBanner6thDept();
    }

    @Then("^User Clicked on '([^']*)' seveth department$")
    public void user_Clicked_on_seveth_department(String deptVal) throws InterruptedException{
    	departmentSection=new AllDepartmentsSection(driver);
    	departmentSection.clickOnBrandDept(deptVal);
    }

    @And("^Verify navigation link of seveth department$")
    public void verify_navigation_link_of_seveth_department(){
    	departmentSection=new AllDepartmentsSection(driver);
    	departmentSection.verifyNavigationLink7thdtDept();
    }

    @Then("^User clicked on '([^']*)' Brand under '([^']*)' seveth department$")
    public void user_clicked_on_Brand_under_seveth_department(String deptVal, String brandVal) throws InterruptedException{
    	departmentSection=new AllDepartmentsSection(driver);
    	departmentSection.clickedOnBrand7thdDept(deptVal,brandVal);
    }

    @And("^Verify navigation link of Banner under '([^']*)' seveth department$")
    public void verify_navigation_link_of_Banner_under_seveth_department(String deptVal){
    	departmentSection=new AllDepartmentsSection(driver);
    	departmentSection.verifyNavigationLinkBrand7thDept();
    }

}
