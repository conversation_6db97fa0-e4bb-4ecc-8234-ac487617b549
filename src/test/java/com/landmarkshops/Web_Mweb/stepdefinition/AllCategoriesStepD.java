package com.landmarkshops.Web_Mweb.stepdefinition;


import com.landmarkshops.Web_Mweb.helper.LoggerHelper;
import com.landmarkshops.Web_Mweb.launcher.TestHarness;
import com.landmarkshops.Web_Mweb.pageobjects.AllCategoriesFlyoutScreen;
import com.landmarkshops.Web_Mweb.pageobjects.PDPScreen;
import cucumber.api.java.en.*;

public class AllCategoriesStepD extends TestHarness {
	public AllCategoriesFlyoutScreen category;
	public PDPScreen pdpScreen;
	protected static org.apache.log4j.Logger log = LoggerHelper.getLogger(AllCategoriesStepD.class);
	
	@When("^User Landed to All categories$")
	public void user_Landed_to_All_categories() throws Throwable {
		category = new AllCategoriesFlyoutScreen(driver);
		log.info("driver" +driver);
		
		category.mouseOverToAllCategories();
	}

	@Then("^User Mouse to '([^']*)' on sub Category$")
	public void user_Click_on_Mobile_Category(String strValue) throws Throwable {
		category =new AllCategoriesFlyoutScreen(driver);
		category.mouseOverToMobile(strValue);
		}
	
	@Then("^User clicked on '([^']*)' on sub Category$")
	public void user_clicked_on_IPHONE_on_sub_Category(String strValue) throws Throwable {
		category =new AllCategoriesFlyoutScreen(driver);
		category.userClickedOniPhone(strValue);	
		Thread.sleep(5000);
	    
	}
	
	@And("^Verify navigation link$")
	public void verify_navigation_link() throws Throwable {
		category =new AllCategoriesFlyoutScreen(driver);
		
		category.verifyMobileUrl();
	    
	}
	@And("^Verify Header Text$")
    public void verify_header_text() throws Throwable {
		category =new AllCategoriesFlyoutScreen(driver);
		
		category.verifyHeaderText();
    }
	
	@And("^Verify All Products are Dislapyed in PLP$")
	public void verify_All_Products_are_Dislapyed_in_PLP() throws Throwable {
		category =new AllCategoriesFlyoutScreen(driver);
		category.verifyAllProdcutsDisplayed();
		
		
	}
	@Then("^User Clicked on Product in PLP Page$")
	public void user_Clicked_on_Product_in_PLP_Page() throws Throwable {
		pdpScreen =new PDPScreen(driver);
		pdpScreen.clickedOnProduct();
		
	}

	@And("^Verify Product Details$")
	public void verify_Product_Details() throws Throwable {
		pdpScreen =new PDPScreen(driver);
		pdpScreen.verifyProductID();
	    
	}

	@And("^Verify Price$")
	public void verify_Price() throws Throwable {
		pdpScreen =new PDPScreen(driver);
		pdpScreen.verfiyProductPrice();
	    
	}

	
}