package com.landmarkshops.Web_Mweb.stepdefinition;

import com.landmarkshops.Web_Mweb.config.ProductURLConfigBean;
import com.landmarkshops.Web_Mweb.helper.LoggerHelper;
import com.landmarkshops.Web_Mweb.launcher.TestHarness;
import com.landmarkshops.Web_Mweb.pageobjects.PDPScreen;
import com.landmarkshops.Web_Mweb.utilities.StepContext;
import cucumber.api.java.en.And;
import cucumber.api.java.en.Then;
import cucumber.api.java.en.When;

public class PDPStepDef extends TestHarness {

    PDPScreen pdp;
    protected static org.apache.log4j.Logger log = LoggerHelper.getLogger(PDPStepDef.class);
    private StepContext context;
    @When("^User navigate to product details page$")
    public void userNavigateToProductDetailsPage() {
        pdp =new PDPScreen(driver);
        pdp.userNavigateToProductDetailsPage();
    }

    @Then("^User click on Add to basket$")
    public void userClickOnAddToBasket() {
        pdp = new PDPScreen(driver);
        pdp.yellowProductVariant();
        pdp.userClickOnAddToBasket();
    }

    @When("^User navigate to product details screen$")
    public void verifyUserLandedToProductDetailScreen() {
        pdp= new PDPScreen(driver);
        pdp.userNavigateToProductDetailsPage();
    }

    @When("^User navigate to product detail screen$")
    public void verifyUserLandedToProductDetailsScreen() {
        pdp= new PDPScreen(driver);
        pdp.navigateToProductDetailPage("https://uat1emaxme.lmsin.net/ae/en/product/01FEXHN0TVNAJS0MBY81C40Y57/Samsung%2085inch%20Neo%20tst");
    }

    @When("^User navigate to apple watch product detail screen$")
    public void verifyUserLandedToAppleWatchProductDetailsScreen() {
        pdp= new PDPScreen(driver);
        pdp.navigateToProductDetailPage("https://uat1emaxme.lmsin.net/ae/en/shop-apple-watch-series-6-p-01FF2S0GHBJVHG0BN8S9HY0YR2-256GB-Green.html");
    }

    @And("^User navigate to apple watch product details screen$")
    public void verifyUserLandedToAppleWatchProductDetailsScreens() {
        pdp= new PDPScreen(driver);
        pdp.navigateToProductDetailPage("https://uat1emaxme.lmsin.net/ae/en/shop-apple-watch-series-6-p-01FF2S0GHBJVHG0BN8S9HY0YR2-256GB-Green.html");
    }

    @When("^User navigate to samsung-galaxy product detail screen$")
    public void verifyUserLandedToSamsungGalaxyProductDetailsScreen() {
        pdp= new PDPScreen(driver);
        pdp.navigateToProductDetailPage("https://uat1emaxme.lmsin.net/ae/en/product/01FEXGDHVR275C0VKGWS8D08F9/samsung-galaxy");
    }

    @When("^User navigate to Samsung 85inch Neo product detail screen$")
    public void verifyUserLandedToSamsungNeoProductDetailsScreen() {
        pdp = new PDPScreen(driver);

        pdp.navigateToProductDetailPage(ProductURLConfigBean.getExtendWarranty());
    }
    @When("^User navigate to free gift coupon details page$")
    public void verifyNavigateToFreeGiftCouponDetailsPage() {
        pdp= new PDPScreen(driver);
        pdp.navigateToProductDetailPage(ProductURLConfigBean.getFreeGiftUrl());
    }

    @When("^User navigate to iPhone 256GB Gold Sachin Non CNC product detail screen$")
    public void verifyUserLandedToiPhone256GBGoldSachinNonCNCProductDetailsScreen() {
        pdp= new PDPScreen(driver);
        pdp.navigateToProductDetailPage("https://uat1emaxme.lmsin.net/ae/en/shop-i-phone-256-gb-gold-sachin-p-01FR5DDBJH9XA10CWBGN3H0AJG--.html");
    }

    @Then("^User Verify all component in product details screen$")
    public void verifyAllElementsInProductDetailScreen(){
        pdp= new PDPScreen(driver);
        pdp.isProductDetailsScreenDisplayed();
        pdp.verifyBreadcrumbs();
        pdp.verifyBreadcrumbsProductName("Mobile Accessories");
        pdp.verifyBreadcrumbsProductName("Apple Watch Series 6");
        pdp.clickBreadcrumbsProductName("Mobile Accessories");
        pdp.navigateBack();
        pdp.verifyProductImage();
        pdp.swipeLeftProductImage(); //Not Working
        pdp.swipeRightProductImage(); //Not Working
        //pdp.verifyImageBadges(); //Dev implementation not completed
        pdp.verifyImageList();
        pdp.verifyProductText();
        //pdp.verifyProductText(String productText);
        pdp.verifyProductPrice();
        //pdp.verifyBasePrice(String basePrice);
        //pdp.verifySoldPrice(String soldPrice);
        //pdp.verifyProductPrice(String basePrice, String soldPrice);
        pdp.verifyProductDiscountedAmountAndPercentage();
        //pdp.verifyProductDiscountedAmountAndPercentage(String discountedAmountAndPercentage);
        pdp.verifyProductInstallmentText();
        //pdp.verifyProductInstallmentDetails(String installmentText);
        pdp.verifyProductVariantSection();
        pdp.verifyFirstProductVariantSection();
        pdp.verifySecondProductVariantSection();
        pdp.clickPrimaryVariantByName("256GB");
        pdp.clickSecondaryVariantByName("Green");
        pdp.verifyExternalWarrantySection();
        pdp.clickExternalWarrantyByName("Extended Warranty - Assure ++ 1 Year Accidental Damage\nAED 100");
        //pdp.verifyEmaxCareWarrantySection();
        //pdp.clickEmaxCareWarrantyByName("Extended Warranty - Assure ++ 1 Year Accidental Damage\nAED 100");
        pdp.verifyNoAdditionalProtectionSection();
        pdp.clickNoAdditionalProtectionWarranty();
        pdp.verifyDeliveryOptionsSection();
        pdp.verifyAddToCartButton();
        pdp.verifyAllAboutItemSection();
    }

    @Then ("^Verify warranty section is displayed in Product detail screen$")
    public void verifyWarrantySectionDisplayedInProductDetailScreen(){
        pdp= new PDPScreen(driver);
        pdp.verifyWarrantySection();
    }

    @And ("^Verify External Warranty section displayed with different warranty types$")
    public void verifyExternalWarrantySectionInProductDetailScreen(){
        pdp= new PDPScreen(driver);
        pdp.verifyExternalWarrantySection();
    }

    @Then ("^Verify Emax Warranty section displayed with different warranty types$")
    public void verifyEmaxWarrantySectionInProductDetailScreen(){
        pdp= new PDPScreen(driver);
        pdp.verifyEmaxCareWarrantySection();
    }

    @And ("^Verify No Additional Protection section displayed$")
    public void verifyNoAdditionalProtectionSectionInProductDetailScreen(){
        pdp= new PDPScreen(driver);
        pdp.verifyNoAdditionalProtectionSection();
    }

    @Then ("^Click External Warranty option one by one and verify selected option in red borders$")
    public void clickAllExternalWarrantySectionOptionInProductDetailScreen(){
        pdp= new PDPScreen(driver);
        pdp.clickExternalWarrantyByName("Extended Warranty - Assure ++ 1 Year Accidental Damage\nAED 100");
    }

    @And ("^Click Emax Warranty option one by one and verify selected option in red borders$")
    public void clickAllEmaxWarrantySectionOptionInProductDetailScreen(){
        pdp= new PDPScreen(driver);
        pdp.clickEmaxCareWarrantyByName("Extended Warranty - Assure ++ 1 Year Accidental Damage\nAED 100");
        pdp.clickEmaxCareWarrantyByName("2 Yr Extended Warranty\nAED 200");
    }

    @Then ("^Click No Additional Protection button and verify selected option in red borders$")
    public void clickNoAdditionalProtectionWarrantySectionOptionInProductDetailScreen(){
        pdp= new PDPScreen(driver);
        pdp.clickNoAdditionalProtectionWarranty();
    }

    @Then("^Verify Click and Collect section$")
    public void verifyClickAndCollectSession() {
        pdp= new PDPScreen(driver);
        pdp.verifyClickAndCollectSection();
        pdp.verifyClickAndCollectTitle();
        pdp.verifyClickAndCollectIcon();
        pdp.verifyClickAvailableStoresLink();
    }

    @And("^User click on Check Available Stores button$")
    public void clickOnCheckAvailableStores() {
        pdp= new PDPScreen(driver);
        pdp.clickAvailableStoresLink();
    }

    @Then("^Verify Click and Collect pop-up dialog$")
    public void verifyClickAndCollectPopUpDialog() {
        pdp= new PDPScreen(driver);
        pdp.verifyClickAndCollectPopUpTitle();
        pdp.verifyClickAndCollectPopUpSubTitle();
        pdp.verifyClickAndCollectPopUpLocationIcon();
        pdp.verifyClickAndCollectPopUpStoreName();
        pdp.verifyClickAndCollectPopUpInStock();
        pdp.closeClickAndCollectPopup();
    }

    @Then ("^Verify variant section is displayed in Product detail screen$")
    public void verifyVariantSection() {
        pdp = new PDPScreen(driver);
        pdp.verifyProductVariantSection();
        pdp.verifyFirstProductVariantSection();
        pdp.verifySecondProductVariantSection();
    }

    @And ("^Click on color '([^']*)'$")
    public void clickPrimaryVariant(String primaryVariant) throws Throwable {
        pdp = new PDPScreen(driver);
        pdp.clickPrimaryVariantByName(primaryVariant);
    }

    @And ("^Click on memory '([^']*)'$")
    public void clickSecondaryVariant(String secondaryVariant) throws Throwable {
        pdp = new PDPScreen(driver);
        pdp.clickSecondaryVariantByName(secondaryVariant);
    }

    @Then ("^Verify pre order product in Product detail screen$")
    public void verifyPreOrderProduct() {
        pdp = new PDPScreen(driver);
        pdp.verifyPreOrderButton();
    }

    @And ("^Click pre order button in Product detail screen$")
    public void clickPreOrderProduct() {
        pdp = new PDPScreen(driver);
        pdp.userClickOnPreOrderBasket();
    }

    @Then("^verify free gift section in details page$")
    public void verifyFreeGiftSectionInDetailsPage() {
        pdp = new PDPScreen(driver);
        pdp.verifyFreGiftSection();
    }

    @And("^User click on Add to basket from pdp page$")
    public void userClickOnAddToBasketFromPdpPage() {
        pdp = new PDPScreen(driver);
        pdp.userClickOnAddToBasket();
    }

    @And("^get price details from product details page$")
    public void getPriceDetailsFromProductDetailsPage() {
        pdp = new PDPScreen(driver);
        context =new StepContext();
        context.sellingPrice = pdp.getProductPrice();
    }
}