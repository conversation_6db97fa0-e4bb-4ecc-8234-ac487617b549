package com.landmarkshops.Web_Mweb.stepdefinition;

import com.landmarkshops.Web_Mweb.launcher.TestHarness;
import com.landmarkshops.Web_Mweb.pageobjects.WriteToUs;
import cucumber.api.java.en.And;
import cucumber.api.java.en.Then;

public class WriteToUsStepDef extends TestHarness {
    WriteToUs wts;



    @Then("^Verify user landed to write to us page$")
    public void verifyUserLandedToWriteToUsPage() {
        wts =new WriteToUs(driver);
        wts.verifyUserLandedOnWriteToUs();
    }

    @And("^Verify all UI components in write to us page$")
    public void verifyAllUIComponentsInWriteToUsPage() {
        wts =new WriteToUs(driver);
        wts.verifyLabelsInWriteToUs();
    }

    @Then("^Verify required error message in write to us page$")
    public void verifyRequiredErrorMessageInWriteToUsPage() {
        wts =new WriteToUs(driver);
        wts.requiredWriteUsErrorMessage();
    }

    @And("^Verify Invalid error message in write to us page$")
    public void verifyInvalidErrorMessageInWriteToUsPage() {
        wts =new WriteToUs(driver);
        wts.inValidWriteUsErrorMessage();
    }

    @And("^User enter full details in write to us$")
    public void userEnterFullDetailsInWriteToUs() {
        wts =new WriteToUs(driver);
        wts.enterFullName("Test automation");
    }

    @Then("^User enter email detail in write to us$")
    public void userEnterEmailDetailInWriteToUs() {
        wts =new WriteToUs(driver);
        wts.enterEmailDetail();
    }

    @And("^User enter mobile number details in write to us$")
    public void userEnterMobileNumberDetailsInWriteToUs() {
        wts =new WriteToUs(driver);
        wts.enterMobileNumber();
    }

    @Then("^choose topic in as '([^']*)' in write to us$")
    public void chooseTopicInAsInWriteToUs(String dropDownValue) throws Throwable {
        wts =new WriteToUs(driver);
        wts.chooseTopic(dropDownValue);
    }

    @And("^User write comments in tell us what you think$")
    public void userWriteCommentsInTellUsWhatYouThink() {
        wts =new WriteToUs(driver);
        wts.tellUsWhatYouThink();
    }

    @Then("^user click on submit in write to us$")
    public void userClickOnSubmitInWriteToUs() {
        wts =new WriteToUs(driver);
        wts.clickOnSubmit();
    }
}
