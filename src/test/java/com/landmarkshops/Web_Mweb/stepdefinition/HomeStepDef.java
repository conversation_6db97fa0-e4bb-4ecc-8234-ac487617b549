package com.landmarkshops.Web_Mweb.stepdefinition;

import com.landmarkshops.Web_Mweb.helper.LoggerHelper;
import com.landmarkshops.Web_Mweb.launcher.TestHarness;
import com.landmarkshops.Web_Mweb.pageobjects.HomeScreen;
import com.landmarkshops.Web_Mweb.pageobjects.PDPScreen;
import cucumber.api.java.en.And;
import cucumber.api.java.en.Then;
import cucumber.api.java.en.When;
import org.testng.asserts.SoftAssert;

import static org.testng.Assert.*;

/**
 * This class is used to do all core navigation once user enter url
 * <AUTHOR>
 */


public class HomeStepDef extends TestHarness {

    protected static org.apache.log4j.Logger log = LoggerHelper.getLogger(HomeStepDef.class);
    public HomeScreen hs;
    public PDPScreen pdp;
    public SoftAssert sa;

    @When("^User click on static banner in home page$")
    public void userClickOnStaticBannerInHomePage() {
        hs = new HomeScreen(driver);
        hs.clickOnBanner();


    }

    @Then("^User navigate to respective page$")
    public void userNavigateToRespectivePage() {
        hs = new HomeScreen(driver);
        String url=hs.verifyNavigatePageURL();
        System.out.println("navigation url"+url);
        assertEquals(url,"https://uat1emaxme.lmsin.net/ae/en/shop-mxwomen-clothing-tops-tshirtsandvests","URL mismatch");

    }

    @And("^User can use previous carousel button to check previous banner$")
    public void userCanUsePreviousCarouselButtonToCheckPreviousBanner() {
        hs = new HomeScreen(driver);
        hs.clickOnPreviousCarouselBanner();
    }

    @And("^User can use next carousel button to check next banner$")
    public void userCanUseNextCarouselButtonToCheckNextBanner() {
        hs = new HomeScreen(driver);
        hs.clickOnNextCarouselBanner();
    }


    @Then("^User can able to click video banners in home page$")
    public void userCanAbleToClickVideoBannersInHomePage() {
        hs = new HomeScreen(driver);
        hs.clickOnVideoBanner();
    }

    @And("^User can able to see title of the banner$")
    public void userCanAbleToSeeTitleOfTheBanner() {
        hs = new HomeScreen(driver);
        String staticBannerTitle=hs.verifyBannerTitle();
        System.out.println("static banner title /t"+staticBannerTitle);

    }

    @And("^User navigate back to previous page$")
    public void userNavigateBackToPreviousPage() {
        hs = new HomeScreen(driver);
        hs.navigateBack();
    }

    @Then("^User navigate to respective page having url as '([^']*)'$")
    public void userNavigateToRespectivePageHavingUrlAs(String expectedUrl) throws Throwable {
        hs = new HomeScreen(driver);
        String actualUrl=hs.verifyNavigatePageURL();
        assertEquals(actualUrl,expectedUrl,"URL mismatch");

    }

    @Then("^User can get video banner title$")
    public void userCanGetVideoBannerTitle() {
        hs = new HomeScreen(driver);
        String name=hs.getVideoTitle();
        assertFalse(name.isEmpty(),"video banner text is empty");
    }



    @And("^verify swiper pagination linked to each image in the slider and navigation from first to last$")
    public void verifySwiperPaginationLinkedToEachImageInTheSliderAndNavigationFromFirstToLast() {
        hs = new HomeScreen(driver);
        hs.carouselBannerNavigationFromBegin();
    }

    @Then("^verify swiper pagination linked to each image in the slider and navigation from last to first$")
    public void verifySwiperPaginationLinkedToEachImageInTheSliderAndNavigationFromLastToFirst() {
        hs = new HomeScreen(driver);
        hs.carouselBannerNavigationFromEnd();
    }

    @Then("^User click on certain pagination$")
    public void userClickOnCertainPagination() {
        hs = new HomeScreen(driver);
        hs.clickOnCertainPagination();
    }

    @When("^Verify user can able to access product rail section$")
    public void verifyUserCanAbleToAccessProductRailSection() {
        hs = new HomeScreen(driver);
        hs.verifyProductRailSection();
    }
    @Then("^Verify rail header section and sales end timer$")
    public void verifyRailHeaderSectionAndSalesEndTimer() {
        hs = new HomeScreen(driver);
        String title = hs.verifyRailProductTile();
        log.info("-----title------"+title);
        String timer = hs.getRailTimer();
        System.out.println("---timer--------"+timer);
    }


    @Then("^Verify user can able to see rails description and price$")
    public void verifyUserCanAbleToSeeRailsDescriptionAndPrice() {
        hs = new HomeScreen(driver);
        String productDescription = hs.verifyProductDescription();
        log.info("-----product Description------"+productDescription);
        String railDescription = hs.getRailProductDescription();
        log.info("-----rail Description------"+railDescription);
        String productPrice = hs.getRailProductPrice();
        log.info("----product price------"+productPrice);

    }

    @And("^User can able to click on right carousel arrow to swipe product$")
    public void userCanAbleToClickOnRightCarouselArrowToSwipeProduct() {
        hs = new HomeScreen(driver);
        hs.clickRailRightCarousel();
    }

    @Then("^User can able to find left carousel arrow to swipe product$")
    public void userCanAbleToFindLeftCarouselArrowToSwipeProduct() {
        hs = new HomeScreen(driver);
        hs.clickRailLeftCarousel();
    }

    @And("^User click on viewAll link to navigate to respective offer or category page$")
    public void userClickOnViewAllLinkToNavigateToRespectiveOfferOrCategoryPage() {

        hs = new HomeScreen(driver);
        String viewAll = hs.getRailViewAll();
        log.info("view all link---------- "+viewAll);

        hs.clickRailSeeAll();
    }

    @Then("^User click on product image to redirect to product details page$")
    public void userClickOnProductImageToRedirectToProductDetailsPage() {
        hs = new HomeScreen(driver);
        hs.clickRailProductImage();
    }

    @And("^User click on description to redirect to product details page$")
    public void userClickOnDescriptionToRedirectToProductDetailsPage() {
        hs = new HomeScreen(driver);
        hs.clickRailProductDescription();
    }

    @Then("^Verify total products per set of rail as '([^']*)'$")
    public void verifyTotalProductsPerSetOfRailAs(String arg0) throws Throwable {
        hs = new HomeScreen(driver);
        hs.railProductsPerSection();
    }

    @And("^Verify '([^']*)' products before the arrow swipe$")
    public void verifyProductsBeforeTheArrowSwipe(String count) throws Throwable {
        hs = new HomeScreen(driver);
        hs.validateProductCountBeforeArrowSwipe();
    }

    @And("^Verify another '([^']*)' after the swipe$")
    public void verifyAnotherAfterTheSwipe(String count) throws Throwable {
        hs = new HomeScreen(driver);
        hs.validateProductCountAfterTheSwipe();
    }


    @When("^user clear cache and landed to home page$")
    public void userClearCacheAndLandedToHomePage() {
        hs = new HomeScreen(driver);
        pdp =new PDPScreen(driver);
        hs.clearBrowserCache();
        pdp.navigateToParticularPage("https://uat1emaxme.lmsin.net/ae/en/department/men");

    }

    @Then("^Verify product image badges in rail and PDP$")
    public void verifyProductBadgesInHomePageAndPdp() {
        hs = new HomeScreen(driver);
        pdp =new PDPScreen(driver);
        hs.verifyFirstProductWithBadgesInMostLovedSlider();
        hs.verifyAndClickFirstProductInMostLoved();
        pdp.verifyImageBadges();
    }

    @And("^User click the search box and enters '([^']*)'$")
    public void enterSearch(String name) throws Throwable {
        hs = new HomeScreen(driver);
        hs.enterSearch(name);
    }


    @And("^User click the search box and enters '([^']*)' and press enter key$")
    public void enterSearchAndPressEnterKey(String name) throws Throwable {
        hs = new HomeScreen(driver);
        hs.enterSearchTextAndPressEnterKey(name);
    }

    @Then("^User clears entered text in search box$")
    public void clearEnteredSearchText(){
        hs = new HomeScreen(driver);
        hs.clearEnterSearch();
    }

    @And("^Verify Recent searches$")
    public void verifyRecentSearches(){
        hs = new HomeScreen(driver);
        hs.verifyRecentSearches();
    }

    @And("^Clear all Recent searches$")
    public void clearAllRecentSearches(){
        hs = new HomeScreen(driver);
        hs.clearAllRecentSearch();
    }

    @And("^Verify Categories in search list$")
    public void verifyCategoriesInSearch(){
        hs = new HomeScreen(driver);
        hs.verifyCategoriesInSearch();
    }

    @And("^Verify Top Products in search list$")
    public void verifyTopProductsInSearch(){
        hs = new HomeScreen(driver);
        hs.verifyTopProductsInSearch();
    }

    @And("^Click '([^']*)' in Top Products$")
    public void clickOnTopProductsByNameInSearch(String name) throws Throwable {
        hs = new HomeScreen(driver);
        hs.clickOnTopProductsByNameInSearch(name);
    }

    @And("^Click '([^']*)' in Categories$")
    public void clickOnCategoriesByNameInSearch(String name) throws Throwable {
        hs = new HomeScreen(driver);
        hs.clickOnCategoriesByNameInSearch(name);
    }

    @And("^Verify '([^']*)' no match Search message$")
    public void VerifyNoMatchMessage(String search) throws Throwable {
        hs = new HomeScreen(driver);
        hs.verifyNoMatchSearchMessage(search);
    }

    @When("^user mouse hover on department section$")
    public void userMouseHoverOnDepartmentSection() {
        hs = new HomeScreen(driver);
        hs.verifyDepartmentSection();
    }

    @Then("^user select any product from '([^']*)'$")
    public void userSelectAnyProductFrom(String arg0) throws Throwable {
        hs = new HomeScreen(driver);
        hs.chooseMobileAndAccessories();
    }

    @And("^verify user landed to respective product details page$")
    public void verifyUserLandedToRespectiveProductDetailsPage() {
        hs = new HomeScreen(driver);
        hs.getProductTitle();
    }

    @Then("^user check other subcategories '([^']*)' and '([^']*)'$")
    public void userCheckOtherSubcategoriesAnd(String categoryName, String subCategoryName) throws Throwable {
        hs = new HomeScreen(driver);
        hs.chooseTablets();




    }


    @Then("^verify pride logo in home page$")
    public void verifyPrideLogoInHomePage() {
        hs = new HomeScreen(driver);
        hs.verifyPrideMemberShipLogo();
    }
}
