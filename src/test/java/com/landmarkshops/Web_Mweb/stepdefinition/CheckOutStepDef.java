package com.landmarkshops.Web_Mweb.stepdefinition;

import com.landmarkshops.Web_Mweb.helper.LoggerHelper;
import com.landmarkshops.Web_Mweb.launcher.TestHarness;
import com.landmarkshops.Web_Mweb.pageobjects.CheckOutScreen;
import com.landmarkshops.Web_Mweb.utilities.AddressBean;
import com.landmarkshops.Web_Mweb.utilities.WebUtils;
import cucumber.api.java.en.And;
import cucumber.api.java.en.Then;
import org.json.simple.parser.ParseException;

import java.io.IOException;


public class CheckOutStepDef extends TestHarness {

     CheckOutScreen cs;
    protected static org.apache.log4j.Logger log = LoggerHelper.getLogger(CheckOutScreen.class);

    @And("^User landed to checkout page$")
    public void userLandedToCheckoutPage() {
        cs = new CheckOutScreen(driver);
        cs.verifyUserLandedToCheckOutPage();
    }


    @And("^Verify all UI components in select your shipping speed$")
    public void verifyAllUIComponentsInSelectYourShippingSpeed() {
        cs = new CheckOutScreen(driver);
        cs.verifyAllUIComponentsInSelectYourShippingSpeed();
    }

    @And("^Verify and click on Click and Collect section$")
    public void verifyClickAndCollectSection() {
        cs = new CheckOutScreen(driver);
        cs.VerifyClickAndCollectSectionDisplayed();
        cs.clickOnclickAndCollectButton();
    }

    @And("^Verify and click on Click and Collect section title and subtitle$")
    public void verifyClickAndCollectSectionTitle() {
        cs = new CheckOutScreen(driver);
        cs.VerifyClickAndCollectButton();
        cs.VerifyFindAPickupPointSection();
    }

    @And("^Verify and select Click and Collect store in map$")
    public void verifyAndSelectClickAndCollectStoreInMap() {
        cs = new CheckOutScreen(driver);
        cs.clickOnClickAndCollectSearchBox();
        cs.enterDataOnClickAndCollectSearchBox("Emax Oasis Dubai");
        cs.clearDataOnClickAndCollectSearchBox();
        cs.enterDataOnClickAndCollectSearchBox("Emax IBN BATUTA MALL");
        cs.clickOnClickAndCollectSearchBoxList();
        cs.clickOnClickAndCollectConfirmPickupPointButton();
        cs.verifyOnClickAndCollectConfirmPickupPointMapLocation();
        cs.verifyOnClickAndCollectConfirmPickupPointMapStoreDetails();
    }

    @And("^Verify Click and Collect Add Your Contact details$")
    public void verifyClickAndCollectAddYourContactDetails() {
        cs = new CheckOutScreen(driver);
        cs.verifyClickAndCollectAddYourContactDetails();
        cs.enterFullNameInClickAndCollectAddYourContactDetails("Name");
        cs.enterMobileInClickAndCollectAddYourContactDetails("522345678");
        cs.enterEmailInClickAndCollectAddYourContactDetails("<EMAIL>");
    }

    @And("^Verify Click and Collect Add Your Contact details Register user$")
    public void verifyClickAndCollectAddYourContactDetailsRegisgterUser() {
        cs = new CheckOutScreen(driver);
        cs.verifyClickAndCollectAddYourContactDetails();
        cs.enterFullNameInClickAndCollectAddYourContactDetails("Name");
        cs.enterMobileInClickAndCollectAddYourContactDetails("522345678");
    }

    @And("^Verify Click and Collect Your Order Summary details$")
    public void verifyClickAndCollectYourOrderSummaryDetails() {
        cs = new CheckOutScreen(driver);
        cs.verifyClickAndCollectYourOrderSummaryDetails();
    }


    @Then("^User select '([^']*)' from select your shipping speed and verify in order summery$")
    public void userSelectFromSelectYourShippingSpeedAndVerifyInOrderSummery(String shippingModeValue) throws Throwable {
        cs = new CheckOutScreen(driver);
       cs.chooseShippingMode(shippingModeValue);
    }

    @Then("^User click on home delivery$")
    public void userClickOnHomeDelivery() {
        cs = new CheckOutScreen(driver);
        cs.verifyClickAndCollectLabels();
        cs.verifyHomeDeliveryLabels();
        cs.clickOnHomeDelivery();
    }

    @And("^User verify all UI components in Add your shipping address$")
    public void userVerifyAllUIComponentsInAddYourShippingAddress() {
        cs = new CheckOutScreen(driver);
        cs.verifyAddYourShippingAddressUIComponents();
    }

    @Then("^User verify all UI components in select a payment section$")
    public void userVerifyAllUIComponentsInSelectAPaymentSection() {
        cs = new CheckOutScreen(driver);
        cs.verifySelectPaymentSection();
    }

    @Then("^enter shipping address details$")
    public void enterShippingAddressDetails() throws IOException, ParseException {
        cs = new CheckOutScreen(driver);
        String fullName="WebAutomation "+WebUtils.generateRandomString(15);
        AddressBean.setFullName(fullName);
        cs.enterFullName(fullName);
        cs.enterEmailId();
        cs.enterMobileNumber();
        cs.selectYOurEmirate("Abu Dhabi");
        cs.selectedArea("Abu Dhabi University");
        cs.enterBuildingName("Test 123");
        cs.enterMakaniNumber("300523432");
        cs.defaultAddressClick();
        cs.defaultAddressCheckBox();

    }

    @Then("^enter additional shipping address details$")
    public void enterAdditionalShippingAddressDetails() throws IOException, ParseException {
        cs = new CheckOutScreen(driver);
        cs.enterFullName("WebAutomation");
        cs.enterEmailId();
        cs.enterMobileNumber();
        cs.selectYOurEmirate("Abu Dhabi");
        cs.selectedArea("Abu Dhabi University");
        cs.enterBuildingName("Test 123");
        cs.enterMakaniNumber("300523432");
        cs.defaultAddressClick();
        cs.defaultAddressCheckBox();
    }

    @Then("^enter payment details for the guest user$")
    public void enterPaymentDetailsForTheGuestUser() {
        cs = new CheckOutScreen(driver);
        cs.enterCreditCardNumber("****************");
        cs.enterNameOnCard("test user");
        cs.selectExpiryMonth();
        cs.setSelectExpiryYear();
        cs.enterCVV("123");


    }
    @Then("^enter payment details for the register user$")
    public void enterPaymentDetailsForTheRegisterUser() {
        cs = new CheckOutScreen(driver);
        cs.enterCreditCardNumber("****************");
        cs.enterNameOnCard("test user");
        cs.selectExpiryMonth();
        cs.setSelectExpiryYear();
        cs.enterCVV("123");


    }
    @And("^click on Payment now for guest user$")
    public void clickOnPaymentNowForGuestUser() {
        cs = new CheckOutScreen(driver);
        cs.payNowClick();
    }

    @And("^click on Payment now for register user$")
    public void clickOnPaymentNowForRegisterUser() {
        cs = new CheckOutScreen(driver);
        cs.payNowClick();
    }
    @Then("^User change address type as '([^']*)'$")
    public void userChangeAddressTypeAs(String addressType) throws Throwable {
        cs = new CheckOutScreen(driver);
        cs.changeAddressTypeAs(addressType);
    }

    @And("^User click on Add a new Address$")
    public void userClickOnAddANewAddress() {
        cs = new CheckOutScreen(driver);
        cs.clickOnAddANewAddressLink();
    }

    @And("^click on ship to this address button$")
    public void clickOnShipToThisAddressButton() {
        cs = new CheckOutScreen(driver);
        cs.clickOnShipToThisAddress();
    }

    @Then("^click on show all my address link$")
    public void clickOnShowAllMyAddressLink() {
        cs = new CheckOutScreen(driver);
        cs.clickOnShowAllMyAddresses();
    }

    @Then("^verify added address details in select your shipping address section$")
    public void verifyAddedAddressDetailsInSelectYourShippingAddressSection() {
        cs = new CheckOutScreen(driver);
        cs.checkFullName();
    }

    @And("^User choose shipping address in my address list$")
    public void userChooseShippingAddressInMyAddressList() {
        cs = new CheckOutScreen(driver);
        cs.chooseShippingAddress();
    }

    @Then("^user choose payment card from payment section$")
    public void userChoosePaymentCardFromPaymentSection() {
        cs = new CheckOutScreen(driver);
        cs.choosePaymentCard();
        cs.verifyCardUIComponents();
        cs.enterCVVNumber();
    }

    @Then("^user click on add a new card for register$")
    public void userClickOnAddANewCardForRegister() {
        cs = new CheckOutScreen(driver);
        cs.clickOnAddNewCard();

    }

    @Then("^user click on save this card button for register user$")
    public void userClickOnSaveThisCardButtonForRegisterUser() {
        cs = new CheckOutScreen(driver);
        cs.clickOnSaveThisCard();
    }

    @And("^validate error message for shipping address fields$")
    public void validateErrorMessageForShippingAddressFields() {
        cs = new CheckOutScreen(driver);
        cs.validateEmailErrorMessage();
        cs.validateFullNameErrorMessage();
        cs.validateMobileNumberErrorMessage();
        cs.emirateErrorMessage();
        cs.areaErrorMessage();
        cs.buildingErrorMessage();
        cs.makaniErrorMessage();
    }

    @Then("^validate error message for payment fields$")
    public void validateErrorMessageForPaymentFields() {
        cs = new CheckOutScreen(driver);
        cs.validateCardNumberErrorMessage();
        cs.validateNameOnCardErrorMessage();
        cs.validateExpiryMonthErrorMessage();
        cs.validateExpiryYearErrorMessage();
        cs.validateEnterYourCVV();
    }

}
