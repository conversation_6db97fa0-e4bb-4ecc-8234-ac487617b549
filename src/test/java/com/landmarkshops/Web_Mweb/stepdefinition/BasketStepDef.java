package com.landmarkshops.Web_Mweb.stepdefinition;

import com.landmarkshops.Web_Mweb.launcher.TestHarness;
import com.landmarkshops.Web_Mweb.pageobjects.BasketScreen;
import cucumber.api.java.en.And;
import cucumber.api.java.en.Then;
import org.testng.Assert;

public class BasketStepDef extends TestHarness {

    BasketScreen bs;



    @And("^Verify user landed to basket page$")
    public void verifyUserLandedToBasketPage() {
         bs= new BasketScreen(driver);
         bs.navigateToCartPage();

    }

    @And("^Verify product details in basket page$")
    public void verifyProductDetailsInBasketPage() {
        bs= new BasketScreen(driver);
        bs.verifyYourShoppingBasket();
    }

    @And("^Verify all UI components in basket page$")
    public void verifyAllUIComponentsInBasketPage() {
        bs= new BasketScreen(driver);
        bs.verifyProductHeaderSection();
       //bs.verifyWarrantySection();
        bs.verifySubtotalSection();
        bs.verifyCardSection();
        bs.verifyPromoAndGiftCardSection();
        bs.verifyNeedHelpSection();


    }

    @Then("^change quantity as '([^']*)' for the product$")
    public void changeQuantityAsForTheProduct(String quantity) throws Throwable {
        bs= new BasketScreen(driver);
        bs.getQuantityValue();
        bs.updateQuantity();
    }

    @And("^verify updated product quantity value as '([^']*)'$")
    public void verifyUpdatedProductQuantityValueAs(String updateQuantity) throws Throwable {
        bs= new BasketScreen(driver);;
        Assert.assertEquals(bs.getQuantityValue(),updateQuantity,"updated quantity not same");
    }

    @Then("^remove product from the cart page$")
    public void removeProductFromTheCartPage() {
        bs= new BasketScreen(driver);
        bs.removeProduct();
    }

    @And("^verify removed product is not displayed$")
    public void verifyRemovedProductIsNotDisplayed() {
        bs= new BasketScreen(driver);
        bs.verifyProductRemovedFromCartPage();
    }

    @Then("^User click on checkout now$")
    public void userClickOnCheckoutNow() {
        bs = new BasketScreen(driver);
        bs.checkoutButton();
    }

    @And("^User click on Use it here link$")
    public void userClickOnUseItHereLink() {
        bs = new BasketScreen(driver);
        bs.clickUseItHereLink();

    }



    @And("^User click on apply promo$")
    public void userClickOnApplyPromo() {
        bs = new BasketScreen(driver);
        bs.applyVoucher();
    }

    @Then("^enter promo code to input as '([^']*)'$")
    public void enterPromoCodeToInputAs(String promoCode) throws Throwable {
        bs = new BasketScreen(driver);
        bs.enterPromoTextBoxPresent();
        bs.enterPromoCode(promoCode);
    }
}
