package com.landmarkshops.Web_Mweb.launcher;

import com.google.common.collect.ImmutableMap;
import com.landmarkshops.Web_Mweb.config.config;
import com.landmarkshops.Web_Mweb.utilities.BaseURLBean;
import com.landmarkshops.Web_Mweb.utilities.TestData;
import io.appium.java_client.AppiumDriver;
//import io.appium.java_client.MobileElement;
import io.appium.java_client.android.AndroidDriver;
import io.appium.java_client.ios.IOSDriver;
//import io.appium.java_client.remote.AndroidMobileCapabilityType;
//import io.appium.java_client.remote.MobileCapabilityType;
import io.appium.java_client.remote.MobilePlatform;
import io.appium.java_client.remote.options.BaseOptions;
import io.appium.java_client.service.local.AppiumDriverLocalService;
import io.appium.java_client.service.local.AppiumServiceBuilder;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.openqa.selenium.*;
import org.openqa.selenium.chrome.ChromeDriver;
import org.openqa.selenium.chrome.ChromeOptions;
import org.openqa.selenium.remote.Browser;
import org.openqa.selenium.remote.CapabilityType;
import org.openqa.selenium.remote.DesiredCapabilities;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.openqa.selenium.safari.SafariDriver;
import io.github.bonigarcia.wdm.WebDriverManager;


import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

public class WebDriverInstance {
	public WebDriver driver;

	DesiredCapabilities capabilities;
	private AppiumDriverLocalService service;
	private static Logger log = LogManager.getLogger(Logger.class.getName());
	Map<String,String> deviceEmulation = new HashMap<String, String>();
	ChromeOptions chromeOptions = new ChromeOptions();
	ChromeOptions chromeMobileOptions = new ChromeOptions();


	/*
	 * This code enable test case parallel execution. public
	 * WebDriverInstance(WebDriver driver) { this.driver = driver; string browser }
	 */
/*		WebDriverManager.chromedriver().setup();
		WebDriverManager.firefoxdriver().setup();
		WebDriverManager.edgedriver().setup();
		WebDriverManager.operadriver().setup();
		WebDriverManager.chromiumdriver().setup()
		WebDriverManager.iedriver().setup();*/


	public WebDriver launchBrowser() throws InterruptedException, IOException {
//		 DesiredCapabilities ds= DesiredCapabilities.chrome();
//		ds.setJavascriptEnabled(true);
//		ds.acceptInsecureCerts();
//		ds.setCapability(CapabilityType.ACCEPT_INSECURE_CERTS,true);
//		ds.setCapability(CapabilityType.ACCEPT_SSL_CERTS,true);
//		ds.setCapability(CapabilityType.SUPPORTS_JAVASCRIPT,true);
//		ds.setCapability(CapabilityType.ENABLE_PROFILING_CAPABILITY,true);
		String browser = config.getBrowser();
		System.setProperty("cucumber.reporting.config.buildNumber", "123");
		switch (browser.toUpperCase()) {

		case "FIREFOX":
//			Need to fix error
//			//case "FIREFOX":
//			WebDriverManager.firefoxdriver().setup();
//			FirefoxOptions ffOptions = new FirefoxOptions();
//			ffOptions.merge(getFireFoxDriverCapabilites());
//			driver = new FirefoxDriver(ffOptions);
//			driver.get(config.getAuthenticationURL());
//			driver.manage().window().maximize();
//			break;
		case "CHROME":
			//System.setProperty("webdriver.chrome.driver", System.getProperty("user.dir") + "/chromeDriver/chromedriver");
			//System.setProperty("webdriver.chrome.driver", System.getProperty("user.dir") + "/windowsDriver/chromedriver.exe");

			WebDriverManager.chromedriver().setup();


			//WebDriverManager.chromedriver().setup();


//			ChromeOptions options = new ChromeOptions();
//			options.setExperimentalOption("excludeSwitches", Collections.singletonList("enable-automation"));
//			options.setExperimentalOption("useAutomationExtension", false);
//			options.addArguments("start-maximized");

//			chromeOptions.addArguments("--start-maximized");
//			chromeOptions.setExperimentalOption("excludeSwitches", List.of("disable-popup-blocking"));
			//WebDriver driver = new ChromeDriver();
			//driver.get("https://www.google.com/");



//			//chromeOptions.merge(ds);Dec
//			chromeOptions.addArguments("--disable-notifications");
//			//chromeOptions.merge(getChromeDriverCapabilites());
////			chromeOptions.addArguments("--disable-gpu");
//			chromeOptions.addArguments("--incognito");
////			chromeOptions.addArguments("window-size=1920,1080");
////			chromeOptions.addArguments("disable-infobars");
//			chromeOptions.setPageLoadStrategy(PageLoadStrategy.EAGER);
//			chromeOptions.addArguments("--enable-automation");
//			chromeOptions.setAcceptInsecureCerts(true);
////			chromeOptions.addArguments("--user-agent=AASA-Bot/1.0.0");// This line is added for handling Bot Error
//			chromeOptions.addArguments("--disable-gpu");
//			WebDriverManager.chromedriver().forceDownload().setup();



//			chromeOptions.addArguments("start-maximized");
			chromeOptions.addArguments("--disable-notifications");
			chromeOptions.addArguments("--remote-allow-origins=*");
			chromeOptions.addArguments("--user-agent=AASA-Bot/1.0.0");
			chromeOptions.addArguments("X-Forwaded-For=LMD-PT");
			chromeOptions.addArguments("User-Agent=Landmak123");
			//chromeOptions.addArguments("X-Context-Request={ 'applicationId': '102',  'tenantId': '5DF1363059675161A85F576D' }");
			driver = new ChromeDriver(chromeOptions);
			//driver.get(config.getAuthenticationURL());
			driver.get(BaseURLBean.getUrl());
			log.info("The driver version is " + config.getBrowser());
			driver.manage().window().setSize(new Dimension(1410, 1100));
			driver.manage().timeouts().implicitlyWait(10, TimeUnit.SECONDS);
			driver.manage().timeouts().pageLoadTimeout(25, TimeUnit.SECONDS);

			



//			chromeOptions.merge(ds);
//			chromeOptions.addArguments("--disable-notifications");
//			chromeOptions.merge(getChromeDriverCapabilites());
////			chromeOptions.addArguments("--disable-gpu");
//			chromeOptions.addArguments("--incognito");
////			chromeOptions.addArguments("window-size=1920,1080");
////			chromeOptions.addArguments("disable-infobars");
//			chromeOptions.setPageLoadStrategy(PageLoadStrategy.EAGER);
//			chromeOptions.addArguments("--enable-automation");
//			chromeOptions.setAcceptInsecureCerts(true);
////			chromeOptions.addArguments("--user-agent=AASA-Bot/1.0.0");// This line is added for handling Bot Error
//			chromeOptions.addArguments("--disable-gpu");
//			WebDriverManager.chromedriver().forceDownload().setup();
//			//driver = new RemoteWebDriver(new URL("http://10.44.10.6:8080/"), chromeOptions);
//			driver = new ChromeDriver(chromeOptions);
//			driver.get(config.getAuthenticationURL());
//			log.info("The driver version is " + config.getBrowser());
//			driver.manage().window().setSize(new Dimension(1410, 1100));
//			driver.manage().timeouts().implicitlyWait(10, TimeUnit.SECONDS);
//			driver.manage().timeouts().pageLoadTimeout(25, TimeUnit.SECONDS);
			break;

//			case "CHROME":
//
//				System.setProperty("webdriver.chrome.driver", System.getProperty("user.dir") + "/chromeDriver/chromedriver");
//				chromeOptions.merge(ds);
//				chromeOptions.addArguments("--disable-notifications");
//				chromeOptions.merge(getChromeDriverCapabilites());
////			chromeOptions.addArguments("--disable-gpu");
//				chromeOptions.addArguments("--incognito");
////			chromeOptions.addArguments("window-size=1920,1080");
////			chromeOptions.addArguments("disable-infobars");
//				chromeOptions.setPageLoadStrategy(PageLoadStrategy.EAGER);
//				chromeOptions.addArguments("--enable-automation");
//				chromeOptions.setAcceptInsecureCerts(true);
////			chromeOptions.addArguments("--user-agent=AASA-Bot/1.0.0");// This line is added for handling Bot Error
//				chromeOptions.addArguments("--disable-gpu");
//				//WebDriverManager.chromedriver().forceDownload().setup();
//				//driver = new RemoteWebDriver(new URL("http://10.44.10.6:8080/"), chromeOptions);
//				driver = new ChromeDriver(chromeOptions);
//				driver.get(config.getAuthenticationURL());
//				log.info("The driver version is " + config.getBrowser());
//				driver.manage().window().setSize(new Dimension(1410, 1100));
//				driver.manage().timeouts().implicitlyWait(10, TimeUnit.SECONDS);
//				driver.manage().timeouts().pageLoadTimeout(25, TimeUnit.SECONDS);
//				break;



			case "MOBILEWEBCHROMEANDROID":

				deviceEmulation.put("deviceName", "Pixel 2");
				//chromeOptions.addArguments("--disable-notifications");
				//chromeOptions.merge(getChromeDriverCapabilites());
				chromeOptions.addArguments("--disable-blink-features=AutomationControlled");
				chromeOptions.setExperimentalOption("mobileEmulation", deviceEmulation);
				//chromeOptions.merge(ds);
				driver = new ChromeDriver(chromeOptions);
				driver.get(config.getAuthenticationURL());
				log.info("The driver version is " + config.getBrowser());
				Thread.sleep(3000);
				driver.manage().window().setSize(new Dimension(1410, 1100));
				driver.manage().timeouts().implicitlyWait(10, TimeUnit.SECONDS);
				driver.manage().timeouts().pageLoadTimeout(100, TimeUnit.SECONDS);
				break;

			case "MOBILEWEBCHROMEIOS":
				deviceEmulation.put("deviceName", "iPhone X");
				//chromeOptions.merge(ds);
				chromeOptions.addArguments("--disable-notifications");
				chromeOptions.setAcceptInsecureCerts(true);
				//chromeOptions.merge(getChromeDriverCapabilites());
				chromeOptions.addArguments("--disable-blink-features=AutomationControlled");
				chromeOptions.setExperimentalOption("mobileEmulation", deviceEmulation);
				driver = new ChromeDriver(chromeOptions);
				driver.get(config.getAuthenticationURL());
				log.info("The driver version is " + config.getBrowser());
				Thread.sleep(3000);
				driver.manage().window().setSize(new Dimension(1410, 1100));
				driver.manage().timeouts().implicitlyWait(10, TimeUnit.SECONDS);
				driver.manage().timeouts().pageLoadTimeout(100, TimeUnit.SECONDS);
				break;
		case "MOBILECHROME":
			log.info("Launching mobile device browser - Chrome for test execution");
			System.out.println("Start appium service");
			this.startAppiumServiceForMobileChrome();
			//chromeOptions.merge(ds);
			chromeOptions.setCapability("autoAcceptAlerts", true);
			//chromeOptions.setCapability(AndroidMobileCapabilityType.AUTO_GRANT_PERMISSIONS, true);
			chromeOptions.addArguments("--disable-notifications");
			chromeOptions.setCapability("autoDismissAlerts", true);
			chromeOptions.setCapability("autoGrantPermissions", true);
			chromeOptions.setCapability("autoAcceptAlerts", true);
			chromeOptions.setAcceptInsecureCerts(true);
			//chromeOptions.merge(getChromeDriverCapabilites());
			chromeOptions.addArguments("--disable-blink-features=AutomationControlled");
			chromeOptions.setExperimentalOption("mobileEmulation", deviceEmulation);
			driver = new AndroidDriver(new URL("http://127.0.0.1:4723"),
					getMobileChromeCapabilities());
			driver.get(config.getURL());
			driver.manage().timeouts().implicitlyWait(25, TimeUnit.SECONDS);
			driver.manage().timeouts().pageLoadTimeout(60, TimeUnit.SECONDS);
			break;

//			Need to fix error
//		case "CHROMEHEADLESS":
//			WebDriverManager.chromedriver().create();
//			Optional<Path> browserPath = WebDriverManager.chromedriver()
//					.getBrowserPath();
//			System.out.println("browser path present or not:--->"+browserPath);
//
//			chromeOptions.merge(ds);
//			ChromeOptions options = new ChromeOptions();
//			options.addArguments("headless");
//			options.addArguments("window-size=1200x600");
//			driver = new ChromeDriver(options);
//			driver.get(config.getAuthenticationURL());
//			log.info("The driver version is " + config.getBrowser());
//			Thread.sleep(3000);
//			break;
//		case "FireFoxHEADLESS":
//			System.setProperty("webdriver.gecko.driver", System.getProperty("user.dir") + "/geckodriver");
//			WebDriverManager.firefoxdriver().setup();
//			FirefoxOptions option = new FirefoxOptions();
//			option.setHeadless(true);
//			driver = new FirefoxDriver(option);
//			driver.get(config.getAuthenticationURL());
//			log.info("The driver version is " + config.getBrowser());
//			Thread.sleep(3000);
//			break;
		case "GRIDCHROME":
			//driver = new RemoteWebDriver(new URL("http://localhost:4444/wd/hub"), getGridChromeCapabilities());
			driver.get(config.getAuthenticationURL());
			Thread.sleep(3000);
			driver.manage().window().setSize(new Dimension(1340, 1040));
			driver.manage().timeouts().pageLoadTimeout(60, TimeUnit.SECONDS);
			break;
		case "GRIDFIREFOX":
			//driver = new RemoteWebDriver(new URL("http://localhost:4444/wd/hub"), getGridFireFoxCapabilities());
			driver.get(config.getAuthenticationURL());
			Thread.sleep(3000);
			driver.manage().timeouts().pageLoadTimeout(60, TimeUnit.SECONDS);
			break;
//			Need to fix error
//		case "IE":
//			//System.setProperty("driver.IE.exe", "IE.exe");
//			WebDriverManager.iedriver().setup();
//			driver.get(config.getAuthenticationURL());
//			log.info("The driver version is " + config.getBrowser());
//			driver.manage().window().maximize();
//			driver.manage().timeouts().pageLoadTimeout(60, TimeUnit.SECONDS);
//			break;
		case "SAFARI":
			//	WebDriverManager.safaridriver().setup();
			driver = new SafariDriver();
			driver.get(config.getAuthenticationURL());
			log.info("The driver version is " + config.getBrowser());
			driver.manage().window().maximize();
			driver.manage().timeouts().pageLoadTimeout(60, TimeUnit.SECONDS);
			break;
		case "MOBILEFIREFOX":
			log.info("Launching mobile device browser - Firefox for test execution");
			System.out.println("Start appium service");
			this.startAppiumService();
			//driver = new AndroidDriver<WebElement>(new URL("http://127.0.0.1:4723"), getMobileFireFoxCapabilities());
			driver.get(config.getURL());
			driver.manage().timeouts().pageLoadTimeout(60, TimeUnit.SECONDS);
			break;
		case "MOBILESAFARI":
			System.out.println("Before start appium server");
			log.info("Launching mobile device browser - Safari for test execution");
			this.startAppiumService();
			//driver = new IOSDriver<WebElement>(new URL("http://127.0.0.1:4723/wd/hub"), getMobileSafariCapabilities());
			driver.get(config.getURL());
			loginServer(TestData.getServerUserNameAndPassword());
			break;
		default:
			log.info("Browser not supported");
			driver.quit();
		}
		log.info("");
		return driver;

	}

//	private Capabilities getMobileFireFoxCapabilities() {
////		capabilities = new DesiredCapabilities();
////		capabilities.setCapability(MobileCapabilityType.DEVICE_NAME, "Android");
////		capabilities.setCapability(MobileCapabilityType.NEW_COMMAND_TIMEOUT, 40);
////		capabilities.setCapability(MobileCapabilityType.BROWSER_NAME, "Firefox");
////		capabilities.setCapability(MobileCapabilityType.APP, "com.android.Firefox");
////		capabilities.setCapability(MobileCapabilityType.TAKES_SCREENSHOT, "true");
////		return capabilities;
//	}

	private Capabilities getMobileChromeCapabilities() {
		BaseOptions options = new BaseOptions()
				.setPlatformName("Android")
				.setAutomationName("UiAutomator2")
				.amend("appium:browserName", "Chrome")
				.amend("TAKES_SCREENSHOT", "true")
				.amend("deviceName","emulator-5554")
				.amend("appium_driver_auto_download_chromedriver",true);
		return options;
	}

//	private Capabilities getMobileSafariCapabilities() {
////		capabilities = new DesiredCapabilities();
////		capabilities.setCapability(MobileCapabilityType.DEVICE_NAME, config.getIphoneDeviceName());
////		capabilities.setCapability(MobileCapabilityType.AUTOMATION_NAME, config.getAutomationName());
////		capabilities.setCapability(MobileCapabilityType.BROWSER_NAME, BrowserType.SAFARI);
////		capabilities.setCapability(MobileCapabilityType.NEW_COMMAND_TIMEOUT, 150);
////		capabilities.setCapability(MobileCapabilityType.PLATFORM_NAME, MobilePlatform.IOS);
////		capabilities.setCapability(MobileCapabilityType.FULL_RESET, true);
////		capabilities.setCapability(MobileCapabilityType.UDID, config.getUdid());
////		capabilities.setCapability(MobileCapabilityType.PLATFORM_VERSION, config.getPlatformVersion());
////		return capabilities;
//	}

//	private Capabilities getGridFireFoxCapabilities() {
////		capabilities = DesiredCapabilities.firefox();
////		capabilities.setBrowserName("firefox");
////		capabilities.setPlatform(Platform.ANY);
////		capabilities.setCapability("AUTOMATION NAME", "LandmarkShops Web Automation");
////		capabilities.setJavascriptEnabled(true);
////		return capabilities;
//	}

//	public DesiredCapabilities getFireFoxDriverCapabilites() {
//
//		//System.setProperty("webdriver.gecko.driver", System.getProperty("user.dir") + "/geckodriver");
//
////		capabilities = new DesiredCapabilities();
////		capabilities.setCapability("AUTOMATION NAME", "LandmarkShops Web Automation");
////		capabilities.setCapability("marionette", true);
////		capabilities.setJavascriptEnabled(true);
////		log.info("Launching: " + capabilities.getCapability("AUTOMATION NAME"));
////		log.info("The platform is " + capabilities.getPlatform());
////		log.info("Running tests on Browser: " + capabilities.getBrowserName());
////		log.info(capabilities.getVersion());
////		log.info(capabilities.getCapability("profile"));
////		return capabilities;
//	}
	/*
	 * To Connect Proxy Server .addPreference("network.proxy.type", 1)
	 * .addPreference("network.proxy.http", networkServerProxy)
	 * .addPreference("network.proxy.http_port",8080)
	 */

//	public DesiredCapabilities getChromeDriverCapabilites() {
//		//System.setProperty("webdriver.chrome.driver", System.getProperty("user.dir") + "/chromedriver");
////		capabilities = new DesiredCapabilities();
////		capabilities.setCapability("AUTOMATION NAME", "LandmarkShops Web Automation");
////		capabilities.setJavascriptEnabled(true);
////		capabilities.setCapability(CapabilityType.ACCEPT_SSL_CERTS, true);
////		log.info("Launching: " + capabilities.getCapability("AUTOMATION NAME"));
////		capabilities.setPlatform(Platform.MAC);
////		log.info("The platform is " + "MAC");
////		log.info("Running tests on Browser: " + capabilities.getBrowserName());
////		log.info(capabilities.getVersion());
////		return capabilities;
//	}

	// log.info(capabilities.getCapability("profile"));

	// log.info(capabilities.getCapability("profile"));

	/*
	 * * To Connect Proxy Server Proxy proxy = new Proxy();
	 * proxy.setHttpProxy(networkServerProxy+":"+Port);
	 * capabilities.setCapability("proxy", proxy);
	 */
	// capabilities.setCapability("version", "51.0.1 ");

//	public DesiredCapabilities getGridChromeCapabilities() {
////		capabilities = DesiredCapabilities.chrome();
////		capabilities.setBrowserName("chrome");
////		capabilities.setPlatform(Platform.ANY);
////		capabilities.setCapability("AUTOMATION NAME", "LandmarkShops Web Automation");
////		capabilities.setJavascriptEnabled(true);
////		return capabilities;
//	}

	public void startAppiumService() throws IOException, InterruptedException {
		service = AppiumDriverLocalService

				.buildService(new AppiumServiceBuilder().usingDriverExecutable(new File("/usr/local/bin/node"))
						.withAppiumJS(new File("/usr/local/lib/node_modules/appium/build/lib/main.js"))
						.withIPAddress("127.0.0.1").usingPort(4723).withLogFile(new File("/tmp/AppiumLogs.txt"))

				);

		service.start();
		Thread.sleep(1000);

	}

	public void startAppiumServiceForMobileChrome() throws IOException, InterruptedException {

		Integer port1=Integer.parseInt(config.getPort());
		service = AppiumDriverLocalService
//
//				.buildService(new AppiumServiceBuilder().usingDriverExecutable(new File("/usr/local/bin/node"))
//						.withAppiumJS(new File("/usr/local/lib/node_modules/appium/build/lib/main.js"))
//						.withIPAddress("127.0.0.1").usingPort(4723).withLogFile(new File("/tmp/AppiumLogs.txt"))
//						.withArgument(() -> "--allow-insecure","chromedriver_autodownload"));
//		service.start();

				.buildService(new AppiumServiceBuilder().usingDriverExecutable(new File("/opt/homebrew/bin/node"))
				.withAppiumJS(new File("/usr/local/lib/node_modules/appium/build/lib/main.js"))
				.withIPAddress("127.0.0.1").usingPort(4723).withLogFile(new File("/tmp/AppiumLogs.txt")).withArgument(() -> "--allow-insecure","chromedriver_autodownload"));
		service.start();


		/*service = AppiumDriverLocalService

				.buildService(new AppiumServiceBuilder().usingDriverExecutable(new File("/opt/homebrew/bin/node"))
						.withAppiumJS(new File("/opt/homebrew/lib/node_modules/appium/build/lib/main.js"))
						.withIPAddress("127.0.0.1").usingPort(port1).withLogFile(new File("/tmp/AppiumLogs.txt")).withArgument(() -> "--allow-insecure","chromedriver_autodownload"));
		service.start();*/
		Thread.sleep(1000);



		///opt/homebrew/lib/node_modules/appium
	}

	// Fill user name & password to login to browser
	private void loginServer(HashMap<String, String> hmap) throws InterruptedException {
		//AppiumDriver<MobileElement> iPhone = (AppiumDriver<MobileElement>) driver;
//		iPhone.context("NATIVE_APP");
//		///HomePage hp = new HomePage(iPhone);
//		//hp.clickSafariUsername();
//		iPhone.switchTo().activeElement().sendKeys(hmap.get("username") + "\n");
//		iPhone.switchTo().activeElement().sendKeys(hmap.get("password") + "\n");
//		Set<String> contextNames = iPhone.getContextHandles();
//		iPhone.context((String) contextNames.toArray()[1]);
//		System.out.println("loginServer - Done");
//		driver = iPhone;
	}

	public void stopAppiumService() {
		service.stop();
	}
}
