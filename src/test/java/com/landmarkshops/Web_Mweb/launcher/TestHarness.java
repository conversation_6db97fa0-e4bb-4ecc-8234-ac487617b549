/*
#  TestHarness
#  LandmarkGroup
#  Created by <PERSON><PERSON><PERSON><PERSON> on 01/2/16.
#  Copyright (c) 2016 LandmarkGroup . All rights reserved.
 */

package com.landmarkshops.Web_Mweb.launcher;

import com.aventstack.extentreports.ExtentReports;
import com.aventstack.extentreports.ExtentTest;
import com.aventstack.extentreports.Status;
//import com.aventstack.extentreports.reporter.ExtentHtmlReporter;
//import com.aventstack.extentreports.reporter.configuration.ChartLocation;
//import com.aventstack.extentreports.reporter.configuration.ChartLocation;
import com.aventstack.extentreports.reporter.configuration.Theme;
import com.landmarkshops.Web_Mweb.config.config;
import com.landmarkshops.Web_Mweb.utilities.BaseURLBean;
import com.landmarkshops.Web_Mweb.utilities.StepContext;
import com.lmg.blc.web.PageObjects.FooterPage;
import com.lmg.blc.web.PageObjects.HeaderSectionPage;
import io.appium.java_client.service.local.AppiumDriverLocalService;
import io.appium.java_client.service.local.AppiumServiceBuilder;
import org.apache.commons.io.FileUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.bson.Document;
import org.openqa.selenium.OutputType;
import org.openqa.selenium.TakesScreenshot;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.logging.LogEntries;
import org.openqa.selenium.logging.LogEntry;
import org.openqa.selenium.logging.LogType;
import org.testng.ITestResult;
import org.testng.annotations.*;

import java.io.*;
import java.lang.reflect.Method;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.Properties;
import java.util.Random;
import java.util.concurrent.TimeUnit;


public class TestHarness {
    static String reportPath;
    public WebDriverInstance webDriverInstance;
    public static WebDriver driver;
    public static String country;
    private static String appWebURL;
    public static String URL;
    //private MongoCollection<Document> coll;
//    protected ArrayList<Document> testResults;
//    Document testReportObject;
    LoadConfigurations load;

    LoadConfigurations loadL18n;
    public static Properties configProperties;
    String path;
    String name;
    static String Createdreport;
    public static ExtentReports extent;
    private AppiumDriverLocalService service;
    // static ExtentHtmlReporter htmlReporter;
    protected static Logger log = LogManager.getLogger(Logger.class.getName());
    Properties prop = new Properties();
    private static SimpleDateFormat calendarDate;
    private static ExtentTest test;
    private FileWriter file;
    private PrintWriter output;
    //MongoClientTestClass mongodb = new MongoClientTestClass();
    private static final String testStart = "Test Execution started:";
    private static final String star = "************";
    private static final String testComplete = "Test Execution completed for Test:";

    HeaderSectionPage hsp;
    FooterPage fp;

    String browser;

    public ArrayList<Document> createDocument() {
        ArrayList<Document> testResults = new ArrayList<Document>();
        return testResults;
    }


    @BeforeSuite
    public void suiteSetUp() throws Exception {
        log.info("load configuration files " + reportPath);
        loadconfigurations("project.properties");
        loadProductURLConfigurations("pdp_urls.properties");
        loadLanguageProperties();
        deleteScreenshots("Screenshots");
        webDriverInstance = new WebDriverInstance();
        driver = webDriverInstance.launchBrowser();
        //changeCountry();
        changeLanguage();
        setReLaunchURL();

    }


    private void setReLaunchURL() {
        hsp = new HeaderSectionPage(driver);
        config.setReLaunchURL(hsp.getBrowserURL());
        System.out.println("Re Launch URL as "+config.getReLaunchURL());
    }


    @BeforeClass
    public void beforeClass() throws Exception, IOException {
        //testResults = createDocument();
        /*
         * This code enable test case parallel execution. //@Parameters({"browser"})
         * //public WebDriver beforeClass(String browser) throws Exception, IOException
         * { if(driver == null){ webDriverInstance = new WebDriverInstance(driver);
         * driver = webDriverInstance.launchBrowser(browser); return driver; } else{
         * return driver; }
         */
    }

    /*
     * This code enable test case parallel execution. Close the current driver
     * instance
     *
     * @AfterClass public void afterClass(){ driver.close(); }
     */

    /*
     * Preparation Of Extent Report
     */
//	@BeforeMethod
//	public void beforeMethod(Method caller) {
//		String TestName = caller.getName().replace("_", " ");
//		TestName = "Test " + TestName;
//		log.info(star + testStart + TestName + star + "\n");
//		String reportPath = System.getProperty("user.dir") + "/TestResults/cucumber-reports/" + "TestAutomation.html";
//		test = extent.createTest(TestName);
//		test.log(Status.INFO, "Test execution started to " + TestName);
//	}

    /*
     * Saving The Logs And Appending Extent Report to Mongo DB
     */
//	@AfterMethod
//	public void afterMethod(ITestResult result) throws IOException {
//		String testName = (String) result.getTestContext().getAttribute("description");
//		browser = config.getBrowser();
//		if (result.isSuccess()) {
//			log.info(result.getTestContext().getAttribute("description") + " ---------->has Passed");
//			Document testResultObject = new Document("status", "PASSED");
//			testResultObject.append("description", result.getTestContext().getAttribute("description"));
//			testResults.add(testResultObject);
//			analyzeBrowserLog(log.getClass().getSimpleName());
//			test.log(Status.PASS, (String) result.getTestContext().getAttribute("description") + " has Passed");
//			if (!(browser.equals("MOBILECHROME")) && !(browser.equals("MOBILESAFARI"))) {
//				String screenShotPath = saveScreenshot(driver, testName, ITestResult.SUCCESS);
//				test.pass("Snapshot is: " + test.addScreenCaptureFromPath(screenShotPath));
//			}
//		}else if (result.getStatus() == ITestResult.FAILURE) {
//			log.info(result.getTestContext().getAttribute("description") + "~~~~~~~~~~~> has Failed");
//			test.log(Status.FAIL, (String) result.getTestContext().getAttribute("description") + " has failed");
//			if (!(browser.equals("MOBILECHROME"))&& !(browser.equals("MOBILESAFARI"))) {
//				String screenShotPath = saveScreenshot(driver, testName, ITestResult.FAILURE);
//				test.fail("Snapshot is: " + test.addScreenCaptureFromPath(screenShotPath));
//			}
//			log.error("~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~"
//					+ result.getTestContext().getAttribute("description") + " has Failed ~~~~~~~~~~~~~~~~~~~~~~~~~~~~");
//			log.error(result.getThrowable());
//			Throwable t = result.getThrowable();
//			for (StackTraceElement st : t.getStackTrace()) {
//				if (st.toString().contains("com.landmarkshops"))
//					log.error(st.toString());
//			}
//			Document testResultObject = new Document("status", "FAILED");
//			testResultObject.append("description", result.getTestContext().getAttribute("description") + " has Failed");
//			testResults.add(testResultObject);
//		//	if (browser.equals("MOBILESAFARI") && browser.equals("MOBILECHROME"))
//				analyzeBrowserLog(log.getClass().getSimpleName());
//
//		} else if (result.getStatus() == ITestResult.SKIP) {
//			log.info(result.getTestContext().getAttribute("description") + "------------>has Skipped");
//			test.log(Status.SKIP, (String) result.getTestContext().getAttribute("description") + " has Skipped");
//			Document testResultObject = new Document("status", "SKIP");
//			testResultObject.append("description", result.getTestContext().getAttribute("description"));
//			testResults.add(testResultObject);
//		//	if (browser.equals("MOBILESAFARI") && browser.equals("MOBILECHROME"))
//				analyzeBrowserLog(log.getClass().getSimpleName());
//		}
//		log.info(star + testComplete + result.getTestContext().getAttribute("description") + star + "\n");
//		extent.flush();
//	}

    @AfterSuite
    public void stopTest() {
        String reportPath = System.getProperty("user.dir") + "/TestResults/cucumber-reports/" + "TestAutomation.html";
        log.info("Report Path Location:  " + reportPath);
        //EmailReporter.Reportgeneration(reportPath);
        browser = config.getBrowser();
        if (browser.equals("MOBILESAFARI") && browser.equals("MOBILECHROME")) {
            webDriverInstance.stopAppiumService();
        } else {
            driver.quit();
        }


//}
//		String reportPath = System.getProperty("user.dir") + "/TestResults/" + Createdreport;
//		log.info("Report Path Location:  " + reportPath);
//		EmailReporter.Reportgeneration(reportPath);
//
//		if (!config.getExecutionType().equals("Dry")) {
//			log.info(star + "Test Execution Completed For Tests.Generating Reports" + star);
//			Document testReportObject = new Document("executionID", WebUtils.getRandomNumber());
//			testReportObject.append("HostName", context.getHost());
//			testReportObject.append("PLATFORM", config.getPlatform());
//			testReportObject.append("executiondate", calendarDate.format(new Date()));
//			testReportObject.append("totaltestsfailed", context.getFailedTests().size());
//			testReportObject.append("totaltestspassed", context.getPassedTests().size());
//			testReportObject.append("country", config.getCountry());
//			testReportObject.append("htmlReportPath", path);
//			testReportObject.append("testResults", testResults);
//		//	coll = mongodb.mongoclientcall();
//		//	coll.insertOne(testReportObject);
//			//mongodb.closeMongoConnection();
//		}


    }


//    	public static ExtentReports createReport() {
//		calendarDate = new SimpleDateFormat("yyyy.MMMMM.dd hh:mm:aaa");
//		Createdreport = config.getPlatform() + " " + config.getCountry() + " " + config.getConcept() + " "
//				+ "TestAutomation" + calendarDate.format(new Date()) + ".html";
//		return TestHarness.createTestResult(Createdreport);
//	}

    public String loadconfigurations(String configFileName) {

        log.info("Method called for loading config file.");
        load = new LoadConfigurations();
        return load.LoadConfigurationsProperties(configFileName);

    }



    public void loadProductURLConfigurations(String configFileName) {

        log.info("Method called for loading config file.");
        load = new LoadConfigurations();
        load.LoadProductURLConfigurationsProperties(configFileName);

    }

    public void Wait(int Seconds) {
        int miliseconds;
        try {
            miliseconds = Seconds * 1000;
            Thread.sleep(miliseconds);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public String getUATURL() throws Exception {
        Properties prop = new Properties();
        String fileName = "src/test/java/com/landmarkshops/Web_Mweb/Resources/project.properties";
        FileInputStream fin = new FileInputStream(fileName);
        prop.load(fin);
        appWebURL = prop.getProperty("UAT_URL");
        return appWebURL;
    }

    // Clean ScreenShot Directory[Files]
    public void deleteScreenshots(String fileName) throws IOException {

        File file = new File(System.getProperty("user.dir") + "/" + fileName + "/");
        String[] myFiles;
        if (file.isDirectory()) {
            myFiles = file.list();
            for (int i = 0; i < myFiles.length; i++) {
                File myFile = new File(file, myFiles[i]);
                myFile.delete();
            }

        }
    }

    public String loadLanguageProperties() {
        loadL18n = new LoadConfigurations();
        String lang = config.getLanguage().trim();
        if (lang.equalsIgnoreCase("AR")) {

            configProperties = loadL18n.loadL18nProperties("base_ar.properties");
        } else {

            configProperties = loadL18n.loadL18nProperties("base_en.properties");
        }

        return lang;
    }

    public static int getRandomNumber() {

        Random rnd = new Random();
        int id = rnd.nextInt(5000);
        return id;

    }

    // Capturing Screenshot For Failed
    public void screenshotFailedTests(String Screenshotname) {
        try {
            File srcPath = ((TakesScreenshot) driver).getScreenshotAs(OutputType.FILE);

            FileUtils.copyFile(srcPath,
                    new File(System.getProperty("user.dir") + "/screenshotsFailedTests/" + Screenshotname + ".png"));
        } catch (Exception e) {
            log.error("Exception while taking a screenshot" + Screenshotname + e.getMessage());
        }
    }

    public void startAppiumService() throws IOException, InterruptedException {
        service = AppiumDriverLocalService

                .buildService(new AppiumServiceBuilder().usingDriverExecutable(new File("/usr/local/bin/node"))
                        .withAppiumJS(new File("/usr/local/lib/node_modules/appium/build/lib/main.js"))
                        .withIPAddress("127.0.0.1").usingPort(4723).withLogFile(new File("/tmp/AppiumLogs.txt")));
        service.start();
        Thread.sleep(1000);

    }


    /**
     * @param
     */
//	public static ExtentReports createTestResult(String fileName) {
//		htmlReporter = new ExtentHtmlReporter(System.getProperty("user.dir") + "/TestResults/" + fileName);
//		htmlReporter.config().setTestViewChartLocation(ChartLocation.TOP);
//		htmlReporter.config().setChartVisibilityOnOpen(true);
//		htmlReporter.config().setTheme(Theme.DARK);
//		htmlReporter.config().setDocumentTitle(fileName);
//		htmlReporter.config().setEncoding("utf-8");
//		htmlReporter.config().setReportName(fileName);
//		htmlReporter.start();
//		htmlReporter.setAppendExisting(true);
//		htmlReporter.loadXMLConfig(System.getProperty("user.dir") + "/extentconfig.xml");
//		extent = new ExtentReports();
//		extent.attachReporter(htmlReporter);
//		return extent;
//	}

//    @BeforeClass
//    public ExtentReports testSetUp() throws Exception { return createReport(); }

    /*
     * Save Screenshot
     */
    public String saveScreenshot(WebDriver driver, String screenShotName, int resultStatus) throws IOException {

        File srcPath = ((TakesScreenshot) driver).getScreenshotAs(OutputType.FILE);
        String dest = null;
        if (resultStatus == 1) {
            dest = System.getProperty("user.dir") + "/Screenshots/" + screenShotName + getCurrentTime() + ".jpg";
            File finalDestination = new File(dest);
            FileUtils.copyFile(srcPath, finalDestination);
        } else if (resultStatus == 2) {
            dest = System.getProperty("user.dir") + "/screenshotsFailedTests/" + screenShotName + getCurrentTime()
                    + ".jpg";
            File finalDestination = new File(dest);
            FileUtils.copyFile(srcPath, finalDestination);
        }
        return dest;
    }

    /*
     * Get Current Time
     */
    public Timestamp getCurrentTime() {
        Timestamp timestamp = new Timestamp(System.currentTimeMillis());
        System.out.println("Current timestamp is: " + timestamp);
        return timestamp;
    }

    // Capturing Screenshot
    public void captureScreenshot(String Screenshotname) {
        try {
            File srcPath = ((TakesScreenshot) driver).getScreenshotAs(OutputType.FILE);

            FileUtils.copyFile(srcPath,
                    new File(System.getProperty("user.dir") + "/Screenshots/" + Screenshotname + ".jpg"));
        } catch (Exception e) {
            log.error("Exception while taking a screenshot" + Screenshotname + e.getMessage());
        }
    }

    public String getCurrentUrl() {

        String url = driver.getCurrentUrl();
        return url;
    }

    public void openurl() throws Exception {
        Thread.sleep(2);
        String URL = config.getURL();
        driver.get(URL + "");
        Thread.sleep(2000);
    }

    public void analyzeBrowserLog(String testcase) throws IOException {
        file = new FileWriter(System.getProperty("user.dir") + "/browserconsole.log", true);
        output = new PrintWriter(file);
        output.println("*************" + testcase + "***************");
        output.println("\n");
        LogEntries logEntries = driver.manage().logs().get(LogType.BROWSER);
        for (LogEntry entry : logEntries) {
            output.println(new Date(entry.getTimestamp()) + "  " + entry.getLevel() + " " + entry.getMessage());
            output.println("\n");
        }
        output.println("\n");
        output.close();
    }

    public void backToPreviousPage() {
        driver.navigate().back();
        driver.manage().timeouts().pageLoadTimeout(15, TimeUnit.SECONDS);
    }

    public void openUrl() throws Exception {
        Thread.sleep(2);
        String URL = config.getURL();
        driver.get(URL + "login");
        driver.manage().timeouts().pageLoadTimeout(15, TimeUnit.SECONDS);
    }

    public void openCurrentUrl() throws Exception {

        String URL = config.getURL();
        driver.get(URL);
        driver.manage().timeouts().pageLoadTimeout(15, TimeUnit.SECONDS);

    }

public void changeLanguage(){
        StepContext.lang = config.getLanguage().trim();
        StepContext.web=(config.getBrowser().equalsIgnoreCase("CHROMEHEADLESS"))||(config.getBrowser().equalsIgnoreCase("CHROME"));

    StepContext.mWeb=(config.getBrowser().equalsIgnoreCase("MOBILEWEBCHROMEANDROID"))||(config.getBrowser().equalsIgnoreCase("MOBILECHROME"));
    if((config.getBrowser().equalsIgnoreCase("MOBILEWEBCHROMEANDROID"))||(config.getBrowser().equalsIgnoreCase("MOBILECHROME"))) {
        hsp = new HeaderSectionPage(driver);
        if (config.getLanguage().equalsIgnoreCase("AR")) {
            hsp.clickOnHamburgerMenu();
            hsp.languageSwitcher();
        }
    }else {
            hsp = new HeaderSectionPage(driver);
            driver.manage().timeouts().pageLoadTimeout(15, TimeUnit.SECONDS);
            if (config.getLanguage().equalsIgnoreCase("AR")) {
                hsp.languageSwitcher(config.getLanguage());
            }
        }

        }


    public void changeCountry() throws Exception {
        if((config.getBrowser().equalsIgnoreCase("MOBILEWEBCHROMEANDROID"))||(config.getBrowser().equalsIgnoreCase("MOBILECHROME"))) {
            String country = config.getCountry();

            hsp = new HeaderSectionPage(driver);
            hsp.clickOnHamburgerMenu();
            hsp.clickOnMwebCountrySwitcher();
            hsp.chooseCountryMobileWeb(country);
            hsp.clickOnDepartment();
            if(config.getLanguage().equalsIgnoreCase("AR")) {
                hsp.clickOnHamburgerMenu();
                hsp.languageSwitcher();
            }

        }else{
            String country = config.getCountry();
            hsp = new HeaderSectionPage(driver);
            hsp.clickOnCountrySwitcher();
            hsp.chooseCountryFromHeader(country);
            driver.manage().timeouts().pageLoadTimeout(15, TimeUnit.SECONDS);
            if(config.getLanguage().equalsIgnoreCase("AR")) {
                hsp.languageSwitcher(config.getLanguage());
            }
        }


    }

    public void openBasketPage() throws Exception {
        Thread.sleep(2);
        String URL = config.getURL();
        driver.get(URL + "cart");
        driver.manage().timeouts().pageLoadTimeout(15, TimeUnit.SECONDS);

    }

    public boolean verifyPageNavigation(String url1, String url2) throws InterruptedException {
        int l1 = url1.length();
        int l2 = url2.length();
        boolean res = (l1 != l2);
        log.info("Navigated To Another Page " + res);
        return res;
    }

}