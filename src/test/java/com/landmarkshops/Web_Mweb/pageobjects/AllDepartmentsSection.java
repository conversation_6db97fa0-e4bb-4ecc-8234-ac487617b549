package com.landmarkshops.Web_Mweb.pageobjects;

import com.landmarkshops.Web_Mweb.utilities.WebUtils;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.testng.asserts.SoftAssert;

public class AllDepartmentsSection extends WebUtils {

	public AllDepartmentsSection(WebDriver driver) {
		super(driver);
		PageFactory.initElements(driver, this);
	}
	

	@FindBy(xpath = "//span[contains(text(),'Mobiles & Accessories')]")
	private WebElement mobileAccElement1stdept;
	
	@FindBy(xpath = "//*[@id='block-emaxae-DeptPage-mobile-v2-Section3BSlot-3']/div/div/div[1]/div/a/div")
		private WebElement banner1stDept;
	

	public boolean clickOnMobilesAccessoriesDept(String deptVal) throws InterruptedException{
		waitForElementPresent(5);
		SoftAssert sa=new SoftAssert();
		sa.assertEquals(mobileAccElement1stdept.getText().trim(),deptVal);
		sa.assertAll();
		mobileAccElement1stdept.click();
		Thread.sleep(2000);
		return true;

	}
	
	public void verifyNavigationLink1stDept() {
		SoftAssert sa=new SoftAssert();
		waitForElementPresent(5);
		sa.assertEquals(stringBuilder(driver), "https://uat1.emax.ae/department/mobile");
		sa.assertAll();
	}
	
	public void clickedOnBanner1stDept() throws InterruptedException {	
		waitForElementPresent(5);
		banner1stDept.click();
	}
	
	public void verifyNavigationLinkBanner1stDept() throws InterruptedException {
		SoftAssert sa=new SoftAssert();
		waitForElementPresent(5);
		sa.assertEquals(stringBuilder(driver), "https://uat1.emax.ae/search?q=Galaxy%20S22%20ultra:allCategories:mobile");
		sa.assertAll();
	}
	
	@FindBy(xpath = "//span[contains(text(),'Computers & Networks')]")
	private WebElement mobileAccElement2nddept;
	
	@FindBy(xpath = "//*[@id='block-emaxae-DeptPage-computers-v2-Section3BSlot-3']/div/div/div[1]/div/a/div")
		private WebElement banner2ndDept;
	
	public boolean clickOnComputerNetworksDept(String deptVal) throws InterruptedException{
		SoftAssert sa=new SoftAssert();
		waitForElementPresent(5);
		sa.assertEquals(mobileAccElement2nddept.getText().trim(),deptVal);
		sa.assertAll();
		mobileAccElement2nddept.click();
		Thread.sleep(1000);
		return true;

	}
	
	public void verifyNavigationLink2ndtDept() {
		SoftAssert sa=new SoftAssert();
		waitForElementPresent(5);
		sa.assertEquals(stringBuilder(driver), "https://uat1.emax.ae/department/computers");
		sa.assertAll();
	}
	
	public void clickedOnBanner2ndDept() throws InterruptedException {
		waitForElementPresent(5);
		banner2ndDept.click();
	}
	
	public void verifyNavigationLinkBanner2ndDept() {
		SoftAssert sa=new SoftAssert();
		waitForElementPresent(5);
		sa.assertEquals(stringBuilder(driver), "https://uat1.emax.ae/shop-laptoptabletandcomputeraccessories-laptops-macbooks");
		sa.assertAll();
	}
	
	@FindBy(xpath = "(//span[contains(text(),'Appliances')])[3]")
	private WebElement ampplianceElement3rddept;
	
	@FindBy(xpath = "//*[@id='hero_banner_wrapper']/div/div/div/div[2]/div/div/a/div")
		private WebElement banner3rdDept;
	
	public void clickOnAppliancesDept(String deptVal) throws InterruptedException{
		SoftAssert sa=new SoftAssert();
		waitForElementPresent(5);
		sa.assertEquals(ampplianceElement3rddept.getText().trim(),deptVal);
		sa.assertAll();
		ampplianceElement3rddept.click();
		Thread.sleep(2000);
		

	}
	
	public void verifyNavigationLink3rddtDept() {
		SoftAssert sa=new SoftAssert();
		waitForElementPresent(5);
		sa.assertEquals(stringBuilder(driver), "https://uat1.emax.ae/department/appliances");
		sa.assertAll();
	}
	
	public void clickedOnBanner3rddDept() throws InterruptedException {	
		waitForElementPresent(5);
		banner3rdDept.click();
	}
	
	public void verifyNavigationLinkBanner3rdDept() {
		SoftAssert sa=new SoftAssert();
		waitForElementPresent(5);
		sa.assertEquals(stringBuilder(driver), "https://uat1.emax.ae/search?q=%3AmanufacturerName%3ADYSON%3AallCategories%3Ahomeappliances%3Aindex%3Aprice");
		sa.assertAll();
	}
	
	@FindBy(xpath = "(//span[contains(text(),'Accessories')])[5]")
	private WebElement accessoriesElement4thdept;
	
	@FindBy(xpath = "//*[@id='block-emaxae-DeptPage-accessories-v2-Section3BSlot-3']/div/div/div[1]/div/a/div")
		private WebElement banner4thDept;
	
	public void clickOnAccessoriesDept(String deptVal) throws InterruptedException{
		SoftAssert sa=new SoftAssert();
		waitForElementPresent(5);
		sa.assertEquals(accessoriesElement4thdept.getText().trim(),deptVal);
		sa.assertAll();
		accessoriesElement4thdept.click();
		Thread.sleep(2000);
	}
	
	public void verifyNavigationLink4thdtDept() {
		SoftAssert sa=new SoftAssert();
		waitForElementPresent(5);
		sa.assertEquals(stringBuilder(driver), "https://uat1.emax.ae/department/accessories");
		sa.assertAll();
	}
	
	public void clickedOnBanner4thdDept() throws InterruptedException {	
		waitForElementPresent(5);
		banner4thDept.click();
	}
	
	public void verifyNavigationLinkBanner4thDept() {
		SoftAssert sa=new SoftAssert();
		waitForElementPresent(5);
		sa.assertEquals(stringBuilder(driver), "https://uat1.emax.ae/shop-mobileaccessories-headsets?q=%3Aindex%3Aprice");
		sa.assertAll();
	}
	
	@FindBy(xpath = "//span[contains(text(),'Audio & Video')]")
	private WebElement audioVideoElement5thdept;
	
	@FindBy(xpath = "//*[@id='block-emaxae-DeptPage-audiovideo-v2-Section3BSlot-3']/div/div/div[1]/div/a/div")
		private WebElement banner5thDept;
	
	public void clickOnAudioVideoDept(String deptVal) throws InterruptedException{
		SoftAssert sa=new SoftAssert();
		waitForElementPresent(5);
		sa.assertEquals(audioVideoElement5thdept.getText().trim(),deptVal);
		sa.assertAll();
		audioVideoElement5thdept.click();
		Thread.sleep(2000);
	}
	
	public void verifyNavigationLink5thdtDept() {
		SoftAssert sa=new SoftAssert();
		waitForElementPresent(5);
		sa.assertEquals(stringBuilder(driver), "https://uat1.emax.ae/department/audiovideo");
		sa.assertAll();
	}
	
	public void clickedOnBanner5thdDept() throws InterruptedException {	
		waitForElementPresent(5);
		banner5thDept.click();
	}
	
	public void verifyNavigationLinkBanner5thDept() {
		SoftAssert sa=new SoftAssert();
		waitForElementPresent(5);
		sa.assertEquals(stringBuilder(driver), "https://uat1.emax.ae/shop-televisionandaudio-television?q=%3Abrand%3ASONY%3Aindex%3Aprice");
		sa.assertAll();
	}
	
	@FindBy(xpath = "(//span[contains(text(),'Tablet')])[2]")
	private WebElement tabletElement6thdept;
	
	@FindBy(xpath = "//*[@id='block-emaxae-DeptPage-tablet-v2-Section3BSlot-3']/div/div/div[1]/div/a/div")
		private WebElement banner6thDept;
	
	public void clickOnTabletDept(String deptVal) throws InterruptedException{
		SoftAssert sa=new SoftAssert();
		waitForElementPresent(5);
		sa.assertEquals(tabletElement6thdept.getText().trim(),deptVal);
		sa.assertAll();
		tabletElement6thdept.click();
		Thread.sleep(2000);	
	}
	
	public void verifyNavigationLink6thdtDept() {
		SoftAssert sa=new SoftAssert();
		waitForElementPresent(5);
		sa.assertEquals(stringBuilder(driver), "https://uat1.emax.ae/department/tablet");
		sa.assertAll();
	}
	
	public void clickedOnBanner6thdDept() throws InterruptedException {	
		waitForElementPresent(5);
		banner6thDept.click();
	}
	
	public void verifyNavigationLinkBanner6thDept() {
		SoftAssert sa=new SoftAssert();
		sa.assertEquals(stringBuilder(driver), "https://uat1.emax.ae/buy-samsung-tablet-galaxy-tab-s6-lite-104-wifi-64gb-4gb-ram-grey-p-01G51KQQZ77JV60QNN6AJX1T2D.html");
		sa.assertAll();
	}
	
	@FindBy(xpath = "//span[contains(text(),'Brand')]")
	private WebElement BrandElement7thdept;
	
	@FindBy(xpath = "//*[@id='main-part']/div/div/section/div/div[2]/a[1]/span[1]/img")
		private WebElement brand7thDept;
	
	public void clickOnBrandDept(String deptVal) throws InterruptedException{
		SoftAssert sa=new SoftAssert();
		waitForElementPresent(5);
		sa.assertEquals(BrandElement7thdept.getText().trim(),deptVal);
		sa.assertAll();
		BrandElement7thdept.click();
		Thread.sleep(2000);	
	}
	
	public void verifyNavigationLink7thdtDept() {
		SoftAssert sa=new SoftAssert();
		waitForElementPresent(5);
		sa.assertEquals(stringBuilder(driver), "https://uat1.emax.ae/brands");
		sa.assertAll();
	}
	
	public void clickedOnBrand7thdDept(String deptVal, String brandVal) throws InterruptedException {	
		waitForElementPresent(5);
		brand7thDept.click();
	}
	
	public void verifyNavigationLinkBrand7thDept() {
		SoftAssert sa=new SoftAssert();
		waitForElementPresent(5);
		sa.assertEquals(stringBuilder(driver), "https://uat1.emax.ae/search?q=%3AmanufacturerName%3AASUS");
		sa.assertAll();
	}
	

}
