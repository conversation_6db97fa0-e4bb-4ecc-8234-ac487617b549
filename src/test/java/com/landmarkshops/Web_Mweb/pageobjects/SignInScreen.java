package com.landmarkshops.Web_Mweb.pageobjects;

import com.landmarkshops.Web_Mweb.config.config;
import com.landmarkshops.Web_Mweb.utilities.WebUtils;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.testng.Assert;

/***
 * This class is used Sign In
 */
public class SignInScreen extends WebUtils {
    public SignInScreen(WebDriver driver) {
        super(driver);
        PageFactory.initElements(driver, this);
    }
    private static String concept = config.getConcept();

    @FindBy(xpath="//label[contains(text(),'E-mail')]")
    private WebElement userNameLabel;

    @FindBy(id="username")
    private WebElement usernameInput;

    @FindBy(id="password")
    private WebElement passwordInput;

    @FindBy(xpath="//*[@id='loginForm_submit']")
    private WebElement logIn;

    @FindBy(id="loginForm_submit")
    private WebElement logInLabel;

    @FindBy(xpath="//*[@id='lmg-error-msg']/div/p")
    private WebElement signInErrorMessage;

    @FindBy(xpath = "//h1[contains(text(),'Login To Continue')]")
    private WebElement loginToContinue;

    @FindBy(xpath = "//span[contains(text(),'DON’T HAVE AN ACCOUNT')]")
    private WebElement dontHaveAccount;

    @FindBy(xpath = "//button[contains(text(),'Register Here')]")
    private WebElement registerHere;

    @FindBy(xpath = "//button[contains(text(),'Checkout As A Guest')]")
    private WebElement checkOutAsGuest;

    @FindBy(xpath = "//span[contains(text(),'Or')]")
    private WebElement or;

    @FindBy(id = "rememberpassword")
    private WebElement rememberMeCheckBox;

    @FindBy(xpath = "//label[@for='notification']")
    private WebElement rememberMeText;

    @FindBy(id="password-icon")
    private WebElement passwordIcon;

    @FindBy(linkText="Forgot Password")
    private WebElement forgotPassword;

    @FindBy(id="email-required")
    private WebElement enterEmailRequired;

    @FindBy(id="password-required")
    private WebElement enterYourPassword;

    @FindBy(id="email-invalid")
    private WebElement pleaseEnterAValidEmailAddress;


    @FindBy(id="")
    private WebElement emailId;

    @FindBy(xpath="//label[@for='password']")
    private WebElement passwordLable;
























    /**
     * This method is used to provide valid email and password for login
     * @param email
     * @param password
     */
    public void signInWithValidCredentials(String email, String password) {

        waitForWebElementPresent(usernameInput,10);
        enterEmail(email);
        enterPassword(password);
        clickOnSignInButton();
    }

    /**
     * This method is used to enter EmailId
     * @param email
     */
    public void enterEmail(String email) {
        waitForWebElementPresent(usernameInput,15);
        usernameInput.sendKeys(email);
    }

    /**
     * This method is used to enter password
     * @param password
     */
    public void enterPassword(String password) {
        waitForWebElementPresent(passwordInput,15);
        passwordInput.sendKeys(password);
    }

    /**
     * This method is used to click on SignIn Button
     */
    public void clickOnSignInButton() {
        waitForElementToBeClickable(logIn,15);
        elementMouseClick(logIn);


    }

    /**
     * This method is used to verify inline message for invalid email or password
     * @return
     */
    public String verifyInvalidSignInErrorMessage() {
        waitForWebElementPresent(signInErrorMessage,20);
        return signInErrorMessage.getText().trim();
    }

    public void verifyUIComponentsInSignInPage() {

        Assert.assertEquals(userNameLabel.getText().trim(),"E-mail","Username is not displayed");
        Assert.assertEquals(loginToContinue.getText().trim(),"Login To Continue","Login To Continue is not displayed");
        Assert.assertEquals(passwordLable.getText().trim(),"Password","Password label is not displayed");
        Assert.assertEquals(rememberMeText.getText().trim(),"Remember Me","Remember Me text is not displayed");
        Assert.assertEquals(passwordLable.getText().trim(),"Password","Password label is not displayed");
        Assert.assertEquals(rememberMeText.getText().trim(),"Remember Me","Forgot password  text is not displayed");
        Assert.assertEquals(logInLabel.getText().trim(),"Login","Sign In text is not displayed");
        Assert.assertEquals(dontHaveAccount.getText().trim(),"DON’T HAVE AN ACCOUNT","DON’T HAVE AN ACCOUNT text is not displayed");
        Assert.assertEquals(registerHere.getText().trim(),"Register Here","Register Here text is not displayed");
        //Assert.assertEquals(or.getText().trim(),"OR","OR text is not displayed");
        //Assert.assertEquals(checkOutAsGuest.getText().trim(),"Checkout As A Guest","Checkout As A Guest text is not displayed");

    }


    public void validateErrorMessagesInSignIn() {
        clickOnSignInButton();
        waitForWebElementPresent(enterEmailRequired,driver,15);
        Assert.assertEquals(enterEmailRequired.getText().trim(),"Enter your email address","Enter your email address is not displayed");
        Assert.assertEquals(enterYourPassword.getText().trim(),"Enter your password","Enter your password is not displayed");
        enterEmail("qaTest");
        Assert.assertEquals(pleaseEnterAValidEmailAddress.getText().trim(),"Please Enter a valid email address","Please Enter a valid email address is not displayed");

    }

    public void forgotPasswordClick() {
        waitForElementToBeClickable(forgotPassword,15);
        forgotPassword.click();
    }


    public void registerHereClick(){
        waitForElementToBeClickable(registerHere,15);
        registerHere.click();
    }

    public void checkOutAsGuest(){
        waitForElementToBeClickable(checkOutAsGuest,15);
        checkOutAsGuest.click();

    }

    public void enableRememberMe(){
        rememberMeCheckBox.click();
    }
    public void disableRememberMe(){
        rememberMeCheckBox.click();
    }
}
