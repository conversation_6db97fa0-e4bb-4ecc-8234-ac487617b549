package com.landmarkshops.Web_Mweb.pageobjects;

import com.landmarkshops.Web_Mweb.config.config;
import com.landmarkshops.Web_Mweb.utilities.WebUtils;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.testng.Assert;
import org.testng.asserts.SoftAssert;

import java.util.List;

/***
 * This class is used for validating and verifying Product Detail page
 */
public class PDPScreen extends WebUtils {
    public PDPScreen(WebDriver driver) {
        super(driver);
        PageFactory.initElements(driver, this);
    }
    private static String concept = config.getConcept();


    @FindBy(xpath ="//*[@id='addToCart_QA']/span")
    WebElement addToBasket;


    @FindBy(xpath ="//p[contains(text(),'Free Gift')]")
    WebElement freeGift;

    @FindBy(xpath ="//*[@id='prod-shppng_QA']//span/p")
    WebElement freeGiftDescription;

    @FindBy(id="prod-detail_QA")
    WebElement productDescription;
    
    @FindBy(xpath = "//*[@id='product-100000345901']/div[2]/a")
	private WebElement productIDPLP;
    
    @FindBy(xpath = "//*[@id='prod-detail_QA']/div[1]/h1")
	private WebElement productIDPDP;
    
    @FindBy(xpath = "//div[contains(@id,'details-price')]/div")
	private WebElement productPricePDP;
    
    @FindBy(xpath = "//*[@id='product-100000345901']")
   	private WebElement productNav;
       
       
   	
   	@FindBy(xpath = "//*[@id='product-100000345901']/div[3]/div")
   	private WebElement productPricePLP;




    /**
     * This method is used to navigate to PDP
     */
    public void userNavigateToProductDetailsPage() {
        navigateToParticularPage("https://uat1emaxme.lmsin.net/ae/en/shop-apple-watch-series-6-p-01FF2S0GHBJVHG0BN8S9HY0YR2-128GB-Red.html");
    }

    /**
     * This method is used to navigate to PDP using given URL
     * * @param url
     */
    public void navigateToProductDetailPage(String url) {
        navigateToParticularPage(url);
        waitThread(5000);
    }

    /**
     * This method is used to click on Add to Basket label
     */
    public void userClickOnAddToBasket() {
        waitForPageToLoad(15);
        waitThread(2000);
        waitForElementToBeClickable(addToBasket,15);
        addToBasket.click();
    }

    @FindBy(xpath = "//*[@id='main-part']/div/div")
    private WebElement productDetailsScreen;

    @FindBy(xpath = "//*[@id='desk-prod-layout']/div/div[1]/nav/ol")
    private WebElement breadcrumbs;
    @FindBy(xpath = "//*[@id='desk-prod-layout']/div/div/nav/ol/li/a")
    private List<WebElement> breadcrumbsList;

    @FindBy(xpath = "//*[@id='myimage']")
    private WebElement productImage;
    @FindBy(xpath = "//div[@class='badges']")
    private WebElement productImageBadges;
    @FindBy(xpath = "//*[@id='prod-img_QA']/div/div/ul/li/a/img")
    private WebElement productImageList;

    @FindBy(xpath = "//*[@id='prod-detail_QA']/div[1]/h1")
    private WebElement productTitle;

    @FindBy(xpath = "//*[@id='details-base-price']")
    private WebElement productBasePrice;
    @FindBy(xpath = "//*[@id='details-price']")
    private WebElement productSoldPrice;
    @FindBy(xpath = "//*[@id='root-prod-details-inner']/div[2]/span")
    private WebElement productDiscountedAmountAndPercentage;

    @FindBy(xpath = "//*[@id='root-prod-details-inner']/div[2]/span")
    private WebElement productInstallmentDetails;

    @FindBy(xpath = "")
    private WebElement productSmartPayLogo;
    @FindBy(xpath = "")
    private WebElement productSmartPayInformationIcon;

    @FindBy(xpath = "//*[@id='root-prod-details-inner']/div[3]")
    private WebElement productVariantSection;
    @FindBy(xpath = "//*/div[1]/div/div[2]/div/div[2]/div[2]/div[3]/div/div/div[1]/div")
    private List<WebElement> productVariantSectionList;
    @FindBy(xpath = "//*[@id='details-memory']/div/div[1]/div")
    private WebElement firstVariantTitle;
    @FindBy(xpath = "//*/div[2]/div/div[2]/div[2]/div[3]/div[1]/div/div[2]/div/div/button")
    private List<WebElement> firstVariantElements;
    @FindBy(xpath = "//*[@id='details-color']/div/div[1]/div")
    private WebElement SecondVariantTitle;
    @FindBy(xpath = "//*/div[2]/div/div[2]/div[2]/div[3]/div[2]/div/div[2]/div/div/button")
    private List<WebElement> SecondVariantElements;

    @FindBy(xpath = "//*[@id='warranty-section_QA']")
    private WebElement warrantySection;
    @FindBy(xpath = "//*[@id='extended-warranty-section_QA']")
    private WebElement warrantyExternalSection;
    @FindBy(xpath = "//*[@id='extended-warranty-section_QA']/div[2]/div[1]")
    private WebElement warrantyExternalTitle;
    @FindBy(xpath = "//*[@id='extended-warranty-section_QA']/div[2]/div[2]")
    private WebElement warrantyExternalSubTitle;
    @FindBy(xpath = "//*[@id='extended-warranty-section_QA']/div[2]/div[3]/div[*]")
    private List<WebElement> warrantyExternalTypeDetails;
    @FindBy(xpath = "//*[@id='extended-warranty-section_QA']/div[1]/img")
    private WebElement warrantyExternalIcon;

    @FindBy(xpath = "//*[@id='emax-care-section_QA']")
    private WebElement warrantyEmaxCareSection;
    @FindBy(xpath = "//*[@id='emax-care-section_QA']/div[2]/div[1]")
    private WebElement warrantyEmaxCareTitle;
    @FindBy(xpath = "//*[@id='emax-care-section_QA']/div[2]/div[2]")
    private WebElement warrantyEmaxCareSubTitle;
    @FindBy(xpath = "//*[@id='emax-care-section_QA']/div[2]/div[3]/div")
    private List<WebElement> warrantyEmaxCareTypeDetails;
    @FindBy(xpath = "//*[@id='emax-care-section_QA']/div[2]/div[3]/div[*]")
    private List<WebElement> warrantyEmaxCareTypeTitle;
    @FindBy(xpath = "//*/div[2]/div/div[2]/div[2]/div[6]/div[3]/div[2]/div[*]/div/div[2]/span[1]")
    private List<WebElement> warrantyEmaxCareTypeAmount;
    @FindBy(xpath = "//*[@id='emax-care-section_QA']/div[1]/img")
    private WebElement warrantyEmaxCareIcon;

    @FindBy(xpath = "//*[@id='no-warranty-section_QA']/div[2]/div")
    private WebElement noAdditionalProtectionSection;
    @FindBy(xpath = "//*[@id='no-warranty-section_QA']/div[2]/div/div[1]/span")
    private WebElement noAdditionalProtectionTitle;
    @FindBy(xpath = "//*[@id='no-warranty-section_QA']/div[2]/div/div[2]/span")
    private WebElement noAdditionalProtectionSubTitle;
    @FindBy(xpath = "//*[@id='no-warranty-section_QA']/div[1]/img")
    private WebElement noAdditionalProtectionIcon;

    @FindBy(xpath = "//*[@id='prod-shppng_QA']/div/div[1]")
    private WebElement deliveryOptionsSection;
    @FindBy(xpath = "//*[@id='prod-shppng_QA']/div/div[1]/div[2]/p")
    private WebElement deliveryOptionsTitle;
    @FindBy(xpath = "//*[@id='prod-shppng_QA']/div/div[1]/div[2]/span")
    private WebElement deliveryOptionsMessage;
    @FindBy(xpath = "//*[@id='prod-shppng_QA']/div/div[1]/div[1]/img")
    private WebElement deliveryOptionsIcon;

    @FindBy(xpath = "//*[@id='addToCart_QA']")
    private WebElement addToCartButton;
    @FindBy(xpath = "//*[@id='addToCart_QA']/span")
    private WebElement addToCartButtonText;

    @FindBy(xpath = "//*[@id='click-collect-info']")
    private WebElement clickAndCollectSection;
    @FindBy(xpath = "//*[@id='click-collect-info']/div[1]")
    private WebElement clickAndCollectIcon;
    @FindBy(xpath = "//*[@id='click-collect-info']/div[2]/div[1]")
    private WebElement clickAndCollectTitle;
    @FindBy(xpath = "//*[@id='click-collect-info']/div[2]/div[2]")
    private WebElement clickAvailableStoresLink;

    @FindBy(xpath = "//*[contains(text(),'Click & Collect')]")
    private WebElement clickAndCollectPopUpTitle;
    @FindBy(xpath = "//*[contains(text(),'This product is in stock at the following stores.')]")
    private WebElement clickAndCollectPopUpSubTitle;
    @FindBy(xpath = "//*[@id='click-collect-store-info']/div[1]/img")
    private WebElement clickAndCollectPopUpLocationIcon;
    @FindBy(xpath = "//*[@id='click-collect-store-info']")
    private List<WebElement> clickAndCollectPopUpStoreNameAndStockList;
    @FindBy(xpath = "//*[@id='click-collect-store-info']/div[2]/div[1]")
    private WebElement clickAndCollectPopUpStoreName;
    @FindBy(xpath = "//*[@id='click-collect-store-info']/div[2]/div[2]")
    private WebElement clickAndCollectPopUpInStock;
    @FindBy(xpath = "//*[@id='store-available-close']")
    private WebElement clickAndCollectPopUpCloseButton;

    @FindBy(xpath = "//*[@id='desk-prod-layout']/div/div[3]/div[1]")
    private WebElement allAboutItemSection;
    @FindBy(xpath = "//*[@id='prod-desc-title']")
    private WebElement productDescriptionDetails;
    @FindBy(xpath = "//*[@id='prod-desc-title']")
    private WebElement productSpecificationDetails;

    @FindBy(xpath = "//button[@value='Yellow']")
    private WebElement variantYellow;

    /**
     * This method is used to verify is PDP screen displayed
     */
    public void isProductDetailsScreenDisplayed() {
        SoftAssert sa = new SoftAssert();
        sa.assertTrue(productDetailsScreen.isDisplayed(), "Product Detail screen is not displayed");
        String logMessage = productDetailsScreen.isDisplayed() ? " is displayed" : " is not displayed";
        testLogger.info("Product Detail screen "+logMessage);
        sa.assertAll();
    }

    /**
     * This method is used to verify Breadcrumbs is displayed in PDP
     */
    public void verifyBreadcrumbs() {
        SoftAssert sa = new SoftAssert();
        sa.assertTrue(breadcrumbs.isDisplayed(), "Breadcrumbs is displayed On Product Detail screen");
        String logMessage = breadcrumbs.isDisplayed() ? " is displayed" : " is not displayed";
        testLogger.info("Breadcrumbs "+logMessage+" On Product Detail screen");
        sa.assertAll();
    }

    /**
     * This method is used to verify a particular Breadcrumbs is displayed in PDP
     * * @param name
     */
    public void verifyBreadcrumbsProductName(String name) {
        boolean breadcrumbsName = VerifyElementsList(breadcrumbsList,name);
        String logMessage = breadcrumbsName ? " is displayed" : " is not displayed";
        testLogger.info("Breadcrumbs Product name "+name+logMessage+" in Product Detail screen");
    }

    /**
     * This method is used to click Breadcrumbs product by name in PDP
     * * @param productName
     */
    public void clickBreadcrumbsProductName(String productName) {
        clickElementsList(breadcrumbsList, productName);
    }

    /**
     * This method is used to navigate back to PDP
     */
    public void navigateBack() {
        implicitWait(2000);
        driver.navigate().back();
        implicitWait(2000);
        testLogger.info("Navigate back to Product Detail screen");
    }

    /**
     * This method is used to verify a Product Image is displayed in PDP
     */
    public void verifyProductImage() {
        SoftAssert sa = new SoftAssert();
        sa.assertTrue(productImage.isDisplayed(), "Product Image is displayed in Product Detail screen");
        String logMessage = productImage.isDisplayed() ? "is displayed" : "is not displayed";
        testLogger.info("Product Image "+logMessage+" in Product Detail screen");
        sa.assertAll();
    }

    /**
     * This method is used to swipe left product Image in PDP
     */
    public void swipeLeftProductImage(){
        if (productImage.isDisplayed()){
            dragAndDrop(productImage, 50, 0);
            testLogger.info("Product Image swiped Left in Product Detail screen");
        }else{
            testLogger.info("Product Image is not displayed in Product Detail screen");
        }
    }

    /**
     * This method is used to swipe right product Image in PDP
     */
    public void swipeRightProductImage(){
        if (productImage.isDisplayed()){
            dragAndDrop(productImage, -50, 0);
            testLogger.info("Product Image swiped Right in Product Detail screen");
        }else{
            testLogger.info("Product Image is not displayed in Product Detail screen");
        }
    }

    /**
     * This method is used to verify Image Badges in PDP
     */
    public void verifyImageBadges() {
        SoftAssert sa = new SoftAssert();
        sa.assertTrue(productImageBadges.isDisplayed(), "Product image badge is displayed in Product Detail screen");
        String logMessage = productImageBadges.isDisplayed() ? "is displayed" : "is not displayed";
        testLogger.info("Product Image badges "+logMessage+" in Product Detail screen");
        sa.assertAll();
        waitThread(10000); //for demo
    }

    /**
     * This method is used to verify Image List in PDP
     */
    public void verifyImageList() {
        SoftAssert sa = new SoftAssert();
        sa.assertTrue(productImageList.isDisplayed(), "Product image list is displayed in Product Detail screen");
        String logMessage = productImageList.isDisplayed() ? "is displayed" : "is not displayed";
        testLogger.info("Product Image List "+logMessage+" in Product Detail screen");
        sa.assertAll();
    }

    /**
     * This method is used to verify Product text without user input in PDP
     */
    public void verifyProductText(){
        verifyProductText("");
    }

    /**
     * This method is used to verify Product text with user input in PDP
     * * @param productText
     */
    public void verifyProductText(String productText)  {
        SoftAssert sa = new SoftAssert();
        sa.assertTrue(productTitle.isDisplayed(), "Product Title is not displayed");
        testLogger.info("This is product Title message : "+productTitle.getText().trim());
        if (productText.trim().isEmpty()) {
            sa.assertNotNull(productTitle.getText().trim(), "Product Title is not displayed");
        }
        else {
            sa.assertEquals(productTitle.getText().trim(),""+productText);
        }
        sa.assertAll();
    }

    /**
     * This method is used to verify Product price with user input in PDP
     */
    public void verifyProductPrice(){
        verifyProductPrice( "", "");
    }

    /**
     * This method is used to verify Product base price with user input in PDP
     * * @param basePrice
     */
    public void verifyBasePrice(String basePrice){
        verifyProductPrice(basePrice, "");
    }

    /**
     * This method is used to verify Product sold price with user input in PDP
     * * @param soldPrice
     */
    public void verifySoldPrice(String soldPrice){
        verifyProductPrice("", soldPrice);
    }

    /**
     * This method is used to verify Product price with user input in PDP
     * * @param basePrice
     * * @param soldPrice
     */
    public void verifyProductPrice(String basePrice, String soldPrice)  {
        SoftAssert sa = new SoftAssert();
        sa.assertTrue(productSoldPrice.isDisplayed() , "Product price is not displayed");

        boolean basePriceVisible = isElementPresentCheckUsingJavaScriptExecutor(productBasePrice);
      // boolean basePriceVisible = isElementDisplayed(productBasePrice); //Not working need to find the issue

        // boolean basePriceVisible = false;
        if (basePriceVisible) {
            sa.assertTrue(productBasePrice.isDisplayed(), "Product base price is not displayed");
            if(basePrice.trim().isEmpty()) {
                sa.assertNotNull(productBasePrice.getText().trim(), "Product base price is not a valid value");
                testLogger.error("Product base price is null");
            }else{
                sa.assertEquals(productBasePrice.getText().trim(),""+basePrice);
                testLogger.info("Product Base Price : " + productBasePrice.getText().trim());
            }
        }
        if(soldPrice.trim().isEmpty()) {
            sa.assertNotNull(productSoldPrice.getText().trim(), "Product Sold price is not a valid value");
        }else{
            sa.assertEquals(productSoldPrice.getText().trim(),""+basePrice);
        }
        testLogger.info("Product Sold Price : " + productSoldPrice.getText().trim());
        sa.assertAll();
    }

    /**
     * This method is used to verify Product Discounted Amount and Percentage without user input in PDP
     */
    public void verifyProductDiscountedAmountAndPercentage(){
        verifyProductDiscountedAmountAndPercentage("");
    }

    /**
     * This method is used to verify Product Discounted Amount and Percentage with user input in PDP
     * * @param discountedAmountAndPercentage
     */
    public void verifyProductDiscountedAmountAndPercentage(String discountedAmountAndPercentage) {
        SoftAssert sa = new SoftAssert();
        sa.assertTrue(productDiscountedAmountAndPercentage.isDisplayed(), "Product discount amount and percentage is not displayed");
        if (discountedAmountAndPercentage.trim().isEmpty()) {
            sa.assertNotNull(productDiscountedAmountAndPercentage.getText().trim(), "Product discount amount and percentage is not displayed");
        }
        else {
            sa.assertEquals(productDiscountedAmountAndPercentage.getText().trim(),""+discountedAmountAndPercentage);
        }
        testLogger.info("This is product discount amount and percentage message : "+productDiscountedAmountAndPercentage.getText().trim());
        sa.assertAll();
    }

    /**
     * This method is used to verify Product Installment Text without user input in PDP
     */
    public void verifyProductInstallmentText(){
        verifyProductInstallmentDetails("");
    }

    /**
     * This method is used to verify Product Installment Text with user input in PDP
     * * @param installmentText
     */
    public void verifyProductInstallmentDetails(String installmentText){
        SoftAssert sa = new SoftAssert();
        sa.assertTrue(productInstallmentDetails.isDisplayed(), "Product installment text is not displayed");
        if (installmentText.trim().isEmpty()) {
            sa.assertNotNull(productInstallmentDetails.getText().trim(), "Product installment text is not displayed");
        }
        else {
            sa.assertEquals(productInstallmentDetails.getText().trim(),""+installmentText.trim());
        }
        testLogger.info("This is product installment Details : "+productInstallmentDetails.getText().trim());
        sa.assertAll();
    }

    /**
     * This method is used to verify Product Variant Section is displayed in PDP
     */
    public void verifyProductVariantSection(){
        SoftAssert sa = new SoftAssert();
        sa.assertTrue(productVariantSection.isDisplayed(), "Product variant section is not displayed");
        testLogger.info("Product Variant present in Product detail screen");
        findElementsList(productVariantSectionList);
        testLogger.info("-------------------------------------------------");
        sa.assertAll();
    }

    /**
     * This method is used to verify first Product Variant Section is displayed and its types in PDP
     */
    public void verifyFirstProductVariantSection(){
        SoftAssert sa = new SoftAssert();
        sa.assertTrue(firstVariantTitle.isDisplayed(), "Product variant "+firstVariantTitle.getText().trim()+" section is not displayed");
        testLogger.info("Product Variant type "+firstVariantTitle.getText().trim());
        testLogger.info(firstVariantTitle.getText().trim()+" - List of variant");
        findElementsList(firstVariantElements);
        testLogger.info("-------------------------------------------------");
        sa.assertAll();
    }

    /**
     * This method is used to verify second Product Variant Section is displayed and its types in PDP
     */
    public void verifySecondProductVariantSection(){
        scrollToElementBasedOnLocation(SecondVariantTitle);
        SoftAssert sa = new SoftAssert();
        sa.assertTrue(SecondVariantTitle.isDisplayed(), "Product variant "+SecondVariantTitle.getText().trim()+" section is not displayed");
        testLogger.info("Product Variant type "+SecondVariantTitle.getText().trim());
        testLogger.info(SecondVariantTitle.getText().trim()+" - List of variant");
        findElementsList(SecondVariantElements);
        testLogger.info("-------------------------------------------------");
        sa.assertAll();
    }

    /**
     * This method is used to click primary Product Variant Section by name in PDP
     * * @param name
     */
    public void clickPrimaryVariantByName(String name){
        scrollElement(productTitle);
        waitThread(5000); //for demo
        clickElementsList(firstVariantElements,name);
    }

    /**
     * This method is used to click secondary Product Variant Section by name in PDP
     * * @param name
     */
    public void clickSecondaryVariantByName(String name){
        scrollElement(productTitle);
        waitThread(5000); //for demo
        clickElementsList(SecondVariantElements,name);
    }

    /**
     * This method is used to verify warranty section is displayed in PDP
     */
    public void verifyWarrantySection(){
        SoftAssert sa = new SoftAssert();
        sa.assertTrue(warrantySection.isDisplayed(), "Product warranty section is not displayed");
        String logMessage = warrantySection.isDisplayed() ? "is displayed" : "is not displayed";
        testLogger.info("Product warranty section "+logMessage+" in Product Detail screen");
        sa.assertAll();
    }

    /**
     * This method is used to verify External warranty section is displayed and its type in PDP
     */
    public void verifyExternalWarrantySection(){
        scrollToParticularElement(warrantyExternalSection);
        SoftAssert sa = new SoftAssert();
        sa.assertTrue(warrantyExternalSection.isDisplayed(), "Product warranty External section is not displayed");
        String logMessage = warrantyExternalSection.isDisplayed() ? "is displayed" : "is not displayed";
        testLogger.info("Product warranty External section "+logMessage+" in Product Detail screen");
        sa.assertNotNull(warrantyExternalTitle.getText().trim(),"Product warranty External section title is not displayed");
        testLogger.info("Product warranty External section title - "+warrantyExternalTitle.getText().trim());
        sa.assertNotNull(warrantyExternalSubTitle.getText().trim(),"Product warranty External section sub-title is not displayed");
        testLogger.info("Product warranty External section sub-title - "+warrantyExternalSubTitle.getText().trim());
        testLogger.info("Product warranty External section types ------------------------------ ");
        testLogger.info("External Warrant Name and Amount");
        for(WebElement element : warrantyExternalTypeDetails){
            testLogger.info(element.getText().trim().replace("\n"," - "));
        }
        testLogger.info("----------------------------------------------------------------------");
        sa.assertTrue(warrantyExternalIcon.isDisplayed(), "Product warranty External Icon is not displayed");
        String logMessageIcon = warrantyExternalIcon.isDisplayed() ? "is displayed" : "is not displayed";
        testLogger.info("Product warranty External Icon "+logMessageIcon+" in Product Detail screen");
        sa.assertAll();
    }

    /**
     * This method is used to click External warranty section by name in PDP
     * * @param name
     */
    public void clickExternalWarrantyByName(String name){
        scrollElement(productInstallmentDetails);
        waitThread(5000);// For Demo
        clickElementsList(warrantyExternalTypeDetails,name);
    }

    /**
     * This method is used to verify Emax warranty section is displayed and its type in PDP
     */
    public void verifyEmaxCareWarrantySection(){
        scrollToParticularElement(warrantyEmaxCareSection);
        SoftAssert sa = new SoftAssert();
        sa.assertTrue(warrantyEmaxCareSection.isDisplayed(), "Product warranty Emax Care section is not displayed");
        String logMessage = warrantyEmaxCareSection.isDisplayed() ? "is displayed" : "is not displayed";
        testLogger.info("Product warranty Emax Care section "+logMessage+" in Product Detail screen");
        sa.assertNotNull(warrantyEmaxCareTitle.getText().trim(),"Product warranty Emax Care section title is not displayed");
        testLogger.info("Product warranty Emax Care section title - "+warrantyEmaxCareTitle.getText().trim());
        sa.assertNotNull(warrantyEmaxCareSubTitle.getText().trim(),"Product warranty Emax Care section sub-title is not displayed");
        testLogger.info("Product warranty Emax Care section sub-title - "+warrantyEmaxCareSubTitle.getText().trim());
        testLogger.info("Product warranty Emax Care section types ------------------------------ ");
        testLogger.info("Emax Warrant Name and Amount");
        for(WebElement element : warrantyEmaxCareTypeDetails){
            testLogger.info(element.getText().trim().replace("\n"," - "));
        }
        sa.assertTrue(warrantyEmaxCareIcon.isDisplayed(), "Product warranty Emax Care Icon is not displayed");
        String logMessageIcon = warrantyEmaxCareIcon.isDisplayed() ? "is displayed" : "is not displayed";
        testLogger.info("Product warranty Emax Care Icon "+logMessageIcon+" in Product Detail screen");
        sa.assertAll();
    }

    /**
     * This method is used to click Emax warranty section by name in PDP
     * * @param name
     */
    public void clickEmaxCareWarrantyByName(String name){
        scrollToParticularElement(productInstallmentDetails);
        waitThread(5000);// For Demo
        clickElementsList(warrantyEmaxCareTypeDetails,name);
    }

    /**
     * This method is used to verify No Additional Protection warranty section is displayed and its type in PDP
     */
    public void verifyNoAdditionalProtectionSection(){
        scrollToParticularElement(noAdditionalProtectionTitle);
        SoftAssert sa = new SoftAssert();
        sa.assertTrue(noAdditionalProtectionSection.isDisplayed(), "No Additional Protection in warranty section is not displayed");
        String logMessage = noAdditionalProtectionSection.isDisplayed() ? "is displayed" : "is not displayed";
        testLogger.info("No Additional Protection in warranty section "+logMessage+" in Product Detail screen");
        sa.assertNotNull(noAdditionalProtectionTitle.getText().trim(),"No Additional Protection in warranty section title is not displayed");
        testLogger.info("No Additional Protection in warranty section title - "+noAdditionalProtectionTitle.getText().trim());
        sa.assertNotNull(noAdditionalProtectionSubTitle.getText().trim(),"No Additional Protection in warranty section sub-title is not displayed");
        testLogger.info("No Additional Protection in warranty section sub-title - "+noAdditionalProtectionSubTitle.getText().trim());
        sa.assertTrue(noAdditionalProtectionIcon.isDisplayed(), "No Additional Protection in warranty section Icon is not displayed");
        String logMessageIcon = noAdditionalProtectionIcon.isDisplayed() ? "is displayed" : "is not displayed";
        testLogger.info("No Additional Protection in warranty section Icon "+logMessageIcon+" in Product Detail screen");
        sa.assertAll();
    }

    /**
     * This method is used to click No Additional Protection warranty section by name in PDP
     */
    public void clickNoAdditionalProtectionWarranty(){
        scrollToParticularElement(productInstallmentDetails);
        waitThread(5000);//For demo
        noAdditionalProtectionTitle.click();
        testLogger.info("No Additional protection warranty button is clicked");
    }

    /**
     * This method is used to verify Delivery Options section in PDP
     */
    public void verifyDeliveryOptionsSection(){
        scrollToParticularElement(deliveryOptionsTitle);
        SoftAssert sa = new SoftAssert();
        sa.assertTrue(deliveryOptionsSection.isDisplayed(), "Product delivery option section is not displayed");
        String logMessage = deliveryOptionsSection.isDisplayed() ? "is displayed" : "is not displayed";
        testLogger.info("Product delivery option section "+logMessage+" in Product Detail screen");
        sa.assertNotNull(deliveryOptionsTitle.getText().trim(),"Product delivery option section title is not displayed");
        testLogger.info("Product delivery option section title - "+deliveryOptionsTitle.getText().trim());
        sa.assertNotNull(deliveryOptionsMessage.getText().trim(),"Product delivery option section message is not displayed");
        testLogger.info("Product delivery option section message - "+deliveryOptionsMessage.getText().trim());
        sa.assertTrue(deliveryOptionsIcon.isDisplayed(), "Product delivery option Icon is not displayed");
        String logMessageIcon = deliveryOptionsIcon.isDisplayed() ? "is displayed" : "is not displayed";
        testLogger.info("Product delivery option Icon "+logMessageIcon+" in Product Detail screen");
        sa.assertAll();
    }

    /**
     * This method is used to verify Add To Cart button in PDP
     */
    public void verifyAddToCartButton(){
        scrollToParticularElement(deliveryOptionsSection);
        SoftAssert sa = new SoftAssert();
        sa.assertTrue(addToCartButton.isDisplayed(), "Add to Basket section is not displayed");
        String logMessage = addToCartButton.isDisplayed() ? "is displayed" : "is not displayed";
        testLogger.info("Add to Basket section "+logMessage+" in Product Detail screen");
        sa.assertEquals(addToCartButtonText.getText().trim(),"ADD TO BASKET");
        testLogger.info("Button displayed message - "+addToCartButtonText.getText().trim());
        sa.assertAll();
    }

    /**
     * This method is used to verify Pre Order button in PDP
     */
    public void verifyPreOrderButton(){
        scrollToParticularElement(deliveryOptionsSection);
        SoftAssert sa = new SoftAssert();
        sa.assertTrue(addToCartButton.isDisplayed(), "Pre Order section is not displayed");
        String logMessage = addToCartButton.isDisplayed() ? "is displayed" : "is not displayed";
        testLogger.info("Pre Order section "+logMessage+" in Product Detail screen");
        sa.assertEquals(addToCartButtonText.getText().trim(),"Pre Order");
        testLogger.info("Button displayed message - "+addToCartButtonText.getText().trim());
        sa.assertAll();
    }

    /**
     * This method is used to click on Pre Order label
     */
    public void userClickOnPreOrderBasket() {
        waitForPageToLoad(15);
        waitThread(2000);
        waitForElementToBeClickable(addToBasket,15);
        addToBasket.click();
        testLogger.info("User click on Pre Order button");
    }

    /**
     * This method is used to verify ClickAndCollect section in PDP
     */
    public void verifyClickAndCollectSection() {
        waitForElementPresent(5);
        SoftAssert sa = new SoftAssert();
        sa.assertTrue(clickAndCollectSection.isDisplayed(), "Click and Collect section is not displayed");
        sa.assertAll();
    }

    /**
     * This method is used to verify ClickAndCollect Icon in PDP
     */
    public void verifyClickAndCollectIcon() {
        SoftAssert sa = new SoftAssert();
        sa.assertTrue(clickAndCollectIcon.isDisplayed(), "Click and Collect icon is not displayed");
        sa.assertAll();
    }

    /**
     * This method is used to verify ClickAndCollect Title in PDP
     */
    public void verifyClickAndCollectTitle() {
        SoftAssert sa = new SoftAssert();
        sa.assertTrue(clickAndCollectTitle.isDisplayed(), "Click and Collect title is not displayed");
        sa.assertEquals(clickAndCollectTitle.getText().trim(),"Click & Collect Available");
        //sa.assertTrue("Click & Collect Available".equals(clickAndCollectTitle.getText()), "");
        sa.assertAll();
    }

    /**
     * This method is used to verify ClickAndCollect AvailableStores link in PDP
     */
    public void verifyClickAvailableStoresLink() {
        SoftAssert sa = new SoftAssert();
        sa.assertTrue(clickAvailableStoresLink.isDisplayed(), "Click Available Stores link is not displayed");
        sa.assertEquals(clickAvailableStoresLink.getText().trim(),"Check Available Stores");
        sa.assertAll();
    }

    /**
     * This method is used to click ClickAndCollect AvailableStores link in PDP
     */
    public void clickAvailableStoresLink() {
        clickAvailableStoresLink.click();
        testLogger.info("Click Available Stores button is clicked");
    }

    /**
     * This method is used to verify ClickAndCollect pop-up title in PDP
     */
    public void verifyClickAndCollectPopUpTitle() {
        waitForElementPresent(10);
        waitForWebElementPresent(clickAndCollectPopUpSubTitle,15);
        testLogger.info("This is C&C pop-up title message : "+clickAndCollectPopUpTitle.getText().trim());
        SoftAssert sa = new SoftAssert();
        sa.assertTrue(clickAndCollectPopUpTitle.isDisplayed(), "Click and Collect popup title is not displayed");
        sa.assertEquals(clickAndCollectPopUpTitle.getText().trim(),"Click & Collect Available");
        sa.assertAll();
    }

    /**
     * This method is used to verify ClickAndCollect pop-up sub-title in PDP
     */
    public void verifyClickAndCollectPopUpSubTitle() {
        SoftAssert sa = new SoftAssert();
        testLogger.info("This is C&C pop-up sub-title message : "+clickAndCollectPopUpSubTitle.getText().trim());
        sa.assertTrue(clickAndCollectPopUpSubTitle.isDisplayed(), "Click and Collect popup sub-title is not displayed");
        sa.assertEquals(clickAndCollectPopUpSubTitle.getText().trim(),"This product is in stock at the following stores.");
        sa.assertAll();
    }

    /**
     * This method is used to verify ClickAndCollect pop-up title in PDP
     */
    public void verifyClickAndCollectPopUpLocationIcon() {
        waitForElementPresent(5);
        SoftAssert sa = new SoftAssert();
        sa.assertTrue(clickAndCollectPopUpLocationIcon.isDisplayed(), "Click and Collect popup location icon is not displayed");
        sa.assertAll();
    }

    /**
     * This method is used to verify ClickAndCollect pop-up store name in PDP
     */
    public void verifyClickAndCollectPopUpStoreName() {
        waitForElementPresent(5);
        SoftAssert sa = new SoftAssert();
        sa.assertTrue(clickAndCollectPopUpStoreName.isDisplayed(), "Click and Collect popup store name icon is displayed");
        testLogger.info("This is C&C pop-up store details : "+clickAndCollectPopUpStoreName.getText().trim());
        sa.assertNotNull(clickAndCollectPopUpStoreName.getText().trim(),"Store name is not displayed");
        sa.assertAll();
    }

    /**
     * This method is used to verify ClickAndCollect pop-up stock details in PDP
     */
    public void verifyClickAndCollectPopUpInStock() {
        waitForElementPresent(5);
        SoftAssert sa = new SoftAssert();
        sa.assertTrue(clickAndCollectPopUpInStock.isDisplayed(), "Click and Collect popup in-stock is not displayed");
        testLogger.info("This is C&C pop-up store stock details : "+clickAndCollectPopUpInStock.getText().trim());
        sa.assertEquals(clickAndCollectPopUpInStock.getText().trim(),"In Stock");
        sa.assertAll();
    }

    /**
     * This method is used to close ClickAndCollect pop-up in PDP
     */
    public void closeClickAndCollectPopup(){
        clickAndCollectPopUpCloseButton.click();
        testLogger.info("Click and Collect pop-up is closed");
    }

    /**
     * This method is used to verify AllAboutItem section in PDP
     */
    public void verifyAllAboutItemSection() {
        waitForElementPresent(5);
        SoftAssert sa = new SoftAssert();
        sa.assertTrue(allAboutItemSection.isDisplayed(), "All About Item section not displayed");
        testLogger.info("Section -  "+allAboutItemSection.getText().trim()+" is displayed");
        testLogger.info("Section -  "+productDescriptionDetails.getText().trim()+" is displayed");
        testLogger.info("Section -  Product Specification is displayed");
        sa.assertEquals(allAboutItemSection.getText().trim(),"About This Item");
        sa.assertAll();
    }

    public void yellowProductVariant(){
       waitForElementPresent(5);
       waitForElementToBeClickable(variantYellow,15);
       elementMouseClick(variantYellow);

    }


    public void verifyFreGiftSection(){
        Assert.assertEquals(freeGift.getText().trim(),"Free Gift","Free Gift text mismatch");

        Assert.assertEquals(freeGiftDescription.getText().trim(),"New Released Airpods promotions as free gift","Free Gift description text mismatch");
    }
    
    public void clickedOnProduct() throws InterruptedException {
    	waitForElementPresent(1);
        SoftAssert sa = new SoftAssert();
		String prodID_PLP=productIDPLP.getText().trim();
		testLogger.info("Product text in plp screen "+prodID_PLP);
		
		String prodPrice=productPricePLP.getText().trim();
		String pricePLP= prodPrice.replaceAll("[^0-9]", "");
		
		
		productNav.click();	
		//Thread.sleep(1000);
		//waitForWebElementPresent(productNav,1000);
		waitForElementPresent(2);
		
		String prodID_PDP=verifyProductID();
		sa.assertEquals(prodID_PLP, prodID_PDP);
			
		String pricePDP=verfiyProductPrice();
		sa.assertEquals(pricePLP, pricePDP);
		sa.assertAll();
		
		
	}

    public String getProductPrice() {
        return productPricePDP.getText().trim();


    }
    
	public String verifyProductID() throws InterruptedException {
		
		
		String prodID_PDP=productIDPDP.getText().trim();
		return prodID_PDP;
		
		
		
	}
	public String verfiyProductPrice() {
		String prodPrice1=productPricePDP.getText().trim();
		String pricePDP=prodPrice1.replaceAll("[^0-9]", "");
		
		return pricePDP;
		
		
		
	}
}
    