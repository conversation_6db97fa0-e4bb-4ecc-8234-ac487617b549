package com.landmarkshops.Web_Mweb.pageobjects;

import com.landmarkshops.Web_Mweb.config.config;
import com.landmarkshops.Web_Mweb.utilities.WebUtils;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.testng.Assert;
import org.testng.asserts.SoftAssert;

import java.util.List;

/**
 * This class is used to show category and sub category section
 * Choose Sort by different values
 */
public class ProductCategoryListingScreen extends WebUtils {
    public ProductCategoryListingScreen(WebDriver driver) {
        super(driver);
        PageFactory.initElements(driver, this);
    }
    private static String concept = config.getConcept();


    @FindBy(xpath = "//*[@id='category-top-layout']/div/div/nav/ol/li[1]/a")
    private WebElement breadCrumbRoot;

    @FindBy(xpath = "//*[@id='category-top-layout']/div/div/nav/ol/li[5]/a")
    private WebElement breadCrumbChild;

    @FindBy(xpath = "//*[@id='category-top-layout']/div/div/nav/ol/li[3]/a")
    private WebElement categoryFilterLayOut;

    @FindBy(xpath ="//*[@id='search-silter-layout']//button" )
    private List<WebElement> categoryList;

    @FindBy(xpath = " //*[@id='category-range-selector']/span/div[1]")
    private WebElement categorySection;

    @FindBy(xpath = "//*[@id='search-silter-layout']/div/div[2]/div/div[1]/button/span/div[2]")
    private WebElement categorySectionExpandArrow;

    @FindBy(xpath = " //*[@id='search-silter-layout']//button/span/div[2]")
    private List<WebElement>  expandArrowsList;


    @FindBy(xpath = "//span/input[@value='green']")
    private WebElement subCategoryItem;

    @FindBy(xpath = "//span/input[@value='128gb']")
    private WebElement subCategoryItem2;

    @FindBy(xpath = "//label[1]/span[2]/div[contains(text(),'green')]")
    private WebElement categoryLabelTitle;

    @FindBy(xpath = "//label[1]/span[2]/div[contains(text(),'128gb')]")
    private WebElement categoryLabelTitle2;

    @FindBy(xpath = "//*[@id='search-top-layout']/div/div/div[1]/div/div[1]/h1")
    private WebElement categoryTitle;

    @FindBy(id = "category-sortby-layout")
    private WebElement sortByLayout;

    @FindBy(xpath = "//*[@id='search-sortby-layout']/div/div[1]")
    private WebElement categoryProductsCount;

    @FindBy(xpath = "//*[@id='search-sortby-layout']/div/div[2]/div[1][contains(text(),'Sort by')]")
    private WebElement sortByText;

    @FindBy(xpath = "//button/span/div[1][contains(text(),'Relevance')]")
    private WebElement relevance;

    @FindBy(xpath = "//*[@id='search-sortby-layout']/div/div[2]/div[2]/button")
    private WebElement sortByIcon;


    @FindBy(xpath = "//*[@id='category-sortby-layout']/div/div[2]/div[2]/div/div/div/div/div/ul/div[1]")
    private WebElement sortByNewArrivals;

    @FindBy(xpath ="//*[@id='search-sortby-layout']/div/div[2]/div[2]/button/span/div[1]")
    private WebElement defaultSortValue;

    @FindBy(xpath ="//*[@id='search-sortby-layout']/div/div[2]/div[2]/div/div/div/div/div/ul/div" )
    private List<WebElement> sortByValues;

    @FindBy(id ="category-list-layout" )
    private WebElement categoryListLayout;

    @FindBy(xpath = "//*[@id='search-list-layout']/div[1]/div/div/div[1]/div[1]/a/img")
    private WebElement productImage;


    @FindBy(xpath = "//*[@id='search-list-layout']/div[1]/div/div/div[1]/div[2]/a")
    private WebElement productDescription;


    @FindBy(xpath = "//*[@id='search-list-layout']/div[1]/div/div/div[1]/div[3]/div/div[1]")
    private WebElement productCurrencyCode;

    @FindBy(xpath = "//*[@id='search-list-layout']/div[1]/div/div/div[1]/div[3]/div/div[2]")
    private WebElement productCurrencyValue;

    @FindBy(xpath = "//*[@id='main-part']/div/div[2]/div/div[2]/div[3]/div/div[1]/a/span/div[1]/span")
    private WebElement subCategoryListItem_1;


    @FindBy(xpath = "//*[@id='search-top-layout']/div/div/div[1]/div/div[1]/h1")
    private WebElement searchResultTitle;

    @FindBy(xpath = "//*[@id='search-list-layout']/div[1]/div/div/div")
    private WebElement totalProductCountPerPage;

    @FindBy(id="search-loadmore-layout")
    private WebElement loadMoreLayout;

    @FindBy(xpath = "//*[@id='search-loadmore-layout'']/div/div[1]")
    private WebElement totalProductscountPerPage2;

    @FindBy(xpath = "//*[@id='search-loadmore-layout']/div/div[3]/button")
    private WebElement loadMoreProducts;


    @FindBy(xpath ="//div[contains(text(),'AED')]//ancestor::div[2]/div[2]" )
    private List<WebElement> priceValueInProductListing;


    @FindBy(xpath = "//*[@id='search-sortby-layout']/div/div[2]/div[2]/button/span/div[1]")
    private WebElement dropDownValue;


    @FindBy(xpath = "//*[@id='search-silter-layout']//span[@class='MuiChip-label']")
    private WebElement seachSilterLabel;

    @FindBy(xpath = "//*[@id='search-silter-layout']//span[@class='MuiChip-label']")
    private List<WebElement> seachSilterLabel2;

    @FindBy(xpath = "//button/span[contains(text(),'Clear All')]")
    private WebElement clearAll;

    @FindBy(xpath = "//span[contains(text(),'Applied Filters')]")
    private WebElement appliedFilters;

    @FindBy(xpath = "//*[@id='search-sortby-layout']/div[1]/div")
    private WebElement searchResultProductCountMsg;

    @FindBy(xpath = "//div[starts-with(@id,'product-sku')]/child::div[2]/a")
    private List<WebElement> facetsProductsTitles;

    @FindBy(xpath = "//*[@id='search-silter-layout']/div/div[2]/div/div[3]/div/div/div/div/div/div/div[2]/span/span[3]")
    private WebElement priceSliderLeftIcon;

    @FindBy(xpath = "//*[@id='search-silter-layout']/div/div[2]/div/div[3]/div/div/div/div/div/div/div[2]/span/span[4]")
    private WebElement priceSliderRightIcon;
    
   



    /**
     * This method is used for navigation
     */
    public void userNavigateToCategoryPage() {
        navigateToParticularPage("https://uat1emaxme.lmsin.net/ae/en/shop-mxwomen-shoes");


    }

    /**
     * This method is used to verify user landed to category listing page
     */
    public void verifyUserLandedToCategoryListingPage() {

        waitForElementPresent(5);
        SoftAssert sa=new SoftAssert();
        waitForWebElementPresent(relevance,15);
       sa.assertEquals(relevance.getText(),"Relevance","Relevance text is not displayed in category page");
        sa.assertEquals(sortByText.getText(),"Sort by","sort by text is not displayed in category page");
        String text = searchResultTitle.getText();
        System.out.println("Search result text validation"+text);
        //sa.assertEquals(searchResultTitle.getText()," You searched for “mobiles”"," You searched for “mobiles” text is not displayed in category page");
        sa.assertAll();

    }

    /**
     * This method is used to click on subcategory section
     */
    public String clickOnSubcategory() {
        waitForElementToBeClickable(categorySectionExpandArrow,15);
        categorySectionExpandArrow.click();
        waitForElementPresent(4);
        String subCategoryText = subCategoryItem.getAttribute("value").trim();
        subCategoryItem.click();
        waitThread(4000);
        visibilityOf(categoryLabelTitle,20);
        String title =categoryLabelTitle.getText().trim();
        title=title.replaceAll("[\\n(2)]","").toLowerCase();
        System.out.println("title"+title);
        System.out.println("subCategoryText"+subCategoryText);
        Assert.assertEquals(title.trim(),subCategoryText.trim(),title.trim()+"mismatch"+subCategoryText.trim());
        Assert.assertEquals(title.trim().toLowerCase(), seachSilterLabel.getText().trim().toLowerCase(),"mismatch search label and search item");
        return title;

    }

    /**
     * This method is used to get facets count
     * @return
     */
    public int getFacetsCount() {
        waitForPageToLoad(10);
        int count = categoryList.size();
        return count;
    }

    /**
     * This method is used to verify sort by section
     */

    public void verifySortBySection() {
        waitForPageToLoad(20);
        waitForWebElementPresent(sortByText,15);
        SoftAssert sa=new SoftAssert();
        sa.assertEquals(sortByText.getText().trim(),"Sort by","Sort by text miss match");
        sa.assertAll();

    }

    /**
     * This method is used to check default vlaued in sort by dropdown
     * @param sortValue
     */
    public void sortByDropDownDefaultValue(String sortValue) {
        SoftAssert sa=new SoftAssert();
        sa.assertTrue(sortByIcon.isDisplayed(),"sortBy layout not displayed");
        String actual = defaultSortValue.getText().trim();
        sa.assertEquals(actual,sortValue,actual+"Sort by text miss match  "+sortValue);
        sa.assertAll();
    }

    /**
     * This method is used to click on sort by dropdown icon
     */

    public void clickOnSortByDropDownIcon() {
        SoftAssert sa=new SoftAssert();
        sa.assertTrue(sortByIcon.isDisplayed(),"Sort By ");
        sa.assertAll();
        mouseHoverClick(sortByIcon);
        //sortByIcon.click();
    }
    /**
     * This method is used to compare price vlaues based on the low to high and high to low drop down value
     * @param value dropdown value "low to high or high to low"
     */
    public void priceValueInProductList(String value) {
        waitForElementPresent(3);
        Assert.assertEquals(dropDownValue.getText().trim(),value.trim(),"drop down value is not same");
        System.out.println("Sort value:"+priceValueInProductListing.size());
        int dropDownSize= priceValueInProductListing.size();
        int priceArray[] = new int[dropDownSize];
        for(int i=0;i< dropDownSize;i++){
            String price = priceValueInProductListing.get(i).getText().trim();
            System.out.println("Given price value : \t"+price);
                priceArray[i] = Integer.parseInt(price);
                if(i>=1){
                    if(priceArray[i]>priceArray[i-1]){
                        System.out.print("Low to high\t:");
                        System.out.println(priceArray[i-1]+ "<"+priceArray[i]);
                    }else{
                        System.out.print("High to low\t:");
                        System.out.println(priceArray[i]+ ">"+priceArray[i-1]);
                    }
                }
            }




    }


    /**
     * This method is used to sort by value
     *
     */
    public void facetsProductTitleValidation(String value) {
        waitForElementPresent(3);
       // Assert.assertEquals(facetsProductsTitles.getText().trim(),value.trim(),"drop down value is not same");
        System.out.println("Sort value:"+facetsProductsTitles.size());
        int dropDownSize= facetsProductsTitles.size();
        for(int i=0;i< dropDownSize;i++){
            String productTitle = facetsProductsTitles.get(i).getText().trim().toLowerCase();

            Assert.assertTrue( productTitle.contains(value),"product description having given search key");


        }




    }
    /**
     * This method is used to sort by value
     * @param value
     */
    public void sortByValue(String value) {
        int dropDownSize= sortByValues.size();
        for(int i=0;i< dropDownSize;i++){
            if(sortByValues.get(i).getText().trim().equals(value)){
                waitForElementPresent(6);
                mouseHoverClick(sortByValues.get(i));
               // sortByValues.get(i).click();
                waitForElementPresent(5);
            }

        }

    }

    /**
     * This method is used to verify category title
     */
    public void verifyCategoryTitle() {
        SoftAssert sa=new SoftAssert();
        sa.assertTrue(categoryTitle.isDisplayed(),"category title is not displayed");
        sa.assertAll();
    }

    /**
     * This method is used to verify total products in category
     */
    public void verifyTotalProducts() {
        SoftAssert sa=new SoftAssert();
        sa.assertTrue(categoryProductsCount.isDisplayed(),"category product count is not displayed");

        sa.assertAll();
    }

    /**
     * This method is used to verify all the product details like image,description,currency and product value
     */
    public void verifyProductDetails() {
        SoftAssert sa=new SoftAssert();
        sa.assertFalse(productDescription.getText().trim().isEmpty(),"Product description is not displayed");
        sa.assertTrue(productImage.isDisplayed(),"Product image is not displayed");
        sa.assertEquals(productCurrencyCode.getText().trim(),"AED","country code is not displayed");
        sa.assertTrue(productCurrencyValue.isDisplayed()," product currency value  is not displayed");
        sa.assertAll();
    }

    /**
     * This method is used to verify bread crumbs
     */
    public void verifyBreadCrumb() {
        SoftAssert sa=new SoftAssert();

        sa.assertEquals(breadCrumbRoot.getText(),"Women");
        sa.assertEquals(breadCrumbChild.getText(),"Insoles");
        sa.assertAll();
    }

    /**
     *This method is used to click on root bread crumb to navigate back
     */
    public void clickOnRootBreadCrumb() {
        waitForElementToBeClickable(breadCrumbChild,15);
        String root= breadCrumbChild.getText().trim();
        breadCrumbChild.click();
        waitThread(5000);
        SoftAssert sa=new SoftAssert();
        sa.assertEquals( breadCrumbChild.getText().trim(),root);
        sa.assertAll();

    }

    public void clickOnLoadMore() {
        scrolldownElement(loadMoreProducts);
        waitForElementPresent(5);
        elementMouseClick(loadMoreProducts);
    }

    public void numberOfProductsRemaining() {

        Assert.assertTrue(totalProductscountPerPage2.isDisplayed());
    }

    public void clickOnClearAll() {
        clearAll.click();
    }

    public void verifyAllUIComponentsInFilterSection() {
        Assert.assertEquals(appliedFilters.getText().trim(),"Applied Filters");
        Assert.assertEquals(clearAll.getText().trim(),"Clear All");
        Assert.assertTrue(searchResultProductCountMsg.isDisplayed(),"search result count is not displayed");

    }


    public void applyMultipleFilterAndValidateProduct() {
        waitForElementPresent(4);
        int dropDownSize= expandArrowsList.size();
        for(int i=0;i< dropDownSize;i++){
                waitForElementPresent(3);
                mouseHoverClick(expandArrowsList.get(i));

                waitForElementPresent(2);
            }
        waitForElementPresent(4);


        String subCategoryText = subCategoryItem.getAttribute("value").trim();
        subCategoryItem.click();
        waitForElementPresent(4);
        visibilityOf(categoryLabelTitle,20);
        String title =categoryLabelTitle.getText().trim();
        title=title.replaceAll("[\\n(2)]","").toLowerCase();
        System.out.println("title"+title);
        System.out.println("subCategoryText"+subCategoryText);
        Assert.assertEquals(title.trim(),subCategoryText.trim(),title.trim()+"mismatch"+subCategoryText.trim());
        Assert.assertEquals(title.trim().toLowerCase(), seachSilterLabel.getText().trim().toLowerCase(),"mismatch search label and search item");
        waitForElementPresent(5);
        String subCategoryText2 = subCategoryItem2.getAttribute("value").trim();
        subCategoryItem2.click();
        waitForElementPresent(5);
        Assert.assertEquals(subCategoryText2.trim().toLowerCase(), seachSilterLabel2.get(1).getText().trim().toLowerCase(),"mismatch search label and search item");

    }

    public String selectPriceRangAndValidate() {
        waitForElementPresent(2);
        expandArrowsList.get(2).click();
        waitForElementPresent(2);
        slider(priceSliderLeftIcon,30,0);
        waitForElementPresent(2);
        String priceRange = seachSilterLabel2.get(0).getText().trim();
        System.out.println("Price range"+priceRange);
        return priceRange;
    }

    public void productPriceValidation(String filter) {
        waitForElementPresent(3);
        // Assert.assertEquals(facetsProductsTitles.getText().trim(),value.trim(),"drop down value is not same");
        System.out.println("Sort value:"+facetsProductsTitles.size());
        int dropDownSize= facetsProductsTitles.size();
        for(int i=0;i< dropDownSize;i++) {
            String productTitle = facetsProductsTitles.get(i).getText().trim().toLowerCase();

            Assert.assertTrue(productTitle.contains(filter), "product description having given search key");
        }
        
        
        }
    
}
