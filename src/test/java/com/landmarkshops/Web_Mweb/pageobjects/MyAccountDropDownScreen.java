package com.landmarkshops.Web_Mweb.pageobjects;

import com.landmarkshops.Web_Mweb.config.config;
import com.landmarkshops.Web_Mweb.utilities.WebUtils;
import com.lmg.blc.web.PageObjects.HeaderSectionPage;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.testng.asserts.SoftAssert;

/**
 * This class used to show username and my account dropdown items
 * User  able to  navigate to MyAccount,MyAddresses,OrderHistory and SignOut
 *
 * <AUTHOR>
 */
public class MyAccountDropDownScreen extends WebUtils {


    HeaderSectionPage hsp;
    public MyAccountDropDownScreen(WebDriver driver) {
        super(driver);
        PageFactory.initElements(driver,this);
    }


/*    emax

@FindBy(xpath = "//*[@id='account-actions-signin']/span/div")
    private WebElement myAccount;*/


    @FindBy(xpath = "//*[@id='desk-header-layout']/div/div[2]/div[1]/div/button/span/div[1]")
    private WebElement myAccount;

    @FindBy(xpath = "//*[@id='auth-container']/div/div/div[1]/div[1]/div")
    private WebElement m_myAccount_profile;


    @FindBy(xpath = "//*[@id='auth-container']/div/div/div[1]/div[2]/div/a/span")
    private WebElement m_myAccount;

    @FindBy(xpath = " //*[@id='root-header']/div/div/div[2]/button")
    private WebElement m_closeHamburgerMenu;









/*    @FindBy(xpath = "//*[@id='account-actions-signin']/span/img")
    private WebElement myAccountDropDownArrow;*/

    @FindBy(xpath = "//*[@id='auth-container']/div/div/div[1]/div[2]/div/a/span")
    private WebElement m_myAccountDropDownArrow;

    @FindBy(xpath = "//*[@id=\"header-basket-signinWrap\"]/div[1]/div/button")
    private WebElement myAccountDropDownArrow;

    @FindBy(xpath = "//*[@id='desk-header-layout']/div/div[2]/div[1]/div/div/div/div/div/div[2]/ul/a[1]/span")
    private WebElement myAccountMenuItem;

    @FindBy(xpath = "//*[@id='desk-header-layout']/div/div[2]/div[1]/div/div/div/div/div/div[2]/ul/a[2]/span")
    private WebElement profile;

    @FindBy(xpath = "//*[@id='root-desk-top-right-inner']/div[2]/div[1]/div/div/div/div/div/div[2]/ul/a[2]/span")
    private WebElement myAddresses;

    @FindBy(xpath = "//*[text()='Orders']")
    private WebElement myOrderHistory;

    @FindBy(xpath = "//a/span[contains(text(),'Address book')]")
    private WebElement address;


    @FindBy(xpath = "//a/span[contains(text(),'Payment')]")
    private WebElement payments;

    @FindBy(xpath = "//a/span[contains(text(),'Orders')]")
    private WebElement orders;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[2]/div/div[9]/a/div")
    private WebElement signOut;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[2]/div[1]/p")
    private WebElement myOrderHistoryTital;

    @FindBy(xpath = "//*[text()='View and track your orders.']")
    private WebElement myOrderHistorySubTital;

    @FindBy(xpath = "//*[text()='Hmmm. You have no orders.']")
    private WebElement emptyMyOrderHistoryMessage1;

    @FindBy(xpath = "//*[text()='Let’s fix that right away.']")
    private WebElement emptyMyOrderHistoryMessage2;

    @FindBy(xpath = "//*[text()='Start Shopping']")
    private WebElement startShoppingButtonInMyOrderHistory;

    @FindBy(xpath = "//*[@id='my-acc-dropdown-5_QA']/span")
    private WebElement myAddressesDropdown;

    /**
     * This method is used to verify username displayed once user Logged in.
     */

    public void verifyLoggedInUserName() {
        if ((config.getBrowser().equalsIgnoreCase("MOBILEWEBCHROMEANDROID")) || (config.getBrowser().equalsIgnoreCase("MOBILECHROME"))) {
            hsp = new HeaderSectionPage(driver);
            hsp.clickOnHamburgerMenu();
            waitForElementPresent(8);
            acceptAlerts();
            visibilityOf(m_myAccount_profile, 20);
            String name = m_myAccount_profile.getText().trim();
            testLogger.info("name as " + name);
            acceptAlerts();
            javaScriptExecuteClick(m_closeHamburgerMenu);
            //m_closeHamburgerMenu.click();

        } else {
            waitForPageToLoad(10);
            //waitForElementPresent(10);
            /**
            Need to remove the refresh. Its frountend issue for ptegy environment.
            */
            if(!verifyNoelement(myAccount)){
                pageRefresh();
                waitForPageToLoad(10);
            }
            visibilityOf(myAccount, 35);
            String name = myAccount.getText().trim();
            testLogger.info("name as " + name);

        }
    }

    /**
     * This method is used to click on MyAccount DropDown
     */

    public void clickOnMyAccountDropDown() {

        if ((config.getBrowser().equalsIgnoreCase("MOBILEWEBCHROMEANDROID")) || (config.getBrowser().equalsIgnoreCase("MOBILECHROME"))) {
            hsp = new HeaderSectionPage(driver);
            hsp.clickOnHamburgerMenu();
            waitForVisibilityOfElementLocated(m_myAccountDropDownArrow, driver, 20);
            waitForElementToBeClickable(m_myAccountDropDownArrow, 25);
            m_myAccountDropDownArrow.click();
            waitForElementPresent(3);
        } else {
            waitForPageToLoad(25);
            waitForVisibilityOfElementLocated(myAccountDropDownArrow, driver, 20);
            waitForElementToBeClickable(myAccountDropDownArrow, 25);
            javaScriptExecuteClick(myAccountDropDownArrow);

            waitForElementPresent(3);
        }
    }

    /**
     * This method is used to click on Profile DropDown
     */

    public void clickOnProfileDropDown() {
        waitForPageToLoad(100);
        visibilityOf(profile,35);
        waitForElementToBeClickable(profile,35);
        profile.click();
    }


    /**
     * This method is used to click on Orders DropDown
     */

    public void clickOnMyOrdersDropDown() {
        waitForElementToBeClickable(myAccountDropDownArrow,15);
        myAccountDropDownArrow.click();
    }

    /**
     * This method is used to click on Address Book DropDown
     */

    public void clickOnMyAddressBookDropDown() {
        waitForElementToBeClickable(myAddressesDropdown,15);
        myAddressesDropdown.click();
    }


    /**
     * This method is used to click on Address Book DropDown
     */

    public void clickOnMyPaymentDropDown() {
        waitForElementToBeClickable(myAccountDropDownArrow,15);
        myAccountDropDownArrow.click();
    }


    /**
     * This method is used to get UI components in MyAccount dropdown
     */
    public void verifyAllMyAccountDropItems() {
        //mouseHover(myAccountMenuItem);
        String  myAccountText=myAccountMenuItem.getText().trim();
        String  addressBook=address.getText().trim();
        String  payments=address.getText().trim();
        String  ordersText=orders.getText().trim();
        String  signOutText=signOut.getText().trim();
        testLogger.info("My accountText :"+myAccountText);
        testLogger.info("My myAddressesText :"+addressBook);
        testLogger.info("My orderHistoryText :"+ordersText);
        testLogger.info("My signOutText :"+signOutText);
    }

    /**
     * This method is used to click  on MyAccount dropdown menu item
     */
    public void clickOnMyAccount() {
        elementMouseClick(myAccountMenuItem);
    }

    /**
     * This method is used to click on MyAddresses dropdown menu item
     */
    public void clickOnMyAddresses() {

        elementMouseClick(address);
    }

    /**
     * This method is used to click on OrderHistory dropdown menu item
     */
    public void clickOnOrderHistory() {

        elementMouseClick(orders);
    }

    /**
     * This method is used to click on SignOUt dropdown menu item
     */
    public void clickOnSignOut() {
        waitForElementToBeClickable(signOut,15);
        signOut.click();
        waitForPageToLoad(5);
    }

    public void verifyEmptyMyOrderHistoryPage() {

        SoftAssert sa = new SoftAssert();
        sa.assertTrue(myOrderHistoryTital.isDisplayed(), "My Order History title is not displayed");
        sa.assertEquals(myOrderHistoryTital.getText().trim(),"Orders");
        testLogger.info("My order History title : "+myOrderHistoryTital.getText().trim());

        sa.assertTrue(myOrderHistorySubTital.isDisplayed(), "My Order History sub-title is not displayed");
        sa.assertEquals(myOrderHistorySubTital.getText().trim(),"View and track your orders.");
        testLogger.info("My order History sub-title : "+myOrderHistorySubTital.getText().trim());

        sa.assertTrue(emptyMyOrderHistoryMessage1.isDisplayed(), "My Order History message is not displayed");
        sa.assertEquals(emptyMyOrderHistoryMessage1.getText().trim(),"Hmmm. You have no orders.");
        testLogger.info("My order History sub-title : "+emptyMyOrderHistoryMessage1.getText().trim());
        sa.assertEquals(emptyMyOrderHistoryMessage2.getText().trim(),"Let’s fix that right away.");
        testLogger.info("My order History sub-title : "+emptyMyOrderHistoryMessage2.getText().trim());
        sa.assertAll();
    }

    public void verifyMyOrderHistoryPageWithMultipleOrder() {

        SoftAssert sa = new SoftAssert();
        sa.assertTrue(myOrderHistoryTital.isDisplayed(), "My Order History title is not displayed");
        sa.assertEquals(myOrderHistoryTital.getText().trim(),"Orders");
        testLogger.info("My order History title : "+myOrderHistoryTital.getText().trim());

        sa.assertTrue(myOrderHistorySubTital.isDisplayed(), "My Order History sub-title is not displayed");
        sa.assertEquals(myOrderHistorySubTital.getText().trim(),"View and track your orders.");
        testLogger.info("My order History sub-title : "+myOrderHistorySubTital.getText().trim());
        waitThread(15000);
//        sa.assertTrue(emptyMyOrderHistoryMessage1.isDisplayed(), "My Order History message is not displayed");
//        sa.assertEquals(emptyMyOrderHistoryMessage1.getText().trim(),"Hmmm. You have no orders.");
//        testLogger.info("My order History sub-title : "+emptyMyOrderHistoryMessage1.getText().trim());
//        sa.assertEquals(emptyMyOrderHistoryMessage2.getText().trim(),"Let’s fix that right away.");
//        testLogger.info("My order History sub-title : "+emptyMyOrderHistoryMessage2.getText().trim());
        sa.assertAll();
    }
}
