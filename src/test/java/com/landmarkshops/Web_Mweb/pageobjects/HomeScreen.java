package com.landmarkshops.Web_Mweb.pageobjects;

import com.landmarkshops.Web_Mweb.utilities.WebUtils;
import org.openqa.selenium.By;
import org.openqa.selenium.Keys;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.testng.Assert;
import org.testng.asserts.SoftAssert;

import java.util.List;


/**
 * This class used to define all UI components and related actions when user first time landed to website
 * This Home screen class used all the banners ,carousel banners, rails, links,previous and next carousel banner navigation
 *
 * <AUTHOR>
 */
public class HomeScreen extends WebUtils {
    public HomeScreen(WebDriver driver) {
        super(driver);
        PageFactory.initElements(driver, this);
    }

    @FindBy(xpath = "//*[@id='js-site-search-input']")
    private WebElement searchBox;

    @FindBy(xpath = "//*[text()='Categories']")
    WebElement searchCategories;
    @FindBy(xpath = "//*[@id='product-list--departments']/a")
    private List<WebElement> searchCategoriesList;

    @FindBy(xpath = "//*[text()='Top Products']")
    WebElement searchTopProducts;
    @FindBy(xpath = "//*[@id='product-list']/a")
    private List<WebElement> searchTopProductsList;

    @FindBy(xpath = "//*[text()='Recent Searches']")
    WebElement recentSearches;
    @FindBy(xpath = "//*[@id='root-desk-top-inner']/div[1]/div/div/form/div[2]/div/div/div/div/div/div[2]/ul/a/span")
    private List<WebElement> recentSearchesList;
    @FindBy(xpath = "//*[text()='Clear']")
    WebElement clearSearchHistory;
    @FindBy(xpath = "//*[contains(text(),'Sorry, there are no matches for')]")
    WebElement emptySearchMessage;

    @FindBy(xpath = " //*[@id='hero_banner_wrapper']/div/button[1]")
    private WebElement previousCarouselBanner;

    @FindBy(xpath = " //*[@id='hero_banner_wrapper']/div/button[2]")
    private WebElement nextCarouselBanner;

    @FindBy(xpath = "//*[@id='hero_banner_wrapper']/div/ul/li")
    private List<WebElement> listOfBanners;

    @FindBy(xpath = "//div[@class='video-wrapper']/video[@class='video-trending-banner']")
    private WebElement videoBanner;


    //@FindBy(xpath = "//*[@id='undefined-14']/div/div/div/div[1]/div/div/a/div")
    @FindBy(xpath = "//*[@id='block-maxae-DeptPage-men-v2-Section3KSlot-12']")
    private WebElement videoBannerSc;


    @FindBy(id = "block-maxae-DeptPage-men-v2-Section3DSlot-5")
    private WebElement staticBannerClick;


    @FindBy(id ="block-maxae-DeptPage-men-v2-Section3DSlot-5")
    private WebElement staticBannerTitle;


    @FindBy(xpath = "//*[@id='block-maxae-DeptPage-men-v2-Section3GSlot-12']/div[1]/div/div/h2")
    private WebElement railTitle;

    @FindBy(xpath = "//*[@id='block-maxae-DeptPage-men-v2-Section3GSlot-12']/div[2]/div/div/div/div/div[2]/div/div/div[1]/a/img")
    private WebElement railImg;

    @FindBy(xpath = "//*[@id='block-maxae-DeptPage-men-v2-Section3GSlot-12']/div[1]/div/div/div")
    private WebElement railTimer;

    @FindBy(xpath = "//*[@id='block-maxae-DeptPage-men-v2-Section3GSlot-12']/div[1]/div/a")
    private WebElement railViewAll;

//    @FindBy(linkText = "View All")
//    private WebElement railViewAll;

    @FindBy(xpath = "//*[@id='block-maxae-DeptPage-men-v2-Section3GSlot-12']/div[2]/div/div/button[2]")
    private WebElement railRightCarousel;

    @FindBy(xpath = "//*[@id='block-maxae-DeptPage-men-v2-Section3GSlot-12']/div[2]/div/div/button[1]")
    private WebElement railLeftCarousel;

    @FindBy(xpath = "")
    private WebElement railCount;

    @FindBy(css = "#block-maxae-DeptPage-men-v2-Section3GSlot-12 > div:nth-child(1) > div > div > div")
    private WebElement railSection;

    @FindBy(xpath = "//*[@id='block-maxae-DeptPage-men-v2-Section3GSlot-12']/div[2]/div/div/div/div/div[1]/div/div/div[3]/div/div[2]")
    private WebElement railProductPrice;

    @FindBy(xpath = "//*[@id='block-maxae-DeptPage-men-v2-Section3GSlot-12']/div[2]/div/div/div/div/div[1]/div/div/div[2]/a")
    private WebElement railProductDescription;

    @FindBy(xpath = "//*[text()='Most loved']")
    WebElement railMostLovedProduct;

    @FindBy(xpath = "//div[contains(@class, 'MuiBox') and @tabindex='-1' and @dir='ltr']")
    WebElement railMostLovedProductSlider;

    @FindBy(xpath = "//div[@class='badges']")
    WebElement railMostLovedProductImageBadges;


    @FindBy(xpath = "//*[@id=\'dept-\']/a/span[contains(text(),'All Categories')]")
    WebElement allCategories;

    @FindBy(xpath = "//*[@id='category-menu-0']/div[1]/div[1]/a/span[contains(text(),'Mobiles & Accessories')]")
    WebElement mobileAndAccessories;

    @FindBy(xpath = "//*[@id='category-menu-0']/div[2]/div[1]/a/span[contains(text(),'Tablets & Accessories')]")
    WebElement tabletsAndAccessories;

    @FindBy(xpath = "//*[@id='category-menu-0']/div[3]/div[1]/a/span[contains(text(),'Computers & Network')]")
    WebElement computersAndNetwork;

    @FindBy(xpath = "//*[@id='category-menu-0']/div[4]/div[1]/a/span[contains(text(),'Televisions & Audio')]")
    WebElement televisionsAndAudio;

    @FindBy(linkText = "iPhone")
    WebElement iphoneLink;

    @FindBy(linkText = "Ipad")
    WebElement ipadLink;

    @FindBy(linkText = "Samsung TV")
    WebElement samsungTVLink;

    @FindBy(xpath = "//*[@id='root-desk-top-right-inner']/div[2]/img")
    WebElement prideLogo;



    /**
     * This method is used to check list of carousel banners in that section and navigate to each on of the page from  begin to end.
     */

    public void carouselBannerNavigationFromBegin() {
        for (int i = 0; i < listOfBanners.size()-6; i++) {
            driver.findElement(By.xpath("//*[@id='hero_banner_wrapper']/div/ul/li[" + (i + 1) + "]/button")).click();
            waitThread(1000);
        }

    }

    /**
     * This method is used to check list of carousel banners in that section and navigate to each on of the page from End to begin.
     */
    public void carouselBannerNavigationFromEnd() {
        for (int i = listOfBanners.size() - 6; i > 0; i--) {
            driver.findElement(By.xpath("//*[@id='hero_banner_wrapper']/div/ul/li[" + (i) + "]/button")).click();
            waitThread(1000);
        }

    }

    /**
     * This method is used to click next banner from right side
     */
    public void clickOnNextCarouselBanner() {
        for (int i = 0; i < listOfBanners.size() - 6; i++) {
            waitForElementToBeClickable(nextCarouselBanner, 20);
            nextCarouselBanner.click();
            waitThread(1000);
        }

    }

    /**
     * This method is used to click previous banner from left side
     */
    public void clickOnPreviousCarouselBanner() {
        for (int i = 0; i < listOfBanners.size() - 6; i++) {
            waitForElementToBeClickable(previousCarouselBanner, 20);
            previousCarouselBanner.click();
            waitThread(1000);
        }

    }

    /**
     * This method is used to click on any static banner in home page.
     */
    public void clickOnBanner() {
        scrolldownElement(staticBannerClick);
        waitForElementToBeClickable(staticBannerClick, 20);
        elementMouseClick(staticBannerClick);

        waitThread(3000);

    }

    /**
     * This method is used to fetch title of the banner.
     */
    public String verifyBannerTitle() {
        scrolldownElement(staticBannerTitle);
        waitForElementToBeClickable(staticBannerTitle, 20);
        return staticBannerTitle.getText().trim();

    }

    /**
     * This method is used to get url of the page.
     */
    public String verifyNavigatePageURL() {
        waitForPageToLoad(10);
        return driver.getCurrentUrl().trim();

    }

    /**
     * This method is used to get title of the video banner.
     */

    public String getVideoTitle() {

        scrollToParticularElement(videoBannerSc);
        scrolldown();
        String id = videoBanner.getAttribute("id");
        String number = extractIntegersInString(id);
        String firstIndex = number.substring(0, number.length() - 1).trim();
        String lastIndex = number.substring(number.length() - 1).trim();
        By category = By.id("category_title_" + firstIndex + "_" + lastIndex);
        visibilityOf(driver.findElement(category), 15);
        return driver.findElement(category).getText();
    }


    /**
     *This method is used to get the product title of the page
     * @return product title
     */

    public String verifyRailProductTile(){

        return railTitle.getText().trim();
    }

    /**
     * This method is used to get timer of the producut rail section
     * @return Timer value
     */
    public String getRailTimer(){
        scrolldownElement(railTimer);
        return railTitle.getText().trim();

    }

    /**
     *This method is used to get product description
     * @return product description
     */
    public String getRailProductDescription(){
        return railProductDescription.getText();
    }

    /**
     *This method used to get product price
     * @return product price value
     */
    public String getRailProductPrice(){
        return railProductPrice.getText();
    }

    /**
     * User click on seeAll link it should redirect to respective category or offers page
     */
    public String getRailViewAll() {

         return railViewAll.getText().trim();
    }

    public void clickRailSeeAll() {
        elementMouseClick(railViewAll);

    }

    /**
     * This method is used to click on right carousel and navigate to right side
     */
    public void clickRailRightCarousel(){
        railRightCarousel.click();
    }

    /**
     * This method is used to click on left carousel and navigate to products
     *
     */
    public void clickRailLeftCarousel(){
        railLeftCarousel.click();
    }

    /**
     * This method is used to click on product Image in Rail section and navigate to respective category or offers page
     */
    public void clickRailProductImage(){
        elementMouseClick(railImg);

    }

    /**
     * This method is used to click on product description and navigate to respective page
     */
    public void clickRailProductDescription(){
        elementMouseClick(railProductDescription);

    }

    /**
     * This method is used to click on video banner in home page.
     */

    public void clickOnVideoBanner() {

        videoBannerSc.click();
    }
    /**
     * This method is used to click pagination button to navigate particular img/video.
     */
    public void clickOnCertainPagination() {
        int i=5;
        driver.findElement(By.xpath("//*[@id='hero_banner_wrapper']/div/ul/li[" + (i) + "]/button")).click();
    }

    /**
     * This method used to check the section of the product rail in home page
     * @return true or false
     */

    public Boolean verifyProductRailSection() {
        implicitWait(10);
        scrollToParticularElement(videoBannerSc);
        visibilityOf(videoBannerSc,15);
        scrollToElementBasedOnLocation(railSection);
        return railSection.isDisplayed();
    }

    public String verifyProductDescription() {
        waitForElementToBeClickable(railProductDescription,10);
        return railProductDescription.getText();
    }

    /**
     * This method is used verify products per each section
     */
    public void railProductsPerSection() {
        // Need Dev support for coding this
    }

    /**
     * This method is used to check count (5) of the products in rail section before swipe arrow
     */
    public void validateProductCountBeforeArrowSwipe() {
        // Need Dev support for coding this
    }


    /**
     * This method is used to check count (5) of the products in rail section after swipe arrow
     */
    public void validateProductCountAfterTheSwipe() {
        // Need Dev support for coding this
    }


    public void verifyMostLovedProductTitle(){
        scrollToParticularElement(railMostLovedProduct);
        SoftAssert sa = new SoftAssert();
        sa.assertTrue(railMostLovedProduct.isDisplayed(), "Most viewed product title is not displayed");
        String logMessage = railMostLovedProduct.isDisplayed() ? "is displayed" : "is not displayed";
        testLogger.info("Most viewed product title "+logMessage+" in rails");
        sa.assertAll();
    }

    public void verifyFirstProductWithBadgesInMostLovedSlider(){
        implicitWait(10);
        scrollToParticularElement(videoBannerSc);
        scrollToParticularElement(railMostLovedProduct);
        SoftAssert sa = new SoftAssert();
        sa.assertTrue(railMostLovedProductSlider.isDisplayed(), "Most viewed product is not displayed");
        String logMessage = railMostLovedProductSlider.isDisplayed() ? "is displayed" : "is not displayed";
        testLogger.info("Most viewed product "+logMessage+" in rails");
        sa.assertTrue(railMostLovedProductImageBadges.isDisplayed(), "Most viewed product Badge is not displayed");
        logMessage = railMostLovedProductImageBadges.isDisplayed() ? "is displayed" : "is not displayed";
        testLogger.info("Most viewed product badge "+logMessage+" in rails");
        sa.assertAll();
    }

    public void verifyAndClickFirstProductInMostLoved(){
        scrollToParticularElement(railMostLovedProduct);
        waitThread(10000); // for Demo
        railMostLovedProductSlider.click();
        testLogger.info("First product is clicked in the Most Loved slider");
    }

    public void verifyDepartmentSection() {
        String departmentName = allCategories.getText().trim();
        mouseHover(allCategories);
        Assert.assertEquals(departmentName,"All Categories" ,"All categories text not yet implemented");
        System.out.println(" All Categories text validation "+departmentName);
    }

    public void chooseMobileAndAccessories() {
        mouseHover3(allCategories,mobileAndAccessories,iphoneLink);
    }

    public void getProductTitle() {
        String getProductURl = driver.getCurrentUrl();
        System.out.println("Given url value as:"+getProductURl);
    }

    public void chooseTablets() {

        mouseHover3(allCategories,tabletsAndAccessories,ipadLink);
    }

    public void enterSearch(String search){
        waitThread(5000); //for demo
        searchBox.sendKeys(search);
        testLogger.info("Entered Search: "+searchBox.getText().trim());
    }

    public void enterSearchTextAndPressEnterKey(String search){
        waitThread(5000); //for demo
        searchBox.sendKeys(search);
        searchBox.sendKeys(Keys.ENTER);
        testLogger.info("Entered Search: "+searchBox.getText().trim());
    }

    public void verifyPrideMemberShipLogo() {
        waitForWebElementPresent(prideLogo,15);
        prideLogo.click();
    }

    public void clearEnterSearch(){
        waitThread(5000); //for demo
        searchBox.click();
        searchBox.clear();
        searchBox.click();
        for(int i=0; i<=20; i++){
            searchBox.sendKeys(Keys.BACK_SPACE);
            i++;
        }
        testLogger.info("clear entered search result");
    }

    public void verifyRecentSearches(){
        waitThread(5000); //for demo
        searchBox.click();
        SoftAssert sa = new SoftAssert();
        sa.assertTrue(recentSearches.isDisplayed(), "Recent Searches section is not displayed");
        testLogger.info("Recent search displayed in search results");
        findElementsList(recentSearchesList);
        testLogger.info("-------------------------------------------------");
        sa.assertAll();
    }

    public void clearAllRecentSearch(){
        waitThread(5000); //for demo
        clearSearchHistory.click();
        waitThread(5000); //for demo
        searchBox.click();
        testLogger.info("All Recent Searches is cleared");
    }

    public void verifyCategoriesInSearch(){
        SoftAssert sa = new SoftAssert();
        sa.assertTrue(searchCategories.isDisplayed(), "Categories section is not displayed");
        testLogger.info("Categories displayed in search results");
        findElementsList(searchCategoriesList);
        testLogger.info("-------------------------------------------------");
        sa.assertAll();
    }

    public void clickOnCategoriesByNameInSearch(String categoriesName){
        waitThread(5000); //for demo
        clickElementsList(searchCategoriesList,categoriesName);
        waitThread(10000);
        testLogger.info(categoriesName+" is clicked in Categories");
    }

    public void verifyTopProductsInSearch(){
        SoftAssert sa = new SoftAssert();
        sa.assertTrue(searchTopProducts.isDisplayed(), "Top Products section is not displayed");
        testLogger.info("Top Products displayed in search results");
        findElementsList(searchTopProductsList);
        testLogger.info("-------------------------------------------------");
        sa.assertAll();
    }

    public void clickOnTopProductsByNameInSearch(String categoriesName){
        waitThread(5000); //for demo
        clickElementsList(searchTopProductsList,categoriesName);
        waitThread(10000);
        testLogger.info(categoriesName+" is clicked in Top Products");
    }

    public void verifyNoMatchSearchMessage(String search){
        SoftAssert sa = new SoftAssert();
        waitThread(5000); //for demo
        sa.assertTrue(emptySearchMessage.isDisplayed(), "No Match screen is not displayed");
        //sa.assertEquals(emptySearchMessage.getText().trim(),"Sorry, there are no matches for “"+search+"”");
        testLogger.info("Button displayed message - "+emptySearchMessage.getText().trim());
        waitThread(10000);
        sa.assertAll();
    }




}