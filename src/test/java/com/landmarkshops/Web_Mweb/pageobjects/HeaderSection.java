package com.landmarkshops.Web_Mweb.pageobjects;

import com.landmarkshops.Web_Mweb.config.config;
import com.landmarkshops.Web_Mweb.utilities.BaseURLBean;
import com.landmarkshops.Web_Mweb.utilities.WebUtils;
import com.lmg.blc.web.PageObjects.BasketPage;
import com.lmg.blc.web.PageObjects.HeaderSectionPage;
import com.lmg.blc.web.PageObjects.MyAccountDropDownItems;
import org.openqa.selenium.Keys;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;


/**
 * This class is used validate header components and actions of the web page
 * <AUTHOR>
 */
public class HeaderSection extends WebUtils {


    HeaderSectionPage hsp;

    public HeaderSection(WebDriver driver) {
        super(driver);
        PageFactory.initElements(driver, this);

    }
    //*[@id="page-header"]/a/span/img

    @FindBy(xpath="//*[@id='page-header']/a/span/img")
    private WebElement maxlogo;

    @FindBy(xpath="//*[@id='__next']/div[1]//div[1]/div/img")
    private WebElement maxlogoCheckoutPage;

    @FindBy(xpath = "//*[@id='root-desk-top-inner']/div[1]/a/span/img")
    private WebElement emaxLogo;

    @FindBy(xpath = "//*[@name='q']")
    private WebElement search;

//    @FindBy(xpath = "//*[@id='root-desk-top-right-inner']/div[1]/div/div/div[1]/button")
//    private WebElement countryDropDown;

    @FindBy(xpath = "//*[@id='root-desk-top-right-inner']/div[10]/div/div/div[1]/button/span/div[2]")
    private WebElement countryDropDown;


    @FindBy(id = "account-actions-signin")
    private WebElement login;

    @FindBy(xpath = "//*[@id='onboarding_shukran']/div[1]")
    private WebElement shukranPopUpCloseButton;

    @FindBy(id = "account-actions-signup")
    private WebElement register;


    @FindBy(id = "root-desk-top-wrapper")
    private WebElement desktopWrapper;

    @FindBy(xpath = "//a/div[contains(text(),'UAE')]")
    private WebElement uae;

    @FindBy(xpath = "//a/div[contains(text(),'Oman')]")
    private WebElement oman;

    @FindBy(xpath = "//*[@id='root-desk-top-right-inner']/div[10]/div/div/div[2]/div/div/div/div/nav/div[3]")
    private WebElement Egypt;


    @FindBy(id = "header-basket-button-QA")
    private WebElement basket;

    @FindBy(xpath = "//div[contains(text(),'Basket')]")
    private WebElement basketText;

    @FindBy(xpath = "//*[@id='dept-']/a/span[contains(text(),'Offer Of The Day')]")
    private WebElement offerOfTheDay;

    @FindBy(xpath = "//*[@id='dept-']/a/span[contains(text(),'Mobiles & Accessories')]")
    private WebElement mobilesAndAccessories;

    @FindBy(xpath = "//*[@id='dept-']/a/span[contains(text(),'Computers & Network')]")
    private WebElement computersAndNetworks;

    @FindBy(xpath = "//*[@id='dept-']/a/span[contains(text(),'TV & Audio')]")
    private WebElement tvAndAudio;

    @FindBy(xpath = "//*[@id='dept-']/a/span[contains(text(),'Brands')]")
    private WebElement brands;

    @FindBy(xpath = "//*[@id='dept-']/a/span[contains(text(),'Online Exclusive Offers')]")
    private WebElement onlineExclusiveOffers;

    @FindBy(xpath = "//*[@id='dept-']/a/span[contains(text(),'Students Promotions')]")
    private WebElement studentPromotions;

    @FindBy(xpath = "//*[@id='langChange']/a/span")
    private WebElement langChange;


    public void userNavigateToHomePage(){
        if((config.getBrowser().equalsIgnoreCase("CHROME"))||(config.getBrowser().equalsIgnoreCase("CHROMEHEADLESS")||(config.getBrowser().equalsIgnoreCase("CHROMEHEADLESS"))) ){
//            if(!verifyNoelement(maxlogoCheckoutPage)) {
//                waitForVisibilityOfElementLocated(maxlogo, driver, 25);
//                if (config.getLanguage().equalsIgnoreCase("ar") && !(langChange.getText().trim().equalsIgnoreCase("EN"))) {
//                    //navigateToParticularPage(config.getAuthenticationURL());
//                    waitForElementToBeClickable(langChange, driver, 20);
//                    langChange.click();
//                } else {
//                    navigateToParticularPage(config.getAuthenticationURL());
//                }
//            }
            navigateToParticularPage(BaseURLBean.getUrl());
            //Close Shukran program popup
            while(verifyNoelement(shukranPopUpCloseButton)) {
                shukranPopUpCloseButton.click();
                waitForPageToLoad(10);
            }
            //pageRefresh();
//            for (int i = 0; i <= 10; i++) {
//                pageRefresh();
//                //waitForPageToLoad(5);
//               waitForElementPresent(5);
//                if (!verifyNoelement(login)) {
//                    MyAccountDropDownItems mdit = new MyAccountDropDownItems(driver);
//                    mdit.clickOnMyAccountDropDownIcon();
//                    mdit.clickOnSignOut();
//                } else{
//                    break;
//                }
//            }
            while(!verifyNoelement(login)){
                MyAccountDropDownItems mdit = new MyAccountDropDownItems(driver);
                mdit.clickOnMyAccountDropDownIcon();
                mdit.clickOnSignOut();
                pageRefresh();
                waitForPageToLoad(10);
            }
            while(!verifyNoelement(emptyBasket)){
                mouseHoverClick(nonEmptyBasket);
                waitForPageToLoad(10);
                BasketPage basket = new BasketPage(driver);
                basket.removeAllProductInBasket();
                navigateToParticularPage(BaseURLBean.getUrl());
                pageRefresh();
                waitForPageToLoad(10);
            }
        }
        }

    @FindBy(xpath = "//*[@id='root-nav-mini-basket']/div/button/span/div")
    private WebElement emptyBasket;
    @FindBy(xpath = "//*[@id='root-nav-mini-basket']/div/button/span")
    private WebElement nonEmptyBasket;
    @FindBy(xpath = "//*[@id='cart-removeitem_QA']")
    private WebElement removeItemInBasket;

    /**
     * This method is used to click on EMAX logo.
     */

    public void clickOnEmaxLogo() {
        waitForElementToBeClickable(emaxLogo, 15);
        emaxLogo.click();
    }

    /**
     * This method is used to click on login link in header section of the page.
     */

    public void clickOnLogin() {
        waitForPageToLoad(100);
        if((config.getBrowser().equalsIgnoreCase("MOBILEWEBCHROMEANDROID"))||(config.getBrowser().equalsIgnoreCase("MOBILECHROME"))) {
            hsp = new HeaderSectionPage(driver);
            hsp.clickOnHamburgerMenu();
            waitForElementToBeClickable(login, 30);
            login.click();
        }else {
            waitForElementToBeClickable(login, 30);
            pageRefresh();
            waitForPageToLoad(30);
            login.click();
            waitForPageToLoad(20);
        }
    }

    /**
     * This method is used to click on register link in header section of the page.
     *
     * @throws InterruptedException
     */

    public void clickOnRegister() throws InterruptedException {

        if((config.getBrowser().equalsIgnoreCase("MOBILEWEBCHROMEANDROID"))||(config.getBrowser().equalsIgnoreCase("MOBILECHROME"))) {
            hsp = new HeaderSectionPage(driver);
            hsp.clickOnHamburgerMenu();
        }
        waitForElementToBeClickable(register, 15);
        Thread.sleep(2000);
        register.click();
    }

    /**
     * This method is used to click on country switcher dropdown icon
     */
    public void clickOnCountrySwitcher() {
        waitForElementToBeClickable(countryDropDown, 10);
        countryDropDown.click();
    }

    /**
     * This method is for searching given string value
     *
     * @param searchText input value for searching
     */
    public void searchInput(String searchText) {
        visibilityOf(search, 25);

        if (!search.getAttribute("value").isEmpty()) {
            String searchValue = search.getAttribute("value");
            System.out.println("search value" + searchValue);
            search.clear();
            waitForElementPresent(5);
            search.click();
            for (int i = 0; i < 30; i++) {
                search.sendKeys(Keys.BACK_SPACE);
            }
            search.sendKeys(searchText);
            search.sendKeys(Keys.ENTER);
        } else {
            search.sendKeys(searchText);
            search.sendKeys(Keys.ENTER);

        }
    }

    /**
     * This method is used to check section at the top of the page
     *
     * @return
     */
    public boolean verifyDeskTopWrapper() {
        return isElementDisplayed(desktopWrapper, 15);
    }

    /**
     * Used to change country from country dropdown
     */
    public void changeCountry() {
        if (config.getCountry().equalsIgnoreCase("Egypt")) {
            waitForElementToBeClickable(Egypt, 15);
            elementMouseClick(Egypt);
            waitForPageToLoad(10);
        }
    }


    /**
     * This method is used to fetch login text value
     */
    public String verifyLoginText() {
        waitForWebElementPresent(login, 15);
        return login.getText().trim();
    }


    /**
     * This method is used to fetch register text value
     */
    public String verifyRegisterText() {
        waitForWebElementPresent(register, 15);
        return register.getText().trim();
    }

    public void waitForMiniBasketQuantityUpdate() {

    }

    /**
     * This method is used to Click on basket to icon in header section
     */
    public void basketClick() {
        waitThread(3000);
        waitForElementToBeClickable(basket, 15);
        elementMouseClick(basket);
        waitForElementPresent(3);
    }

    /**
     * This method is used to get basket text value
     *
     * @return capture basket text as String
     */
    public String verifyBasketText() {
        waitForWebElementPresent(basketText, 15);
        return basketText.getText().trim();
    }

    /**
     * @return This method is used to check EMax logo displayed or not
     */
    public boolean verifyEMaxLogo() {
        return isElementDisplayed(emaxLogo, 15);

    }

    /**
     * This method is used to check country dropdown icon displayed or not
     *
     * @return
     */

    public boolean dropDownIcon() {
        waitForWebElementPresent(countryDropDown, 15);
        return isElementDisplayed(countryDropDown, 10);
    }

    /**
     * This method is used to check basket Icon displayed or not
     *
     * @return
     */
    public boolean isBasketIconDisplayed() {

        return isElementDisplayed(basket, 15);
    }


    public String verifySearchPlaceholder() {
        waitForWebElementPresent(search, 15);
        return search.getAttribute("placeholder").trim();
    }

    public String verifyDepartment(String name) {

        String title = offerOfTheDay.getText().trim();

        return title;
    }

    public String verifyDepartmentNames(String name) {
        String label = "";
        switch (name) {
            case "Offer Of The Day":
                label = offerOfTheDay.getText().trim();
                break;
            case "Mobiles & Accessories":
                label = mobilesAndAccessories.getText().trim();
                break;

            case "Computers & Network":
                label = computersAndNetworks.getText().trim();
                break;

            case "TV & Audio":
                label = tvAndAudio.getText().trim();
                break;

            case "Brands":
                label = brands.getText().trim();
                break;

            case "Online Exclusive Offers":
                label = onlineExclusiveOffers.getText().trim();
                break;

            case "Students Promotions":
                label = studentPromotions.getText().trim();
                break;

        }
        return label;
    }


    public void clickOnBrands() {
        waitForElementToBeClickable(brands, driver, 15);
        brands.click();
    }


    public void userNavigateToBrandsListingPage() {
        navigateToParticularPage("https://uat1.emax.ae/brands");

    }


    public void launchWebDriver() {
    }
}
