package com.landmarkshops.Web_Mweb.pageobjects;

import com.landmarkshops.Web_Mweb.config.config;
import com.landmarkshops.Web_Mweb.utilities.AddressBean;
import com.landmarkshops.Web_Mweb.utilities.WebUtils;
import org.json.simple.parser.ParseException;
import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.testng.Assert;
import org.testng.asserts.SoftAssert;

import java.io.IOException;
import java.util.List;

/**
 * This class is used to define all different sections like add shipping address,shipping options ,order summary and payment options sections
 * <AUTHOR>
 */
public class CheckOutScreen extends WebUtils {
    public CheckOutScreen(WebDriver driver) {
        super(driver);
        PageFactory.initElements(driver, this);
    }
    private static String concept = config.getConcept();

    @FindBy(xpath = "//*[@id='checkout-cnc_QA']")
    WebElement clickAndCollectBtn;
    @FindBy(xpath = "//*[text()='Click & Collect']")
    WebElement clickAndCollectButtonTitleMessage;
    @FindBy(xpath = "//*[text()='Collect your order from a location of your choice']")
    WebElement clickAndCollectButtonSubTitleMessage;

    @FindBy(xpath = "//*[text()='Find a pickup point']")
    WebElement clickAndCollectFindAPickupPointTitle;
    @FindBy(xpath = "//*[text()='Search or use our map view to find a pickup point']")
    WebElement clickAndCollectFindAPickupPointSubTitle;

    @FindBy(xpath = "//*[@id='store-list_QA']")
    WebElement clickAndCollectSearchBox;
    @FindBy(xpath = "//*[@title='Clear' and @aria-label='Clear']")
    WebElement clickAndCollectClearSearchBox;
    @FindBy(xpath = "//*[contains(@id,'store-list_QA-option')]")
    WebElement clickAndCollectStoreList;
    @FindBy(xpath = "//*[text()='Confirm Pickup Point']")
    WebElement clickAndCollectConfirmPickupPointButton;
    @FindBy(xpath = "//div[@tabindex='-1']")
    WebElement clickAndCollectConfirmPickupPointMapLocation;
    @FindBy(xpath = "//*[@id='mapSctn']/div[2]/div/div")
    WebElement clickAndCollectConfirmPickupPointMapStoreDetails;

    @FindBy(xpath = "//*[text()='Add your contact details']")
    WebElement clickAndCollectAddYourContactDetailsTitle;
    @FindBy(xpath = "//*[text()='Full Name']")
    WebElement clickAndCollectFullNameLabel;
    @FindBy(id = "fullname")
    WebElement clickAndCollectFullNameTextBox;
    @FindBy(xpath = "//*[text()='Mobile']")
    WebElement clickAndCollectMobileLabel;
    @FindBy(id = "mobile_num")
    WebElement clickAndCollectMobileTextBox;
    @FindBy(xpath = "//*[text()='Email']")
    WebElement clickAndCollectEmailLabel;
    @FindBy(id = "email")
    WebElement clickAndCollectEmailTextBox;

    @FindBy(xpath = "//*[text()='Select a payment method']")
    WebElement clickAndCollectSelectAPaymentMethodSection;

    @FindBy(xpath = "//*[text()='Your Order Summary']")
    WebElement clickAndCollectYourOrderSummaryTitle;
    @FindBy(xpath = "//*[@id='checkout-order-summary-section_QA']")
    WebElement clickAndCollectCheckoutOrderSummaryDetails;

    @FindBy(xpath = "//*[@id='shippingForm']/div/div[1]/div[1]/div/div/button[1]/span/div/div[2]")
    WebElement getCollectYourOrderFromMsg;

    @FindBy(xpath = "//*[@id='shippingForm']/div/div[1]/div[1]/div/div/button[2]")
    WebElement homeDeliveryBtn;


    @FindBy(xpath = "//*[@id='shippingForm']/div/div[1]/div[1]/div/div/button[2]/span/div/div[1]")
    WebElement homeDeliveryText;

    @FindBy(xpath = "//*[@id='shippingForm']/div/div[1]/div[1]/div/div/button[2]/span/div/div[2]")
    WebElement deliveryToHomeMsg;

    @FindBy(xpath = "//*[@id='shippingForm']/div/div[1]/div[1]/div/div/button[1]/span/div/div[1]")
    WebElement clickAndCollectText;

    @FindBy(xpath = "//*[@id='shippingForm']/div/div[1]/div[1]/div/div/button[1]/span/div/div[2]")
    WebElement collectYourOrderFromMsg;

    @FindBy(xpath = "//div[contains(text(),'Add your shipping address')]")
    WebElement addYourShippingAddressSection;

    @FindBy(xpath = "//div[contains(text(),'Full Name')]")
    WebElement fullNameLabel;

    @FindBy(id = "checkout-homedelivery-mobile-label_QA")
    WebElement mobileNumberLabel;

    @FindBy(id = "checkout-homedelivery-email-label_QA")
    WebElement emailLabel;

    @FindBy(id = "checkout-homedelivery-emirate-label_QA")
    WebElement emirateLabel;

    @FindBy(id = "checkout-homedelivery-area-label_QA")
    WebElement areaLabel;

    @FindBy(id = "checkout-homedelivery-makani-label_QA")
    WebElement makaniLabel;



    @FindBy(id = "checkout-homedelivery-building-label_QA")
    WebElement buildingNameOrVillaNoText;

    @FindBy(id = "checkout-homedelivery-floor-label_QA")
    WebElement floorApartmentNumberText;

    @FindBy(id = "checkout-homedelivery-makani-label_QA")
    WebElement makaniNoText;

    @FindBy(xpath = "//div[contains(text(),'Help us deliver to your exact location')]")
    WebElement helpUsDeliverToYourExactLocation;

    @FindBy(id = "checkout-homedelivery-address-type-label_QA")
    WebElement addressType;

    @FindBy(xpath = "//*[@id='address-type-home_QA']/span[2]")
    WebElement HomeText;

    @FindBy(id = "address-type-home_QA")
    WebElement HomeRadioButton;

    @FindBy(xpath = "//*[@id='address-type-work_QA']/span[2]")
    WebElement workText;

    @FindBy(id = "address-type-work_QA")
    WebElement workRadioButton;

    @FindBy(xpath = "//*[@id='address-type-work_QA']/span[1]/span/input")
    WebElement optionValue;


    @FindBy(xpath = "//*[@id='address-checkbox-label']/span[2]")
    WebElement defaultAddressText;

    @FindBy(id = "checkout-homedelivery-shippingspeed-label_QA")
    WebElement selectAPaymentMethodSection;

    @FindBy(xpath = "//*[@id='shippingForm']/div/div[1]/div[3]/div/div/form/div[3]/div[2]/label/span[1]/span/input")
    WebElement defaultRadioButton;

    @FindBy(xpath = "//*[@id='shippingForm']/div/div[1]/div[3]/div/div/form/div[3]/div[2]/label/span[2]/img")
    WebElement creditCardImg;

    @FindBy(xpath = "//form/div[3]/div[2]/div[1]/div[1][contains(text(),'Credit/Debit Cart')]")
    WebElement creditOrDebitCardLabel;

    @FindBy(xpath = "//form/div[3]/div[2]/div[1]/div[2][contains(text(),'Pay using Mastercard or Visa cards')]")
    WebElement payUsingMasterOrVisaCardMsg;

    @FindBy(xpath = "//div[contains(text(),'Card Number')]")
    WebElement cardNumber;

    @FindBy(xpath = "//div[contains(text(),'Name on Card')]")
    WebElement nameOnCardText;

    @FindBy(xpath = "//div[contains(text(),'Expiry Date')]")
    WebElement expiryDateText;

    @FindBy(xpath = "//*[@id='shippingForm']/div/div[1]/div[2]/div/div/form/div[3]/div[2]/div[2]/div[6]/div[1]/div/div/div[2]")
    WebElement dd;

    @FindBy(xpath = "//*[@id='shippingForm']/div/div[1]/div[2]/div/div/form/div[3]/div[2]/div[2]/div[6]/div[2]/div/div/div[2]")
    WebElement mm;

    @FindBy(xpath = "//div[contains(text(),'CVV Number')]")
    WebElement cvvNumberText;

    @FindBy(xpath = "//*[@id='shippingForm']/div/div[1]/div[2]/div/div/form/div[3]/div[3]/img")
    WebElement promoImage;

    @FindBy(xpath = "//div[contains(text(),'Got a Promo Code?')]")
    WebElement promoTextMessage;


    @FindBy(id = "cart-promo-click_QA")
    WebElement useItHere;


    @FindBy(xpath = "//*[@id='checkout-paynow_QA']/span")
    WebElement payNow;

    @FindBy(xpath = "//*[@id='shippingForm']/div/div[1]/div[3]/div/div/form/div[3]/div[4]/div")
    WebElement termsAndCondition;

    @FindBy(id = "fullname")
    WebElement fullnameInput;

    @FindBy(id = "mobile_num")
    WebElement mobileInput;

    @FindBy(id = "email")
    WebElement emailInput;

    @FindBy(id = "emirate")
    WebElement emirate;

    @FindBy(xpath = "//*[@id='menu-emirate']/div[3]/ul/li[2]")
    WebElement emirateListItem;

    @FindBy(id = "area")
    WebElement areaInput;

    @FindBy(xpath = "//*[@id='menu-area']/div[3]/ul/li[2]")
    WebElement areaListItem;

    @FindBy(xpath = "//*[@id='menu-area']/div[3]/ul/li[4]")
    WebElement areaDubaiAirPortListItem;


    @FindBy(xpath = "//*[@id='menu-emirate']/div[3]/ul/li[4]")
    WebElement emirateDubaiListItem;



    @FindBy(id = "checkout-homedelivery-building-label_QA")
    WebElement buildingLabel;

    @FindBy(id = "building")
    WebElement buildingInput;

    @FindBy(id = "floor")
    WebElement floorInput;

    @FindBy(id = "makani")
    WebElement makaniInput;

    @FindBy(id = "cardnum")
    WebElement cardnumInput;

    @FindBy(id = "namecard")
    WebElement namecardInput;

    @FindBy(id = "expirymonth")
    WebElement expiryMonth;



    @FindBy(xpath = "//*[@id='menu-expirymonth']/div[3]/ul/li[4]")
    WebElement selectExpiryMonthValue;

    @FindBy(id = "expiryyear")
    WebElement expiryYear;


    @FindBy(xpath = "//*[@id='menu-expiryyear']/div[3]/ul/li[4]")
    WebElement selectExpiryYearValue;

    @FindBy(id = "cvv")
    WebElement cvvInput;


    @FindBy(id = "shippingSection_QA")
    WebElement selectYourShippingSpeedSection;

    @FindBy(id = "checkout-homedelivery-shippingspeed-label_QA")
    WebElement selectYourShippingSpeedText;

    @FindBy(xpath = "//*[@id='shippingSection_QA']/label[1]/span[2]/div/div[1]/span[1]")
    WebElement standardText;

    @FindBy(xpath = "//*[@name='Standard']")
    WebElement radioStandard;

    @FindBy(xpath = "//*[@id='shippingSection_QA']/label[1]/span[2]/div/div[1]/span[2]")
    WebElement standard_shippingChargeLabel;

    @FindBy(xpath = "//*[@id='shippingSection_QA']/label[1]/span[2]/div/div[2]")
    WebElement standard_delivery_msg;

    @FindBy(xpath = "//*[@id='shippingSection_QA']/label[2]/span[2]/div/div[1]/span[1]")
    WebElement expressText;

    @FindBy(xpath = "//*[@name='Express']")
    WebElement radioExpress;

    @FindBy(xpath = "//*[@id='shippingSection_QA']/label[2]/span[2]/div/div[1]/span[2]")
    WebElement express_shippingChargeLabel;

    @FindBy(xpath = "//*[@id='shippingSection_QA']/label[2]/span[2]/div/div[2]")
    WebElement express_delivery_msg;

    @FindBy(xpath = "//*[@id='shippingSection_QA']/label[3]/span[2]/div/div[1]/span[1]")
    WebElement superExpressText;

    @FindBy(xpath = "//*[@name='Super Express']")
    WebElement radioSuperExpress;

    @FindBy(xpath = "//*[@id='shippingSection_QA']/label[3]/span[2]/div/div[1]/span[2]")
    WebElement superExpress_shippingChargeLabel;

    @FindBy(xpath = "//*[@id='shippingSection_QA']/label[3]/span[2]/div/div[2]")
    WebElement superExpress_delivery_msg;

    @FindBy(xpath = "//*[@id='checkout-order-summary-section_QA']/p")
    WebElement orderSummaryText;

    @FindBy(xpath = "//*[@id='checkout-order-summary-section_QA']/div[1]/div[1]/img")
    WebElement orderSummeryProductImg;

    @FindBy(xpath = "//*[@id='checkout-order-summary-section_QA']/div[1]/div[2]/p[1]")
    WebElement orderSummeryProductNumber;

    @FindBy(xpath = "//*[@id='checkout-order-summary-section_QA']/div[1]/div[2]/p[2]")
    WebElement orderSummeryProductDescription;

    @FindBy(xpath = "//*[@id='checkout-order-summary-section_QA']/div[1]/div[2]/div/div[1]")
    WebElement orderSummeryProductQty;

    @FindBy(xpath = "//*[@id='checkout-order-summary-section_QA']/div[1]/div[3]/div[1]/p[1]")
    WebElement shippingOptionType_Title;

    @FindBy(xpath="//*[@id='defaultaddress']")
    WebElement defaultAddressCheckBox;

    @FindBy(xpath="//*[@id='address-checkbox-label']/span[2]")
    WebElement defaultAddressMsg;


    @FindBy(xpath="//*[@id='address-checkbox']/div")
    WebElement defaultAddressMsg1;



    @FindBy(xpath="//div[2][contains(text(),'Add a new Address')]")
    WebElement addANewAddress;

    @FindBy(xpath="//*[@id='checkout-paynow_QA']/span/span[contains(text(),'Ship to this Address')]")
    WebElement shipToThisAddress;

    @FindBy(xpath = " //div[contains(text(),'show all my addresses')]")
    WebElement showAllMyAddresses;

    @FindBy(xpath="//label[1]/span[2]/div/div[1]/div")
    WebElement fullNameFirstLabel;


    @FindBy(xpath = "//input[starts-with(@name,'shippingDetails')]")
    List<WebElement> listOfShippingAddress;


    @FindBy(xpath="//*[@id='shippingForm']/div/div[1]/div[3]/div/div/form/div[3]/div[2]/div[2]")
    WebElement cardFromList;

    @FindBy(xpath="//*[@id='cvv_0']")
    WebElement cvvNumber;

    @FindBy(id="checkout-homedelivery-payment-cvv-label_QA")
    WebElement cvvText;

    @FindBy(xpath = "//*[@id='shippingForm']/div/div[1]/div[3]/div/div/form/div[3]/div[2]/div[2]/div[2]/div[1]")
    WebElement ending;

    @FindBy(xpath = "//*[@id='shippingForm']/div/div[1]/div[3]/div/div/form/div[3]/div[2]/div[2]/div[2]/div[2]")
    WebElement nameOnCard;

    @FindBy(xpath = "//*[@id='shippingForm']/div/div[1]/div[3]/div/div/form/div[3]/div[2]/div[2]/div[2]/div[3]")
    WebElement expiryDateOnCard;

    @FindBy(xpath = "//*[@id='shippingForm']/div/div[1]/div[3]/div/div/form/div[3]/div[2]/div[2]/div[1]")
    WebElement cardImage;

    @FindBy(xpath = "//form/div[3]/div[1]/div[2]/div[2][contains(text(),'Add a new card')]")
    WebElement addANewCardLink;


    @FindBy(xpath = "//*[@id='shippingForm']/div/div[1]/div[3]/div/div/form/div[4]/div[2]/div[2]/button[@id='checkout-paynow_QA']")
    WebElement saveThisCard;

    @FindBy(id ="newpayment-close")
    WebElement newPaymentCloseIcon;

    @FindBy(id ="checkout-homedelivery-payment-label_QA")
    WebElement nc_selectAPaymentMethod;

    @FindBy(id ="checkout-homedelivery-payment-number-label_QA")
    WebElement nc_cardNumberLabel;

    @FindBy(id ="checkout-homedelivery-payment-name-label_QA")
    WebElement nc_nameLabel;

    @FindBy(id ="checkout-homedelivery-payment-expirydate-label_QA")
    WebElement nc_expiryDateLabel;


    @FindBy(xpath ="//form/div[1]/div[2]/div/div[2]/div[contains(text(),'Please enter your full name')]")
    WebElement pleaseEnterFullName;

    @FindBy(xpath ="//form/div[1]/div[2]/div/div[2]/div[contains(text(),'Please enter your full name using alphabet characters only')]")
    WebElement pleaseEnterFullNameUsingAlpha;

    @FindBy(xpath ="//div[2][contains(text(),'Enter your email address')]")
    WebElement enterYourEmailAddress;

    @FindBy(xpath ="//div[2][contains(text(),'Please enter a valid email address')]")
    WebElement pleaseEnterAValidEmailAddress;


    @FindBy(xpath ="//div[2][contains(text(),'Please enter mobile number')]")
    WebElement pleaseEnterMobileNumber;

    @FindBy(xpath ="//div[2][contains(text(),'Please enter a valid 9-digit mobile number.')]")
    WebElement valid9digitMobileNumber;

    @FindBy(xpath ="//div[2][contains(text(),'Select your Emirate')]")
    WebElement selectYourEmirate;

    @FindBy(xpath ="//div[2][contains(text(),'Select your area')]")
    WebElement selectYourArea;

    @FindBy(xpath ="//div[2][contains(text(),'Please enter Building Name')]")
    WebElement pleaseEnterBuildingName;

    @FindBy(xpath ="//div[2][contains(text(),'This field needs to have at least 5 characters')]")
    WebElement buildingValidation2;

    @FindBy(xpath ="//div[2][contains(text(),'Please enter a 10-digit number without spaces')]")
    WebElement makaniValidation;

    @FindBy(xpath ="//div[2][contains(text(),'Please enter a true digit number')]")
    WebElement pleaseEnterTrueDigitNumber;


    @FindBy(xpath ="//div[2][contains(text(),'Please enter a valid card number')]")
    WebElement pleaseEnterAValidCardNumber;


    @FindBy(xpath ="//div[4]/div[2][contains(text(),'Please enter your full name')]")
    WebElement pleaseEnterCardFullNameValidation;

    @FindBy(xpath ="//div[4]/div[2][contains(text(),'Please enter your full name using alphabet characters only')]")
    WebElement pleaseEnterCardValidation;



    @FindBy(xpath ="//div[2][contains(text(),'Please select a valid month')]")
    WebElement pleaseSelectAValidMonth;

    @FindBy(xpath ="//*[@id='shippingForm']/div/div[1]/div[3]/div/div/form/div[3]/div[2]/div[2]/div[6]/div[2]/div[2][contains(text(),'Please select a valid ')]")
    WebElement pleaseSelectAValidYear;

    @FindBy(xpath ="//div[2][contains(text(),'Enter your CVV')]")
    WebElement enterYourCVV;




















    /**
     * Verify user landed to check out page
     */
    public void verifyUserLandedToCheckOutPage() {
        waitForWebElementPresent(selectYourShippingSpeedSection,10);


    }

    /**
     *
     * This method is used to verify all the ui components in add your shipping address section
     */
    public void verifyAddYourShippingAddressUIComponents(){
        SoftAssert sa = new SoftAssert();
        sa.assertEquals(clickAndCollectText.getText().trim(),"Click & Collect","Click & Collect is not displayed");
        sa.assertEquals(collectYourOrderFromMsg.getText().trim(),"Collect your order from a location of your choice","Collect your order from a location of your choice is not displayed");
        sa.assertEquals(homeDeliveryText.getText().trim(),"Home Delivery","Home Delivery is not displayed");
        sa.assertEquals(deliveryToHomeMsg.getText().trim(),"Get your products delivered to your home","Get your products delivered to your homeis not displayed");
        sa.assertEquals(addYourShippingAddressSection.getText().trim(),"Add your shipping address","Add your shipping addressis not displayed");
        sa.assertEquals(fullNameLabel.getText().trim(),"Full Name","Full Name is not displayed");
       // sa.assertEquals(mobileNumberLabel.getText().trim(),"Mobile","Mobile is not displayed");

        sa.assertEquals(emailLabel.getText().trim(),"Email","Email text is not displayed");
        sa.assertEquals(emirateLabel.getText().trim(),"Emirate","Emitrate text is not displayed");
        scrolldownElement(mobileNumberLabel);
        waitForWebElementPresent(areaLabel,10);
        sa.assertEquals(areaLabel.getText().trim(),"Area","Area is not displayed");
        sa.assertEquals(buildingLabel.getText().trim(),"Building Name/Villa No*","Building Name/Villa No* is not displayed");
       // sa.assertEquals(floorApartmentNumberText.getText().trim(),"Floor, Apartment No.","floorApartmentNumberText is not displayed");
        sa.assertTrue(makaniLabel.isDisplayed(),"makaniLabel is not displayed");
        sa.assertEquals(helpUsDeliverToYourExactLocation.getText().trim(),"Help us deliver to your exact location","Help us deliver to your exact location is not displayed");
        testLogger.info("Address Type"+addressType.getText().trim());
        sa.assertEquals(addressType.getText().trim(),"Address Type(optional)","Address Type is not displayed");
        sa.assertEquals(HomeText.getText().trim(),"Home","Home is not displayed");
        sa.assertEquals(workText.getText().trim(),"Work","work is not displayed");
        sa.assertEquals(defaultAddressText.getText().trim(),"Use this as my default shipping address","Use this as my default shipping address is not displayed");
        testLogger.info("HomeRadioButton "+HomeRadioButton.isDisplayed());
        testLogger.info("workRadioButton "+workRadioButton.isDisplayed());
        testLogger.info("defaultAddressCheckBox "+defaultAddressCheckBox.isDisplayed());
        sa.assertAll();

    }

    /**
     * Verify all UI components in checkout page
     */
    public void verifyAllUIComponentsInSelectYourShippingSpeed() {
        SoftAssert sa = new SoftAssert();
        //scrollDown(7);
        scrolldownElement(addressType);
        testLogger.info("select your shipping speed"+selectYourShippingSpeedText.getText().trim());
        testLogger.info("standardText "+standardText.getText().trim());
        testLogger.info("radioStandard  "+radioStandard.isSelected());
        testLogger.info("standard_shippingChargeLabel "+standard_shippingChargeLabel.getText().trim());
        testLogger.info("standard_delivery_msg "+standard_delivery_msg.getText().trim());
        testLogger.info("expressText "+expressText.getText().trim());
        testLogger.info("radioExpress "+ radioExpress.isSelected());
        testLogger.info("express_shippingChargeLabel "+express_shippingChargeLabel.getText().trim());
        testLogger.info("express_delivery_msg "+express_delivery_msg.getText().trim());
        testLogger.info("superExpressText "+superExpressText.getText().trim());
        testLogger.info("radioSuperExpress "+ radioSuperExpress.isSelected());
        testLogger.info("superExpress_shippingChargeLabel "+superExpress_shippingChargeLabel.getText().trim());
        testLogger.info("superExpress_delivery_msg "+superExpress_delivery_msg.getText().trim());
        scrolldownElement(selectYourShippingSpeedText);
        sa.assertEquals(selectYourShippingSpeedText.getText().trim(),"Select your shipping speed","Select your shipping speed section is not displayed");
        sa.assertAll();

    }

    /**
     * This method is used to verify all the Ui components in select a payment section
     */
    public void verifySelectPaymentSection(){
        SoftAssert sa = new SoftAssert();
        scrolldownElement(selectYourShippingSpeedSection);
       testLogger.info("Select a payment method"+selectAPaymentMethodSection.getText().trim());
       sa.assertEquals(creditOrDebitCardLabel.getText().trim(),"Credit/Debit Cart","Credit/Debit Cart text is not displayed");
        testLogger.info("creditOrDebitCardLabel "+creditOrDebitCardLabel.getText().trim());
        sa.assertTrue(creditCardImg.isDisplayed(),"credit card image not displayed");
        testLogger.info("creditCardImg "+creditCardImg.isDisplayed());
        sa.assertTrue(defaultRadioButton.isSelected(),"default radio button is not displayed");
        testLogger.info("defaultRadioButton  "+defaultRadioButton.isSelected());
        sa.assertEquals(cardNumber.getText().trim(),"Card Number","Card Number text is not displayed");
        testLogger.info("cardNumber "+cardNumber.getText().trim());
        sa.assertEquals(nameOnCardText.getText().trim(),"Name on Card","Name on Card text is not displayed");
        testLogger.info("nameOnCardText "+nameOnCardText.getText().trim());
        sa.assertEquals(expiryDateText.getText().trim(),"Expiry Date","Expiry Date text is not displayed");
        testLogger.info("expressText "+expiryDateText.getText().trim());
        sa.assertEquals(cvvNumberText.getText().trim(),"CVV Number","CVV Number text is not displayed");

        testLogger.info("cvv Number Text "+cvvNumberText.getText().trim());
        sa.assertEquals(promoTextMessage.getText().trim(),"Got a Promo Code?","Got a Promo Code? text is not displayed");
        testLogger.info("promo Text Message "+promoTextMessage.getText().trim());
        sa.assertEquals(useItHere.getText().trim(),"Use it here","Use it here text is not displayed");
        testLogger.info("useItHere "+useItHere.getText().trim());
        sa.assertEquals(termsAndCondition.getText().trim(),"By continuing to checkout you agree to our Terms and Conditions","By continuing to checkout you agree to our Terms and Conditions text is not displayed");
        sa.assertEquals(payNow.getText().trim(),"Pay Now","Pay Now text is not displayed");
        testLogger.info("payNow "+payNow.getText().trim());
        sa.assertAll();

    }

    /**
     * This method is used to choose Shipping mode based on order values
     * @param shippingModeValue
     * @return shipping option value
     */

    public String chooseShippingMode(String shippingModeValue) {
        String actual = null;

        if(shippingModeValue.equals("standard delivery")){

            //waitForElementToBeClickable(radioStandard,15);
            elementMouseClick(radioStandard);

            actual = radioStandard.getAttribute("value").trim();
        }else if(shippingModeValue.equals("express delivery")){

            elementMouseClick(radioExpress);
            actual = radioExpress.getAttribute("value").trim();
        }else if(shippingModeValue.equals("super express delivery")){
            elementMouseClick(radioSuperExpress);

            actual = radioSuperExpress.getAttribute("value").trim();
        }
        testLogger.info("shipping text value radio"+actual);
        waitForWebElementPresent(shippingOptionType_Title,10);
       String expected= shippingOptionType_Title.getText().trim();
        Assert.assertEquals(actual,expected,"shipping option mismatch as compared to order summary");
        return actual;
    }

    /**
     * This method is used to click on home delivery section
     */

    public void clickOnHomeDelivery(){
        homeDeliveryBtn.click();
    }



    /**
     * This method is used to enter full name
     * @param name
     */
    public void enterFullName(String name){
        waitForElementPresent(3);
        fullnameInput.sendKeys(name);
    }



    /**
     * This method is used to enter full name
     * @param name
     */
    public void updateFullName(String name){
        waitForElementPresent(3);
        fullnameInput.clear();
        fullnameInput.sendKeys(name);
    }

    /**
     * This method is used to enter mobile number
     *
     */

    public void enterMobileNumber() throws IOException, ParseException {
        mobileNumberDetails("ae");
      // String number = generatePhoneNumberBasedOnCountry().trim();
       mobileInput.clear();
        mobileInput.sendKeys("501231311");
    }


    public void updateMobileNumber(String number) throws IOException, ParseException {
        mobileNumberDetails("ae");
        // String number = generatePhoneNumberBasedOnCountry().trim();
        mobileInput.clear();
        mobileInput.sendKeys(number);
    }
    /**
     * This method is used to enter email id
     */
    public void enterEmailId(){
        String email = randomGenerateEmail();
        emailInput.sendKeys(email);
    }

    public void updateEmailId(){
        String email = randomGenerateEmail();
        emailInput.clear();
        emailInput.sendKeys(email);
    }

    /**
     * This method is used to select area
     * @param name
     * @return
     */

    public String selectYOurEmirate(String name){

        emirate.click();
        waitForElementPresent(1);
        elementMouseClick(emirateListItem);
        waitForElementPresent(1);
        String selectedEmirateValue = emirate.getText().trim();
        testLogger.info("Selected emirate value as:"+selectedEmirateValue);
        return selectedEmirateValue;
    }

    public String updateSelectYOurEmirate(String name){

        emirate.click();
        waitForElementPresent(1);
        elementMouseClick(emirateDubaiListItem);
        waitForElementPresent(1);
        String selectedEmirateValue = emirate.getText().trim();
        testLogger.info("Selected emirate value as:"+selectedEmirateValue);
        return selectedEmirateValue;
    }

    /**
     * This method is used to select area
     * @param name
     * @return
     */
    public String selectedArea(String name){
        waitForElementToBeClickable(areaInput,15);
        areaInput.click();
        elementMouseClick(areaListItem);
        String selectedAreaValue = areaInput.getText().trim();
        testLogger.info("Selected area value as:"+selectedAreaValue);
        return selectedAreaValue;

    }

    public String updateSelectedArea(String name){
        waitForElementToBeClickable(areaInput,15);
        areaInput.click();
        elementMouseClick(areaDubaiAirPortListItem);
        String selectedAreaValue = areaInput.getText().trim();
        testLogger.info("Selected area value as:"+selectedAreaValue);
        return selectedAreaValue;

    }

    /**
     * This method is used to enter building Name
     * @param buildingName
     */
    public void enterBuildingName(String buildingName){

        buildingInput.sendKeys(buildingName);

    }

    public void updateBuildingName(String buildingName){
        buildingInput.clear();
        buildingInput.sendKeys(buildingName);

    }

    /**
     * This method is used to enter building Name
     * @param number
     */
    public void enterFloorApartmentNo(String number){

        floorInput.sendKeys(number);

    }

    public void updateFloorApartmentNo(String number){
        floorInput.clear();
        floorInput.sendKeys(number);

    }

    /**
     * This method is used to enter makani number
     * @param number enter makani number
     */

    public void enterMakaniNumber(String number){
       // moveToElementAndSendKeys(makaniInput,number);
        makaniInput.sendKeys(number);

    }

    public void updateMakaniNumber(String number){
        // moveToElementAndSendKeys(makaniInput,number);
        makaniInput.clear();
        makaniInput.sendKeys(number);

    }

    /**
     * This method is used to enter debit or credit card details
     * @param cardType provide details as visa ,or master
     */
    public void enterCreditCardNumber(String cardType){

        String cardNumber;
        if(cardType.equals("visa")){


        }

        cardnumInput.sendKeys(cardType);
    }

    /**
     * This method is used to enter name on card
     */

    public void enterNameOnCard(String name){

         namecardInput.sendKeys(name);
    }

    /**
     * This method is used select expiry date value
     */
    public void selectExpiryMonth(){
        expiryMonth.click();
        waitForElementPresent(4);
        elementMouseClick(selectExpiryMonthValue);
        waitForElementPresent(4);

    }





    /**
     * This method is used select expiry month value
     */

    public void setSelectExpiryYear(){
       expiryYear.click();
        waitForElementPresent(4);
        elementMouseClick(selectExpiryYearValue);
        waitForElementPresent(4);
    }

    /**
     * This method is used to enter cvv number of the card
     * @param number is used to get cvv number value
     */
    public void enterCVV(String number){
        cvvInput.sendKeys(number);
    }

    /**
     * verify Home delivery UI components
     */
    public void verifyHomeDeliveryLabels() {
        SoftAssert sa = new SoftAssert();
        sa.assertEquals("Home Delivery",homeDeliveryText.getText().trim(),"Home Delivery text mismatch");
        sa.assertEquals("Get your products delivered to your home",deliveryToHomeMsg.getText().trim(),"Home Delivery text mismatch");
        sa.assertAll();
    }
    /**
     * verify Click and Collect UI components
     */
    public void verifyClickAndCollectLabels() {
        SoftAssert sa = new SoftAssert();
        sa.assertEquals("Click & Collect",clickAndCollectText.getText().trim(),"Home Delivery text mismatch");
        sa.assertEquals("Collect your order from a location of your choice",getCollectYourOrderFromMsg.getText().trim(),"Home Delivery text mismatch");
        sa.assertAll();
    }

    /**
     * Verify default address check box message
     */

    public void defaultAddressCheckBox() {
        SoftAssert sa = new SoftAssert();
        sa.assertEquals("Use this as my default shipping address",defaultAddressMsg1.getText().trim(),"Use this as my default shipping address text mismatch");
       // sa.assertFalse(defaultAddressCheckBox.isSelected(),"Default message disabled");
        sa.assertAll();

    }

    /**
     * click on default address click
     */
    public void defaultAddressClick(){
        waitThread(6000);
        elementMouseClick(defaultAddressCheckBox);
        waitThread(3000);
    }

    /**
     * clicking on pay now button
     */
    public void payNowClick(){
        payNow.click();

    }

    /**
     * change address type based on input value
     * @param addressType passed input as work
     */
    public void changeAddressTypeAs(String addressType) {

       if(addressType.equals("Work")){
            scrollElement(makaniLabel);
            waitForElementToBeClickable(workRadioButton,15);
            workRadioButton.click();

           waitForElementPresent(5);
           testLogger.info("clicked on work"+optionValue.getAttribute("value"));

           Assert.assertEquals(optionValue.getAttribute("value"),"Work");
       }
    }

    public void clickOnAddANewAddressLink(){
        waitThread(6000);
        elementMouseClick(addANewAddress);
        //addANewAddress.click();
    }

    public void clickOnShipToThisAddress() {
        shipToThisAddress.click();
    }

    public void clickOnShowAllMyAddresses(){
        waitThread(5000);
        showAllMyAddresses.click();
        waitThread(5000);
    }

    public void checkFullName() {
        waitForElementPresent(3);
        waitForWebElementPresent(fullNameFirstLabel,15);
        String actual=fullNameFirstLabel.getText().trim();
        String name = AddressBean.getFullName();
        Assert.assertEquals(actual,name,"Full Name is not same in the list");
    }

    public void chooseShippingAddress(){
        int listAddress = listOfShippingAddress.size()-1;
        String selectedAddressIndex = String.valueOf(listAddress);
        String pre ="//input[starts-with(@name,";
        String post = "'"+"shippingDetails_"+ selectedAddressIndex+"'"+")]";
        String shippedAddressIndex=pre+post;
        scrollElement( driver.findElement(By.xpath(shippedAddressIndex)));
        driver.findElement(By.xpath(shippedAddressIndex)).click();
    }

    public void choosePaymentCard() {
        scrollElement(payNow);
        cardFromList.click();
    }

    public void verifyCardUIComponents() {
    }

    public void enterCVVNumber() {
        cvvNumber.sendKeys("123");
    }

    public void clickOnAddNewCard() {
        scrollElement(payNow);
        addANewCardLink.click();
    }

    public void clickOnSaveThisCard() {

        saveThisCard.click();
    }

    public void validateEmailErrorMessage() {
        scrollElement(addYourShippingAddressSection);

        Assert.assertEquals("Enter your email address",enterYourEmailAddress.getText().trim(),"Email validation is missing");
        emailInput.sendKeys("test@g");
        Assert.assertEquals("Please enter a valid email address",pleaseEnterAValidEmailAddress.getText().trim(),"Email validation is missing");

    }

    public void validateFullNameErrorMessage() {
        Assert.assertEquals("Please enter your full name",pleaseEnterFullName.getText().trim(),"Email validation is missing");
        fullnameInput.sendKeys("w2");
        Assert.assertEquals("Please enter your full name using alphabet characters only",pleaseEnterFullNameUsingAlpha.getText().trim(),"Email validation is missing");

    }

    public void validateMobileNumberErrorMessage() {
        Assert.assertEquals("Please enter mobile number",pleaseEnterMobileNumber.getText().trim(),"mobile number validation is missing");
        mobileInput.sendKeys("5");
        Assert.assertEquals("Please enter a valid 9-digit mobile number.",valid9digitMobileNumber.getText().trim(),"mobile number validation is missing");


    }

    public void emirateErrorMessage() {
        Assert.assertEquals("Select your Emirate",selectYourEmirate.getText().trim(),"Select your Emirate validation is missing");

    }

    public void areaErrorMessage() {
        Assert.assertEquals("Select your area",selectYourArea.getText().trim(),"Select your area validation is missing");

    }

    public void buildingErrorMessage() {
        Assert.assertEquals("Please enter Building Name/House No.",pleaseEnterBuildingName.getText().trim(),"Please enter building validation is missing");
        buildingInput.sendKeys("ee");
        Assert.assertEquals("This field needs to have at least 5 characters",buildingValidation2.getText().trim(),"Please enter building validation is missing");

    }

    public void makaniErrorMessage() {
        makaniInput.sendKeys("3");
        Assert.assertEquals("Please enter a 10-digit number without spaces",makaniValidation.getText().trim(),"Please enter makani validation is missing");


    }

    public void validateCardNumberErrorMessage() {
        Assert.assertEquals("Please enter a true digit number",pleaseEnterTrueDigitNumber.getText().trim(),"Please enter true digit number validation missing");
        cardnumInput.sendKeys("3");
        Assert.assertEquals("Please enter a valid card number",pleaseEnterAValidCardNumber.getText().trim(),"Please enter building validation is missing");

    }

    public void validateNameOnCardErrorMessage() {
        Assert.assertEquals("Please enter your full name",pleaseEnterCardFullNameValidation.getText().trim(),"Please enter card full name validation is missing");
        namecardInput.sendKeys("2");
        Assert.assertEquals("Please enter your full name using alphabet characters only",pleaseEnterCardValidation.getText().trim(),"Please enter card full name validation is missing");



    }

    public void validateExpiryMonthErrorMessage() {
        Assert.assertEquals("Please select a valid month",pleaseSelectAValidMonth.getText().trim(),"Please select a valid month");

    }

    public void validateExpiryYearErrorMessage() {
        Assert.assertEquals("Please select a valid year",pleaseSelectAValidYear.getText().trim(),"Please select a valid year validation is missing");

    }

    public void validateEnterYourCVV() {
        Assert.assertEquals("Enter your CVV",enterYourCVV.getText().trim(),"enter your cvv validation is missing");

    }

    public void VerifyClickAndCollectSectionDisplayed(){
        scrollToParticularElement(clickAndCollectBtn);
        SoftAssert sa = new SoftAssert();
        sa.assertTrue(clickAndCollectBtn.isDisplayed(), "Click and Collect section is not displayed");
        String logMessage = clickAndCollectBtn.isDisplayed() ? "is displayed" : "is not displayed";
        testLogger.info("Click and Collect section "+logMessage+" in Cart page");
        sa.assertAll();
    }

    public void clickOnclickAndCollectButton(){
        scrollToParticularElement(clickAndCollectBtn);
        waitThread(5000);//For demo
        clickAndCollectBtn.click();
        testLogger.info("Click on Click and Collection section in cart page");
    }
    public void VerifyClickAndCollectButton(){
        scrollToParticularElement(clickAndCollectBtn);
        SoftAssert sa = new SoftAssert();
        sa.assertTrue(clickAndCollectBtn.isDisplayed(), "Click and Collect section is not displayed");
        String logMessage = clickAndCollectBtn.isDisplayed() ? "is displayed" : "is not displayed";
        testLogger.info("Click and Collect section "+logMessage+" in Cart page");
        sa.assertEquals(clickAndCollectButtonTitleMessage.getText().trim(),"Click & Collect");
        testLogger.info("Click and Collect Button title message - "+clickAndCollectButtonTitleMessage.getText().trim());
        sa.assertEquals(clickAndCollectButtonSubTitleMessage.getText().trim(),"Collect your order from a location of your choice");
        testLogger.info("Click and Collect Button sub title displayed message - "+clickAndCollectButtonSubTitleMessage.getText().trim());
        sa.assertAll();
    }

    public void VerifyFindAPickupPointSection(){
        scrollToParticularElement(clickAndCollectBtn);
        SoftAssert sa = new SoftAssert();
        sa.assertTrue(clickAndCollectFindAPickupPointTitle.isDisplayed(), "Find a Pickup Point section is not displayed");
        String logMessage = clickAndCollectFindAPickupPointTitle.isDisplayed() ? "is displayed" : "is not displayed";
        testLogger.info("Find a Pickup Point section "+logMessage+" in Cart page");
        sa.assertEquals(clickAndCollectFindAPickupPointTitle.getText().trim(),"Find a pickup point");
        testLogger.info("Find a Pickup Point title message - "+clickAndCollectFindAPickupPointTitle.getText().trim());
        sa.assertEquals(clickAndCollectFindAPickupPointSubTitle.getText().trim(),"Search or use our map view to find a pickup point");
        testLogger.info("Find a Pickup Point sub title displayed message - "+clickAndCollectFindAPickupPointSubTitle.getText().trim());
        sa.assertAll();
    }

    public void clickOnClickAndCollectSearchBox(){
        scrollToParticularElement(clickAndCollectBtn);
        SoftAssert sa = new SoftAssert();
        sa.assertTrue(clickAndCollectSearchBox.isDisplayed(), "Click and Collect Search Box is not displayed");
        String logMessage = clickAndCollectSearchBox.isDisplayed() ? "is displayed" : "is not displayed";
        testLogger.info("Find a Pickup Point section "+logMessage+" in Cart page");
        clickAndCollectSearchBox.click();
        testLogger.info("Click Available search Box button is clicked");
        sa.assertAll();
    }

    public void enterDataOnClickAndCollectSearchBox(String searchData){
        clickAndCollectSearchBox.click();
        clickAndCollectSearchBox.clear();
        clickAndCollectSearchBox.sendKeys(searchData);
        waitThread(5000); //for demo
        testLogger.info("Click Available search Box entered data: "+clickAndCollectSearchBox.getText().trim());
    }

    public void clearDataOnClickAndCollectSearchBox(){
        //clickAndCollectSearchBox.click();
        //clickAndCollectClearSearchBox.click();
        clickAndCollectSearchBox.clear();
        waitThread(5000); //for demo
        testLogger.info("Click Available search Box is cleared");
    }

    public void clickOnClickAndCollectSearchBoxList(){
        clickAndCollectStoreList.click();
        waitThread(5000); //for demo
        testLogger.info("Address clicked on Click and Collect search box");
    }

    public void clickOnClickAndCollectConfirmPickupPointButton(){
        clickAndCollectConfirmPickupPointButton.click();
        waitThread(5000); //for demo
        testLogger.info("Click on confirm pickup point button in Click and Collect map");
    }

    public void verifyOnClickAndCollectConfirmPickupPointMapLocation(){
        SoftAssert sa = new SoftAssert();
        sa.assertTrue(clickAndCollectConfirmPickupPointMapLocation.isDisplayed(), "Confirm pickup point map location is not displayed");
        String logMessage = clickAndCollectConfirmPickupPointMapLocation.isDisplayed() ? "is displayed" : "is not displayed";
        testLogger.info("Verified confirm pickup point map location "+logMessage+" in Click and Collect section");
        sa.assertAll();
    }

    public void verifyOnClickAndCollectConfirmPickupPointMapStoreDetails(){
        testLogger.info(""+clickAndCollectConfirmPickupPointMapStoreDetails.getText());
        testLogger.info("Verified confirm pickup point map location in Click and Collect section");
    }

    public void verifyClickAndCollectAddYourContactDetails() {
        scrollToParticularElement(clickAndCollectEmailLabel);
        SoftAssert sa = new SoftAssert();
        sa.assertTrue(clickAndCollectAddYourContactDetailsTitle.isDisplayed(), "Add your contact details is not displayed");
        String logMessage = clickAndCollectAddYourContactDetailsTitle.isDisplayed() ? "is displayed" : "is not displayed";
        testLogger.info("Add your contact details " + logMessage + " in Click and Collect page");
        sa.assertTrue(clickAndCollectFullNameLabel.isDisplayed(), "Add your contact details - Full Name label is not displayed");
        logMessage = clickAndCollectFullNameLabel.isDisplayed() ? "is displayed" : "is not displayed";
        testLogger.info("Add your contact details - Full Name label " + logMessage + " in Click and Collect page");
        sa.assertTrue(clickAndCollectMobileLabel.isDisplayed(), "Add your contact details - Mobile label is not displayed");
        logMessage = clickAndCollectMobileLabel.isDisplayed() ? "is displayed" : "is not displayed";
        testLogger.info("Add your contact details - Mobile label " + logMessage + " in Click and Collect page");
        sa.assertTrue(clickAndCollectEmailLabel.isDisplayed(), "Add your contact details - Email label is not displayed");
        logMessage = clickAndCollectEmailLabel.isDisplayed() ? "is displayed" : "is not displayed";
        testLogger.info("Add your contact details - Email label " + logMessage + " in Click and Collect page");
        sa.assertAll();
    }

    public void enterFullNameInClickAndCollectAddYourContactDetails(String name){
        clickAndCollectFullNameTextBox.click();
        clickAndCollectFullNameTextBox.sendKeys(name);
        waitThread(5000); //for demo
        testLogger.info("Add your contact details - entered Full Name as " + name + " in Click and Collect page");
    }

    public void enterMobileInClickAndCollectAddYourContactDetails(String mobile){
        clickAndCollectMobileTextBox.click();
        clickAndCollectMobileTextBox.sendKeys(mobile);
        waitThread(5000); //for demo
        testLogger.info("Add your contact details - entered Mobile Number as " + mobile + " in Click and Collect page");
    }

    public void enterEmailInClickAndCollectAddYourContactDetails(String email){
        scrollToParticularElement(clickAndCollectSelectAPaymentMethodSection);
        clickAndCollectEmailTextBox.click();
        clickAndCollectEmailTextBox.sendKeys(email);
        waitThread(5000); //for demo
        testLogger.info("Add your contact details - entered Email as " + email + " in Click and Collect page");
    }

    public void verifyClickAndCollectYourOrderSummaryDetails() {
        scrollToParticularElement(clickAndCollectBtn);
        SoftAssert sa = new SoftAssert();
        sa.assertTrue(clickAndCollectYourOrderSummaryTitle.isDisplayed(), "Your Order Summary details is not displayed");
        String logMessage = clickAndCollectYourOrderSummaryTitle.isDisplayed() ? "is displayed" : "is not displayed";
        testLogger.info("Your Order Summary details " + logMessage + " in Click and Collect page");
        testLogger.info(""+clickAndCollectCheckoutOrderSummaryDetails.getText());
        testLogger.info("Verified Checkout Order Summary details in Click and Collect section");
        sa.assertAll();
    }
}
