package com.landmarkshops.Web_Mweb.runner;


import com.landmarkshops.Web_Mweb.launcher.TestHarness;
import cucumber.api.CucumberOptions;
import cucumber.api.testng.CucumberFeatureWrapper;
import cucumber.api.testng.TestNGCucumberRunner;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

/**
 * Cucumber Runner
 *
 * <AUTHOR> Created by Ram
 * on 22/07/2019 Copyright (c) 2019 LandmarkGroup . All rights reserved.
 */
@CucumberOptions(features = "src/test/resources/Feature",
        glue = {"com.lmg.blc.web.stepDefinition", "com.landmarkshops.Web_Mweb.stepdefinition"},
        plugin = {"pretty", "html:target/cucumber-reports/cucumber-pretty",
                "json:target/cucumber-reports/CucumberTestReport.json",
                "ru.yandex.qatools.allure.cucumberjvm.AllureReporter",
                "rerun:target/cucumber-reports/rerun.txt",
                "json:target/cucumber.json",
                "com.cucumber.listener.ExtentCucumberFormatter:target/cucumber-reports/report.html"
        },
        monochrome = true,
        dryRun = false,
        //tags = { "@MiniBasket1,@MiniBasket2,@MiniBasket3,@MiniBasket4" })
        //tags = { "@MaxCheckOut,@MaxPLP,@SearchMax,@RegisterCheckOut,@MaxPLP,@ShukranBurn"}
        tags = {"@MaxEgypt"}
        //tags = { "@GuestUser-CnC-GiftCard"}
)
public class CucumberTest extends TestHarness {

    private TestNGCucumberRunner testNGCucumberRunner;

    @BeforeClass(alwaysRun = true)
    public void setUpClass() {
        testNGCucumberRunner = new TestNGCucumberRunner(this.getClass());
    }

    @Test(groups = "cucumber", description = "Runs cucumber Features", dataProvider = "features")
    public void feature(CucumberFeatureWrapper cucumberFeature) {
        testNGCucumberRunner.runCucumber(cucumberFeature.getCucumberFeature());
    }

    @DataProvider
    public Object[][] features() {
        return testNGCucumberRunner.provideFeatures();
    }

    @AfterClass(alwaysRun = true)
    public void testDownClass() {
        testNGCucumberRunner.finish();
    }

}