package com.landmarkshops.Web_Mweb.runner;

import com.landmarkshops.Web_Mweb.launcher.TestHarness;
import cucumber.api.CucumberOptions;
import cucumber.api.junit.Cucumber;
import org.junit.AfterClass;
import org.junit.BeforeClass;
import org.junit.runner.RunWith;


@RunWith(Cucumber.class)
@CucumberOptions(features = "src/test/resources/Feature",
        glue = { "com.lmg.blc.web.stepDefinition","com.landmarkshops.Web_Mweb.stepdefinition"},
        plugin = { "pretty", "html:target/cucumber-reports/cucumber-pretty",
                "json:target/cucumber-reports/CucumberTestReport.json",
                "ru.yandex.qatools.allure.cucumberjvm.AllureReporter",
                "rerun:target/cucumber-reports/rerun.txt",
                "json:target/cucumber.json",
                //"com.aventstack.extentreports.cucumber.adapter.ExtentCucumberAdapter:"
                "com.cucumber.listener.ExtentCucumberFormatter:target/cucumber-reports/report.html," +
                        "html:target/cucumber-reports/html-report.html," +
                        "json:target/cucumber-reports/cucumber.json"
        },
        monochrome = true,
        dryRun = false,
        //tags = { "@MiniBasket1,@MiniBasket2,@MiniBasket3,@MiniBasket4" })
        //tags = { " @CheckOut" }
        //tags = { " @Regression" })
        	//tags = { "@SignUpCheckoutValidation" }
            //tags = { "@MyAccountPride" }
        //tags = { "@MaxSignUp"}
       // tags = { "@MaxSignIn "}
       // tags = { "@MaxSignIn,@MaxFaceBook,@MaxSignUp,@ecom_Header,@Footer"}
           // tags = { "@CentrePointstoresOman"}
        //tags = { "@Testing"}
        //tags = { "@Retest2,@Retest2"}
        //tags = { "@Retest1,@Retest2,@Retest3,@Retest4,@Retest5"}
        //tags = { "@ShukranBurn,@MyCredit,@GiftCard11"}
        //tags = { "@RunScript"}
//        tags = { "@ShukranBurn"}
//        tags = { "@MyCredit"}
//        tags = { "@GiftCard11"}
//        tags = { "@FailReRun1"}
        tags = { "@test0001" }
//        tags = {"@HomeCentreOman"}
 )



public class CucumberJunitRunner extends  TestHarness{

    public static TestHarness th;


       @BeforeClass
        public static void before() throws Exception {
          th=new TestHarness();
           th.suiteSetUp();
       }

       @AfterClass
        public static void after(){
           th.stopTest();
       }

}