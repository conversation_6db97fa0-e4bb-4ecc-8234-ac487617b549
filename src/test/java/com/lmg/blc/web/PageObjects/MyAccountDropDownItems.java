package com.lmg.blc.web.PageObjects;

import com.landmarkshops.Web_Mweb.config.config;
import com.landmarkshops.Web_Mweb.utilities.StepContext;
import com.landmarkshops.Web_Mweb.utilities.TestDataBean;
import com.landmarkshops.Web_Mweb.utilities.WebUtils;
import org.assertj.core.api.SoftAssertions;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;

import java.util.List;
import java.util.NoSuchElementException;

import static com.landmarkshops.Web_Mweb.launcher.TestHarness.configProperties;

public class MyAccountDropDownItems extends WebUtils {
    public MyAccountDropDownItems(WebDriver driver) {
        super(driver);
        PageFactory.initElements(driver,this);
    }
    HeaderSectionPage hsp;

    @FindBy(xpath = "//*[@id=\"header-basket-signinWrap\"]/div[1]/div/div[2]/div/div/div/div[2]/ul/a[1]")
    private WebElement myAccount;

    @FindBy(xpath = "//*[@id='auth-container']/div/div/div[1]/div[2]/div/a/span")
    private WebElement m_myAccount;

    @FindBy(xpath = " //*[@id='root-header']/div/div/div[2]/button")
    private WebElement m_closeHamburgerMenu;



/*    @FindBy(xpath = "//*[@id='account-actions-signin']/span/img")
    private WebElement myAccountDropDownArrow;*/

    @FindBy(xpath = "//*[@id='auth-container']/div/div/div[1]/div[2]/div/a/span")
    private WebElement m_myAccountDropDownArrow;

    @FindBy(xpath = "//*[@id='desk-header-layout']/div/div[2]/div[1]/div/button/span/div[2]")
    private WebElement myAccountDropDownArrow;

    @FindBy(id = "my-acc-dropdown-1_QA")
    private WebElement myAccountItem;

    @FindBy(xpath = "//*[@id='header-basket-signinWrap']/div[1]/div/div/div/div/div/div[2]/ul/a[1]")
    private WebElement ar_myAccountItem;

    @FindBy(id = "my-acc-dropdown-3_QA")
    private WebElement myListsItem;



    @FindBy(id = "my-acc-dropdown-4_QA")
    private WebElement orderHistoryItem;


    @FindBy(id = "my-acc-dropdown-5_QA")
    private WebElement myAddressesItem;


    @FindBy(id = "my-acc-dropdown-6_QA")
    private WebElement paymentItem;



    @FindBy(id = "my-acc-dropdown-7_QA")
    private WebElement myCreditItem;


    @FindBy(id = "my-acc-title-7_QA")
    private WebElement reviewsItem;

    @FindBy(id = "my-acc-title-2_QA")
    private WebElement myShukranCardItem;

    @FindBy(xpath = "//*[@id='desk-header-layout']/div/div[2]/div[1]/div/div/div/div/div/div[2]/ul/a[9]/span")
    private WebElement signOutItem;

    @FindBy(xpath = "//*[text()='Sign out']")
    private WebElement mSignOutItem;

    @FindBy(xpath="//*[@id='main-part']/div/div[3]/div[2]/p/a")
    private WebElement cancel;


    /**
     * This method is used to verify username displayed once user Logged in.
     */

    public void verifyLoggedInUserName() {
        if ((config.getBrowser().equalsIgnoreCase("MOBILEWEBCHROMEANDROID")) || (config.getBrowser().equalsIgnoreCase("MOBILECHROME"))) {
            hsp = new HeaderSectionPage(driver);
            hsp.clickOnHamburgerMenu();
            waitForElementPresent(8);
            visibilityOf(m_myAccount, 20);
            String name = m_myAccount.getText().trim();
            testLogger.info("name as " + name);
            m_closeHamburgerMenu.click();
        } else {

            waitForElementPresent(8);
            visibilityOf(myAccount, 25);
            String name = myAccount.getText().trim();
            testLogger.info("name as " + name);
        }
    }

    /**
     * This method is used to click on MyAccount DropDown
     */

    public void clickOnMyAccountDropDownIcon() {

        if ((config.getBrowser().equalsIgnoreCase("MOBILEWEBCHROMEANDROID")) || (config.getBrowser().equalsIgnoreCase("MOBILECHROME"))) {
            hsp = new HeaderSectionPage(driver);
            hsp.clickOnHamburgerMenu();
            waitForVisibilityOfElementLocated(m_myAccountDropDownArrow, driver, 20);
            waitForElementToBeClickable(m_myAccountDropDownArrow, 25);
            m_myAccountDropDownArrow.click();
            waitForElementPresent(3);
        } else {

            waitForVisibilityOfElementLocated(myAccountDropDownArrow, driver, 30);
            waitForElementToBeClickable(myAccountDropDownArrow, 25);
            myAccountDropDownArrow.click();
            waitForElementPresent(3);
        }
    }


    /**
     * This method is used to click on my account  DropDown item
     */

    public void clickOnMyAccount() {

        if(StepContext.web) {
            implicitWait(20);

           // waitForElementToBeClickable(myAccountItem, 20);
//            myAccountItem.click();
            if(StepContext.lang.equalsIgnoreCase("AR")) {
                waitForVisibilityOfElementLocated(ar_myAccountItem,driver,20);
                ar_myAccountItem.click();
               // clickElementsList(MyAccountDropdownListValue, " حسابي");
            }else{
                waitForVisibilityOfElementLocated(myAccountItem,driver,20);
                clickElementsList(MyAccountDropdownListValue, "My Account");

            }
        }
    }



    /**
     * This method is used to click my lists
     */

    public void myLists() {
        waitForElementToBeClickable(myCreditItem,15);
        mouseHoverClick(myListsItem);
    }

    /**
     * This method is used to click on my account  DropDown item
     */

    public void clickOnMyOrders() {
        waitForElementToBeClickable(orderHistoryItem,15);
        orderHistoryItem.click();
    }

    /**
     * This method is used to click on Address Book DropDown
     */


    @FindBy(xpath = "//*[@id='desk-header-layout']//div[2]/div[1]//div[2]/ul/a")
    private List<WebElement> MyAccountDropdownListValue;

    @FindBy(xpath = "//*[@id='shukran-close']/span/div")
    private WebElement shukranPopUp;

    public void clickOnMyAddresses() {
//        waitForElementToBeClickable(myAddressesItem,15);
//        myAddressesItem.click();
        clickElementsList(MyAccountDropdownListValue, "Addresses");
    }


    /**
     * This method is used to click on Address Book DropDown
     */

    public void clickOnMyPayment() {
        waitForElementToBeClickable(paymentItem,15);
        paymentItem.click();
    }

    /**
     * This method is used to click on My credit DropDown
     */
    public void clickOnSavedCards() {
        // waitForElementToBeClickable(myAddressesItem,15);
        // myAddressesItem.click();
         clickElementsList(MyAccountDropdownListValue, "Saved Cards");
         }

    public void clickOnMyCredit() {
        waitForElementToBeClickable(myCreditItem,15);
        mouseHoverClick(myCreditItem);
    }


    /**
     * This method is used to click  on reviews
     */

    public void clickOnReviews() {
        waitForElementToBeClickable(reviewsItem,15);
        mouseHoverClick(reviewsItem);
    }



    /**
     * This method is used to click on My Shukran Card dropdown menu item
     */

    public void clickOnMyShukranCard() {
        waitForElementToBeClickable(myShukranCardItem,15);
        mouseHoverClick(myShukranCardItem);
    }



    /**
     * This method is used to click on SignOut dropdown menu item
     */
    public void clickOnSignOut() {
        if(config.getBrowser().trim().equalsIgnoreCase("CHROME") || config.getBrowser().trim().equalsIgnoreCase("CHROMEHEADLESS")) {
            implicitWait(10);
//            waitForVisibilityOfElementLocated(signOutItem,driver,30);
//            waitForElementToBeClickable(signOutItem, 30);
//            signOutItem.click();

//            try{
                if(isElementPresent(shukranPopUp)){
                    mouseHoverClick(shukranPopUp);
                }
//            } catch (NoSuchElementException e){
//                testLogger.info("Shukran PopUp is not displayed in Home page");
//            }

            clickElementsList(MyAccountDropdownListValue, "Sign Out");
            implicitWait(40);

        }else if(StepContext.mWeb){
            waitForElementPresent(4);
            scrollToElementBasedOnLocation(mSignOutItem);
            scrollToParticularElement(mSignOutItem);
            mSignOutItem.click();
            waitForElementPresent(10);

        }
    }



    public void verifyAllUIComponentsOfMyAccountDropDownItems() {
        SoftAssertions sas = new SoftAssertions();
        sas.assertThat(myAccountItem.getText().trim()).contains(configProperties.getProperty("myaccount.dropdown.item.MyAccount"));
        sas.assertThat(myListsItem.getText().trim()).isEqualTo(configProperties.getProperty("myaccount.dropdown.item.MyLists"));
        sas.assertThat(orderHistoryItem.getText().trim()).contains(configProperties.getProperty("myaccount.dropdown.item.OrderHistory"));
        sas.assertThat(myAddressesItem.getText().trim()).isEqualTo(configProperties.getProperty("myaccount.dropdown.item.MyAddresses"));
        sas.assertThat(paymentItem.getText().trim()).contains(configProperties.getProperty("myaccount.dropdown.item.Payment"));
        sas.assertThat(myCreditItem.getText().trim()).isEqualTo(configProperties.getProperty("myaccount.dropdown.item.MyCredit"));
        sas.assertThat(reviewsItem.getText().trim()).contains(configProperties.getProperty("myaccount.dropdown.item.Reviews"));
        sas.assertThat(myShukranCardItem.getText().trim()).isEqualTo(configProperties.getProperty("myaccount.dropdown.item.MyShukranCard"));
        sas.assertThat(signOutItem.getText().trim()).isEqualTo(configProperties.getProperty("myaccount.dropdown.item.SignOut"));
        sas.assertAll();

    }

    public void clickOnMyLists() {
        waitForElementToBeClickable(myListsItem,driver,15);
        myListsItem.click();
    }
}
