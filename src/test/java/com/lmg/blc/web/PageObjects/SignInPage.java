package com.lmg.blc.web.PageObjects;


import com.landmarkshops.Web_Mweb.utilities.UserDataBean;
import com.landmarkshops.Web_Mweb.utilities.WebUtils;
import org.assertj.core.api.SoftAssertions;
import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;

import static com.landmarkshops.Web_Mweb.launcher.TestHarness.configProperties;

public class SignInPage extends WebUtils {
	public SignInPage(WebDriver driver) {
		super(driver);
		PageFactory.initElements(driver, this);

	}


	@FindBy(xpath= "//html/body/div[2]/div[1]/h1")
	private WebElement signInNowTitle;

	@FindBy(xpath= "//html/body/div[2]/div[1]/h2")
	private WebElement signInNowSubTitle;

	@FindBy(xpath= "//html/body/div[2]/div[3]/div[1]/div[1]/ul/li/a/span")
	private WebElement signInWithFaceBookCTA;

	@FindBy(xpath= "//html/body/div[2]/div[3]/div[1]/div[1]/div/div[2]")
	private WebElement orViaEmail;

	@FindBy(xpath= "//html/body/div[2]/div[2]/div[1]/div[1]/div[1]/div[2]")
	private WebElement orViaEmail_m;


	@FindBy(xpath = "//*[@id='loginForm']/fieldset/div[1]/label")
	private WebElement eMailLabel;

	@FindBy(id = "username")
	private WebElement emailInput;

	@FindBy(id = "email-required")
	private WebElement emailRequiredErrorMsg;

	@FindBy(xpath= "//*[@id='lmg-error-msg']/div/p")
	private WebElement invalidEmailOrPasswordIncorrectErrorMsg;
	@FindBy(id = "email-invalid")
	private WebElement emailInvalidErrorMsg;

	@FindBy(xpath = "//*[@id='loginForm']/fieldset/div[2]/label")
	private WebElement passwordLabel;


	@FindBy(id = "password")
	private WebElement passwordInput;


	@FindBy(id = "password-required")
	private WebElement passwordRequired;

	@FindBy(id = "password-icon")
	private WebElement passwordIconBtn;

	@FindBy(id = "notification")
	private WebElement rememberPasswordCheckBox;

	@FindBy(xpath = "//*[@id='loginForm']/fieldset/div[3]/div[1]/label")
	private WebElement rememberMeLabel;

	@FindBy(id = "notification")
	private WebElement rememberMeCheckBox;

	@FindBy(xpath = "//*[@id='loginForm']/fieldset/div[3]/div[2]/div/a")
	private WebElement forgotPasswordLink;

	@FindBy(id = "loginForm_submit")
	private WebElement signInBtn;

	@FindBy(xpath = "//html/body/div[2]/div[3]/div[1]/div[2]/div/div/span")
	private WebElement dontHaveAccount;

	@FindBy(xpath = "//*/a/button[contains(text(),'Sign Up For One')]")
	private WebElement signUpForOneBtn;



	@FindBy(xpath ="//html/body/div[2]/div[3]/div[2]/div/div/div[1]/div[1]/img")
	private WebElement shukranImag;

	@FindBy(xpath="//html/body/div[2]/div[3]/div[2]/div/div/div[1]/div[2]")
	private WebElement linkYourShukranTextMessage;


	@FindBy(xpath="//html/body/div[2]/div[3]/div[2]/div/div/div[2]/div[2]")
	private WebElement earnYourShukranTextMsg;

	@FindBy(xpath = "//html/body/div[2]/div[3]/div[2]/div/div/div[3]/div[2]")
	private WebElement getExclusiveOfferTextMsg;


	@FindBy(xpath = "//html/body/div[2]/div[3]/div[2]/div/div/div[4]/div[2]")
	private WebElement instantRefundWithShuranPayTextMsg;

	@FindBy(xpath = "//html/body/div[2]/div[3]/div[2]/div/div/div[5]/div[2]")
	private WebElement useBalanceToShopTextMsg;

	@FindBy(id="email")
	private WebElement fbEmail;

	@FindBy(id="pass")
	private WebElement fbPassword;

	@FindBy(id="loginbutton")
	private WebElement fbLoginButton;

	@FindBy(xpath = "//*[@id='guest-checkout-section']//a/button")
	private WebElement checkOutAsAGuestCTA;

	/**
	 * This method is used to provide valid email and password for login
	 * @param email
	 * @param password
	 */
	public void signInWithValidCredentials(String email, String password) {
		waitForPageToLoad(100);
		waitForElementPresent(10);
		waitForWebElementPresent(emailInput,50);
		enterEmail(email);
		enterPassword(password);
		clickOnSignInButton();
	}

	public void landToLogInPage(){
		waitForPageToLoad(20);
		int count=0;
		while(!verifyNoelement(emailInput)){
			pageRefresh();
			waitForElementToBeClickable(emailInput,30);
			if(count == 20){
				break;
			}
			count++;
		}
	}

	public void signInWithValidCredentials(String user) {
//		waitForPageToLoad(100);
//		waitForElementPresent(10);
//		waitForWebElementPresent(emailInput,50);
		landToLogInPage();
		waitForPageToLoad(30);
		//waitForElementPresent(10);
		waitForWebElementPresent(emailInput,10);
		userData(user);
		enterEmail(UserDataBean.getEmail());
		enterPassword(UserDataBean.getPassword());
		clickOnSignInButton();
		waitForPageToLoad(10);
	}

	/**
	 * This method is used to enter EmailId
	 * @param email
	 */
	public void enterEmail(String email) {
		waitForWebElementPresent(emailInput,35);
		emailInput.sendKeys(email);
	}

	/**
	 * This method is used to enter password
	 * @param password
	 */
	public void enterPassword(String password) {
		waitForWebElementPresent(passwordInput,35);
		passwordInput.sendKeys(password);

	}

	/**
	 * This method is used to click on SignIn Button
	 */
	public void clickOnSignInButton() {
		waitForElementToBeClickable(signInBtn,35);
		//elementMouseClick(signInBtn);
		javaScriptExecuteClick(signInBtn);
		//mouseHoverClick(signInBtn);


	}

	/**
	 * This method is used to verify inline message for invalid email or password
	 * @return
	 */
	public void verifyInvalidSignInErrorMessage() {
		SoftAssertions sas =new SoftAssertions();
		waitForWebElementPresent(invalidEmailOrPasswordIncorrectErrorMsg,20);
		sas.assertThat(invalidEmailOrPasswordIncorrectErrorMsg.getText().trim()).contains(configProperties.getProperty("signIn.error.invalid.credentials"));
		sas.assertAll();

	}

	public void verifyUIComponentsInSignInPage() {
		SoftAssertions sas =new SoftAssertions();
		sas.assertThat(signInNowTitle.getText().trim()).contains(configProperties.getProperty("signIn.title"));
//		sas.assertThat(signInNowSubTitle.getText().trim()).isEqualTo(configProperties.getProperty("signIn.header.text"));
		//sas.assertThat(signInWithFaceBookCTA.getText().trim()).contains(configProperties.getProperty("signIn.facebook.label"));
		//sas.assertThat(orViaEmail.getText().trim()).isEqualTo(configProperties.getProperty("signIn.orViaEmail.text"));
		sas.assertThat(eMailLabel.getText().trim()).isEqualTo(configProperties.getProperty("signIn.yourEmail.label"));
		sas.assertThat(passwordLabel.getText().trim()).isEqualTo(configProperties.getProperty("signIn.password"));
		sas.assertThat(rememberMeLabel.getText().trim()).isEqualTo(configProperties.getProperty("signIn.email.input.remember"));
		sas.assertThat(forgotPasswordLink.getText().trim()).isEqualTo(configProperties.getProperty("signIn.forgotpassword.link"));
		sas.assertThat(signInBtn.getText().trim()).isEqualTo(configProperties.getProperty("signIn.cta"));
		//sas.assertThat(dontHaveAccount.getText().trim()).isEqualTo(configProperties.getProperty("signIn.donotHaveAnAccount.text"));
		//sas.assertThat(signUpForOneBtn.getText().trim()).isEqualTo(configProperties.getProperty("signIn.signupForOne.cta"));
		sas.assertThat(linkYourShukranTextMessage.getText().trim()).isEqualTo(configProperties.getProperty("signIn.shukran.link.text"));
		sas.assertThat(earnYourShukranTextMsg.getText().trim()).isEqualTo(configProperties.getProperty("signIn.shukran.earnShukran.text"));
		sas.assertThat(getExclusiveOfferTextMsg.getText().trim()).isEqualTo(configProperties.getProperty("signIn.shukran.get.exclusive.text"));
		sas.assertThat(instantRefundWithShuranPayTextMsg.getText().trim()).isEqualTo(configProperties.getProperty("signIn.shukran.instant"));
		sas.assertThat(useBalanceToShopTextMsg.getText().trim()).isEqualTo(configProperties.getProperty("signIn.shukran.use.balance"));
		sas.assertAll();
	}


	public void validateEmailAndPwdErrorMessageInSignIn(){
		SoftAssertions sas =new SoftAssertions();
		clickOnSignInButton();
		waitForWebElementPresent(emailRequiredErrorMsg,driver,15);
		sas.assertThat(emailRequiredErrorMsg.getText().trim()).isEqualTo(configProperties.getProperty("signIn.email.input"));
		sas.assertThat(passwordInput.getText().trim()).isEqualTo(configProperties.getProperty("signIn.password.input"));
		enterEmail("qaTest");
		waitForWebElementPresent(emailInvalidErrorMsg,driver,15);
		sas.assertThat(emailInvalidErrorMsg.getText().trim()).isEqualTo(configProperties.getProperty("signIn.email.input.error.valid"));
		enterPassword("a");
		passwordInput.clear();
		sas.assertThat(passwordRequired.getText().trim()).isEqualTo(configProperties.getProperty("signIn.password.input.error"));

	}



	public void forgotPasswordClick() {
		waitForElementToBeClickable(forgotPasswordLink,15);
		forgotPasswordLink.click();
	}


	public void signUpForOne(){
		waitForElementToBeClickable(signUpForOneBtn,15);
		signUpForOneBtn.click();
	}



	public void enableRememberMe(){

		rememberMeCheckBox.click();
	}
	public void disableRememberMe(){

		rememberMeCheckBox.click();
	}


	/**
	 *
	 * Perform Scroll to the element using Java script.
	 */

	public void performScrollIntoView(WebElement webElement) {
		JavascriptExecutor javascriptExector = (JavascriptExecutor) driver;
		javascriptExector.executeScript("arguments[0].scrollIntoView();", webElement);
		javascriptExector.executeScript("window.scrollTo(0, document.body.scrollHeight)");
	}

	public void clickOnSignUpForOne() {
		waitForPageToLoad(50);
		waitForElementPresent(3);
		waitForElementToBeClickable(signUpForOneBtn, 35);
		signUpForOneBtn.click();
		testLogger.info("clicked on signUp For One");

	}

	public String clickOnSignInWithFaceBook() {
		waitForElementToBeClickable(signInWithFaceBookCTA, 35);
		signInWithFaceBookCTA.click();
		testLogger.info("SignIn with FaceBook button clicked");
		return driver.getTitle();
	}

	public void navigateBack() {

		driver.navigate().back();
	}



	public void enterFaceBookEmail(String email) {
		waitForWebElementPresent(fbEmail,15);
		fbEmail.sendKeys(email);
	}

	public void enterFaceBookPwd(String password) {
		waitForWebElementPresent(fbPassword,15);
		fbPassword.sendKeys(password);
	}

	public void clickOnFBLogin() {
		waitForWebElementPresent(fbLoginButton,15);
		fbLoginButton.click();
	}

	public void signInWithFaceBook() {
		waitForElementToBeClickable(signInWithFaceBookCTA,driver,15);
		signInWithFaceBookCTA.click();
	}

	public void signInClickButton() {
		waitForPageToLoad(50);
		waitForElementToBeClickable(signInBtn,driver,35);
		signInBtn.click();
	}

	public void validateErrorMessagesInSignIn() {
	}


	public void clickOnCheckOutAsGuestUser() {
		//waitForElementPresent(5);
		//pageRefresh();
		landToLogInPage();
		waitForPageToLoad(10);
		scrollPageEnd();
		waitForElementPresent(5);
		waitForElementToBeClickable(checkOutAsAGuestCTA,20);
		checkOutAsAGuestCTA.click();
	}


    public void verifyUserLandedSignInPage() {
		SoftAssertions sas =new SoftAssertions();
		sas.assertThat(signInNowTitle.getText().trim()).contains(configProperties.getProperty("signIn.title"));
//		sas.assertThat(signInNowSubTitle.getText().trim()).isEqualTo(configProperties.getProperty("signIn.header.text"));
		sas.assertAll();
    }
}
