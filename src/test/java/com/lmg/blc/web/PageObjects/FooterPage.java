package com.lmg.blc.web.PageObjects;

import com.landmarkshops.Web_Mweb.config.config;
import com.landmarkshops.Web_Mweb.utilities.WebUtils;
import org.assertj.core.api.SoftAssertions;
import org.json.simple.JSONObject;
import org.junit.Assert;
import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;

import java.util.List;

import static com.landmarkshops.Web_Mweb.launcher.TestHarness.configProperties;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertAll;

public class FooterPage extends WebUtils {

    public FooterPage(WebDriver driver) {
        super(driver);
        PageFactory.initElements(driver, this);

    }

    /**
     * Footer - Subscribe email section
     */
    @FindBy(id = "subscribe-title")
    private WebElement subscribeTitle;

    @FindBy(id = "subscribe-info")
    private WebElement subscribeInfo;

    @FindBy(id = "subscribe-email")
    private WebElement subscribeEmail;

    @FindBy(xpath = "//*[@id='btn-subscribe']/span[1]")
    private WebElement subscribeButton;

    @FindBy(id = "email")
    private WebElement emailInput;


    @FindBy(xpath = "//*[@id='email']")
    private WebElement enterYourEmailAddressValidation;

    @FindBy(xpath = "//*[@id='email']")
    private WebElement mEnterYourEmailAddressValidation;


    @FindBy(xpath = "//*[@id='footer-subscribe']/form/div/div[1]/div/div[2]")
    private WebElement mEnterValidEmailAddress;


    @FindBy(xpath = "//*[@id='subscribe-msg']")
    private WebElement subscribeSuccessMessage;


    /**
     * Footer - DownLoad Apps section
     */
    @FindBy(id = "download-apps")
    private WebElement downLoadOurAppsText;

    @FindBy(id = "download-apps-info")
    private WebElement downLoadOurAppsSubTitle;

    @FindBy(xpath = "//*[@id='apple-app-store']/a/span/img")
    private WebElement appStoreImg;

    @FindBy(xpath = "//*[@id='google-play-store']/a/span/img")
    private WebElement googlePlayStoreImg;

    @FindBy(xpath = "//*[@id='huawei-app']/a/span/img")
    private WebElement appGalleryImg;


    @FindBy(xpath = "//*[@id='footer-menu']/div[1]/h6")
    private WebElement footerMenuDivOne;

    /**
     * Footer - Menu section
     */


    @FindBy(xpath = "//*[@id='footer-menu']/div[1]/h6")
    private WebElement footerMenuWomen;

    @FindBy(xpath = "//*[@id='footer-menu']/div[2]/h6")
    private WebElement footerMenuMen;

    @FindBy(xpath = "//*[@id='footer-menu']/div[3]/h6")
    private WebElement footerMenuKids;

    @FindBy(xpath = "//*[@id='footer-menu']/div[4]/h6")
    private WebElement footerMenuShoes;

    @FindBy(xpath = "//*[@id='footer-menu']/div[5]/h6")
    private WebElement footerMenuMaxHome;

    @FindBy(xpath = "//*[@id='footer-menu']/div[6]/h6")
    private WebElement footerMenuExplore;

    @FindBy(xpath = "//*[@id='footer-menu']/div[7]/h6")
    private WebElement footerMenuAbout;

    @FindBy(xpath = "//*[@id='footer-menu']/div[8]/h6")
    private WebElement footerMenuMaxHelp;

    /**
     * Footer - Talk to us section
     */

    @FindBy(xpath = "//*[@id='footer-contact-talk']/a/span/div[1]")
    private WebElement footerTalkToUsImg;

    @FindBy(xpath = "//*[@id='footer-contact-talk']/a/span/div[2]/div[1]")
    private WebElement footerTalkToUsText;

    @FindBy(xpath = "//*[@id='footer-contact-talk']/a/span/div[2]/div[2]")
    private WebElement footerTalkToUsDailNumber;

    /**
     * Footer - Contact us section
     */

    @FindBy(xpath = "//*[@id='footer-contact-helpcentre']/a/span/div[1]")
    private WebElement footerContactHelpCentreImg;

    @FindBy(xpath = "//*[@id='footer-contact-helpcentre']/a/span/div[2]/div[1]")
    private WebElement footerContactHelpCentreText;

    @FindBy(xpath = "//*[@id='footer-contact-helpcentre']/a/span/div[2]/div[1]")
    private WebElement footerContactHelpCentreSiteAddress;

    /**
     * Footer -Contact us section
     */

    @FindBy(xpath = "//*[@id='footer-contact-write']/a/span/div[1]")
    private WebElement footerWriteToUsImg;

    @FindBy(xpath = "//*[@id='footer-contact-write']/a/span/div[2]/div[1]")
    private WebElement footerWriteToUsText;

    @FindBy(xpath = "//*[@id='footer-contact-write']/a/span/div[2]/div[2]")
    private WebElement footerWriteToUseSupportEmailAddress;

    /**
     * Social media share icon section-----Footer
     */

    @FindBy(id = "footer-social-facebook")
    private WebElement footerSocialFaceBookImg;

    @FindBy(id = "footer-social-twitter")
    private WebElement footerSocialTwitterImg;

    @FindBy(id = "footer-social-instagram")
    private WebElement footerSocialInstagramImg;


    /**
     * Terms and condition section
     */


    @FindBy(id = "footer-logo")
    private WebElement footerMaxLogo;

    @FindBy(id = "footer-copywrite")
    private WebElement footerCopyWrite;

    @FindBy(xpath = "//*[@id='footer-term-condition']/a[1]")
    private WebElement footerTermsAndConditons;

    @FindBy(xpath = "//*[@id='footer-term-condition']/a[2]")
    private WebElement footerPrivacyPolicy;


    /**
     * Footer - country switcher
     */


    @FindBy(id = "footer-language-switch")
    private WebElement footerLangChange;

    @FindBy(xpath = "//*[@id='footer-country-lang-switcher']/div[3]/a/span[1]")
    private WebElement mFooterLangChange;


    @FindBy(xpath = "//*[@id='footer-country-switch']/div/div/div[1]/button/span/div[1]/div")
    private WebElement footerCountryValue;

    @FindBy(xpath = "//*[@id='footer-country-switch']/div/div/div[1]/button/span/div[2]")
    private WebElement footerCountryDropDownIcon;

    @FindBy(xpath = "//*[@id='footer-country-lang-switcher']/div[1]/div/div[1]/button/span[1]/div[2]")
    private WebElement mFooterCountryDropDownIcon;


    @FindBy(xpath = "//*[@id='footer-menu']/div[6]/div/div[5]/a")
    private WebElement help;

    @FindBy(xpath = "//*[@id='footer-menu']/div[2]/div/a[1]/span[1]")
    private WebElement m_help;


    //mweb Footer

    @FindBy(xpath = "//*[@id='footer-wrapper']/div[1]")
    private WebElement mSubscribeTitle;


    @FindBy(xpath = "//*[@id='footer-wrapper']/div[2]")
    private WebElement mSubscribeInfo;

    @FindBy(id = "footer-social")
    private WebElement mFooterSocialMediaSection;


    @FindBy(id = "apple-app-store")
    private WebElement app_Store;


    @FindBy(id = "google-play-store")
    private WebElement googlePlay;

    @FindBy(id = "huawei-app")
    private WebElement appGallery;

    @FindBy(xpath = "//html/body/div/div[2]/div[1]/div")
    private WebElement appGallerySiteText;
    @FindBy(xpath="//*[@id='footer-menu']/div[4]/div/div[2]/a")
    private WebElement storeLocator;

    @FindBy(xpath="//*[@id='footer-menu']/div")
    private List<WebElement> footerCategoryLink;


    public void verifySubscriptionSection() {

        JSONObject footerObject = new JSONObject();
         Object[] keyValuePairs = footerCategoryLink.toArray();

        for (int i = 0; i < keyValuePairs.length; i += 2) {
            // Ensure we don't go out of bounds
            if (i + 1 < keyValuePairs.length) {
                Object key = keyValuePairs[i].toString();
                Object value = keyValuePairs[i + 1];

                System.out.println("Key: " + key + ", Value: " + value);
            }
        }




        if (config.getBrowser().trim().equalsIgnoreCase("CHROME") || config.getBrowser().trim().equalsIgnoreCase("CHROMEHEADLESS") || config.getBrowser().trim().equalsIgnoreCase("MOBILECHROME")) {
            SoftAssertions sas = new SoftAssertions();
            scrollToParticularElement(subscribeTitle);
            sas.assertThat(subscribeTitle.getText().trim()).isEqualTo(configProperties.getProperty("footer.subscription.title"));
            sas.assertThat(subscribeInfo.getText().trim()).isEqualTo(configProperties.getProperty("footer.subscription.info"));
            sas.assertThat(subscribeButton.isDisplayed());
            sas.assertThat(downLoadOurAppsText.getText().trim()).isEqualTo(configProperties.getProperty("footer.download.app.text"));
            sas.assertThat(downLoadOurAppsSubTitle.getText().trim()).isEqualTo(configProperties.getProperty("footer.download.app.subtext"));
            sas.assertAll();
        } else {
            SoftAssertions sas = new SoftAssertions();
            scrollToParticularElement(mSubscribeTitle);
            sas.assertThat(mSubscribeTitle.getText().trim()).isEqualTo(configProperties.getProperty("footer.subscription.title"));
            sas.assertThat(mSubscribeInfo.getText().trim()).isEqualTo(configProperties.getProperty("footer.subscription.info"));
            sas.assertThat(subscribeButton.isDisplayed());
            // sas.assertThat(downLoadOurAppsText.getText().trim()).isEqualTo(configProperties.getProperty("footer.download.app.text"));
            //sas.assertThat(downLoadOurAppsSubTitle.getText().trim()).isEqualTo(configProperties.getProperty("footer.download.app.subtext"));
            sas.assertAll();
        }


    }

    public void userClickOnSubscribe() {
        scrollToParticularElement(subscribeButton);
        waitForWebElementPresent(subscribeButton, driver, 15);
        mouseHoverClick(subscribeButton);


    }

    public void enterEmailForSubscription() {
        String email = randomGenerateEmail();
        emailInput.clear();
        emailInput.sendKeys(email);
    }

    public void enterEmailAddressValidation() {
        if (config.getBrowser().trim().equalsIgnoreCase("CHROME") || config.getBrowser().trim().equalsIgnoreCase("CHROMEHEADLESS") || config.getBrowser().trim().equalsIgnoreCase("MOBILECHROME")) {
            SoftAssertions sas = new SoftAssertions();
            sas.assertThat(enterYourEmailAddressValidation.getAttribute("placeholder")).isEqualTo(configProperties.getProperty("footer.subscription.error.email"));
            emailInput.sendKeys("a");
            waitForElementPresent(2);
            mouseHoverClick(subscribeButton);
            waitForElementPresent(3);
         //   sas.assertThat(enterYourEmailAddressValidation.getAttribute("placeholder").trim()).isEqualTo(configProperties.getProperty("footer.subscription.error.valid.email"));
            sas.assertAll();

        } else {
            SoftAssertions sas = new SoftAssertions();
            scrollPageEnd();
            sas.assertThat(mEnterYourEmailAddressValidation.getAttribute("placeholder").trim()).isEqualTo(configProperties.getProperty("footer.subscription.error.email"));
            moveToElementAndClickAndSendKeys(emailInput,"a");
            subscribeButton.click();
           waitForVisibilityOfElementLocated(mEnterValidEmailAddress,driver,15);
            sas.assertThat(mEnterValidEmailAddress.getText().trim()).isEqualTo(configProperties.getProperty("footer.subscription.error.valid.email"));
            sas.assertAll();
        }
    }

    public void verifySubscriptionConfirmationMessage() {
        waitForVisibilityOfElementLocated(subscribeSuccessMessage, driver, 20);
        assertThat(subscribeSuccessMessage.getText().trim()).isEqualTo(configProperties.getProperty("footer.subscription.success.msg"));


    }

    public void chooseLanguageAs(String lang) {

        if (config.getBrowser().trim().equalsIgnoreCase("CHROME") || config.getBrowser().trim().equalsIgnoreCase("CHROMEHEADLESS") || config.getBrowser().trim().equalsIgnoreCase("MOBILECHROME")) {
            scrollToParticularElement(footerLangChange);
            if ((lang.equalsIgnoreCase("English")) && (footerLangChange.getText().trim().equalsIgnoreCase("العربية"))) {
                waitForElementToBeClickable(footerLangChange, 10);
                footerLangChange.click();
                System.out.println("language changed from english to arabic ");
            } else if ((lang.equalsIgnoreCase("العربية")) && (footerLangChange.getText().trim().equalsIgnoreCase("العربية"))) {
                waitForElementToBeClickable(footerLangChange, 10);
                mouseHoverClick(footerLangChange);
                footerLangChange.click();
                System.out.println("language changed from english to arabic ");
            }
        } else {
            scrollPageEnd();
            if ((lang.equalsIgnoreCase("English")) && (mFooterLangChange.getText().trim().equalsIgnoreCase("العربية"))) {
                waitForElementToBeClickable(mFooterLangChange, 10);
                waitForWebElementPresent(mFooterLangChange, 15);
                mouseHoverClick(mFooterLangChange);
                System.out.println("language changed from english to arabic ");
            } else if ((lang.equalsIgnoreCase("العربية")) && (mFooterLangChange.getText().trim().equalsIgnoreCase("العربية"))) {
                waitForElementToBeClickable(mFooterLangChange, 10);
                mouseHoverClick(mFooterLangChange);
                System.out.println("language changed from english to arabic ");
            }
        }
    }

    public void clickOnCountryDropDown() {
        if (config.getBrowser().trim().equalsIgnoreCase("CHROME") || config.getBrowser().trim().equalsIgnoreCase("CHROMEHEADLESS") || config.getBrowser().trim().equalsIgnoreCase("MOBILECHROME")) {
            waitForElementToBeClickable(footerCountryDropDownIcon, driver, 15);
            mouseHoverClick(footerCountryDropDownIcon);
            waitForElementPresent(3);
        } else {
            scrollPageEnd();
            waitForElementToBeClickable(mFooterCountryDropDownIcon, driver, 15);
            mouseHoverClick(mFooterCountryDropDownIcon);
            waitForElementPresent(3);
        }

    }

    public void chooseCountry(String countryInput) {
        String country_pre = "//*[@id='footer-country-switch']/div/div/div[2]/div/div/div/nav/div[";
        String country_post = "]/div";
        String countryName;
        for (int i = 1; i <= 6; i++) {
            String countryLocator = country_pre + i + country_post;
            countryName = driver.findElement(By.xpath(countryLocator)).getText().trim();
            System.out.println("iterate country as:------>  " + countryName);
            if (countryName.equalsIgnoreCase(countryInput.trim())) {
                driver.findElement(By.xpath(countryLocator)).click();
                waitForPageToLoad(15);
                //waitForVisibilityOfElementLocated(footerCountryValue,driver,15);
                // assertThat(footerCountryValue.getText().trim()).isEqualTo(countryName);
                break;
            }

        }

    }

    public void chooseCountryFromFooter(String countryNameValue) {
        String country_pre = "//*[@id='footer-country-lang-switcher']/div/div/div[2]/div/div/div/nav/div[";
        String country_post = "]/div";
        String countryName;
        for (int i = 1; i <= 6; i++) {
            String countryLocator = country_pre + i + country_post;
            countryName = driver.findElement(By.xpath(countryLocator)).getText().trim();
            System.out.println("iterate country as:------>  " + countryName);
            if (countryName.equalsIgnoreCase(countryNameValue.trim())) {
                driver.findElement(By.xpath(countryLocator)).click();
                waitForPageToLoad(15);
                //waitForVisibilityOfElementLocated(footerCountryValue,driver,15);
                // assertThat(footerCountryValue.getText().trim()).isEqualTo(countryName);
                break;
            }

        }
    }

    public void clickOnHelp() {
        if (config.getBrowser().trim().equalsIgnoreCase("CHROME") || config.getBrowser().trim().equalsIgnoreCase("CHROMEHEADLESS") || config.getBrowser().trim().equalsIgnoreCase("MOBILECHROME")) {
            scrollToParticularElement(help);
            waitForPageToLoad(18);
            waitForElementToBeClickable(help, 30);
            mouseHoverClick(help);


        } else {
            scrollToParticularElement(m_help);
            waitForElementToBeClickable(m_help, 30);
            m_help.click();
        }
    }

    public void verifyUserLandedToAppPage() {

        waitForElementToBeClickable(app_Store,15);
        waitForElementPresent(10);
        waitForPageToLoad(20);
        app_Store.click();
        String url =driver.getCurrentUrl();
        Assert.assertTrue(url.contains("https://apps.apple.com/ae/app/id1161312751"));
        navigateBack();
        waitForElementPresent(10);
    }

    public void verifyUserLandedToGooglePlayPage() {

        scrollPageEnd();
        waitForElementToBeClickable(googlePlay,15);
        mouseHoverClick(googlePlay);
        waitForPageToLoad(15);
        String url =driver.getCurrentUrl();
        Assert.assertTrue(url.contains("https://play.google.com/store/apps/details?id=com.landmarkgroup.maxstore"));
        navigateBack();
       waitForElementPresent(10);

    }

    public void verifyUserLandedToAppGalleryPage() {
        waitForElementToBeClickable(appGallery,10);
        waitForPageToLoad(25);
        waitForVisibilityOfElementLocated(appGallerySiteText,driver,18);
        appGallery.click();
        String url =driver.getCurrentUrl();
        Assert.assertTrue(url.contains("https://appgallery.huawei.com/#/app/C103177063"));
        navigateBack();
        waitForElementPresent(10);
    }


    public void clickOnStoreLocator() {
       scrollToParticularElement(storeLocator);
        javaScriptExecute(storeLocator);
        /*WebDriverWait wait = new WebDriverWait(driver, 10);
        WebElement element = wait.until(ExpectedConditions.elementToBeClickable(By.xpath("//*[@id='footer-menu']/div[4]/div/div[2]/a")));
        ((JavascriptExecutor)driver).executeScript("arguments[0].click();", element);*/
        waitForElementToBeClickable(storeLocator,driver,15);
        storeLocator.click();
       waitForPageToLoad(20);

    }
}