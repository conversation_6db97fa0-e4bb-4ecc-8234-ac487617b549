package com.lmg.blc.web.PageObjects;

import com.landmarkshops.Web_Mweb.utilities.StepContext;
import com.landmarkshops.Web_Mweb.utilities.WebUtils;
import org.assertj.core.api.SoftAssertions;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;

import java.util.List;

import static com.landmarkshops.Web_Mweb.launcher.TestHarness.configProperties;

public class MyAccountPage extends WebUtils {
    public MyAccountPage(WebDriver driver) {
        super(driver);
        PageFactory.initElements(driver, this);
    }
    /*    @FindBy(xpath = "//*[@id='main-part']/div/div/div[1]/div/h1")
	private WebElement myAccount;*/

    @FindBy(xpath = "//*[@id='my-acc-hdng_QA']")
    private WebElement myAccount;

    @FindBy(id = "my-acc-title-1_QA")
    private WebElement myAccount_purchase;

    @FindBy(id = "my-acc-title-2_QA")
    private WebElement myAccount_reviews;

    @FindBy(id = "my-acc-title-3_QA")
    private WebElement myAccount_addresses;


    @FindBy(xpath = "//*[@id='my-acc-subHdng_QA']")
    private WebElement manageYourProfileMsg;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[2]/div/div[1]/a/div/div[1]/img")
    private WebElement profileSectionImg;

    @FindBy(xpath = "//*[@id='my-acc-title-1_QA']")
    private WebElement profileSection;



    @FindBy(xpath = "//*[@id='main-part']/div/div/div[1]/div/a/div[2]")
    private WebElement m_ShukranJoinNow;
    @FindBy(xpath = "//*[@id='main-part']/div/div/div[2]/div[1]/a[1]")
    private WebElement m_purchase;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[2]/div[1]/a[2]")
    private WebElement m_reviews;


    @FindBy(xpath = "//*[@id='main-part']/div/div/div[2]/div[1]/a[3]")
    private WebElement m_addresses;

    @FindBy(xpath = "//*[@id='my-acc-subTitle-1_QA']")
    private WebElement manageYourPersonalDetails;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[2]/div/div[2]/a/div/div[1]/img")
    private WebElement myShukranCardImg;

    @FindBy(xpath = "//*[@id='my-acc-title-2_QA']")
    private WebElement myShukranCard;

    @FindBy(xpath = "//*[@id='my-acc-subTitle-2_QA']")
    private WebElement earnAndSpend;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[2]/div/div[3]/a/div/div[1]/img")
    private WebElement myListsImg;

    @FindBy(xpath = "//*[@id='my-acc-title-3_QA']")
    private WebElement myLists;

    @FindBy(xpath = "//*[@id='my-acc-subTitle-3_QA']")
    private WebElement myList_viewMostWantedProducts;


    @FindBy(xpath = "//*[@id='main-part']//a/div/div[2]/h5")
    private List<WebElement> MyAccountPageList;
    @FindBy(xpath = "//*[@id='main-part']/div/div/div[2]/div/div[5]/a/div/div[1]/img")
    private WebElement paymentImg;

    @FindBy(xpath = "//*[@id='my-acc-title-5_QA']")
    private WebElement payment;


    @FindBy(xpath = "//*[@id='my-acc-subTitle-5_QA']")
    private WebElement manageYourPaymentPreferences;



    @FindBy(xpath = "//*[@id='main-part']/div/div/div[2]/div/div[7]/a/div/div[1]/img")
    private WebElement reviewsImg;

    @FindBy(xpath = "//*[@id='my-acc-title-7_QA']")
    private WebElement reviews;


    @FindBy(xpath = "//*[@id='my-acc-subTitle-7_QA']")
    private WebElement viewAllYourReviews;


    @FindBy(xpath = "//*[@id='main-part']/div/div/div[2]/div/div[4]/a/div/div[1]/img")
    private WebElement myAddressesImg;

    @FindBy(xpath = "//*[@id='my-acc-title-4_QA']")
    private WebElement myAddresses;


    @FindBy(xpath = "//*[@id='my-acc-subTitle-4_QA']")
    private WebElement manageYourShippingAndBillingAddresses;



    @FindBy(xpath = "//*[@id='main-part']/div/div/div[2]/div/div[6]/a/div/div[1]/img")
    private WebElement myCreditImg;

    @FindBy(xpath = "//*[@id='my-acc-title-6_QA']")
    private WebElement myCredit;


    @FindBy(xpath = "//*[@id='my-acc-subTitle-6_QA']")
    private WebElement viewYourAvailableCreditBalance;



    @FindBy(xpath = "//*[@id='main-part']/div/div/div[2]/div/div[8]/a/div/div[1]/img")
    private WebElement orderHistoryImg;

    @FindBy(xpath = "//*[@id='my-acc-title-8_QA']")
    private WebElement orderHistory;


    @FindBy(xpath = "//*[@id='my-acc-subTitle-8_QA']")
    private WebElement viewAndTrackYourOrders;

    @FindBy(xpath = "//*[@id='root-header']/div/div/div[5]/div/img[2]")
    private WebElement addYouLocationPopupClose;

    @FindBy(id="myAcc-topNav_QA")
    private WebElement myAccountTopNavigation;

    @FindBy(xpath = "(//*[@id='my-acc-title-1_QA'])[1]")
    private WebElement purchases;

    @FindBy(xpath= "//*[@id='main-part']/div/div[2]/div[2]/div[1]/a[1]")
    private WebElement mPurchase;

//    @FindBy(xpath= " //*[text()='Purchases']")
//    private WebElement mPurchase;




    @FindBy(xpath="//*[@id='main-part']/div/div[2]/div[2]/div[2]/a[2]")
    private WebElement savedCardLink;
    /**
     * click on profile section to navigate to profile page
     */
    public void profileSectionClick() {
        waitForPageToLoad(100);
        waitForElementPresent(2);
        waitForElementToBeClickable(profileSection,25);
        profileSection.click();
    }

    /**
     * click on my Lists to navigate my Lists page
     */
    public void myListsClick() {

        myLists.click();
    }

    /**
     * click on payment section to navigate to payment page
     */
    public void paymentSectionClick() {
        boolean status=verifyNoelement(addYouLocationPopupClose);
        if(status) {
            addYouLocationPopupClose.click();
        }
        //payment.click();
        clickElementsList(MyAccountPageList, "Saved Cards");
    }

    /**
     * click on reviews section to navigate reviews page
     */
    public void reviewSectionClick() {

        reviews.click();
    }


    /**
     * click on my Shukran card section to navigate reviews page
     */
    public void myShukranCardSectionClick() {

        myShukranCard.click();
    }


    /**
     * click on myAddresses section to navigate reviews page
     */
    public void myAddressesSectionClick() {
        boolean status=verifyNoelement(addYouLocationPopupClose);
        if(status) {
            addYouLocationPopupClose.click();
        }
        if(StepContext.mWeb) {
            m_addresses.click();
            waitForPageToLoad(4);
        }else {
            //waitClick(myAddresses,driver,15);
            clickElementsList(MyAccountPageList, "Addresses");
            //myAddresses.click();

        }
        }

    /**
     * click on my credit section to  my credit page
     */
    public void myCreditSectionClick() {

        myCredit.click();
    }


    /**
     * click on order section to navigate to order history page
     */
    public void orderHistorySectionClick() {
        waitForPageToLoad(25);
//        waitForWebElementPresent(orderHistory, 15);
        orderHistory.click();
    }

    /**
     * click on signOut section to logged out from account
     */
    public void signOutClick() {
        waitForElementPresent(2);
        //signOut.click();
    }

    /**
     * click on My list section
     */
    public void myListsSectionClick() {
        waitForWebElementPresent(myLists, 15);
        myLists.click();
    }

    /**
     * click on reviews section
     */
    public void reviewsSectionClick() {
        waitForWebElementPresent(reviews, 15);
        reviews.click();
    }


    /**
     * Verify all UI components in My account page
     */






    public void verifyUIComponentsInMyAccountPage() {
        SoftAssertions sas = new SoftAssertions();
        if(!StepContext.mWeb) {
            sas.assertThat(myAccount.getText().trim()).isEqualTo(configProperties.getProperty("myAccountPage.title.text"));
        }
        sas.assertThat(manageYourProfileMsg.getText().trim()).isEqualTo(configProperties.getProperty("myAccountPage.title.subTitle"));

        sas.assertThat(profileSection.getText().trim()).isEqualTo(configProperties.getProperty("myAccountPage.profile.section"));
        sas.assertThat(manageYourPersonalDetails.getText().trim()).isEqualTo(configProperties.getProperty("myAccountPage.profile.p"));

        sas.assertThat(myLists.getText().trim()).isEqualTo(configProperties.getProperty("myAccountPage.myLists.section"));
        sas.assertThat(myList_viewMostWantedProducts.getText().trim()).isEqualTo(configProperties.getProperty("myAccountPage.myList.p"));

        sas.assertThat(payment.getText().trim()).isEqualTo(configProperties.getProperty("myAccountPage.payment.section"));
        sas.assertThat(manageYourPaymentPreferences.getText().trim()).isEqualTo(configProperties.getProperty("myAccountPage.payment.p"));

        sas.assertThat(reviews.getText().trim()).isEqualTo(configProperties.getProperty("myAccountPage.reviews.section"));
        sas.assertThat(viewAllYourReviews.getText().trim()).isEqualTo(configProperties.getProperty("myAccountPage.reviews.p"));


        sas.assertThat(myShukranCard.getText().trim()).isEqualTo(configProperties.getProperty("myAccountPage.myShukranCard.section"));
        sas.assertThat(earnAndSpend.getText().trim()).isEqualTo(configProperties.getProperty("myAccountPage.myShukranCard.p"));

        sas.assertThat(myAddresses.getText().trim()).isEqualTo(configProperties.getProperty("myAccountPage.addressBook.section"));
        sas.assertThat(manageYourShippingAndBillingAddresses.getText().trim()).isEqualTo(configProperties.getProperty("myAccountPage.addressBook.p"));
        sas.assertThat(myCredit.getText().trim()).isEqualTo(configProperties.getProperty("myAccountPage.myCredit.section"));
        sas.assertThat(viewYourAvailableCreditBalance.getText().trim()).isEqualTo(configProperties.getProperty("myAccountPage.myCredit.p"));
        sas.assertThat(orderHistory.getText().trim()).isEqualTo(configProperties.getProperty("myAccountPage.orderHistory.section"));
        sas.assertThat(viewAndTrackYourOrders.getText().trim()).isEqualTo(configProperties.getProperty("myAccountPage.orderHistory.p"));
        sas.assertThat(profileSectionImg.isDisplayed());
        sas.assertThat(myListsImg.isDisplayed());
        sas.assertThat(paymentImg.isDisplayed());
        sas.assertThat(reviewsImg.isDisplayed());
        sas.assertThat(myShukranCardImg.isDisplayed());
        sas.assertThat(myAddressesImg.isDisplayed());
        sas.assertThat(myCreditImg.isDisplayed());
        sas.assertThat(orderHistoryImg.isDisplayed());
        sas.assertAll();

    }


    public void verifyUserLandedToMyAccountPage() {
        if(StepContext.mWeb) {
            SoftAssertions sas = new SoftAssertions();
            sas.assertThat(m_purchase.isDisplayed());
            sas.assertThat(m_reviews.isDisplayed());
            sas.assertThat(m_addresses.isDisplayed());
            StepContext.shukranJoinNow=verifyNoelement(m_ShukranJoinNow);

            sas.assertAll();

        }else{
            if(StepContext.lang.equalsIgnoreCase("AR")){
                SoftAssertions sas = new SoftAssertions();
                waitForWebElementPresent(myAccount_purchase, driver, 15);
                waitForWebElementPresent(myAccount_reviews, driver, 15);
                waitForWebElementPresent(myAccount_addresses, driver, 15);

            }else {


                SoftAssertions sas = new SoftAssertions();
                waitForWebElementPresent(myAccount, driver, 15);
                sas.assertThat(myAccount.getText().trim()).isEqualTo(configProperties.getProperty("myAccountPage.title.text"));
                sas.assertThat(manageYourProfileMsg.getText().trim()).isEqualTo(configProperties.getProperty("myAccountPage.title.subTitle"));
                sas.assertAll();
            }
        }
    }







    public void clickOnMyShukranCard() {
        waitForWebElementPresent(myShukranCard,driver,15);
        myShukranCard.click();
    }

    public void navigateBack() {
       driver.navigate().back();
       waitForPageToLoad(50);
    }

    public void clickOnMyAccountToPNavigation() {
        waitForElementToBeClickable(myAccountTopNavigation,driver,20);
        myAccountTopNavigation.click();
    }

    public void purchasesSectionClick() {
        if (StepContext.mWeb) {
            waitForPageToLoad(25);
            waitForLoadJavaScripts(driver,15);
            waitForElementToBeClickable(mPurchase,20);
            waitForVisibilityOfElementLocated(mPurchase,driver,20);
            waitForElementPresent(15);
            javaScriptExecuteClick(mPurchase);
            waitForElementPresent(10);

        } else {
            if(StepContext.lang.equalsIgnoreCase("AR")) {
                waitForPageToLoad(30);
                waitForElementToBeClickable(myAccount_purchase, 30);
                myAccount_purchase.click();
            }else{
                waitForPageToLoad(30);
                waitForElementToBeClickable(purchases, 20);
                purchases.click();
            }
        }
    }

    public void clickOnSavedCardLinkFromMyAccountPage() {
        waitForElementToBeClickable(savedCardLink,driver,15);
        savedCardLink.click();
    }
}






