package com.lmg.blc.web.PageObjects;

import com.landmarkshops.Web_Mweb.config.config;
import com.landmarkshops.Web_Mweb.utilities.*;
import com.lmg.blc.web.stepDefinition.CheckoutStepDefination;
import io.restassured.response.Response;
import org.assertj.core.api.SoftAssertions;
import org.json.JSONArray;
import org.json.JSONObject;
import org.json.simple.parser.ParseException;
import org.openqa.selenium.By;
import org.openqa.selenium.Keys;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.WebDriverWait;
import org.testng.Assert;
import org.testng.asserts.SoftAssert;

import java.io.FileWriter;
import java.io.IOException;
import java.time.Duration;
import java.util.List;

import static com.landmarkshops.Web_Mweb.launcher.TestHarness.configProperties;
import static com.lmg.blc.web.stepDefinition.CheckoutStepDefination.*;

public class CheckoutPage extends WebUtils {
    public CheckoutPage(WebDriver driver) {
        super(driver);
        PageFactory.initElements(driver, this);
    }

    private static String concept = config.getConcept();
//    private static void addGiftCard(JSONObject jsonObject, String giftCardNumber, String pin, String actualGiftCardAmount, String remainingGiftCardAmount) {
//        JSONObject giftCard = new JSONObject();

    @FindBy(css = "#checkout-cnc_QA")
    WebElement clickAndCollectBtn;
    @FindBy(xpath = "//*[@id='checkout-cnc_QA']/span/div/div[1]")
    WebElement clickAndCollectButtonTitleMessage;
    @FindBy(xpath = "//*[@id='checkout-cnc_QA']/span/div/div[2]")
    WebElement clickAndCollectButtonSubTitleMessage;

    @FindBy(xpath = "//*[@id='shippingForm']/div/div[1]/form/div[3]/div/div/form/div[1]/div")
    WebElement clickAndCollectFindAPickupPointTitle;
    @FindBy(xpath = "//*[@id='shippingForm']/div/div[1]/form/div[3]/div/div/form/div[2]")
    WebElement clickAndCollectFindAPickupPointSubTitle;

    @FindBy(xpath = "//*[@id='store-list_QA']")
    WebElement clickAndCollectSearchBox;

    @FindBy(xpath = "//*[@title='Clear' and @aria-label='Clear']")
    WebElement clickAndCollectClearSearchBox;
    //@FindBy(xpath = "//*[contains(@id,'store-list_QA-option')]")
    @FindBy(xpath = "//*[@id='store-list_QA-popup']/li[1]")
    WebElement clickAndCollectStoreList;
    @FindBy(xpath = "//*[@id='store-list_QA-popup']/li/div/div[2]")
    WebElement clickAndCollectConfirmPickupPointButton;
    @FindBy(xpath = "//div[@tabindex='-1']")
    WebElement clickAndCollectConfirmPickupPointMapLocation;
    @FindBy(xpath = "//*[@id='mapSctn']/div[2]/div/div")
    WebElement clickAndCollectConfirmPickupPointMapStoreDetails;

    @FindBy(xpath = "//*[@id='shippingForm']/div/div[1]/form/div[3]/div/div/form/div[5]")
    WebElement clickAndCollectAddYourContactDetailsTitle;
    @FindBy(xpath = "//*[@id='checkout-clickNCollect-name-label_QA']")
    WebElement clickAndCollectFullNameLabel;
    @FindBy(xpath = "//*[@id='fullname']")
    WebElement clickAndCollectFullNameTextBox;
    @FindBy(xpath = "//*[@id='checkout-homedelivery-mobile-label_QA']")
    WebElement clickAndCollectMobileLabel;
    @FindBy(xpath = "//*[@id='mobile_num']")
    WebElement clickAndCollectMobileTextBox;
    @FindBy(xpath = "//*[@id='checkout-homedelivery-email-label_QA']")
    WebElement clickAndCollectEmailLabel;
    @FindBy(xpath = "//*[@id='email']")
    WebElement clickAndCollectEmailTextBox;

    @FindBy(xpath = "//*[@id='checkout-homedelivery-payment-label_QA']")
    WebElement clickAndCollectSelectAPaymentMethodSection;

    @FindBy(xpath = "//*[@id='checkout-order-summary-section_QA']/div[1]/p")
    WebElement clickAndCollectYourOrderSummaryTitle;
    @FindBy(xpath = "//*[@id='checkout-order-summary-section_QA']")
    WebElement clickAndCollectCheckoutOrderSummaryDetails;

    @FindBy(xpath = "//*[@id='shippingForm']/div/div[1]/div[1]/div/div/button[1]/span/div/div[2]")
    WebElement getCollectYourOrderFromMsg;

    @FindBy(xpath = "//*[@id='checkout-homedelivery_QA']/span/div/div[1]")
    WebElement homeDeliveryBtn;

    @FindBy(xpath = "//*[@id='checkout-homedelivery_QA']/span/div/div[1]")
    WebElement homeDeliveryText;

    @FindBy(xpath = "//*[@id='shippingForm']/div/div[1]/form/div[1]/div[2]")
    WebElement changeDeliveryType;

    @FindBy(xpath = "//*[@id='checkout-homedelivery_QA']/span[1]/div/div")
    WebElement mHomeDeliveryText;

    @FindBy(xpath = "//*[@id='shippingForm']/div/div[1]/div[1]/div/div/button[2]/span/div/div[2]")
    WebElement deliveryToHomeMsg;

    @FindBy(xpath = "//*[@id='checkout-cnc_QA']/span/div/div[1]")
    WebElement clickAndCollectText;

    @FindBy(xpath = "//*[@id='checkout-cnc_QA']/span[1]/div/div")
    WebElement mClickAndCollectText;

    @FindBy(xpath = "//*[@id='shippingForm']/div/div[1]/form/div[3]/div/div/form/div[1]/div")
    WebElement findAPickupPointText;

    @FindBy(xpath = "//*[@id='shippingForm']/div/div[1]/form/div[3]/div/div/div[1]/div")
    WebElement mFindAPickupPointText;

    @FindBy(xpath = "//*[@id='shippingForm']/div/div[1]/form/div[3]/div/div/form/div[2]")
    WebElement searchOrUseOurMapViewToFindAPickupPoint;

    @FindBy(xpath = "//*[@id='shippingForm']/div/div[1]/form/div[3]/div/div/div[2]")
    WebElement mSearchOrUseOurMapViewToFindAPickupPoint;

    @FindBy(xpath = "//*[@id='store-list_QA']")
    WebElement searchByMallorCity;

    @FindBy(xpath = "//*[@id='store-list_QA-option-1']/div/div/div[2]/div/p[2]")
    WebElement clickoncityormall;

    @FindBy(xpath = "//*[@id='store-list_QA']")
    WebElement mSearchByMallorCity;

    @FindBy(xpath = "//*[@id='mapSctn']/div[3]/div/div[3]/div")
    WebElement searchByMallorCityIcon;

    @FindBy(xpath = "//*[@id='mapSctn']/div[2]/div/div[2]")
    WebElement mSearchByMallorCityIcon;

    @FindBy(xpath = "//*[@id='store-list_QA-option-0']/div/div/div[2]/div/p[1]")
    WebElement storesDropdwon;

    @FindBy(xpath = "//*[@id='store-list_QA-option-0']/div/div/div[2]/div/p[1]")
    WebElement mStoresDropdwon;

    @FindBy(xpath = "//*[@id='shippingForm']/div/div[1]/form/div[3]/div/div/form/div[5]")
    WebElement addYourContactDetails;

    @FindBy(xpath = "//*[@id='shippingForm']/div/div[1]/form/div[3]/div/div/div[5]")
    WebElement mAddYourContactDetails;

    @FindBy(xpath = "//*[@id='checkout-clickNCollect-name-label_QA']")
    WebElement fullNameText;

    @FindBy(xpath = "//*[@id='checkout-clickNCollect-name-label_QA']")
    WebElement mFullNameText;

    @FindBy(xpath = "//*[@id='fullname']")
    WebElement fullNameTextBox;

    @FindBy(xpath = "//*[@id='shippingForm']/div/div[1]/form/div[3]/div/div/form/div[6]/div[2]/div[2]/div/p")
    WebElement CNCcountryCode;

    @FindBy(xpath = "//*[@id='shippingForm']//div[1]/form/div[4]//div[2]//div/div[2]/div/div[1]/p")
    WebElement HDcountryCode;

    @FindBy(xpath = "//*[@id='fullname']")
    WebElement mFullNameTextBox;

    @FindBy(xpath = "//*[@id='checkout-homedelivery-mobile-label_QA']")
    WebElement mobileText;

    @FindBy(xpath = "//*[@id='checkout-homedelivery-mobile-label_QA']")
    WebElement mMobileText;

    @FindBy(xpath = "//*[@id='mobile_num']")
    WebElement mobileTextBox;

    @FindBy(xpath = "//*[@id='mobile_num']")
    WebElement mMobileTextBox;

    @FindBy(xpath = "//*[@id='checkout-homedelivery-email-label_QA']")
    WebElement emailText;

    @FindBy(xpath = "//*[@id='checkout-homedelivery-email-label_QA']")
    WebElement mEmailText;

    @FindBy(xpath = "//*[@id='email']")
    WebElement emailTextBox;

    @FindBy(xpath = "//*[@id='email']")
    WebElement mEmailTextBox;

    @FindBy(xpath = "//*[@id='shippingForm']/div/div[1]/div[1]/div/div/button[1]/span/div/div[2]")
    WebElement collectYourOrderFromMsg;

    @FindBy(xpath = "//*[@id='checkout-homedelivery-shipping-label_QA']")
    WebElement addYourShippingAddressSection;

    @FindBy(xpath = "//*[@id='checkout-homedelivery-shipping-label_QA']")
    WebElement mAddYourShippingAddressSection;

    @FindBy(xpath = "//*[@id='checkout-homedelivery-name-label_QA']")
    WebElement fullNameLabel;

    @FindBy(id = "checkout-homedelivery-mobile-label_QA")
    WebElement mobileNumberLabel;

    @FindBy(id = "checkout-homedelivery-email-label_QA")
    WebElement emailLabel;

    @FindBy(id = "checkout-homedelivery-emirate-label_QA")
    WebElement emirateLabel;

    @FindBy(xpath = "//*[@id='shippingForm']/div/div[1]/form/div[4]//div[2]/div/div[3]/div[1]/div/div[1]")
    WebElement cityLabel;

    @FindBy(xpath = "//*[@id='shippingForm']/div/div[1]/form/div[4]/div/div/div/div[2]/div/div[1]")
    WebElement mCityLabel;

    @FindBy(xpath = "//*[@id='checkout-homedelivery-area-label_QA']")
    WebElement areaLabel;

    @FindBy(xpath = "//*[@id='checkout-homedelivery-area-label_QA']")
    WebElement mAreaLabel;

    @FindBy(xpath = "//*[@id='checkout-homedelivery-floor-label_QA']")
    WebElement streetNameNum;

    @FindBy(xpath = "//*[@id='checkout-homedelivery-floor-label_QA']")
    WebElement mStreetNameNum;

    @FindBy(id = "checkout-homedelivery-makani-label_QA")
    WebElement makaniLabel;


    @FindBy(id = "checkout-homedelivery-building-label_QA")
    WebElement buildingNameOrVillaNoText;

    @FindBy(xpath = "//*[@id='checkout-homedelivery-building-label_QA']")
    WebElement floorApartmentNumberText;

    @FindBy(xpath = "//*[@id='checkout-homedelivery-building-label_QA']")
    WebElement mFloorApartmentNumberText;

    @FindBy(xpath = "//*[@id='checkout-homedelivery-makani-label_QA']")
    WebElement landmarkText;

    @FindBy(xpath = "//*[@id='checkout-homedelivery-makani-label_QA']")
    WebElement mLandmarkText;

    @FindBy(id = "checkout-homedelivery-makani-label_QA")
    WebElement makaniNoText;

    @FindBy(xpath = "//div[contains(text(),'Help us deliver to your exact location')]")
    WebElement helpUsDeliverToYourExactLocation;

    @FindBy(xpath = "//*[@id='checkout-homedelivery-name-label_QA']")
    WebElement hdFullNameText;

    @FindBy(xpath = "//*[@id='checkout-homedelivery-name-label_QA']")
    WebElement mHdFullNameText;

    @FindBy(xpath = "//*[@id='fullname']")
    WebElement hdFullNameTextBox;

    @FindBy(xpath = "//*[@id='shippingForm']/div/div[1]/form/div[3]/div/div/form/div[6]/div[2]/div[2]/div/p")
    WebElement hdCountryCode;

    @FindBy(xpath = "//*[@id='shippingForm']/div/div[1]/form/div[4]/div/div/div/div[2]/div/div[15]/div/div[1]/p")
    WebElement mHdCountryCode;

    @FindBy(xpath = "//*[@id='shippingForm']/div/div[1]/form/div[4]/div/div/div/div[2]/div/div[1]/div[2]/div/div[1]")
    WebElement hdMobileText;

    @FindBy(xpath = "//*[@id='shippingForm']/div/div[1]/form/div[4]/div/div/div/div[2]/div/div[14]")
    WebElement mHdMobileText;

    @FindBy(xpath = "//*[@id='mobile_num']")
    WebElement hdMobileTextBox;

    @FindBy(xpath = "//*[@id='shippingForm']/div/div[1]/form/div[4]/div/div/div/div[2]/div/div[2]/div/div/div[1]")
    WebElement hdEmailText;

    @FindBy(xpath = "//*[@id='shippingForm']/div/div[1]/form/div[4]/div/div/div/div[2]/div/div[16]")
    WebElement mHdEmailText;

    @FindBy(xpath = "//*[@id='email']")
    WebElement hdEmailTextBox;

    //*[@id="checkout-homedelivery-shipping-label_QA"]
    @FindBy(xpath = "//*[@id='checkout-homedelivery-address-type-label_QA']")
    WebElement addressType;

    @FindBy(xpath = "//*[@id='checkout-homedelivery-address-type-label_QA']")
    WebElement mAddressType;

    @FindBy(xpath = "//*[@id='address-type-home-work_QA']//div[1]/label/span[2]")
    WebElement HomeText;

    @FindBy(xpath = "//*[@id='address-type-home-work_QA']/div/div/label[1]/span[2]")
    WebElement mHomeText;

    // @FindBy(xpath = "//*[@id='address-type-home-work_QA']/div/div/label[1]/span[1]/span[1]/input")
    @FindBy(xpath = "//*[@id='address-type-home-work_QA']//div[1]/label/span[1]/span/input")
    WebElement homeRadioButton;

    @FindBy(xpath = "//*[@id='address-type-home-work_QA']/div/div/label[1]/span[1]/span[1]/input")
    WebElement mHomeRadioButton;

    @FindBy(xpath = "//*[@id='address-type-home-work_QA']//div[2]/label/span[2]")
    WebElement officeText;

    @FindBy(xpath = "//*[@id='address-type-home-work_QA']/div/div/label[2]/span[2]")
    WebElement mOfficeText;

    @FindBy(xpath = "//*[@id='address-type-home-work_QA']//div[2]/label/span[1]/span/input")
    WebElement officeRadioButton;

    @FindBy(xpath = "//*[@id='address-type-home-work_QA']/div/div/label[2]/span[1]/span[1]/input")
    WebElement mOfficeRadioButton;

    @FindBy(xpath = "//*[@id='address-type-work_QA']/span[1]/span/input")
    WebElement optionValue;


    @FindBy(xpath = "//*[@id='address-checkbox-label']/span[2]")
    WebElement defaultAddressText;

    @FindBy(xpath = "//div[@id='checkout-homedelivery-payment-label_QA']")
    WebElement selectAPaymentMethodSection;

    @FindBy(xpath = "//*[@id='shippingForm']/div/div[1]/form/div[6]/div/div/div[2]/label/span[1]/span/input")
    WebElement defaultRadioButton;

    @FindBy(xpath = "//*[@id='shippingForm']/div/div[1]/form/div[*]/div/div/div[2]/label/span[1]/span[1]/input")
    WebElement mDefaultRadioButton;

    @FindBy(xpath = "//*[@id='shippingForm']/div/div[1]/form/div[6]/div/div/div[2]/label/span[2]/img")
    WebElement creditCardImg;

    @FindBy(xpath = "//*[@id='shippingForm']/div/div[1]/form/div[*]/div/div/div[2]/label/span[2]/img")
    WebElement mCreditCardImg;

    @FindBy(xpath = "//*[@id='checkout-homedelivery-payment-cardDetails_QA']/*[@id='checkout-homedelivery-payment-creditdebit-label_QA']")
    WebElement creditOrDebitCardLabel;

    @FindBy(xpath = "//*[@id='checkout-homedelivery-payment-cardDetails_QA']/*[@id='checkout-homedelivery-payment-mastervisa-label_QA']")
    WebElement payUsingMasterOrVisaCardMsg;

    @FindBy(xpath = "//*[@id='checkout-homedelivery-payment-number-label_QA']")
    WebElement cardNumber;

    @FindBy(xpath = "//*[@id='checkout-homedelivery-payment-name-label_QA']")
    WebElement nameOnCardText;

    @FindBy(xpath = "//*[@id='checkout-homedelivery-payment-expirydate-label_QA']")
    WebElement expiryDateText;

    @FindBy(xpath = "//*[@id='shippingForm']/div/div[1]/div[2]/div/div/form/div[3]/div[2]/div[2]/div[6]/div[1]/div/div/div[2]")
    WebElement dd;

    @FindBy(xpath = "//*[@id='shippingForm']/div/div[1]/div[2]/div/div/form/div[3]/div[2]/div[2]/div[6]/div[2]/div/div/div[2]")
    WebElement mm;

    @FindBy(xpath = "//*[@id='checkout-homedelivery-payment-cvv-label_QA']")
    WebElement cvvNumberText;

    @FindBy(xpath = "//*[@id='shippingForm']/div/div[1]/div[2]/div/div/form/div[3]/div[3]/img")
    WebElement promoImage;

    @FindBy(xpath = "//*[@id='shippingForm']/div/div[1]/form/div[7]/div/div/div[1]")
    WebElement promoTextMessage;

    @FindBy(xpath = "//*[@id='checkout-order-summary-section_QA']/div/div[3]/div/div[1]")
    WebElement mPromoTextMessage;

    @FindBy(id = "cart-promo-click_QA")
    WebElement useItHere;

    @FindBy(xpath = "//*[@id='checkout-homedelivery-payment-cashondelivery-label_QA']")
    WebElement cashOnDeliveryText;

    @FindBy(xpath = "//*[@id='checkout-homedelivery-payment-cashondeliverycharges-label_QA']")
    WebElement processingFeeOfEGP20applicableText;

    @FindBy(xpath = "//img[@src='https://media.maxfashion.com/i/max/cod']")
    WebElement cashOnDeliveryImg;

    @FindBy(xpath = "(//input[@name='payment'])[2]")
    WebElement cashOnDeliveryRadioButton;

    @FindBy(xpath = "//*[@id='shippingForm']/div/div[1]/form/div[6]/button/span[1]")
    WebElement mProceedToPayment;

    @FindBy(xpath = "//*[@id='shippingForm']/div/div[1]/form/div[2]/div/div/div[*]/div[3]/div/label/span[1]/span[1]/input")
    WebElement mCashOnDeliveryRadioButton;

    @FindBy(xpath = "//*[@id='checkout-paynow_QA']/span")
    WebElement payNow;

    @FindBy(id = "store-available-close")
    WebElement ratingCloseIcon;

    @FindBy(xpath = "//*[@id='main-part']/div/div[1]/div[1]/div[1]/div[2]/div[3]/span")
    WebElement orderConfirmationNumber;

    @FindBy(xpath = "//*[@id='shippingForm']/div/div[1]/form/div[6]/button/span[1]")
    WebElement proceedToPayment;

    @FindBy(xpath = "//*[@id='shippingForm']/div/div[1]/form/div[6]/div[1]")
    WebElement mCheckoutAgree;

    @FindBy(xpath = "//*[@id='shippingForm']/div/div[1]/form/div[8]/div[1]/div[1]/a/span")
    WebElement termsAndCondition;

    @FindBy(xpath = "//*[@id='shippingForm']/div/div[1]/form/div[6]/div[1]/a")
    WebElement mTermsAndCondition;

    @FindBy(xpath = "//*[@id='shippingForm']/div/div[1]/form/div[*]/div/div[2]/a/span")
    WebElement mTermsAndCondition1;

    @FindBy(xpath = "//*[@id='shippingForm']/div/div[1]/form/div[6]/div[2]/div[1]")
    WebElement mSubtotal;

    @FindBy(xpath = "//*[@id='shippingForm']/div/div[1]/form/div[6]/div[2]/div[2]")
    WebElement mSubtotalValue;

    @FindBy(id = "fullname")
    WebElement fullnameInput;

    @FindBy(id = "mobile_num")
    WebElement mobileInput;

    @FindBy(id = "email")
    WebElement emailInput;

    @FindBy(xpath = "//*[@id='emirate'][1]")
    WebElement chooseCity;

    @FindBy(xpath = "//*[@id='menu-emirate']/div[3]/ul/li")
    private List<WebElement> cityNames;

    @FindBy(xpath = "//*[@id='menu-emirate']/div[3]/ul/li[2]")
    WebElement emirateListItem;

    @FindBy(xpath = "//*[@id='area'][1]")
    WebElement areaInput;

    @FindBy(xpath = "//*[@id='menu-area']/div[3]/ul/li[2]")
    WebElement areaListItem;

    @FindBy(xpath = "//*[@id='menu-area']/div[3]/ul")
    private List<WebElement> areaNames;

    @FindBy(xpath = "//*[@id='main-checkout-form-fields']/div[2]/div[2]/div/span/div/div/span/span/ul/li[3]/span")
    WebElement areaDubaiAirPortListItem;


    @FindBy(xpath = "//*[@id='menu-emirate']/div[3]/ul/li[4]")
    WebElement emirateDubaiListItem;


    @FindBy(id = "checkout-homedelivery-building-label_QA")
    WebElement buildingLabel;

    @FindBy(id = "building")
    WebElement buildingInput;

    @FindBy(id = "floor")
    WebElement floorInput;

    @FindBy(id = "landmark")
    WebElement landmarkOptionalInput;

    @FindBy(id = "building")
    WebElement streentNameOrNum;

    @FindBy(id = "cardnum")
    WebElement cardnumInput;


    @FindBy(id = "namecard")
    WebElement namecardInput;

    //@FindBy(xpath = "//*[@id='expirymonth']")
    @FindBy(xpath = "//*[@id='expMonthYear']")
    WebElement expiryMonth;

    @FindBy(xpath = "//*[@id='menu-expirymonth']/div[3]/ul")
    private List<WebElement> expiryMonthValues;

    @FindBy(xpath = "//*[@id='menu-expirymonth']/div[3]/ul/li[4]")
    WebElement selectExpiryMonthValue;

    @FindBy(xpath = "//*[@class='MuiButtonBase-root MuiListItem-root MuiMenuItem-root MuiMenuItem-gutters MuiListItem-gutters MuiListItem-button']")
    WebElement selectExpiryMonthText;

    @FindBy(id = "expiryyear")
    WebElement expiryYear;

    @FindBy(xpath = "//*[@id='menu-expiryyear']/div[3]/ul")
    private List<WebElement> expiryYearValues;


    @FindBy(xpath = "//*[@id='menu-expiryyear']/div[3]/ul/li[4]")
    WebElement selectExpiryYearValue;

    @FindBy(id = "cvv")
    WebElement cvvInput;

    @FindBy(id = "cvv_0")
    WebElement cvvInput1;

    @FindBy(xpath = "//*[@id='selectedCardItem']/div/div[3]/div/div[1]/div")
    WebElement cvvInputDynamic;


    //    @FindBy(xpath = "//*[@id='shippingForm']/div/div[1]/form/div[6]/div/div/div[3]/div[2]/div[2]/div/div/div[2]/div[1]")
//    List<WebElement> savedCardNumber;
    // @FindBy(xpath = "//*[@id='shippingForm']/div/div[1]/form/div[6]/div/div/div[3]/div[2]/div[2]/div/div/div[2]/div[1]")
    @FindBy(xpath = "//*[@id='checkout-payments-savedcards_QA']/div/div/div[2]/div[1]")
    List<WebElement> savedCardNumber;

    @FindBy(xpath = "shippingSection_QA")
    WebElement selectYourShippingSpeedSection;

    @FindBy(id = "checkout-homedelivery-shippingspeed-label_QA")
    WebElement selectYourShippingSpeedText;

    @FindBy(xpath = "//*[@id='shippingSection_QA']/label[1]/span[2]/div/div[1]/span[1]")
    WebElement standardText;

    @FindBy(xpath = "//*[@name='Standard']")
    WebElement radioStandard;

    @FindBy(xpath = "//*[@id='shippingSection_QA']/label[1]/span[2]/div/div[1]/span[2]")
    WebElement standard_shippingChargeLabel;

    @FindBy(xpath = "//*[@id='shippingSection_QA']/label[1]/span[2]/div/div[2]")
    WebElement standard_delivery_msg;

    @FindBy(xpath = "//*[@id='shippingSection_QA']/label[2]/span[2]/div/div[1]/span[1]")
    WebElement expressText;

    @FindBy(xpath = "//*[@name='Express']")
    WebElement radioExpress;

    @FindBy(xpath = "//*[@id='shippingSection_QA']/label[2]/span[2]/div/div[1]/span[2]")
    WebElement express_shippingChargeLabel;

    @FindBy(xpath = "//*[@id='shippingSection_QA']/label[2]/span[2]/div/div[2]")
    WebElement express_delivery_msg;

    @FindBy(xpath = "//*[@id='shippingSection_QA']/label[3]/span[2]/div/div[1]/span[1]")
    WebElement superExpressText;

    @FindBy(xpath = "//*[@name='Super Express']")
    WebElement radioSuperExpress;

    @FindBy(xpath = "//*[@id='shippingSection_QA']/label[3]/span[2]/div/div[1]/span[2]")
    WebElement superExpress_shippingChargeLabel;

    @FindBy(xpath = "//*[@id='shippingSection_QA']/label[3]/span[2]/div/div[2]")
    WebElement superExpress_delivery_msg;

    @FindBy(xpath = "//*[@id='checkout-order-summary-section_QA']/p")
    WebElement orderSummaryText;

    @FindBy(xpath = "//*[@id='checkout-order-summary-section_QA']/div[1]/div[1]/img")
    WebElement orderSummeryProductImg;

    @FindBy(xpath = "//*[@id='checkout-order-summary-section_QA']/div[1]/div[2]/p[1]")
    WebElement orderSummeryProductNumber;

    @FindBy(xpath = "//*[@id='checkout-order-summary-section_QA']/div[1]/div[2]/p[2]")
    WebElement orderSummeryProductDescription;

    @FindBy(xpath = "//*[@id='checkout-order-summary-section_QA']/div[1]/div[2]/div/div[1]")
    WebElement orderSummeryProductQty;

    @FindBy(xpath = "//*[@id='checkout-order-summary-section_QA']/div[1]/div[3]/div[1]/p[1]")
    WebElement shippingOptionType_Title;

    @FindBy(xpath = "//*[@id='defaultaddress']")
    WebElement defaultAddressCheckBox;

    @FindBy(xpath = "//*[@id='address-checkbox-label']/span[2]")
    WebElement defaultAddressMsg;


    @FindBy(xpath = "//*[@id='address-checkbox']/div")
    WebElement defaultAddressMsg1;


    @FindBy(xpath = "//div[2][contains(text(),'Add a new Address')]")
    WebElement addANewAddress;

    @FindBy(xpath = "//*[@id='checkout-paynow_QA']/span/span[contains(text(),'Ship to this Address')]")
    WebElement shipToThisAddress;

    @FindBy(xpath = " //div[contains(text(),'show all my addresses')]")
    WebElement showAllMyAddresses;

    @FindBy(xpath = "//label[1]/span[2]/div/div[1]/div")
    WebElement fullNameFirstLabel;


    @FindBy(xpath = "//input[starts-with(@name,'shippingDetails')]")
    List<WebElement> listOfShippingAddress;


    @FindBy(xpath = "//*[@id='shippingForm']/div/div[1]/div[3]/div/div/form/div[3]/div[2]/div[2]")
    WebElement cardFromList;

    @FindBy(xpath = "//*[@id='cvv_0']")
    WebElement cvvNumber;

    @FindBy(id = "checkout-homedelivery-payment-cvv-label_QA")
    WebElement cvvText;

    @FindBy(xpath = "//*[@id='shippingForm']/div/div[1]/div[3]/div/div/form/div[3]/div[2]/div[2]/div[2]/div[1]")
    WebElement ending;

    @FindBy(xpath = "//*[@id='shippingForm']/div/div[1]/div[3]/div/div/form/div[3]/div[2]/div[2]/div[2]/div[2]")
    WebElement nameOnCard;

    @FindBy(xpath = "//*[@id='shippingForm']/div/div[1]/div[3]/div/div/form/div[3]/div[2]/div[2]/div[2]/div[3]")
    WebElement expiryDateOnCard;

    @FindBy(xpath = "//*[@id='shippingForm']/div/div[1]/div[3]/div/div/form/div[3]/div[2]/div[2]/div[1]")
    WebElement cardImage;

    @FindBy(xpath = "//*[@class='icon-plus1']")
    WebElement addANewCardLink;


    @FindBy(xpath = "//*[@id='defaultsavecard']")
    WebElement saveThisCardCheckBox;


    @FindBy(xpath = "//*[@id='save-new-card_QA']")
    WebElement saveThisCard;

    @FindBy(id = "newpayment-close")
    WebElement newPaymentCloseIcon;

    @FindBy(id = "checkout-homedelivery-payment-label_QA")
    WebElement nc_selectAPaymentMethod;

    @FindBy(id = "checkout-homedelivery-payment-number-label_QA")
    WebElement nc_cardNumberLabel;

    @FindBy(id = "checkout-homedelivery-payment-name-label_QA")
    WebElement nc_nameLabel;

    @FindBy(id = "checkout-homedelivery-payment-expirydate-label_QA")
    WebElement nc_expiryDateLabel;


    @FindBy(xpath = "//form/div[1]/div[2]/div/div[2]/div[contains(text(),'Please enter your full name')]")
    WebElement pleaseEnterFullName;

    @FindBy(xpath = "//form/div[1]/div[2]/div/div[2]/div[contains(text(),'Please enter your full name using alphabet characters only')]")
    WebElement pleaseEnterFullNameUsingAlpha;

    @FindBy(xpath = "//div[2][contains(text(),'Enter your email address')]")
    WebElement enterYourEmailAddress;

    @FindBy(xpath = "//div[2][contains(text(),'Please enter a valid email address')]")
    WebElement pleaseEnterAValidEmailAddress;


    @FindBy(xpath = "//div[2][contains(text(),'Please enter mobile number')]")
    WebElement pleaseEnterMobileNumber;

    @FindBy(xpath = "//div[2][contains(text(),'Please enter a valid 9-digit mobile number.')]")
    WebElement valid9digitMobileNumber;

    @FindBy(xpath = "//div[2][contains(text(),'Select your Emirate')]")
    WebElement selectYourEmirate;

    @FindBy(xpath = "//div[2][contains(text(),'Select your area')]")
    WebElement selectYourArea;

    @FindBy(xpath = "//div[2][contains(text(),'Please enter Building Name')]")
    WebElement pleaseEnterBuildingName;

    @FindBy(xpath = "//div[2][contains(text(),'This field needs to have at least 5 characters')]")
    WebElement buildingValidation2;

    @FindBy(xpath = "//div[2][contains(text(),'Please enter a 10-digit number without spaces')]")
    WebElement makaniValidation;

    @FindBy(xpath = "//div[2][contains(text(),'Please enter a true digit number')]")
    WebElement pleaseEnterTrueDigitNumber;


    @FindBy(xpath = "//div[2][contains(text(),'Please enter a valid card number')]")
    WebElement pleaseEnterAValidCardNumber;


    @FindBy(xpath = "//div[4]/div[2][contains(text(),'Please enter your full name')]")
    WebElement pleaseEnterCardFullNameValidation;

    @FindBy(xpath = "//div[4]/div[2][contains(text(),'Please enter your full name using alphabet characters only')]")
    WebElement pleaseEnterCardValidation;


    @FindBy(xpath = "//div[2][contains(text(),'Please select a valid month')]")
    WebElement pleaseSelectAValidMonth;

    @FindBy(xpath = "//*[@id='shippingForm']/div/div[1]/div[3]/div/div/form/div[3]/div[2]/div[2]/div[6]/div[2]/div[2][contains(text(),'Please select a valid ')]")
    WebElement pleaseSelectAValidYear;

    @FindBy(xpath = "//div[2][contains(text(),'Enter your CVV')]")
    WebElement enterYourCVV;

    @FindBy(xpath = "//*[@id='checkout-order-summary-section_QA']/div[1]/div[1]/div/div/div/div[2]/p")
    WebElement checkoutProductDescription;

    @FindBy(xpath = "//*[@id='checkout-order-summary-section_QA']/div[1]/p")
    WebElement yourOrderSummaryTitle;

    @FindBy(xpath = "//*[@id='checkout-order-summary-section_QA']/div[1]/div[1]/div/div/div/div[1]/img")
    WebElement orderSummaryImg;

    @FindBy(xpath = "//*[@id='checkout-order-summary-section_QA']/div[1]/div[1]/div/div/div/div[2]/p[1]")
    WebElement maxText;

    @FindBy(xpath = "//*[@id='checkout-order-summary-section_QA']/div[1]/div[1]/div/div/div/div[2]/div/div[1]")
    WebElement orderSummaryQtySection;

    @FindBy(xpath = "//*[@id='shopping-cart-item-group-option-02']")
    WebElement orderSummaryPriceSection;

    @FindBy(xpath = "//*[@id='checkout-order-summary-section_QA']/div[1]/div[1]/div/div/div/div[2]/div/div[2]/span")
    WebElement orderSummaryPriceLabel;

    @FindBy(xpath = "//*[@id='checkout-order-summary-section_QA']/div[1]/div[1]/div/div/div/div[2]/div/div[2]")
    WebElement orderSummaryPriceLabelValue;

    @FindBy(xpath = "//*[@id='shopping-actions-total-item']")
    WebElement orderSummarySubTotalSection;

    @FindBy(xpath = "//*[@id='checkout-order-summary-section_QA']/div[1]/div[2]/div[1]")
    WebElement orderSummarySubTotalLabel;

    @FindBy(xpath = "//*[@id='checkout-order-summary-section_QA']/div/div[4]/div[1]")
    WebElement mOrderSummarySubTotalLabel;

    @FindBy(xpath = "//*[@id='checkout-order-summary-section_QA']/div[1]/div[2]/div[2]")
    WebElement orderSummarySubTotalLabelValue;

    @FindBy(xpath = "//*[@id='checkout-order-summary-section_QA']/div/div[4]/div[2]")
    WebElement mOrderSummarySubTotalLabelValue;

    @FindBy(xpath = "//*[@class=' standardInfo ']")
    WebElement orderSummaryStandardShippingInfoSection;

    @FindBy(xpath = "//*[@id='checkout-order-summary-section_QA']/div[1]/div[3]/div[1]/p[1]")
    WebElement orderSummaryStandardShippingInfoLabel;

    @FindBy(xpath = "//*[@id='shippingForm']/div/div[1]/form/div[6]/div[*]/div[1]/div[1]/div")
    WebElement mOrderSummaryStandardShippingInfoLabelValue;

    @FindBy(xpath = "(//*[@id='checkout-order-summary-section_QA']/div/div[*]/div[1]/p[1])[1]")
    WebElement mOrderSummaryStandardShippingInfoLabelValue1;

    @FindBy(xpath = "//*[@id='checkout-order-summary-section_QA']/div[1]/div[3]/div[2]")
    WebElement orderSummaryStandardShippingInfoLabelValue;

    @FindBy(xpath = "(//*[@id='checkout-order-summary-section_QA']/div/div[5]/div[2])[1]")
    WebElement mOrderSummaryStandardShippingInfoLabelValue2;

    @FindBy(xpath = "//*[@id='checkout-order-summary-section_QA']/div[1]/div[3]/div[1]/p[1]/div")
    WebElement orderSummaryDeliveryEstimateValue;

    @FindBy(xpath = "//*[@id='shippingForm']/div/div[1]/form/div[6]/div[*]/div/div[1]/div/div")
    WebElement mOrderSummaryDeliveryEstimateValue;

    @FindBy(xpath = "(//*[@id='checkout-order-summary-section_QA']/div/div[*]/div[1]/p[1]/div)[1]")
    WebElement mOrderSummaryDeliveryEstimateValue1;

    @FindBy(xpath = "//*[@id='checkout-order-summary-section_QA']/div[1]/div[3]/div[1]/p[2]")
    WebElement orderSummaryDeliveryEstimateTimeValue;

    @FindBy(xpath = "//*[@id='shippingForm']/div/div[1]/form/div[6]/div[*]/div/div[1]/div/div")
    WebElement mOrderSummaryDeliveryEstimateTimeValue;

    @FindBy(xpath = "(//*[@id='checkout-order-summary-section_QA']/div/div[*]/div[1]/p[2])[1]")
    WebElement mOrderSummaryDeliveryEstimateTimeValue1;


    @FindBy(xpath = "//*[@id='shippingForm']/div/div[1]/form/div[6]/div[*]/div/div[1]/p")
    WebElement mStandardGroundShippingValue;

    @FindBy(xpath = "//*[@class='total']")
    WebElement orderSummaryTotalSection;

    @FindBy(xpath = "//*[@id='checkout-order-summary-section_QA']/div[1]/div[6]/div[1]/p[1]")
    WebElement orderSummaryTotalLabel;

    @FindBy(xpath = "(//*[@id='checkout-order-summary-section_QA']/div/div[*]/div[1]/p[1])[2]")
    WebElement mOrderSummaryTotalLabel;

    @FindBy(xpath = "//*[@id='checkout-order-summary-section_QA']/div[1]/div[*]/div[1]/p[1]")
    WebElement cncorderSummaryTotalLabel;

    @FindBy(xpath = "//*[@id='checkout-order-summary-section_QA']/div/div[*]/div[1]/p[1]")
    WebElement mCncorderSummaryTotalLabel;

    @FindBy(xpath = "//*[@id='checkout-order-summary-section_QA']/div[1]/div[6]/div[2]")
    WebElement orderSummaryTotalLabelValue;

    @FindBy(xpath = "(//*[@id='checkout-order-summary-section_QA']/div/div[*]/div[2])[5]")
    WebElement mOrderSummaryTotalLabelValue;

    @FindBy(xpath = "//*[@id='checkout-order-summary-section_QA']/div[1]/div[*]/div[2]")
    WebElement cncorderSummaryTotalLabelValue;

    @FindBy(xpath = "//*[@id='checkout-order-summary-section_QA']/div/div[5]/div")
    WebElement mCncorderSummaryTotalLabelValue;

    @FindBy(xpath = "//*[@id='checkout-order-summary-section_QA']/div[1]/div[6]/div[1]/p[2]")
    WebElement orderSummaryApplicableVATValueText;

    @FindBy(xpath = "(//*[@id='checkout-order-summary-section_QA']/div/div[*]/div[1]/p[2])[2]")
    WebElement mOrderSummaryApplicableVATValueText;

    @FindBy(xpath = "//*[@id='checkout-order-summary-section_QA']/div[1]/div[*]/div[1]/p[2]")
    WebElement cncorderSummaryApplicableVATValueText;

    @FindBy(xpath = "//*[@id='checkout-order-summary-section_QA']/div/div[*]/div[1]/p[2]")
    WebElement mCncorderSummaryApplicableVATValueText;

    @FindBy(xpath = "//*[@id='checkout-order-summary-section_QA']/div[2]/div[1]")
    private WebElement waysYouCanPayText;

    @FindBy(xpath = "//*[@id='checkout-order-summary-section_QA']/div/div[2]/div[1]")
    private WebElement mWaysYouCanPayText;

    @FindBy(xpath = "//*[@id='checkout-order-summary-section_QA']/div[2]/div[2]/div[2]/img")
    private WebElement masterCardIcon;

    @FindBy(xpath = "//*[@id='checkout-order-summary-section_QA']/div/div[2]/div[2]/div[2]/img")
    private WebElement mMasterCardIcon;

    @FindBy(xpath = "//*[@id='checkout-order-summary-section_QA']/div[2]/div[2]/div[1]/img")
    private WebElement visCardIcon;

    @FindBy(xpath = "//*[@id='checkout-order-summary-section_QA']/div/div[2]/div[2]/div[1]/img")
    private WebElement mVisCardIcon;

    @FindBy(xpath = "//*[@id='checkout-order-summary-section_QA']/div[2]/div[2]/div[3]/img")
    private WebElement cashOnDeliveryIcon;

    @FindBy(xpath = "//*[@id='checkout-order-summary-section_QA']/div/div[2]/div[2]/div[3]/img")
    private WebElement mCashOnDeliveryIcon;

    @FindBy(xpath = "//*[@id='checkout-order-summary-section_QA']/div[2]/div[2]/div[4]/img")
    private WebElement shukranIcon;

    @FindBy(xpath = "//*[@id='checkout-order-summary-section_QA']/div/div[2]/div[2]/div[4]/img")
    private WebElement mShukranIcon;

    @FindBy(xpath = "//*[@id='shippingForm']/div/div[1]/form/div[4]/div/div/div/div[2]/div[1]/label[1]/span[2]/div/div[1]/div")
    private WebElement defaultAddressName;

    @FindBy(xpath = "//*[@id='shippingForm']/div/div[1]/form/div[3]/div/div/form/div[4]/div/div[2]")
    private WebElement pleaseSelectAPickupPointErrorMsg;

    @FindBy(xpath = "//*[@id='shippingForm']/div/div[1]/form/div[3]/div/div/form/div[6]/div[1]/div[2]/div[2]")
    private WebElement pleaseEnterYourFullNameErrorMsg;

    @FindBy(xpath = "//*[@id='shippingForm']/div/div[1]/form/div[3]/div/div/form/div[6]/div[2]/div[2]/div[2]")
    private WebElement pleaseEnterMobileNumberErrorMsg;

    @FindBy(xpath = "//*[@id='shippingForm']/div/div[1]/form/div[3]/div/div/form/div[6]/div[3]/div[2]/div[2]")
    private WebElement enterYourEmailAddressErrorMsg;

    @FindBy(xpath = "//*[@id='shippingForm']//div[1]/form/div[4]//div[2]/div/div[1]/div[1]/div/div[2]/div/div[2]/em")
    private WebElement HDPleaseEnterYourFullNameErrorMsg;

    @FindBy(xpath = "//*[@id='shippingForm']//div[1]/form/div[4]//div[2]/div/div[1]/div[2]/div/div[2]/div/div[2]/em")
    private WebElement HDPleaseEnterMobileNumberErrorMsg;

    @FindBy(xpath = "//*[@id='shippingForm']//div[1]/form/div[4]//div[2]/div/div[2]/div/div/div[2]/div/div[2]/em")
    private WebElement HDEnterYourEmailAddressErrorMsg;

    @FindBy(xpath = "//*[@id='shippingForm']/div/div[1]/form/div[6]//div[2]/div[2]/div/div[1]/div[2]/em")
    private WebElement pleaseEnterATrueDigitNumberErrorMsg;


    @FindBy(xpath = "//*[@id='shippingForm']//div[1]/form/div[6]//div[2]/div[2]/div/div[2]/div[1]/div[2]/em")
    private WebElement pleaseSelectAValidExpiryDateErrorMsg;

    @FindBy(xpath = "//*[@id='shippingForm']/div/div[1]/form/div[6]/div/div/div[2]/div[2]/div/div[3]/div[1]/em")
    private WebElement pleaseSelectAValidMonthErrorMsg;

    @FindBy(xpath = "//*[@id='shippingForm']/div/div[1]/form/div[6]/div/div/div[2]/div[2]/div/div[3]/div[2]/em")
    private WebElement pleaseSelectAValidYearErrorMsg;

    @FindBy(xpath = "//*[@id='shippingForm']/div/div[1]/form/div[6]//div[2]/div[2]//div[2]/div[2]/div[2]/em")
    private WebElement enterYourCVVErrorMsg;

    @FindBy(xpath = "//*[@id='shippingForm']/div/div[1]/form/div[6]//div[2]/div[2]/div/div[4]/em")
    private WebElement pleaseEnterYourFullNameCardErrorMsg;


    @FindBy(xpath = "//*[@id='shippingForm']//div[1]/form/div[4]//div[2]/div/div[3]/div[1]/div/div[2]/div[2]/em")
    private WebElement selectYourCityErrorMsg;

    @FindBy(xpath = "//*[@id='shippingForm']//div[1]/form/div[4]//div[2]/div/div[3]/div[2]/div/div[2]/div[2]/em")
    private WebElement selectYourAreaErrorMsg;

    @FindBy(xpath = "//*[@id='shippingForm']//div[1]/form/div[4]//div[2]/div/div[4]/div[1]/div/div[2]/div/div[2]/em")
    private WebElement pleaseEnterBuildingNameErrorMsg;

    @FindBy(xpath = "//*[@id='shippingForm']//div[1]/form/div[4]//div[2]/div/div[4]/div[2]/div/div[2]/div/div[2]/em")
    private WebElement pleaseEnterYourAddressErrorMsg;

    /**
     * Verify user landed to check out page
     */

    public void verifyUserLandedToCheckOutPage() {
        String URL = driver.getCurrentUrl();
        SoftAssertions sa = new SoftAssertions();
        waitForPageToLoad(25);
//                    if (config.getLanguage().equalsIgnoreCase("ar") ) {
//                        sa.assertThat(URL).isEqualTo(ProductURLConfigBean.getChekcoutPageURLAR());
//                    }else {
//                        sa.assertThat(URL).isEqualTo(ProductURLConfigBean.getChekcoutPageURL());
//                    }
        sa.assertAll();
    }

    /**
     * This method is used to validate same product or not in PDP page and checkout page
     */

    public void verifyProductInCheckoutPage() {
        //waitForPageToLoad(100);
        SoftAssertions sa = new SoftAssertions();
        if (config.getBrowser().trim().equalsIgnoreCase("CHROME") || config.getBrowser().trim().equalsIgnoreCase("CHROMEHEADLESS") || config.getBrowser().trim().equalsIgnoreCase("MOBILECHROME")) {
            //implicitWait(25);
            waitForElementPresent(20);
            //waitForPresenceOfElementLocated(checkoutProductDescription, driver, 30);
            //Need to Verify
            //String checkoutProductDescriptionText = checkoutProductDescription.getText().trim();
            testLogger.info("product description" + TestDataBean.getProductTitle());
//                sa.assertThat(TestDataBean.getProductTitle()).isEqualTo(checkoutProductDescriptionText);
        }
        sa.assertAll();
    }

    /**
     * This method is used to verify all UI elements are displayed or not
     */

    public void deliveryTypeMethod(String deliveryType) {
        waitForPageToLoad(100);
        testLogger.info("User Chosen Delivery Type As " + deliveryType);
        SoftAssertions sa = new SoftAssertions();
        if (deliveryType.equals("click & Collect")) {
            if (config.getBrowser().trim().equalsIgnoreCase("CHROME") || config.getBrowser().trim().equalsIgnoreCase("CHROMEHEADLESS") || config.getBrowser().trim().equalsIgnoreCase("MOBILECHROME")) {
                waitForElementPresent(5);
                clickAndCollectText.click();
                sa.assertThat(clickAndCollectText.getText().trim()).isEqualTo(configProperties.getProperty("lmgcheckout.shippingpage.clickcollect.title"));
                testLogger.info("clickAndCollectText " + clickAndCollectText.getText().trim());
                sa.assertThat(findAPickupPointText.getText().trim()).isEqualTo(configProperties.getProperty("lmgcheckout.shippingpage.clickcollect.pickuppoint.text"));
                testLogger.info("findAPickupPointText " + findAPickupPointText.getText().trim());
                sa.assertThat(searchOrUseOurMapViewToFindAPickupPoint.getText().trim()).isEqualTo(configProperties.getProperty("lmgcheckout.shippingpage.clickcollect.pickuppoint.subtext"));
                testLogger.info("searchOrUseOurMapViewToFindAPickupPoint " + searchOrUseOurMapViewToFindAPickupPoint.getText().trim());
                sa.assertThat(searchByMallorCity.isDisplayed());
                testLogger.info("searchByMallorCity " + searchByMallorCity.isDisplayed());
                sa.assertThat(searchByMallorCityIcon.isDisplayed());
                testLogger.info("searchByMallorCityIcon " + searchByMallorCityIcon.isDisplayed());
                sa.assertThat(storesDropdwon.isDisplayed());
                testLogger.info("storesDropdwon " + storesDropdwon.isDisplayed());
                sa.assertThat(addYourContactDetails.getText().trim()).isEqualTo(configProperties.getProperty("lmgcheckout.shippingpage.aacontactdetails.label"));
                testLogger.info("addYourContactDetails " + addYourContactDetails.getText().trim());
                sa.assertThat(fullNameText.getText().trim()).isEqualTo(configProperties.getProperty("lmgaddaddress.label.fullname"));
                testLogger.info("fullNameText " + fullNameText.getText().trim());
                sa.assertThat(fullNameTextBox.isDisplayed());
                testLogger.info("fullNameTextBox " + fullNameTextBox.isDisplayed());
                sa.assertThat(CNCcountryCode.getText().trim()).isEqualTo(AddressDataBean.getCountryCode());
                testLogger.info("countryCode " + CNCcountryCode.getText().trim());
                sa.assertThat(mobileText.getText().trim()).isEqualTo(configProperties.getProperty("lmgaddaddress.label.mobile"));
                testLogger.info("mobileText " + mobileText.getText().trim());
                sa.assertThat(mobileTextBox.isDisplayed());
                testLogger.info("mobileTextBox " + mobileTextBox.isDisplayed());
                sa.assertThat(emailText.getText().trim()).isEqualTo(configProperties.getProperty("lmgaddaddress.label.email"));
                testLogger.info("emailText " + emailText.getText().trim());
                sa.assertThat(emailTextBox.isDisplayed());
                testLogger.info("emailTextBox " + emailTextBox.isDisplayed());
                sa.assertAll();
            } else {
                waitForElementPresent(2);
                clickAndCollectText.click();
                sa.assertThat(mClickAndCollectText.getText().trim()).isEqualTo(configProperties.getProperty("lmgcheckout.shippingpage.clickcollect.title"));
                sa.assertThat(mFindAPickupPointText.getText().trim()).isEqualTo(configProperties.getProperty("lmgcheckout.shippingpage.clickcollect.pickuppoint.text"));
                sa.assertThat(mSearchOrUseOurMapViewToFindAPickupPoint.getText().trim()).isEqualTo(configProperties.getProperty("lmgcheckout.shippingpage.clickcollect.pickuppoint.subtext"));
                sa.assertThat(mSearchByMallorCity.isDisplayed());
                sa.assertThat(mSearchByMallorCityIcon.isDisplayed());
                sa.assertThat(mStoresDropdwon.isDisplayed());
                clickAndCollectSearchBox.clear();
                clickAndCollectSearchBox.sendKeys("cairo");
                waitForElementToBeClickable(clickAndCollectStoreList, 2);
                clickAndCollectStoreList.click();
                waitForPageToLoad(2);
                scrolldownElement(mAddYourContactDetails);
                sa.assertThat(mAddYourContactDetails.getText().trim()).isEqualTo(configProperties.getProperty("lmgcheckout.shippingpage.aacontactdetails.label"));
                sa.assertThat(mFullNameText.getText().trim()).isEqualTo(configProperties.getProperty("lmgaddaddress.label.fullname"));
                sa.assertThat(mFullNameTextBox.isDisplayed());
                sa.assertThat(mMobileText.getText().trim()).isEqualTo(configProperties.getProperty("lmgaddaddress.label.mobile"));
                sa.assertThat(mMobileTextBox.isDisplayed());
                sa.assertThat(mEmailText.getText().trim()).isEqualTo(configProperties.getProperty("lmgaddaddress.label.email"));
                sa.assertThat(mEmailTextBox.isDisplayed());
                scrolldownElement(proceedToPayment);
                sa.assertThat(proceedToPayment.getText().trim()).isEqualTo(configProperties.getProperty("lmgcheckout.shippingpage.Proceed.To.Payment"));
                testLogger.info("proceedToPayment " + proceedToPayment.getText().trim());
                sa.assertThat(mCheckoutAgree.getText().trim()).isEqualTo(configProperties.getProperty("lmgaddaddress.termsandcondition.part3"));
                testLogger.info("By continuing to checkout you agree to our Text: " + mCheckoutAgree.getText().trim());
                sa.assertThat(mTermsAndCondition.getText().trim()).isEqualTo(configProperties.getProperty("lmgaddaddress.termsandcondition.part2"));
                testLogger.info("Terms and Conditions Text: " + mTermsAndCondition.getText().trim());
                sa.assertThat(mSubtotal.getText().trim()).isEqualTo(configProperties.getProperty("checkout.page.order.subtotal"));
                testLogger.info("Subtotal Text: " + mSubtotal.getText().trim());
                String total = mSubtotalValue.getText().trim();
                String totalValue = removeText(total);
                sa.assertThat(mSubtotalValue.isDisplayed());
                testLogger.info("Subtotal Value: " + totalValue);
                String fullName = "WebAutomation " + WebUtils.generateRandomString(15);
                AddressBean.setFullName(fullName);
                enterFullName(fullName);
                mobileTextBox.sendKeys(AddressDataBean.getMobileNumber());
                enterEmailId();
                proceedToPayment.click();
                sa.assertAll();
            }

        } else if (deliveryType.equals("Home Delivery")) {
            if (config.getBrowser().trim().equalsIgnoreCase("CHROME") || config.getBrowser().trim().equalsIgnoreCase("CHROMEHEADLESS") || config.getBrowser().trim().equalsIgnoreCase("MOBILECHROME")) {
                waitForPageToLoad(30);
                waitForElementPresent(2);
                scrollUp();
                homeDeliveryBtn.click();
                sa.assertThat(homeDeliveryText.getText().trim()).isEqualTo(configProperties.getProperty("lmgcheckout.shippingpage.homedelivery.title"));
                testLogger.info("homeDeliveryText " + homeDeliveryText.getText().trim());
                sa.assertThat(addYourShippingAddressSection.getText().trim()).isEqualTo(configProperties.getProperty("lmgaddyouraddress.title"));
                testLogger.info("addYourShippingAddressSection " + addYourShippingAddressSection.getText().trim());
                waitForWebElementPresent(cityLabel, 10);
                sa.assertThat(cityLabel.getText().trim()).isEqualTo(configProperties.getProperty("lmgaddaddress.label.city"));
                testLogger.info("cityLabel " + cityLabel.getText().trim());
                sa.assertThat(areaLabel.getText().trim()).isEqualTo(configProperties.getProperty("lmgaddaddress.label.area"));
                testLogger.info("areaLabel " + areaLabel.getText().trim());
                sa.assertThat(floorApartmentNumberText.getText().trim()).isEqualTo(configProperties.getProperty("lmgaddaddress.label.building.name"));
                testLogger.info("floorApartmentNumberText " + floorApartmentNumberText.getText().trim());
                sa.assertThat(streetNameNum.getText().trim()).isEqualTo(configProperties.getProperty("lmgaddaddress.label.street.name"));
                testLogger.info("streetNameNum " + streetNameNum.getText().trim());
//                    sa.assertThat(landmarkText.getText().trim()).isEqualTo(configProperties.getProperty("lmgaddaddress.label.Landmark.optional"));
//                    testLogger.info("landmarkText " + landmarkText.getText().trim());
                sa.assertThat(hdFullNameText.getText().trim()).isEqualTo(configProperties.getProperty("lmgaddaddress.label.fullname"));
                testLogger.info("hdFullNameText " + hdFullNameText.getText().trim());
                sa.assertThat(hdFullNameTextBox.isDisplayed());
                testLogger.info("hdFullNameTextBox " + hdFullNameTextBox.isDisplayed());
                sa.assertThat(HDcountryCode.getText().trim()).isEqualTo(AddressDataBean.getCountryCode());
                testLogger.info("countryCode " + HDcountryCode.getText().trim());
                //testLogger.info("hdCountryCode " + hdCountryCode.getText().trim());
                sa.assertThat(hdMobileText.getText().trim()).isEqualTo(configProperties.getProperty("lmgaddaddress.label.mobilenumber"));
                testLogger.info("hdMobileText " + hdMobileText.getText().trim());
                sa.assertThat(hdMobileTextBox.isDisplayed());
                testLogger.info("hdMobileTextBox " + hdMobileTextBox.isDisplayed());
                sa.assertThat(hdEmailText.getText().trim()).isEqualTo(configProperties.getProperty("lmgaddaddress.label.email"));
                testLogger.info("hdEmailText " + hdEmailText.getText().trim());
                sa.assertThat(hdEmailTextBox.isDisplayed());
                testLogger.info("hdEmailTextBox" + hdEmailTextBox.getText().trim());
                sa.assertThat(addressType.getText().trim()).isEqualTo(configProperties.getProperty("lmgaddaddress.label.Address.Type.optional"));
                testLogger.info("addressType " + addressType.getText().trim());
                testLogger.info("HomeRadioButton " + homeRadioButton.isDisplayed());
                sa.assertThat(HomeText.getText().trim()).isEqualTo(configProperties.getProperty("addresstype.Home"));
                testLogger.info("HomeText " + HomeText.getText().trim());
                testLogger.info("officeRadioButton " + officeRadioButton.isDisplayed());
                sa.assertThat(officeText.getText().trim()).isEqualTo(configProperties.getProperty("addresstype.Office"));
                testLogger.info("officeText " + officeText.getText().trim());
//                sa.assertThat(defaultAddressText.getText().trim()).isEqualTo("Use this as my default shipping address");
//                testLogger.info("defaultAddressCheckBox " + defaultAddressCheckBox.isDisplayed());
                sa.assertAll();
            } else {
                mHomeDeliveryText.click();
                sa.assertThat(mHomeDeliveryText.getText().trim()).isEqualTo(configProperties.getProperty("lmgcheckout.shippingpage.homedelivery.title"));
                testLogger.info("Home Delivery Text " + mHomeDeliveryText.getText().trim());
                sa.assertThat(mAddYourShippingAddressSection.getText().trim()).isEqualTo(configProperties.getProperty("lmgaddyouraddress.title"));
                testLogger.info("Add your shipping address Text " + mAddYourShippingAddressSection.getText().trim());
                sa.assertThat(mCityLabel.getText().trim()).isEqualTo(configProperties.getProperty("lmgaddaddress.label.city"));
                testLogger.info("City Text " + mCityLabel.getText().trim());
                sa.assertThat(mAreaLabel.getText().trim()).isEqualTo(configProperties.getProperty("lmgaddaddress.label.area"));
                testLogger.info("Area Text " + mAreaLabel.getText().trim());
                sa.assertThat(mFloorApartmentNumberText.getText().trim()).isEqualTo(configProperties.getProperty("lmgaddaddress.label.building.name"));
                testLogger.info("Building Name/House No, Floor, Apartment No.* Text " + mFloorApartmentNumberText.getText().trim());
                sa.assertThat(mStreetNameNum.getText().trim()).isEqualTo(configProperties.getProperty("lmgaddaddress.label.street.name"));
                testLogger.info("Street Name/No.* Text " + mStreetNameNum.getText().trim());
                sa.assertThat(mLandmarkText.getText().trim()).isEqualTo(configProperties.getProperty("lmgaddaddress.label.Landmark.optional"));
                testLogger.info("Landmark (optional) Text " + mLandmarkText.getText().trim());
                sa.assertThat(mHdFullNameText.getText().trim()).isEqualTo(configProperties.getProperty("lmgaddaddress.label.fullname"));
                testLogger.info("hdFullNameText " + mHdFullNameText.getText().trim());
                sa.assertThat(mHdMobileText.getText().trim()).isEqualTo(configProperties.getProperty("lmgaddaddress.label.mobilenumber"));
                testLogger.info("hdMobileText " + mHdMobileText.getText().trim());
                sa.assertThat(mHdCountryCode.getText().trim()).isEqualTo(AddressDataBean.getCountryCode());
                testLogger.info("hdCountryCode " + mHdCountryCode.getText().trim());
                sa.assertThat(mHdEmailText.getText().trim()).isEqualTo(configProperties.getProperty("lmgaddaddress.label.email"));
                testLogger.info("hdEmailText " + mHdEmailText.getText().trim());
                sa.assertThat(mAddressType.getText().trim()).isEqualTo(configProperties.getProperty("lmgaddaddress.label.Address.Type.optional"));
                testLogger.info("Address Type" + mAddressType.getText().trim());
//                sa.assertThat(mHomeRadioButton.isDisplayed());
                testLogger.info("HomeRadioButton " + mHomeRadioButton.isDisplayed());
                sa.assertThat(mHomeText.getText().trim()).isEqualTo(configProperties.getProperty("addresstype.Home"));
                testLogger.info("Home Type Text " + mHomeText.getText().trim());
//                sa.assertTrue(mOfficeRadioButton.isDisplayed(),"Office Radio button is not displayed");
                testLogger.info("OfficeRadioButton " + mOfficeRadioButton.isDisplayed());
                sa.assertThat(mOfficeText.getText().trim()).isEqualTo(configProperties.getProperty("addresstype.Office"));
                testLogger.info("Office Type Text " + mOfficeText.getText().trim());
                scrolldownElement(proceedToPayment);
                sa.assertThat(proceedToPayment.getText().trim()).isEqualTo(configProperties.getProperty("lmgcheckout.shippingpage.Proceed.To.Payment"));
                testLogger.info("proceedToPayment " + proceedToPayment.getText().trim());
                sa.assertThat(mCheckoutAgree.getText().trim()).isEqualTo(configProperties.getProperty("lmgaddaddress.termsandcondition.part3"));
                testLogger.info("By continuing to checkout you agree to our Text: " + mCheckoutAgree.getText().trim());
                sa.assertThat(mTermsAndCondition.getText().trim()).isEqualTo(configProperties.getProperty("lmgaddaddress.termsandcondition.part2"));
                testLogger.info("Terms and Conditions Text: " + mTermsAndCondition.getText().trim());
                sa.assertThat(mSubtotal.getText().trim()).isEqualTo(configProperties.getProperty("checkout.page.order.subtotal"));
                testLogger.info("Subtotal Text: " + mSubtotal.getText().trim());
                String total = mSubtotalValue.getText().trim();
                String totalValue = removeText(total);
                sa.assertThat(mSubtotalValue.isDisplayed());
                testLogger.info("Subtotal Value: " + totalValue);
                sa.assertThat(mOrderSummaryStandardShippingInfoLabelValue.isDisplayed());
                testLogger.info("StandardShippingInfo Value " + mOrderSummaryStandardShippingInfoLabelValue.getText().trim());
                sa.assertThat(mOrderSummaryDeliveryEstimateValue.isDisplayed());
                testLogger.info("Delivery Estimate Value " + mOrderSummaryDeliveryEstimateValue.getText().trim());
                sa.assertThat(mOrderSummaryDeliveryEstimateTimeValue.isDisplayed());
                testLogger.info("Delivery Estimate Value " + mOrderSummaryDeliveryEstimateTimeValue.getText().trim());
                sa.assertThat(mStandardGroundShippingValue.isDisplayed());
                testLogger.info("Standard Ground Shipping Value " + mStandardGroundShippingValue);
                String fullName = "WebAutomation " + WebUtils.generateRandomString(15);
                selectYOurEmirate(AddressDataBean.getCity());
                selectedArea(AddressDataBean.getArea());
                enterBuildingName(AddressDataBean.getFloorNo());
                enterStreetNameOrNumber(AddressDataBean.getStreetNo());
                enterLandmarkOptional(AddressDataBean.getLandmark());
                AddressBean.setFullName(fullName);
                enterFullName(fullName);
                mobileTextBox.sendKeys(AddressDataBean.getMobileNumber());
                enterEmailId();
                proceedToPayment.click();
                sa.assertAll();
            }

        }

    }

    /**
     * This method is used to verify all the Ui components in select a payment section
     */

    public void verifySelectPaymentSection() {
        if (config.getBrowser().trim().equalsIgnoreCase("CHROME") || config.getBrowser().trim().equalsIgnoreCase("CHROMEHEADLESS") || config.getBrowser().trim().equalsIgnoreCase("MOBILECHROME")) {
            SoftAssertions sa = new SoftAssertions();
            waitForElementPresent(2);
            waitForPageToLoad(100);
//                scrolldownElement(selectAPaymentMethodSection);
            waitForWebElementVisibility(selectAPaymentMethodSection, 15);
            sa.assertThat(selectAPaymentMethodSection.getText().trim()).isEqualTo(configProperties.getProperty("paymentPage.selectPayentMethod"));
            testLogger.info("Select a payment method" + selectAPaymentMethodSection.getText().trim());
            sa.assertThat(creditOrDebitCardLabel.getText().trim()).isEqualTo(configProperties.getProperty("paymentPage.selectPayentMethod.Credit.Debit.Card"));
            testLogger.info("creditOrDebitCardLabel " + creditOrDebitCardLabel.getText().trim());
            sa.assertThat(payUsingMasterOrVisaCardMsg.getText().trim()).isEqualTo(configProperties.getProperty("paymentPage.selectPayentMethod.Payusing.Mastercard.Visacards"));
            testLogger.info("payUsingMasterOrVisaCardMsg " + payUsingMasterOrVisaCardMsg.getText().trim());
            sa.assertThat(creditCardImg.isDisplayed());
            testLogger.info("creditCardImg " + creditCardImg.isDisplayed());
            sa.assertThat(defaultRadioButton.isSelected());
            testLogger.info("defaultRadioButton  " + defaultRadioButton.isSelected());
            sa.assertThat(cardNumber.getText().trim()).isEqualTo(configProperties.getProperty("paymentPage.cardNumber"));
            testLogger.info("cardNumber " + cardNumber.getText().trim());
            sa.assertThat(expiryDateText.getText().trim()).isEqualTo(configProperties.getProperty("paymentPage.expiryDate"));
            testLogger.info("expressText " + expiryDateText.getText().trim());
            sa.assertThat(cvvNumberText.getText().trim()).isEqualTo(configProperties.getProperty("paymentPage.cvv"));
            testLogger.info("cvv Number Text " + cvvNumberText.getText().trim());
            sa.assertThat(nameOnCardText.getText().trim()).isEqualTo(configProperties.getProperty("paymentPage.nameOnCard"));
            testLogger.info("nameOnCardText " + nameOnCardText.getText().trim());
            //sa.assertThat(termsAndCondition.getText().trim()).isEqualTo(configProperties.getProperty("lmgaddaddress.termsandcondition.part2"));
            sa.assertThat(payNow.getText().trim()).isEqualTo(configProperties.getProperty("checkout.Pay.Now"));
            testLogger.info("payNow " + payNow.getText().trim());
            sa.assertAll();
        } else {
            waitForPageToLoad(3);
            SoftAssertions sa = new SoftAssertions();
            waitForVisibilityOfElementLocated(selectAPaymentMethodSection, driver, 2);
            sa.assertThat(selectAPaymentMethodSection.getText().trim()).isEqualTo(configProperties.getProperty("paymentPage.selectPayentMethod"));
            testLogger.info("Select a payment method" + selectAPaymentMethodSection.getText().trim());
            sa.assertThat(mDefaultRadioButton.isSelected());
            testLogger.info("defaultRadioButton  " + mDefaultRadioButton.isSelected());
            sa.assertThat(mCreditCardImg.isDisplayed());
            testLogger.info("creditCardImg " + mCreditCardImg.isDisplayed());
            sa.assertThat(creditOrDebitCardLabel.getText().trim()).isEqualTo(configProperties.getProperty("paymentPage.selectPayentMethod.Credit.Debit.Card"));
            testLogger.info("creditOrDebitCardLabel " + creditOrDebitCardLabel.getText().trim());
            sa.assertThat(payUsingMasterOrVisaCardMsg.getText().trim()).isEqualTo(configProperties.getProperty("paymentPage.selectPayentMethod.Payusing.Mastercard.Visacards"));
            testLogger.info("payUsingMasterOrVisaCardMsg " + payUsingMasterOrVisaCardMsg.getText().trim());
            sa.assertThat(cardNumber.getText().trim()).isEqualTo(configProperties.getProperty("paymentPage.cardNumber"));
            testLogger.info("cardNumber " + cardNumber.getText().trim());
            sa.assertThat(expiryDateText.getText().trim()).isEqualTo(configProperties.getProperty("paymentPage.expiryDate"));
            testLogger.info("expressText " + expiryDateText.getText().trim());
            sa.assertThat(cvvNumberText.getText().trim()).isEqualTo(configProperties.getProperty("paymentPage.cvv"));
            testLogger.info("cvv Number Text " + cvvNumberText.getText().trim());
            sa.assertThat(nameOnCardText.getText().trim()).isEqualTo(configProperties.getProperty("paymentPage.nameOnCard"));
            testLogger.info("nameOnCardText " + nameOnCardText.getText().trim());
            sa.assertThat(mTermsAndCondition1.getText().trim()).isEqualTo(configProperties.getProperty("lmgaddaddress.termsandcondition.part2"));
            testLogger.info("Terms and Conditions " + mTermsAndCondition1.getText().trim());
            sa.assertThat(payNow.getText().trim()).isEqualTo(configProperties.getProperty("checkout.Pay.Now"));
            testLogger.info("payNow " + payNow.getText().trim());
            sa.assertAll();
        }
    }

    /**
     * Verify UI Elements Order Summary Details
     */
    public void verifyYourOrderSummaryDetails(String deliveryType) {
        waitForPageToLoad(100);
        if (deliveryType.equals("click & Collect")) {
            if (config.getBrowser().trim().equalsIgnoreCase("CHROME") || config.getBrowser().trim().equalsIgnoreCase("CHROMEHEADLESS") || config.getBrowser().trim().equalsIgnoreCase("MOBILECHROME")) {
                SoftAssertions sa = new SoftAssertions();
                sa.assertThat(yourOrderSummaryTitle.getText().trim()).isEqualTo(configProperties.getProperty("checkout.order.summary"));
                testLogger.info("Your Order Summary details " + yourOrderSummaryTitle.getText().trim());
                sa.assertThat(orderSummaryImg.isDisplayed());
                testLogger.info("orderSummaryImg " + orderSummaryImg.isDisplayed());
//                sa.assertThat(maxText.getText().trim()).isEqualTo("MAX");
//                testLogger.info("max Text is " + maxText.getText().trim());
                sa.assertThat(checkoutProductDescription.isDisplayed());
                testLogger.info("Order Summary Product Description " + checkoutProductDescription.getText().trim());
                sa.assertThat(orderSummaryQtySection.isDisplayed());
                testLogger.info("Qty Section " + orderSummaryQtySection.getText().trim());
                String qty = orderSummaryQtySection.getText().trim();
                String productQty = removeText(qty);
                testLogger.info("Product QTY" + productQty);
                sa.assertThat(orderSummaryPriceLabel.getText().trim()).isEqualTo(configProperties.getProperty("checkout.page.order.price"));
                testLogger.info("Price Label " + orderSummaryPriceLabel.getText().trim());
                String productPrice = orderSummaryPriceLabelValue.getText().trim();
                String price = removeText(productPrice);
                sa.assertThat(orderSummaryPriceLabelValue.isDisplayed());
                testLogger.info("Price Value " + price);
                sa.assertThat(orderSummarySubTotalLabel.getText().trim()).isEqualTo(configProperties.getProperty("checkout.page.order.subtotal"));
                testLogger.info("Subtotal: Label " + orderSummarySubTotalLabel.getText().trim());
                String productSubtotal = orderSummarySubTotalLabelValue.getText().trim();
                String subtotal = removeText(productSubtotal);
                sa.assertThat(orderSummarySubTotalLabelValue.isDisplayed());
                testLogger.info("SubTotal Value " + subtotal);
                sa.assertThat(cncorderSummaryTotalLabel.getText().trim()).isEqualTo(configProperties.getProperty("order.total"));
                testLogger.info("Total Label " + cncorderSummaryTotalLabel.getText().trim());
                String productTotal = cncorderSummaryTotalLabelValue.getText().trim();
                String total = removeText(productTotal);
                sa.assertThat(cncorderSummaryTotalLabelValue.isDisplayed());
                testLogger.info("Total Value " + total);
                sa.assertThat(cncorderSummaryApplicableVATValueText.getText().trim()).isEqualTo(configProperties.getProperty("vat.included.message"));
                testLogger.info("Inclusive of any applicable VAT Value " + cncorderSummaryApplicableVATValueText.getText().trim());
                sa.assertThat(cashOnDeliveryImg.isDisplayed());
                testLogger.info("cashOnDeliveryImg " + cashOnDeliveryImg.isDisplayed());
                sa.assertThat(cashOnDeliveryText.getText().trim()).isEqualTo(configProperties.getProperty("landMarkPayment.header.user.payment.cashondelivery"));
                testLogger.info("Cash On Delivery Text " + cashOnDeliveryText.getText().trim());
                sa.assertThat(processingFeeOfEGP20applicableText.getText().trim()).isEqualTo(configProperties.getProperty("landMarkPayment.header.user.payment.cod.notavailable"));
                testLogger.info("We’re sorry, this option is not available for Click & Collect. Text " + processingFeeOfEGP20applicableText.getText().trim());
                sa.assertAll();
            } else {
                SoftAssertions sa = new SoftAssertions();
                sa.assertThat(yourOrderSummaryTitle.getText().trim()).isEqualTo(configProperties.getProperty("checkout.order.summary"));
                testLogger.info("Your Order Summary details " + yourOrderSummaryTitle.getText().trim());
                sa.assertThat(orderSummaryImg.isDisplayed());
                testLogger.info("orderSummaryImg " + orderSummaryImg.isDisplayed());
//                    sa.assertThat(maxText.getText().trim()).isEqualTo("MAX");
//                    testLogger.info("max Text is " + maxText.getText().trim());
                sa.assertThat(checkoutProductDescription.isDisplayed());
                testLogger.info("Order Summary Product Description " + checkoutProductDescription.getText().trim());
                sa.assertThat(orderSummaryQtySection.isDisplayed());
                testLogger.info("Qty Section " + orderSummaryQtySection.getText().trim());
                String qty = orderSummaryQtySection.getText().trim();
                String productQty = removeText(qty);
                testLogger.info("Product QTY" + productQty);
                sa.assertThat(orderSummaryPriceLabel.getText().trim()).isEqualTo(configProperties.getProperty("checkout.page.order.price"));
                testLogger.info("Price Label " + orderSummaryPriceLabel.getText().trim());
                String productPrice = orderSummaryPriceLabelValue.getText().trim();
                String price = removeText(productPrice);
                sa.assertThat(orderSummaryPriceLabelValue.isDisplayed());
                testLogger.info("Price Value " + price);
                sa.assertThat(mOrderSummarySubTotalLabel.getText().trim()).isEqualTo(configProperties.getProperty("checkout.page.order.subtotal"));
                testLogger.info("Subtotal: Label " + mOrderSummarySubTotalLabel.getText().trim());
                String productSubtotal = mOrderSummarySubTotalLabelValue.getText().trim();
                String subtotal = removeText(productSubtotal);
                sa.assertThat(mOrderSummarySubTotalLabelValue.isDisplayed());
                testLogger.info("SubTotal Value " + subtotal);
                sa.assertThat(mCncorderSummaryTotalLabel.getText().trim()).isEqualTo(configProperties.getProperty("order.total"));
                testLogger.info("Total Label " + mCncorderSummaryTotalLabel.getText().trim());
                String productTotal = mCncorderSummaryTotalLabelValue.getText().trim();
                String total = removeText(productTotal);
                sa.assertThat(mCncorderSummaryTotalLabelValue.isDisplayed());
                testLogger.info("Total Value " + total);
                sa.assertThat(mCncorderSummaryApplicableVATValueText.getText().trim()).isEqualTo(configProperties.getProperty("vat.included.message"));
                testLogger.info("Inclusive of any applicable VAT Value " + mCncorderSummaryApplicableVATValueText.getText().trim());
                sa.assertThat(cashOnDeliveryText.getText().trim()).isEqualTo(configProperties.getProperty("landMarkPayment.header.user.payment.cashondelivery"));
                testLogger.info("Cash On Delivery Text " + cashOnDeliveryText.getText().trim());
                sa.assertThat(processingFeeOfEGP20applicableText.getText().trim()).isEqualTo(configProperties.getProperty("landMarkPayment.header.user.payment.cod.notavailable"));
                testLogger.info("We’re sorry, this option is not available for Click & Collect. Text " + processingFeeOfEGP20applicableText.getText().trim());
                sa.assertAll();
            }
        } else if (deliveryType.equals("Home Delivery")) {
            if (config.getBrowser().trim().equalsIgnoreCase("CHROME") || config.getBrowser().trim().equalsIgnoreCase("CHROMEHEADLESS") || config.getBrowser().trim().equalsIgnoreCase("MOBILECHROME")) {
                SoftAssertions sa = new SoftAssertions();
                waitForVisibilityOfElementLocated(yourOrderSummaryTitle, driver, 5);
                sa.assertThat(yourOrderSummaryTitle.getText().trim()).isEqualTo(configProperties.getProperty("checkout.order.summary"));
                testLogger.info("Your order summary Text " + yourOrderSummaryTitle);
                sa.assertThat(orderSummaryImg.isDisplayed());
                testLogger.info("orderSummaryImg " + orderSummaryImg.isDisplayed());
//                sa.assertThat(maxText.getText().trim()).isEqualTo("MAX");
//                testLogger.info("max Text is " + maxText.getText().trim());
                sa.assertThat(checkoutProductDescription.isDisplayed());
                testLogger.info("Order Summary Product Description " + checkoutProductDescription.getText().trim());
                sa.assertThat(orderSummaryQtySection.isDisplayed());
                testLogger.info("Qty Section " + orderSummaryQtySection.getText().trim());
                String qty = orderSummaryQtySection.getText().trim();
                String productQty = removeText(qty);
                testLogger.info("Product QTY" + productQty);
                sa.assertThat(orderSummaryPriceLabel.getText().trim()).isEqualTo(configProperties.getProperty("checkout.page.order.price"));
                testLogger.info("Price Label " + orderSummaryPriceLabel.getText().trim());
                String productPrice = orderSummaryPriceLabelValue.getText().trim();
                String price = removeText(productPrice);
                sa.assertThat(orderSummaryPriceLabelValue.isDisplayed());
                testLogger.info("Price Value " + price);
                sa.assertThat(orderSummarySubTotalLabel.getText().trim()).isEqualTo(configProperties.getProperty("checkout.page.order.subtotal"));
                testLogger.info("Subtotal: Label " + orderSummarySubTotalLabel.getText().trim());
                String productSubtotal = orderSummarySubTotalLabelValue.getText().trim();
                String subtotal = removeText(productSubtotal);
                sa.assertThat(orderSummarySubTotalLabelValue.isDisplayed());
                testLogger.info("SubTotal Value " + subtotal);
//                sa.assertThat(orderSummaryStandardShippingInfoLabel.getText().trim()).contains(configProperties.getProperty("landMarkOrders.header.standardGroundShipping.part2"));
//                testLogger.info("StandardShippingInfo Label " + orderSummaryStandardShippingInfoLabel.getText().trim());
                sa.assertThat(orderSummaryStandardShippingInfoLabelValue.isDisplayed());
                testLogger.info("StandardShippingInfo Value " + orderSummaryStandardShippingInfoLabelValue.getText().trim());
//                sa.assertThat(orderSummaryDeliveryEstimateValue.isDisplayed());
//                testLogger.info("Delivery Estimate Value " + orderSummaryDeliveryEstimateValue.getText().trim());
//                sa.assertThat(orderSummaryDeliveryEstimateTimeValue.isDisplayed());
//                testLogger.info("Delivery Estimate Value " + orderSummaryDeliveryEstimateTimeValue.getText().trim());
                sa.assertThat(orderSummaryTotalLabel.getText().trim()).isEqualTo(configProperties.getProperty("order.total"));
                testLogger.info("Total Label " + orderSummaryTotalLabel.getText().trim());
                String productTotal = orderSummaryTotalLabelValue.getText().trim();
                String toal = removeText(productTotal);
                sa.assertThat(orderSummaryTotalLabelValue.isDisplayed());
                testLogger.info("Total Value " + toal);
                sa.assertThat(orderSummaryApplicableVATValueText.getText().trim()).isEqualTo(configProperties.getProperty("vat.included.message"));
                testLogger.info("Inclusive of any applicable VAT Value " + orderSummaryApplicableVATValueText.getText().trim());
                sa.assertAll();
            } else {
                SoftAssertions sa = new SoftAssertions();
                sa.assertThat(yourOrderSummaryTitle.getText().trim()).isEqualTo(configProperties.getProperty("checkout.order.summary"));
                testLogger.info("Your order summary Text " + yourOrderSummaryTitle);
                sa.assertThat(orderSummaryImg.isDisplayed());
                testLogger.info("orderSummaryImg " + orderSummaryImg.isDisplayed());
//            sa.assertThat(maxText.getText().trim()).isEqualTo("MAX");
//            testLogger.info("max Text is " + maxText.getText().trim());
                sa.assertThat(checkoutProductDescription.isDisplayed());
                testLogger.info("Order Summary Product Description " + checkoutProductDescription.getText().trim());
                sa.assertThat(orderSummaryQtySection.isDisplayed());
                testLogger.info("Qty Section " + orderSummaryQtySection.getText().trim());
                String qty = orderSummaryQtySection.getText().trim();
                String productQty = removeText(qty);
                testLogger.info("Product QTY" + productQty);
                sa.assertThat(orderSummaryPriceLabel.getText().trim()).isEqualTo(configProperties.getProperty("checkout.page.order.price"));
                testLogger.info("Price Label " + orderSummaryPriceLabel.getText().trim());
                String productPrice = orderSummaryPriceLabelValue.getText().trim();
                String price = removeText(productPrice);
                sa.assertThat(orderSummaryPriceLabelValue.isDisplayed());
                testLogger.info("Price Value " + price);
                sa.assertThat(mOrderSummarySubTotalLabel.getText().trim()).isEqualTo(configProperties.getProperty("checkout.page.order.subtotal"));
                testLogger.info("Subtotal: Label " + mOrderSummarySubTotalLabel.getText().trim());
                String productSubtotal = mOrderSummarySubTotalLabelValue.getText().trim();
                String subtotal = removeText(productSubtotal);
                sa.assertThat(mOrderSummarySubTotalLabelValue.isDisplayed());
                testLogger.info("SubTotal Value " + subtotal);
                sa.assertThat(mOrderSummaryStandardShippingInfoLabelValue1.getText().trim()).contains(configProperties.getProperty("landMarkOrders.header.standardGroundShipping.part2"));
                testLogger.info("StandardShippingInfo Label " + mOrderSummaryStandardShippingInfoLabelValue1.getText().trim());
                sa.assertThat(mOrderSummaryStandardShippingInfoLabelValue2.isDisplayed());
                testLogger.info("StandardShippingInfo Value " + mOrderSummaryStandardShippingInfoLabelValue2.getText().trim());
                sa.assertThat(mOrderSummaryDeliveryEstimateValue1.isDisplayed());
                testLogger.info("Delivery Estimate Value " + mOrderSummaryDeliveryEstimateValue1.getText().trim());
                sa.assertThat(mOrderSummaryDeliveryEstimateTimeValue1.isDisplayed());
                testLogger.info("Delivery Estimate Value " + mOrderSummaryDeliveryEstimateTimeValue1.getText().trim());
                sa.assertThat(mOrderSummaryTotalLabel.getText().trim()).isEqualTo(configProperties.getProperty("order.total"));
                testLogger.info("Total Label " + mOrderSummaryTotalLabel.getText().trim());
                String productTotal = mOrderSummaryTotalLabelValue.getText().trim();
                String toal = removeText(productTotal);
                sa.assertThat(mOrderSummaryTotalLabelValue.isDisplayed());
                testLogger.info("Total Value " + toal);
                sa.assertThat(mOrderSummaryApplicableVATValueText.getText().trim()).isEqualTo(configProperties.getProperty("vat.included.message"));
                testLogger.info("Inclusive of any applicable VAT Value " + mOrderSummaryApplicableVATValueText.getText().trim());
                sa.assertAll();
            }
        }
    }

    /**
     * This method used for verify card section
     */

    public void verifyCheckoutCardSection() {
        waitForPageToLoad(100);
        SoftAssertions sa = new SoftAssertions();
        if (config.getBrowser().trim().equalsIgnoreCase("CHROME") || config.getBrowser().trim().equalsIgnoreCase("CHROMEHEADLESS") || config.getBrowser().trim().equalsIgnoreCase("MOBILECHROME")) {
            sa.assertThat(waysYouCanPayText.getText().trim()).isEqualTo(configProperties.getProperty("basket.page.footer.payment.payWays"));
            testLogger.info("Ways you can pay Text " + waysYouCanPayText.getText().trim());
            sa.assertThat(masterCardIcon.isDisplayed());
            sa.assertThat(visCardIcon.isDisplayed());
            sa.assertThat(cashOnDeliveryIcon.isDisplayed());
            sa.assertThat(shukranIcon.isDisplayed());
        } else {
            scrolldownElement(mWaysYouCanPayText);
            sa.assertThat(mWaysYouCanPayText.getText().trim()).isEqualTo(configProperties.getProperty("basket.page.footer.payment.payWays"));
            testLogger.info("Ways you can pay Text " + mWaysYouCanPayText.getText().trim());
            sa.assertThat(mMasterCardIcon.isDisplayed());
            sa.assertThat(mVisCardIcon.isDisplayed());
            sa.assertThat(mCashOnDeliveryIcon.isDisplayed());
            sa.assertThat(mShukranIcon.isDisplayed());
            testLogger.info("Change Delivery Text " + changeDeliveryType.getText().trim());
            scrollUp();
            changeDeliveryType.click();
        }
        sa.assertAll();

    }

    /**
     * This method is used to verify all UI elements are displayed or not
     */

    public void deliveryTypeMethodCOD(String deliveryType) {
        if (deliveryType.equals("click & Collect")) {
            if (config.getBrowser().trim().equalsIgnoreCase("CHROME") || config.getBrowser().trim().equalsIgnoreCase("CHROMEHEADLESS") || config.getBrowser().trim().equalsIgnoreCase("MOBILECHROME")) {
                waitForPageToLoad(100);
                //waitForElementPresent(40);
                waitForElementPresent(30);
                waitForPresenceOfElementLocated(clickAndCollectBtn,driver,40);
                //waitForElementToBeClickable(clickAndCollectBtn,driver,30);
                //clickAndCollectText.click();
                //waitForLoadJavaScripts(driver,15);

                //waitForWebElementVisibility(clickAndCollectBtn,19);
                clickAndCollectBtn.click();
                testLogger.info("URL " + driver.getCurrentUrl());
            } else {
                //waitForPageToLoad(25);
                waitForElementToBeClickable(mClickAndCollectText, driver, 15);
                waitClick(mClickAndCollectText, driver, 16);
                //  mClickAndCollectText.click();
                testLogger.info("URL " + driver.getCurrentUrl());
            }

        } else if (deliveryType.equals("Home Delivery")) {
            if (config.getBrowser().trim().equalsIgnoreCase("CHROME") || config.getBrowser().trim().equalsIgnoreCase("CHROMEHEADLESS") || config.getBrowser().trim().equalsIgnoreCase("MOBILECHROME")) {
                // waitForPageToLoad(25);
                waitForElementToBeClickable(homeDeliveryBtn, 15);
                homeDeliveryBtn.click();
            } else {
                //waitForPageToLoad(25);
                //waitForElementPresent(3);
                waitForElementToBeClickable(mHomeDeliveryText, 15);
                mHomeDeliveryText.click();
            }

        }

    }

    /**
     * This method is used to verify all the ui components in add your shipping address section
     */
    public void verifyAddYourShippingAddressUIComponents() {
        SoftAssertions sa = new SoftAssertions();
        sa.assertThat(clickAndCollectText.getText().trim()).isEqualTo("Click & Collect");
        sa.assertThat(collectYourOrderFromMsg.getText().trim()).isEqualTo("Collect your order from a location of your choice");
        sa.assertThat(homeDeliveryText.getText().trim()).isEqualTo("Home Delivery");
        sa.assertThat(deliveryToHomeMsg.getText().trim()).isEqualTo("Get your products delivered to your home");
        sa.assertThat(addYourShippingAddressSection.getText().trim()).isEqualTo("Add your shipping address");
        sa.assertThat(fullNameLabel.getText().trim()).isEqualTo("Full Name");
//        sa.assertThat(mobileNumberLabel.getText().trim()).isEqualTo("Mobile");
        sa.assertThat(emailLabel.getText().trim()).isEqualTo("Email");
        sa.assertThat(emirateLabel.getText().trim()).isEqualTo("Emirate");
        scrolldownElement(mobileNumberLabel);
        waitForWebElementPresent(areaLabel, 10);
        sa.assertThat(areaLabel.getText().trim()).isEqualTo("Area");
        sa.assertThat(buildingLabel.getText().trim()).isEqualTo("Building Name/Villa No*");
//       sa.assertThat(floorApartmentNumberText.getText().trim()).isEqualTo("Floor, Apartment No.");
        sa.assertThat(makaniLabel.isDisplayed());
        sa.assertThat(helpUsDeliverToYourExactLocation.getText().trim()).isEqualTo("Help us deliver to your exact location");
        testLogger.info("Address Type" + addressType.getText().trim());
        sa.assertThat(addressType.getText().trim()).isEqualTo("Address Type(optional)");
        sa.assertThat(HomeText.getText().trim()).isEqualTo("Home");
        sa.assertThat(officeText.getText().trim()).isEqualTo("Office");
        sa.assertThat(defaultAddressText.getText().trim()).isEqualTo("Use this as my default shipping address");
        testLogger.info("HomeRadioButton " + homeRadioButton.isDisplayed());
        testLogger.info("OfficeRadioButton " + officeRadioButton.isDisplayed());
        testLogger.info("defaultAddressCheckBox " + defaultAddressCheckBox.isDisplayed());
        sa.assertAll();

    }

    /**
     * This method is used to select area
     *
     * @param name
     */

    public void selectYOurEmirate(String name) {
        if ((config.getBrowser().equalsIgnoreCase("MOBILEWEBCHROMEANDROID")) || (config.getBrowser().equalsIgnoreCase("MOBILECHROME"))) {
            waitClick(chooseCity, driver, 20);
        } else {
            waitClick(chooseCity, driver, 30);
        }
        waitForElementPresent(15);
        int count = cityNames.size();
        System.out.println("city Name count as" + count);
        WebElement updateCity = null;
//        String preFix = "//*[@id='menu-emirate']/div[3]/ul/li/span[text()='";
//        String postFix = "']";
        String preFix = "//*[@id='menu-emirate']/div[3]/ul/li[";
        String postFix = "]";
        for (int i = 1; i <= count; i++) {
            //String path = preFix + name + postFix;
            String path = preFix + i + postFix;
            updateCity = driver.findElement(By.xpath(path));
            waitForVisibilityOfElementLocated(updateCity, driver, 15);
            String dropDownValue = updateCity.getText().trim();
            if (name.equals(dropDownValue)) break;
        }
        mouseHoverClick(updateCity);
        //waitClick(updateCity,driver,15);
        waitForElementPresent(10);
        waitForWebElementVisibility(chooseCity, 20);
        String selectedEmirateValue = chooseCity.getText().trim();
        System.out.println("Selected area as" + selectedEmirateValue);
        System.out.println("Selected area as" + selectedEmirateValue);

    }

    public String updateSelectYOurEmirate(String name) {
        chooseCity.click();
        elementMouseClick(emirateDubaiListItem);
        String selectedEmirateValue = chooseCity.getText().trim();
        testLogger.info("Selected emirate value as:" + selectedEmirateValue);
        return selectedEmirateValue;
    }

    /**
     * This method is used to select area
     *
     * @param name
     */

    public void selectedArea(String name) {
        waitForVisibilityOfElementLocated(areaInput, driver, 20);
        waitForElementToBeClickable(areaInput, driver, 16);
        implicitWait(10);
        new WebDriverWait(driver, Duration.ofSeconds(10)).until(ExpectedConditions.elementToBeClickable(areaInput)).click();
        //areaInput.click();
        waitForPageToLoad(20);
        int count = areaNames.size();
        WebElement updateArea = null;
        String preFix = "//*[@id='menu-area']/div[3]/ul/li/span[text()='";
        String postFix = "']";
        for (int i = 1; i <= count; i++) {
            String path = preFix + name + postFix;
            updateArea = driver.findElement(By.xpath(path));
        }
        implicitWait(10);
        waitForElementToBeClickable(updateArea, 12);
        mouseHoverClick(updateArea);
        waitForPageToLoad(10);
        String selectedAreaValue = areaInput.getText().trim();
        testLogger.info("Selected area value as:" + selectedAreaValue);
    }

    public String updateSelectedArea(String name) {
        waitForElementToBeClickable(areaInput, 10);
        areaInput.click();
        waitForElementToBeClickable(areaDubaiAirPortListItem, 12);
        elementMouseClick(areaDubaiAirPortListItem);
        String selectedAreaValue = areaInput.getText().trim();
        testLogger.info("Selected area value as:" + selectedAreaValue);
        return selectedAreaValue;
    }

    /**
     * This method is used to enter building Name
     *
     * @param buildingName
     */
    public void enterBuildingName(String buildingName) {
        buildingInput.sendKeys(buildingName);
    }

    public void updateBuildingName(String buildingName) {
        buildingInput.clear();
        buildingInput.sendKeys(buildingName);
    }

    /**
     * This method is used to enter makani number
     *
     * @param number enter makani number
     */

    public void enterStreetNameOrNumber(String number) {
        floorInput.sendKeys(number);
    }

    /**
     * This method is used to enter lanmaark text
     *
     * @param text enter landmark text
     */

    public void enterLandmarkOptional(String text) {
        landmarkOptionalInput.sendKeys(text);
    }

    /**
     * This method is used to enter full name
     *
     * @param name
     */
    public void enterFullName(String name) {
        waitForPresenceOfElementLocated(fullNameTextBox, driver, 15);
        fullNameTextBox.sendKeys(name);
    }

    /**
     * This method is used to enter mobile number
     */

    public void enterMobileNumber(String mobNum) {
        waitForPresenceOfElementLocated(mobileTextBox, driver, 15);
        mobileTextBox.clear();
        mobileTextBox.sendKeys(mobNum);
    }

    /**
     * This method is used to enter email id
     */
    public void enterEmailId() {
        String email = randomGenerateEmail();
        emailInput.sendKeys(email);
    }

    /**
     * change address type based on input value
     *
     * @param addressType passed input as work
     */
    public void changeAddressTypeAs(String addressType) {
        if (addressType.equals("Office")) {
            waitForElementPresent(1);
            waitForElementToBeClickable(officeRadioButton, 15);
            officeRadioButton.click();
        } else {
            waitForElementPresent(3);
//            waitForElementToBeClickable(homeRadioButton,15);
            mouseHoverClick(homeRadioButton);
        }
    }

    public void clickOnProceedToPayment() {
        if (config.getBrowser().trim().equalsIgnoreCase("CHROME") || config.getBrowser().trim().equalsIgnoreCase("CHROMEHEADLESS") || config.getBrowser().trim().equalsIgnoreCase("MOBILECHROME")) {
        } else {
            //waitForPageToLoad(3);
            waitForWebElementPresent(proceedToPayment, driver, 15);
            proceedToPayment.click();
        }
    }


    public void clickandCollectCashOnDelivery() {
        if (config.getBrowser().trim().equalsIgnoreCase("CHROME") || config.getBrowser().trim().equalsIgnoreCase("CHROMEHEADLESS") || config.getBrowser().trim().equalsIgnoreCase("MOBILECHROME")) {
            SoftAssertions sa = new SoftAssertions();
            scrollToParticularElement(cashOnDeliveryRadioButton);
            boolean status = false;
            status = isClickable(driver, cashOnDeliveryRadioButton);
            if (!status) {
                testLogger.info("cashOnDeliveryRadioButton is disabled " + cashOnDeliveryRadioButton.isDisplayed());
            } else {
                sa.assertThat(status).isEqualTo(false);
                testLogger.info("cashOnDeliveryRadioButton is enabled " + cashOnDeliveryRadioButton.isDisplayed());
            }
            sa.assertAll();
        }
    }

    /**
     * This method is used to enter debit or credit card details
     *
     * @param cardType provide details as visa ,or master
     */

    public void enterCreditCardNumber(String cardType) {
        String cardNumber;
//        if(cardType.equals("visa")){
//        }
        if (config.getBrowser().trim().equalsIgnoreCase("CHROME") || config.getBrowser().trim().equalsIgnoreCase("CHROMEHEADLESS") || config.getBrowser().trim().equalsIgnoreCase("MOBILECHROME")) {
            waitForVisibilityOfElementLocated(cardnumInput, driver, 3);
            cardnumInput.sendKeys(cardType);
        } else {
            cardnumInput.sendKeys(cardType);
        }
    }


    @FindBy(xpath = "//*[@id='gcCardNumberId']")
    private WebElement giftCardNumber;
    @FindBy(xpath = "//*[@id='gcPinNumberId']")
    private WebElement giftCardPin;


    @FindBy(xpath = "//*[@id='shippingForm']/div/div/form/div/div/a")
    private WebElement giftCardSectionUseItHereHyperlink;


    @FindBy(xpath = "//*[@id='shippingForm']//form//div[3]/div/button/span/span")
    private WebElement giftCardApplyButton;

    public void clickGiftCardSectionUseItHereHyperlink() {
//        scrolldown();
        //scrollDown(50);
        //scrolldownElement(selectAPaymentMethodSection);
        //scrolldownElement(cashOnDeliveryImg);
        // scrolldownElement(proceedToPayment);
        //waitForElementToBeClickable(giftCardSectionUseItHereHyperlink,driver,15);
        scrollPageEnd();
        mouseHoverClick(giftCardSectionUseItHereHyperlink);
        // giftCardSectionUseItHereHyperlink.click();
        implicitWait(10);
    }

    public void enterGiftCardNumber(String cardNumber) {
        waitForElementPresent(20);
        scrollPageEnd();
        //waitForElementToBeClickable(giftCardNumber, 15);
//        giftCardNumber.click();
//        giftCardNumber.sendKeys(cardNumber);
        moveToElementAndClickAndSendKeys(giftCardNumber, cardNumber);
    }

    public void enterGiftCardPIN(String cardPin) {
        waitForElementToBeClickable(giftCardPin, driver, 15);
//        giftCardPin.click();
//        giftCardPin.sendKeys(cardPin);
        moveToElementAndClickAndSendKeys(giftCardPin, cardPin);
    }


    public void clickGiftCardApplyButton() {
        giftCardApplyButton.click();
    }

    @FindBy(xpath = "//*[@id='shippingForm']//form//div[2]/span/span[2]")
    private WebElement shukranPayToggleButton;

    public void userPayUsingShukranPoints() {
        scrollPageEnd();
        //shukranPayToggleButton.click();
        waitForElementPresent(10);
//        mouseHoverClick(shukranPayToggleButton);
        //mouseHover(shukranPayToggleButton);
        mouseHoverClick(shukranPayToggleButton);
        waitThread(1000);
    }

    //*[@id="shippingForm"]//form/div[6]/div/div/div[2]/div/div/div[1]/label/span[1]/span/input

    @FindBy(xpath = "//*[@id='shippingForm']//form/div[6]//div[2]//div[1]/label/span[1]/span/input")
    private WebElement userPayUsingMyCredit;

    @FindBy(xpath = "//*[@id='checkout-payment-shukranPay_QA']//input")
    private WebElement shukranPayCheckBox;



    public void userPayUsingMyCredit() {
        scrolldownElement(selectAPaymentMethodSection);
        waitForElementPresent(10);
        int checkoutTotal = 10000; //Assuming need to update run time
        int myCreditBalance = Integer.parseInt(MyCreditBalanceUpdate.getMyCreditAccountBalance().split("\\.")[0]);
        if (checkoutTotal > myCreditBalance) {
            MyCreditBalanceUpdate.addAmountToMyCreditAccount(checkoutTotal - myCreditBalance);
        }
        pageRefresh();
        waitForPageToLoad(10);
        if (verifyNoelement(addYourContactDetails)) {
            scrollElement(addYourContactDetails);
        } else {
            scrollElement(addYourShippingAddressSection);
        }
        userPayUsingMyCredit.click();
        waitThread(1000);
    }


    /**
     * This method is used to enter debit or credit card details
     */

    public void enterCreditDebitCardNumber(String cardType) throws Throwable {
        CreditDebitCardDetails(cardType);
        //sdfvs
        waitForElementPresent(3);
                if(isElementDisplayed(shukranPayCheckBox) ){
                    shukranPayCheckBox.click();
                    waitForElementPresent(2);
                }


        boolean status = clickElementsList(savedCardNumber, CardDataBean.getCardNumber().substring(Math.max(0, CardDataBean.getCardNumber().length() - 4)), cvvInputDynamic);
        if (status) {
            waitForElementToBeClickable(cvvInputDynamic, driver, 15);
            waitForElementPresent(2);
            cvvInputDynamic.click();
            moveToElementAndClickAndSendKeys(cvvInputDynamic, CardDataBean.getCvvNumber());
        } else {
            boolean isClickAndCollectScenario = verifyNoelement(clickAndCollectFullNameLabel);
            if (verifyNoelement(addANewCardLink)) {
                scrolldownElement(selectAPaymentMethodSection);
                waitForElementPresent(3);
                addANewCardLink.click();
            }
            waitForElementToBeClickable(cardnumInput, driver, 15);
            cardnumInput.click();
            cardnumInput.sendKeys(CardDataBean.getCardNumber());
            scrollDown(5);
            waitForElementPresent(2);
            expiryMonth.click();
            String date = CardDataBean.getExpiryMonth() + CardDataBean.getExpiryYear().substring(CardDataBean.getExpiryYear().length() - 2);
            expiryMonth.sendKeys(date);
            waitForElementToBeClickable(cvvInput, driver, 35);
            cvvInput.click();
            cvvInput.sendKeys(CardDataBean.getCvvNumber());
            waitForElementToBeClickable(namecardInput, driver, 35);
            namecardInput.click();
            namecardInput.sendKeys(CardDataBean.getNameOnCard());
            if (verifyNoelement(saveThisCard)) {
                saveThisCardCheckBox.click();
                saveThisCard.click();
                waitForPageToLoad(10);
                //need to handel CnC add new card
                if (isClickAndCollectScenario) {
                    waitForElementPresent(20);
                    clickAndCollectBtn.click();
                    scrolldownElement(selectAPaymentMethodSection);
                    waitForElementPresent(3);
                    CheckoutStepDefination csd = new CheckoutStepDefination();
                    csd.searchAndSelectTheAddressInMap(saveStoreCityNameSaveRuntime);
                    csd.userEnterContactDetails(saveContactRuntime);
                    csd.userClickOnProceedToPayment();
                }
                clickElementsList(savedCardNumber, CardDataBean.getCardNumber().substring(Math.max(0, CardDataBean.getCardNumber().length() - 4)), cvvInputDynamic);
                waitForElementToBeClickable(cvvInputDynamic, driver, 15);
                waitForElementPresent(5);
                cvvInputDynamic.click();
                moveToElementAndClickAndSendKeys(cvvInputDynamic, CardDataBean.getCvvNumber());
            }
        }
    }

    /**
     * This method is used select expiry date value
     */

    public void selectExpiryMonth() {
        expiryMonth.click();
        elementMouseClick(selectExpiryMonthValue);
    }

    /**
     * This method is used select expiry month value
     */

    public void setSelectExpiryYear() {
        waitForPageToLoad(20);
        waitForElementToBeClickable(expiryYear, driver, 20);
        expiryYear.click();
        waitForElementToBeClickable(selectExpiryYearValue, driver, 15);
        elementMouseClick(selectExpiryYearValue);
    }

    /**
     * This method is used to enter cvv number of the card
     *
     * @param number is used to get cvv number value
     */
    public void enterCVV(String number) {
        cvvInput.sendKeys(number);
    }

    /**
     * This method is used to enter name on card
     */

    public void enterNameOnCard(String name) {
        namecardInput.sendKeys(name);
    }

    /**
     * clicking on pay now button
     */
    public void payNowClick() {
        setCheckoutData();
        waitForElementPresent(2);
        scrolldown();
        waitForElementPresent(10);
        //waitForPageToLoad(10);
        payNow.click();
        waitForElementPresent(10);
        testLogger.info("user clicked PayNow Button");
        //here thread is using handle for  payment gateway
        //waitThread(12000);
    }

    @FindBy(xpath = "//*[@id='checkout-order-summary-section_QA']/div[1]/div[1]/div/div[*]/div/div[2]/p")
    List<WebElement> productDescription;

    @FindBy(xpath = "//*[@id='checkout-order-summary-section_QA']/div[1]/div[1]/div/div[*]/div/div[2]/div[1]/span[2]")
    List<WebElement> size;

    @FindBy(xpath = "//*[@id='checkout-order-summary-section_QA']/div[1]/div[1]/div/div[*]/div/div[2]/div[2]/span[2]")
    List<WebElement> color;

    @FindBy(xpath = "//*[@id='checkout-order-summary-section_QA']/div[1]/div[1]/div/div[*]/div/div[2]/div[3]/div[1]")
    List<WebElement> quantity;

    @FindBy(xpath = "//*[@id='checkout-order-summary-section_QA']/div[1]/div[1]/div/div[*]/div/div[2]/div[3]/div[2]")
    List<WebElement> actualprice;

    @FindBy(xpath = "//*[@id='checkout-order-summary-section_QA']/div[1]/div[2]/div[2]")
    WebElement subTotal;


    private JSONObject setCheckoutData() {
        JSONArray productDescriptionData = setListData(productDescription);
        JSONArray sizeData = setListData(size);
        JSONArray colorData = setListData(color);
        JSONArray productPriceData = setListData(actualprice);
        JSONArray quantityData = setListData(quantity);
        JSONObject checkout = new JSONObject();
        for (int i = 0; i < productDescriptionData.length(); i++) {
            JSONObject product = new JSONObject();
            product.put("productName", productDescriptionData.get(i).toString());
            product.put("size", sizeData.get(i).toString());
            //-- Need to fix Xpath to get the static data
            //product.put("color", colorData.get(i).toString());
            //product.put("price", (productPriceData.get(i).toString().replaceAll("[^0-9]", "")));
            //product.put("quantity", quantityData.get(i).toString());
            //checkout.put("product" + (i + 1), product);
        }
        checkout.put("subTotal", subTotal.getText().trim().replaceAll("[^0-9]", ""));
        // checkout.put("total", productTotalPrice.getText().trim().replaceAll("[^0-9]", ""));

         /*
         If we want to create a json and save the execution uncomment below code
           */
        try (FileWriter file = new FileWriter("outputCheckout.json")) {
            file.write(checkout.toString());
            System.out.println("JSON data has been saved to output.json");
        } catch (IOException e) {
            e.printStackTrace();
        }
        return checkout;
    }

    /**
     * Verify all UI components in checkout page
     */
    public void verifyAllUIComponentsInSelectYourShippingSpeed() {
        SoftAssertions sa = new SoftAssertions();
        //scrollDown(7);
        scrolldownElement(addressType);
        testLogger.info("select your shipping speed" + selectYourShippingSpeedText.getText().trim());
        testLogger.info("standardText " + standardText.getText().trim());
        testLogger.info("radioStandard  " + radioStandard.isSelected());
        testLogger.info("standard_shippingChargeLabel " + standard_shippingChargeLabel.getText().trim());
        testLogger.info("standard_delivery_msg " + standard_delivery_msg.getText().trim());
        testLogger.info("expressText " + expressText.getText().trim());
        testLogger.info("radioExpress " + radioExpress.isSelected());
        testLogger.info("express_shippingChargeLabel " + express_shippingChargeLabel.getText().trim());
        testLogger.info("express_delivery_msg " + express_delivery_msg.getText().trim());
        testLogger.info("superExpressText " + superExpressText.getText().trim());
        testLogger.info("radioSuperExpress " + radioSuperExpress.isSelected());
        testLogger.info("superExpress_shippingChargeLabel " + superExpress_shippingChargeLabel.getText().trim());
        testLogger.info("superExpress_delivery_msg " + superExpress_delivery_msg.getText().trim());
        scrolldownElement(selectYourShippingSpeedText);
        sa.assertThat(selectYourShippingSpeedText.getText().trim()).isEqualTo("Select your shipping speed");
        sa.assertAll();

    }

    /**
     * Click on cash on delivery raido button
     */

    public void clickOnCashOnDeliveryRadioButton() {
        if (config.getBrowser().trim().equalsIgnoreCase("CHROME") || config.getBrowser().trim().equalsIgnoreCase("CHROMEHEADLESS") || config.getBrowser().trim().equalsIgnoreCase("MOBILECHROME")) {
            waitForPageToLoad(2);
            //waitForElementPresent(1);
            scrollPageEnd();
            //waitForPageToLoad(2);
            waitForElementPresent(10);
            cashOnDeliveryRadioButton.click();
        } else {
            waitForPageToLoad(15);
            scrolldown();
            mCashOnDeliveryRadioButton.click();
        }
    }
    /**
     * Click on Place your order button for Cash on Delivery
     * */

    /**
     * This method is used to choose Shipping mode based on order values
     *
     * @param shippingModeValue
     * @return shipping option value
     */

    public String chooseShippingMode(String shippingModeValue) {
        String actual = null;
        if (shippingModeValue.equals("standard delivery")) {
            //waitForElementToBeClickable(radioStandard,15);
            elementMouseClick(radioStandard);
            actual = radioStandard.getAttribute("value").trim();
        } else if (shippingModeValue.equals("express delivery")) {
            elementMouseClick(radioExpress);
            actual = radioExpress.getAttribute("value").trim();
        } else if (shippingModeValue.equals("super express delivery")) {
            elementMouseClick(radioSuperExpress);
            actual = radioSuperExpress.getAttribute("value").trim();
        }
        testLogger.info("shipping text value radio" + actual);
        waitForWebElementPresent(shippingOptionType_Title, 10);
        String expected = shippingOptionType_Title.getText().trim();
        Assert.assertEquals(actual, expected, "shipping option mismatch as compared to order summary");
        return actual;
    }

    /**
     * This method is used to click on home delivery section
     */

    public void clickOnHomeDelivery() {
        homeDeliveryBtn.click();
    }


    /**
     * This method is used to enter full name
     *
     * @param name
     */
    public void updateFullName(String name) {
        fullNameTextBox.clear();
        fullNameTextBox.sendKeys(name);
    }


    public void updateMobileNumber(String number) throws IOException, ParseException {
        mobileNumberDetails("ae");
        // String number = generatePhoneNumberBasedOnCountry().trim();
        mobileInput.clear();
        mobileInput.sendKeys(number);
    }

    public void updateEmailId() {
        String email = randomGenerateEmail();
        emailInput.clear();
        emailInput.sendKeys(email);
    }

    /**
     * This method is used to enter building Name
     *
     * @param number
     */
    public void enterFloorApartmentNo(String number) {
        floorInput.sendKeys(number);
    }

    public void updateFloorApartmentNo(String number) {
        floorInput.clear();
        floorInput.sendKeys(number);
    }

    public void updateMakaniNumber(String number) {
        // moveToElementAndSendKeys(makaniInput,number);
        floorInput.clear();
        floorInput.sendKeys(number);
    }

    /**
     * verify Home delivery UI components
     */
    public void verifyHomeDeliveryLabels() {
        SoftAssertions sa = new SoftAssertions();
        sa.assertThat(homeDeliveryText.getText().trim()).isEqualTo("Home Deliveryt");
        sa.assertThat(deliveryToHomeMsg.getText().trim()).isEqualTo("Get your products delivered to your home");
        sa.assertAll();
    }

    /**
     * verify Click and Collect UI components
     */
    public void verifyClickAndCollectLabels() {
        SoftAssertions sa = new SoftAssertions();
        sa.assertThat(clickAndCollectText.getText().trim()).isEqualTo("Click & Collect");
        sa.assertThat(getCollectYourOrderFromMsg.getText().trim()).isEqualTo("Collect your order from a location of your choice");
        sa.assertAll();
    }

    /**
     * Verify default address check box message
     */

    public void defaultAddressCheckBox() {
        SoftAssertions sa = new SoftAssertions();
        sa.assertThat(defaultAddressMsg1.getText().trim()).isEqualTo("Use this as my default shipping address");
//        sa.assertThat(defaultAddressCheckBox.isSelected());
        sa.assertAll();
    }

    /**
     * click on default address click
     */
    public void defaultAddressClick() {
        waitForElementToBeClickable(defaultAddressCheckBox, 2);
        elementMouseClick(defaultAddressCheckBox);
    }

    public void closeRatingPopup() {
        waitForPageToLoad(7);
        waitForElementToBeClickable(ratingCloseIcon, 2);
        ratingCloseIcon.click();
        testLogger.info("user clicked on rating close icon");
    }

    public void clickOnShipToThisAddress() {
        shipToThisAddress.click();
    }

    public void clickOnShowAllMyAddresses() {
        waitForElementToBeClickable(showAllMyAddresses, 2);
        showAllMyAddresses.click();
    }

    public void checkFullName() {
        waitForWebElementPresent(fullNameFirstLabel, 15);
        String actual = fullNameFirstLabel.getText().trim();
        String name = AddressBean.getFullName();
        Assert.assertEquals(actual, name, "Full Name is not same in the list");
    }

    public void chooseShippingAddress() {
        int listAddress = listOfShippingAddress.size() - 1;
        String selectedAddressIndex = String.valueOf(listAddress);
        String pre = "//input[starts-with(@name,";
        String post = "'" + "shippingDetails_" + selectedAddressIndex + "'" + ")]";
        String shippedAddressIndex = pre + post;
        scrollElement(driver.findElement(By.xpath(shippedAddressIndex)));
        driver.findElement(By.xpath(shippedAddressIndex)).click();
    }

    public void choosePaymentCard() {
        scrollElement(payNow);
        cardFromList.click();
    }

    public void verifyCardUIComponents() {
    }

    public void enterCVVNumber() {
        cvvNumber.sendKeys("123");
    }

    public void clickOnAddNewCard() {
        scrollElement(payNow);
        addANewCardLink.click();
    }

    public void clickOnSaveThisCard() {
        saveThisCard.click();
    }

    public void validateEmailErrorMessage() {
        scrollElement(addYourShippingAddressSection);
        Assert.assertEquals("Enter your email address", enterYourEmailAddress.getText().trim(), "Email validation is missing");
        emailInput.sendKeys("test@g");
        Assert.assertEquals("Please enter a valid email address", pleaseEnterAValidEmailAddress.getText().trim(), "Email validation is missing");

    }

    public void validateFullNameErrorMessage() {
        Assert.assertEquals("Please enter your full name", pleaseEnterFullName.getText().trim(), "Email validation is missing");
        fullnameInput.sendKeys("w2");
        Assert.assertEquals("Please enter your full name using alphabet characters only", pleaseEnterFullNameUsingAlpha.getText().trim(), "Email validation is missing");

    }

    public void validateMobileNumberErrorMessage() {
        Assert.assertEquals("Please enter mobile number", pleaseEnterMobileNumber.getText().trim(), "mobile number validation is missing");
        mobileInput.sendKeys("5");
        Assert.assertEquals("Please enter a valid 9-digit mobile number.", valid9digitMobileNumber.getText().trim(), "mobile number validation is missing");


    }

    public void emirateErrorMessage() {
        Assert.assertEquals("Select your Emirate", selectYourEmirate.getText().trim(), "Select your Emirate validation is missing");

    }

    public void areaErrorMessage() {
        Assert.assertEquals("Select your area", selectYourArea.getText().trim(), "Select your area validation is missing");

    }

    public void buildingErrorMessage() {
        Assert.assertEquals("Please enter Building Name/House No.", pleaseEnterBuildingName.getText().trim(), "Please enter building validation is missing");
        buildingInput.sendKeys("ee");
        Assert.assertEquals("This field needs to have at least 5 characters", buildingValidation2.getText().trim(), "Please enter building validation is missing");

    }

    public void makaniErrorMessage() {
        streetNameNum.sendKeys("3");
        Assert.assertEquals("Please enter a 10-digit number without spaces", makaniValidation.getText().trim(), "Please enter makani validation is missing");


    }

    public void validateCardNumberErrorMessage() {
        Assert.assertEquals("Please enter a true digit number", pleaseEnterTrueDigitNumber.getText().trim(), "Please enter true digit number validation missing");
        cardnumInput.sendKeys("3");
        Assert.assertEquals("Please enter a valid card number", pleaseEnterAValidCardNumber.getText().trim(), "Please enter building validation is missing");

    }

    public void validateNameOnCardErrorMessage() {
        Assert.assertEquals("Please enter your full name", pleaseEnterCardFullNameValidation.getText().trim(), "Please enter card full name validation is missing");
        namecardInput.sendKeys("2");
        Assert.assertEquals("Please enter your full name using alphabet characters only", pleaseEnterCardValidation.getText().trim(), "Please enter card full name validation is missing");


    }

    public void validateExpiryMonthErrorMessage() {
        Assert.assertEquals("Please select a valid month", pleaseSelectAValidMonth.getText().trim(), "Please select a valid month");

    }

    public void validateExpiryYearErrorMessage() {
        Assert.assertEquals("Please select a valid year", pleaseSelectAValidYear.getText().trim(), "Please select a valid year validation is missing");

    }

    public void validateEnterYourCVV() {
        Assert.assertEquals("Enter your CVV", enterYourCVV.getText().trim(), "enter your cvv validation is missing");

    }

    public void VerifyClickAndCollectSectionDisplayed() {
        scrollToParticularElement(clickAndCollectBtn);
        SoftAssertions sa = new SoftAssertions();
        sa.assertThat(clickAndCollectBtn.isDisplayed());
        String logMessage = clickAndCollectBtn.isDisplayed() ? "is displayed" : "is not displayed";
        testLogger.info("Click and Collect section " + logMessage + " in Cart page");
        sa.assertAll();
    }

    public void clickOnclickAndCollectButton() {
        scrollToParticularElement(clickAndCollectBtn);
        waitForElementToBeClickable(clickAndCollectBtn, 2);
        clickAndCollectBtn.click();
        testLogger.info("Click on Click and Collection section in cart page");
    }

    public void VerifyClickAndCollectButton() {
        scrollToParticularElement(clickAndCollectBtn);
        SoftAssertions sa = new SoftAssertions();
        sa.assertThat(clickAndCollectBtn.isDisplayed());
        String logMessage = clickAndCollectBtn.isDisplayed() ? "is displayed" : "is not displayed";
        testLogger.info("Click and Collect section " + logMessage + " in Cart page");
        sa.assertThat(clickAndCollectButtonTitleMessage.getText().trim()).isEqualTo("Click & Collect");
        testLogger.info("Click and Collect Button title message - " + clickAndCollectButtonTitleMessage.getText().trim());
        sa.assertThat(clickAndCollectButtonSubTitleMessage.getText().trim()).isEqualTo("Collect your order from a location of your choice");
        testLogger.info("Click and Collect Button sub title displayed message - " + clickAndCollectButtonSubTitleMessage.getText().trim());
        sa.assertAll();
    }

    public void VerifyFindAPickupPointSection() {
        scrollToParticularElement(clickAndCollectBtn);
        SoftAssertions sa = new SoftAssertions();
        sa.assertThat(clickAndCollectFindAPickupPointTitle.isDisplayed());
        String logMessage = clickAndCollectFindAPickupPointTitle.isDisplayed() ? "is displayed" : "is not displayed";
        testLogger.info("Find a Pickup Point section " + logMessage + " in Cart page");
        sa.assertThat(clickAndCollectFindAPickupPointTitle.getText().trim()).isEqualTo("Find a pickup point");
        testLogger.info("Find a Pickup Point title message - " + clickAndCollectFindAPickupPointTitle.getText().trim());
        sa.assertThat(clickAndCollectFindAPickupPointSubTitle.getText().trim()).isEqualTo("Search or use our map view to find a pickup point");
        testLogger.info("Find a Pickup Point sub title displayed message - " + clickAndCollectFindAPickupPointSubTitle.getText().trim());
        sa.assertAll();
    }

    public void clickOnClickAndCollectSearchBox() {
        scrollToParticularElement(clickAndCollectBtn);
        SoftAssertions sa = new SoftAssertions();
        sa.assertThat(clickAndCollectSearchBox.isDisplayed());
        String logMessage = clickAndCollectSearchBox.isDisplayed() ? "is displayed" : "is not displayed";
        testLogger.info("Find a Pickup Point section " + logMessage + " in Cart page");
        clickAndCollectSearchBox.click();
        testLogger.info("Click Available search Box button is clicked");
        sa.assertAll();
    }

    public void enterDataOnClickAndCollectSearchBox(String searchData) {
        //waitForPageToLoad(10);
        waitForVisibilityOfElementLocated(clickAndCollectSearchBox, driver, 12);
        clickAndCollectSearchBox.clear();
        clickAndCollectSearchBox.sendKeys(searchData);
        testLogger.info("Click Available search Box entered data: " + clickAndCollectSearchBox.getText().trim());
    }

    public void enterDataOnClickAndCollectSearchBoxFromJson(String searchData) {
        //waitForPageToLoad(10);
        storeDetails(searchData);
        waitForVisibilityOfElementLocated(clickAndCollectSearchBox, driver, 12);
        clickAndCollectSearchBox.clear();
        clickAndCollectSearchBox.sendKeys(StoreDetailsBean.getStoreKeySearch());
        testLogger.info("Click Available search Box entered data: " + clickAndCollectSearchBox.getText().trim());
    }

    public void clearDataOnClickAndCollectSearchBox() {
        waitForVisibilityOfElementLocated(clickAndCollectSearchBox, driver, 2);
        clickAndCollectSearchBox.clear();
        testLogger.info("Click Available search Box is cleared");
    }

    public void clickOnClickAndCollectSearchBoxList() {
        //waitForVisibilityOfElementLocated(clickAndCollectStoreList, driver, 20);
        //waitForByElementPresent(,20);
        //pageRefresh();//Need to remove. Added for bulk run failure.
        waitForElementPresent(10);
        clickAndCollectStoreList.click();
        testLogger.info("Address clicked on Click and Collect search box");
    }

    public void clickOnClickAndCollectConfirmPickupPointButton() {
        if (config.getBrowser().trim().equalsIgnoreCase("CHROME") || config.getBrowser().trim().equalsIgnoreCase("CHROMEHEADLESS") || config.getBrowser().trim().equalsIgnoreCase("MOBILECHROME")) {
            waitForVisibilityOfElementLocated(clickAndCollectConfirmPickupPointButton, driver, 25);
            clickAndCollectConfirmPickupPointButton.click();
            testLogger.info("Click on confirm pickup point button in Click and Collect map");
        }
    }

    public void verifyOnClickAndCollectConfirmPickupPointMapLocation() {
        SoftAssertions sa = new SoftAssertions();
        sa.assertThat(clickAndCollectConfirmPickupPointMapLocation.isDisplayed());
        String logMessage = clickAndCollectConfirmPickupPointMapLocation.isDisplayed() ? "is displayed" : "is not displayed";
        testLogger.info("Verified confirm pickup point map location " + logMessage + " in Click and Collect section");
        sa.assertAll();
    }

    public void verifyOnClickAndCollectConfirmPickupPointMapStoreDetails() {
        testLogger.info("" + clickAndCollectConfirmPickupPointMapStoreDetails.getText());
        testLogger.info("Verified confirm pickup point map location in Click and Collect section");
    }

    public void verifyClickAndCollectAddYourContactDetails() {
        scrollToParticularElement(clickAndCollectEmailLabel);
        SoftAssertions sa = new SoftAssertions();
        sa.assertThat(clickAndCollectAddYourContactDetailsTitle.isDisplayed());
        String logMessage = clickAndCollectAddYourContactDetailsTitle.isDisplayed() ? "is displayed" : "is not displayed";
        testLogger.info("Add your contact details " + logMessage + " in Click and Collect page");
        sa.assertThat(clickAndCollectFullNameLabel.isDisplayed());
        logMessage = clickAndCollectFullNameLabel.isDisplayed() ? "is displayed" : "is not displayed";
        testLogger.info("Add your contact details - Full Name label " + logMessage + " in Click and Collect page");
        sa.assertThat(clickAndCollectMobileLabel.isDisplayed());
        logMessage = clickAndCollectMobileLabel.isDisplayed() ? "is displayed" : "is not displayed";
        testLogger.info("Add your contact details - Mobile label " + logMessage + " in Click and Collect page");
        sa.assertThat(clickAndCollectEmailLabel.isDisplayed());
        logMessage = clickAndCollectEmailLabel.isDisplayed() ? "is displayed" : "is not displayed";
        testLogger.info("Add your contact details - Email label " + logMessage + " in Click and Collect page");
        sa.assertAll();
    }

    public void enterFullNameInClickAndCollectAddYourContactDetails(String name) {
        waitForElementPresent(10);
        waitForElementToBeClickable(clickAndCollectFullNameTextBox, 2);
        scrollToParticularElement(clickAndCollectFullNameTextBox);
        clickAndCollectFullNameTextBox.click();
        clickAndCollectFullNameTextBox.clear();
        clickAndCollectFullNameTextBox.sendKeys(name);
        testLogger.info("Add your contact details - entered Full Name as " + name + " in Click and Collect page");
    }

    public void enterMobileInClickAndCollectAddYourContactDetails(String mobile) {
        waitForElementToBeClickable(clickAndCollectMobileTextBox, 2);
        clickAndCollectMobileTextBox.click();
        clickAndCollectMobileTextBox.clear();
        clickAndCollectMobileTextBox.sendKeys(mobile);
        testLogger.info("Add your contact details - entered Mobile Number as " + mobile + " in Click and Collect page");
    }

    public void enterEmailInClickAndCollectAddYourContactDetails() {
        String email1 = randomGenerateEmail();
        StepContext.emailIdValidation = email1;
        System.out.println("email id as" + StepContext.emailIdValidation);
        if (config.getBrowser().trim().equalsIgnoreCase("CHROME") || config.getBrowser().trim().equalsIgnoreCase("CHROMEHEADLESS") || config.getBrowser().trim().equalsIgnoreCase("MOBILECHROME")) {
            scrollToParticularElement(clickAndCollectSelectAPaymentMethodSection);
        }
        String textInsideInputBox = clickAndCollectEmailTextBox.getAttribute("value");
        if (textInsideInputBox.isEmpty()) {
            if (verifyNoelement(addYourContactDetails)) {
                scrollElement(addYourContactDetails);
            } else {
                scrollElement(addYourShippingAddressSection);
            }
            //scrolldownElement(addYourShippingAddressSection);
            waitForElementToBeClickable(clickAndCollectEmailTextBox, 2);
            clickAndCollectEmailTextBox.click();
            clickAndCollectEmailTextBox.sendKeys(email1);
        }
        testLogger.info("Add your contact details - entered Email as " + email1 + " in Click and Collect page");
    }


    public void verifyClickAndCollectYourOrderSummaryDetails() {
        scrollToParticularElement(clickAndCollectBtn);
        SoftAssertions sa = new SoftAssertions();
        sa.assertThat(clickAndCollectYourOrderSummaryTitle.isDisplayed());
        String logMessage = clickAndCollectYourOrderSummaryTitle.isDisplayed() ? "is displayed" : "is not displayed";
        testLogger.info("Your Order Summary details " + logMessage + " in Click and Collect page");
        testLogger.info("" + clickAndCollectCheckoutOrderSummaryDetails.getText());
        testLogger.info("Verified Checkout Order Summary details in Click and Collect section");
        sa.assertAll();
    }

    public void verifyDefaultAddressNameFromCheckoutPage() {
        waitForWebElementPresent(defaultAddressName, 15);
        String checkOutDefaultName = defaultAddressName.getText().trim();
        System.out.println("Default address name in checkout page as" + checkOutDefaultName);
        System.out.println("Default address name in my address as" + StepContext.defaultAddress);
        SoftAssert sa = new SoftAssert();
        sa.assertEquals(checkOutDefaultName, StepContext.defaultAddress, "Default address name both are not same");
        sa.assertAll();

    }

    public void deliveryTypeMethodValidateErrorMessage(String deliveryType) {
        if (deliveryType.equals("click & Collect")) {
            if (config.getBrowser().trim().equalsIgnoreCase("CHROME") || config.getBrowser().trim().equalsIgnoreCase("CHROMEHEADLESS") || config.getBrowser().trim().equalsIgnoreCase("MOBILECHROME")) {
                waitForElementPresent(5);
                waitForPageToLoad(25);
                mouseHoverClick(clickAndCollectText);
//                clickAndCollectText.click();
                waitForPageToLoad(25);
                payNowClick();
                waitForPageToLoad(25);
                SoftAssertions sa = new SoftAssertions();
                scrollToParticularElement(clickAndCollectText);
                // waitForPageToLoad(50);
                ////scrolldown();
                //waitThread(10);
                waitForElementPresent(15);
                scrollToParticularElement(fullNameText);
                //waitForPageToLoad(25);
                //scrollToParticularElement(cardnumInput);
                //waitForPageToLoad(50);
                waitForVisibilityOfElementLocated(pleaseSelectAPickupPointErrorMsg, driver, 40);
                sa.assertThat(pleaseSelectAPickupPointErrorMsg.getText().trim()).isEqualTo(configProperties.getProperty("lmgform.field.required.pickupPoint"));
                sa.assertThat(pleaseEnterYourFullNameErrorMsg.getText().trim()).isEqualTo(configProperties.getProperty("lmgform.required.fullname"));
                sa.assertThat(pleaseEnterMobileNumberErrorMsg.getText().trim()).isEqualTo(configProperties.getProperty("landMarkAddressBook.address.enter.mobile1"));
                sa.assertThat(enterYourEmailAddressErrorMsg.getText().trim()).isEqualTo(configProperties.getProperty("login.signup.email.required"));
                //sa.assertThat(pleaseEnterATrueDigitNumberErrorMsg.getText().trim()).isEqualTo(configProperties.getProperty("payment.valid.card.error1"));
                sa.assertThat(pleaseSelectAValidExpiryDateErrorMsg.getText().trim()).isEqualTo(configProperties.getProperty("payment.select.date"));
                //sa.assertThat(pleaseSelectAValidMonthErrorMsg.getText().trim()).isEqualTo(configProperties.getProperty("payment.select.month"));
                //sa.assertThat(pleaseSelectAValidYearErrorMsg.getText().trim()).isEqualTo(configProperties.getProperty("payment.select.year"));
                sa.assertThat(enterYourCVVErrorMsg.getText().trim()).isEqualTo(configProperties.getProperty("payment.enter.cvv"));
                sa.assertThat(pleaseEnterYourFullNameCardErrorMsg.getText().trim()).isEqualTo(configProperties.getProperty("payment.enter.fullname"));
                sa.assertAll();
            } else {
//                mClickAndCollectText.click();
//                SoftAssertions sa = new SoftAssertions();
//                mProceedToPayment.click();
//
//                mProceedToPayment.click();
//                payNowClick();
//
//
//                sa.assertAll();
            }
        } else if (deliveryType.equals("Home Delivery")) {
            if (config.getBrowser().trim().equalsIgnoreCase("CHROME") || config.getBrowser().trim().equalsIgnoreCase("CHROMEHEADLESS") || config.getBrowser().trim().equalsIgnoreCase("MOBILECHROME")) {
                waitForElementPresent(5);
                waitForPageToLoad(25);
                mouseHoverClick(homeDeliveryBtn);
//                homeDeliveryBtn.click();
                waitForPageToLoad(25);
                payNowClick();
                waitForPageToLoad(25);
                SoftAssertions sa = new SoftAssertions();
                //scrollToParticularElement(homeDeliveryBtn);
                //waitForVisibilityOfElementLocated(selectYourCityErrorMsg,driver,20);
                sa.assertThat(selectYourCityErrorMsg.getText().trim()).isEqualTo(configProperties.getProperty("myAccountPage.addAddress.error.city"));
                sa.assertThat(selectYourAreaErrorMsg.getText().trim()).isEqualTo(configProperties.getProperty("myAccountPage.addAddress.error.area"));
                sa.assertThat(pleaseEnterBuildingNameErrorMsg.getText().trim()).isEqualTo(configProperties.getProperty("myAccountPage.addAddress.error.buildingName"));
                sa.assertThat(pleaseEnterYourAddressErrorMsg.getText().trim()).isEqualTo(configProperties.getProperty("myAccountPage.addAddress.error.streetName"));
                sa.assertThat(HDPleaseEnterYourFullNameErrorMsg.getText().trim()).isEqualTo(configProperties.getProperty("lmgform.required.fullname1"));
                sa.assertThat(HDPleaseEnterMobileNumberErrorMsg.getText().trim()).isEqualTo(configProperties.getProperty("landMarkAddressBook.address.enter.mobile"));
                sa.assertThat(HDEnterYourEmailAddressErrorMsg.getText().trim()).isEqualTo(configProperties.getProperty("login.signup.email.required"));
                sa.assertThat(pleaseEnterATrueDigitNumberErrorMsg.getText().trim()).isEqualTo(configProperties.getProperty("payment.valid.card.error1"));
                sa.assertThat(pleaseSelectAValidExpiryDateErrorMsg.getText().trim()).isEqualTo(configProperties.getProperty("payment.select.date"));
                //sa.assertThat(pleaseSelectAValidMonthErrorMsg.getText().trim()).isEqualTo(configProperties.getProperty("payment.select.month"));
                //sa.assertThat(pleaseSelectAValidYearErrorMsg.getText().trim()).isEqualTo(configProperties.getProperty("payment.select.year"));
                sa.assertThat(enterYourCVVErrorMsg.getText().trim()).isEqualTo(configProperties.getProperty("payment.enter.cvv"));
                sa.assertThat(pleaseEnterYourFullNameCardErrorMsg.getText().trim()).isEqualTo(configProperties.getProperty("payment.enter.fullname"));
                sa.assertAll();
            } else {
//                mHomeDeliveryText.click();
//                SoftAssertions sa = new SoftAssertions();
//                mProceedToPayment.click();
//
//                mProceedToPayment.click();
//                payNowClick();
//
//
//                sa.assertAll();
            }
        }

    }

    public void generateGiftCardUsingAPI(String amount) {
        Response response = GiftCardCreation.generateGiftCard(amount);
        String giftCardNumber = response.jsonPath().getString("barcodeNumber").trim();
        String pinNumber = response.jsonPath().getString("pinCode").trim();
        updateGiftCardDetails(giftCardNumber, pinNumber, amount);

    }

    public void generateGiftCardUsingAPI() {
        String amount = "10000"; //Once the total element identifier is fixed we will make it dynamic.
        Response response = GiftCardCreation.generateGiftCard(amount);
        String giftCardNumber = response.jsonPath().getString("barcodeNumber").trim();
        String pinNumber = response.jsonPath().getString("pinCode").trim();
        updateGiftCardDetails(giftCardNumber, pinNumber, amount);

    }

    public String getLastGiftCardNo() {
        return getLastGiftCardNumber();
    }

    public String getLastGiftCardPin() {
        return getLastGiftCardNumberPin();
    }

    @FindBy(xpath = "//*[@id='Secure3dsForm_password']")
    private WebElement otpPasswordTextBoxPayFort;

    @FindBy(xpath = "//*[@id='password']")
    private WebElement otpPasswordTextBoxCheckoutDotCom;
    @FindBy(xpath = "//*[@id='submit-simulator']")
    private WebElement otpConfirmButtonPayFort;

    @FindBy(xpath = "//*[@id='txtButton']")
    private WebElement otpConfirmButtonCheckoutDotCom;


    @FindBy(xpath = "//*[@id='cko-3ds2-challenge-iframe']")
    private WebElement pageCheckoutDotComDisplayed;



    public void enterValidOTPAndClickOnConfirmButton() {

        if(isElementDisplayed(otpPasswordTextBoxPayFort)){
            otpPasswordTextBoxPayFort.sendKeys(CardDataBean.getOtp());
            otpConfirmButtonPayFort.click();
            waitForPageToLoad(20);
        }else if (isElementDisplayed(pageCheckoutDotComDisplayed)){
            //switchFromIFrame();
            //switchFromIFrame();
            waitForPageToLoad(100);
            waitForElementPresent(40);
            waitForElementToBeClickable(pageCheckoutDotComDisplayed,driver,30);
            switchToIFrame(pageCheckoutDotComDisplayed);
            //switchToIFrameAndSendKey(pageCheckoutDotComDisplayed,CardDataBean.getOtp());
            otpPasswordTextBoxCheckoutDotCom.sendKeys(CardDataBean.getOtp());
            otpConfirmButtonCheckoutDotCom.click();
            //waitForPageToLoad(10);
            switchFromIFrame();
            waitForPageToLoad(10);
        }


    }

    @FindBy(xpath = "//*[@id='QA_DateSlots']/div[3]")
    private WebElement chooseDeliveryDate;
    @FindBy(xpath = "//div[@id='QA_DateSlots']/../../../following::div/div/div[2]/div[1]/div[1]")
    private WebElement chooseDeliveryTimeSlot;


    public void selectGdmsSlot() {

        if(verifyNoelement(chooseDeliveryDate)){
            chooseDeliveryDate.click();
            waitForElementPresent(3);
            scrolldownElement(chooseDeliveryDate);
            chooseDeliveryTimeSlot.click();
        }

    }
}
