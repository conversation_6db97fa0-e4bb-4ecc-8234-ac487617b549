package com.lmg.blc.web.PageObjects;


import com.landmarkshops.Web_Mweb.config.config;
import com.landmarkshops.Web_Mweb.utilities.StepContext;
import com.landmarkshops.Web_Mweb.utilities.WebUtils;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.Select;
import org.testng.Assert;

import java.security.PublicKey;
import java.util.List;

public class SignUpPage extends WebUtils {

//	GregorianCalendar date = new GregorianCalendar();
//	int hour = date.get(Calendar.HOUR_OF_DAY);
//	int second = date.get(Calendar.SECOND);
//	int minute = date.get(Calendar.MINUTE);

    public SignUpPage(WebDriver driver) {
        super(driver);
        PageFactory.initElements(driver, this);

    }

    @FindBy(xpath = "//html/body/div[2]/div[1]/h1")
    private WebElement signUpNowTitle;

    @FindBy(xpath = "//html/body/div[2]/div[1]/h2")
    private WebElement signUpNowSubTitle;

    @FindBy(css = "body > div.tw-container.tw-mx-auto.tw-h-full.tw-flex.tw-justify-center.tw-items-center.lmg-container > div.lmg-main-content > div:nth-child(1) > ul > li > a > span")
    private WebElement signUpWithFaceBookCTA;

    @FindBy(xpath = "//html/body/div[2]/div[3]/div[1]/div[1]/div/div[2]")
    private WebElement orViaEmail;
    @FindBy(xpath = "//*[@id='registerForm']/fieldset/div[1]/div[1]/label")
    private WebElement firstNameLabel;

    @FindBy(id = "firstName")
    private WebElement firstNamePlaceHolder;

    @FindBy(id = "firstName")
    private WebElement firstNameInput;
    @FindBy(id = "firstname-required")
    private WebElement firstNameRequired;

    @FindBy(id = "firstname-invalid")
    private WebElement firstNameInvalid;
    @FindBy(xpath = "//*[@id='registerForm']/fieldset/div[1]/div[2]/label")
    private WebElement lastNameLabel;

    @FindBy(id = "lastName")
    private WebElement lastNamePlaceHolder;

    @FindBy(id = "lastName")
    private WebElement lastNameInput;
    @FindBy(id = "lastname-required")
    private WebElement lastNameRequired;

    @FindBy(id = "lastname-invalid")
    private WebElement lastNameInvalid;

    @FindBy(xpath = "//*[@id='registerForm']/fieldset/div[2]/label")
    private WebElement emailLabel;
    @FindBy(id = "email")
    private WebElement emailInput;

    @FindBy(id = "email")
    private WebElement emailPlaceHolder;

    @FindBy(id = "emailregister-required")
    private WebElement emailRequired;
    @FindBy(id = "emailregister-invalid")
    private WebElement emailInvalid;
    @FindBy(xpath = "//label[contains(text(),'Password')]")
    private WebElement passwordLabel;
    @FindBy(id = "password")
    private WebElement password;

    @FindBy(id = "password")
    private WebElement passwordPlaceHolder;
    @FindBy(id = "passwordConfirmation")
    private WebElement confirmPassword;

    @FindBy(id = "passwordConfirmation")
    private WebElement confirmPasswordPlaceHolder;

    @FindBy(xpath = "//label[contains(text(),'Confirm Password')]")
    private WebElement cofirmPasswordLabel;

    @FindBy(id = "registerForm_submit")
    private WebElement signUPCTA;

    @FindBy(xpath = "//iframe[@title='reCAPTCHA']")
    private WebElement captchaIFrame;

    @FindBy(xpath = "//*[@class='recaptcha-checkbox-border']")
    private WebElement captchaIFrameCheckBox;


    @FindBy(xpath = "//*[@class='rc-anchor-error-msg']")
    private WebElement captchaIFrameCheckBoxErrorMsg;

    @FindBy(xpath = "*[@id='registerForm']/fieldset/div[6]/div[1]/span/div/label")
    private WebElement countryCode;

    @FindBy(xpath = "//*[@id='registerForm']/fieldset/div[6]/label")
    private WebElement mobileLabel;

    @FindBy(id = "mobileNumber")
    private WebElement mobilePlaceHolder;

    @FindBy(id = "mobileNumber")
    private WebElement mobileInput;

    @FindBy(xpath = "//*[@id='btn-mobile-verify']")
    private WebElement mobileVerificationButton;

    @FindBy(xpath = "//*[@id='registerForm']/div[4]/span/span[1]")
    private WebElement createYourAccount;

    @FindBy(xpath = "//*[@id='main_inbox_area']/div[4]/div[@class='os-viewport os-viewport-native-scrollbars-invisible os-viewport-native-scrollbars-overlaid']/div/div")
    private WebElement maxFashionRecord;

   /* xpath for mweb
    //*[@id="main_inbox_area"]/div[4]/div[@class='os-viewport os-viewport-native-scrollbars-invisible os-viewport-native-scrollbars-overlaid']/div/div
*/
    /*
	@FindBy(linkText = "Reset Password")
	private WebElement resetPassword;
*/

    @FindBy(css = "body > center > table > tbody > tr:nth-child(2) > td > table > tbody > tr:nth-child(2) > td > table > tbody > tr > td > table > tbody > tr:nth-child(1) > td > table > tbody > tr > td > table:nth-child(1) > tbody > tr > td > div > a")
    private WebElement resetPassword;


    @FindBy(css = "#row_maxotpvalidation-**********-********* > td:nth-child(2)")
    private WebElement resetPasswordAR;


    @FindBy(id = "firstname-invalid")
    private WebElement firstNameInValid;


    @FindBy(id = "emailregister-required")
    private WebElement emailRegisterRequired;


    @FindBy(id = "password-progress")
    private WebElement passwordProgress;


    @FindBy(id = "password-strength-text")
    private WebElement passwordStrength;


    @FindBy(id = "password-validation-short")
    private WebElement passwordValidationShort;

    @FindBy(id = "password-validation-upper")
    private WebElement passwordValidationUpper;

    @FindBy(id = "password-validation-special")
    private WebElement passwordValidationSpecial;

    @FindBy(id = "password-validation-digit")
    private WebElement passwordValidationDigit;


    @FindBy(id = "password-validation-lower")
    private WebElement passwordValidationLower;

    @FindBy(id = "password-required")
    private WebElement passwordRequired;

    @FindBy(id = "confirmpassword-required")
    private WebElement confirmPasswordRequired;

    @FindBy(id = "confirmpassword-mismatch")
    private WebElement confirmPasswordMismatch;

    @FindBy(id = "show-password")
    private WebElement showPassword;

    @FindBy(id = "show-confirmpassword")
    private WebElement confirmShowPassword;


    @FindBy(id = "mobile-required")
    private WebElement mobileNumberRequired;

    @FindBy(id = "mobile-invalid")
    private WebElement mobileNumberInvalid;


    @FindBy(xpath = "//*[@id='registerForm']/div[4]/span/span[2]/a")
    private WebElement termsAndConditions;


    @FindBy(xpath = "//html/body/div[2]/div[3]/div[1]/div[2]/div/span")
    private WebElement alreadyHaveAnAccount;

    @FindBy(xpath = "//html/body/div[2]/div[3]/div[1]/div[2]/a")
    private WebElement signInNowCTA;


    // date of birth

    @FindBy(name = "dateOfBirthDay")
    private WebElement date;

    @FindBy(name = "dateOfBirthMonth")
    private WebElement month;


    /**
     * This method is used to enter valid email
     */
    public String enterEmail() {
        String email = randomGenerateEmail();

        emailInput.sendKeys(email);
        return email;
    }

    /**
     * This method is used to enter valid email
     */
    public String enterInvalidEmail(String email) {
        emailInput.sendKeys(email);
        return email;
    }


    /**
     * This method is used to enter password
     */
    public void enterPassword(String pass) {
        password.sendKeys(pass);
    }

    /**
     * This method is used to confirm password
     */
    public void confirmPassword(String confPassword) {
        confirmPassword.sendKeys(confPassword);
    }

    /**
     * This method is used to click on register button for signUp
     */


    public void clickRegisterButton() {
        waitForPageToLoad(50);
//        while (verifyNoelement(captchaIFrame)) {
//            waitForElementPresent(2);
//            switchToIFrame(captchaIFrame);
//            captchaIFrameCheckBox.click();
//            waitForElementPresent(10);
//            switchFromIFrame();
//
//        }
        waitForElementToBeClickable(signUPCTA, driver, 10);
        mouseHoverClick(signUPCTA);

    }


    /**
     * This method is used to wait for user landed to particular page
     */
    public void verifyUserLandedToSignUpScreen() {
        waitForElementPresent(3);
        waitForPageToLoad(25);
        waitForWebElementPresent(signUpNowTitle, 15);
    }

    public void enterFirstName(String firstname) {
        firstNameInput.sendKeys(firstname);
    }

    public void enterLastName(String lName) {

        lastNameInput.sendKeys(lName);
    }

    public void navigateToMailinatorSite(String url) {
        navigateToParticularPage(url);
        waitThread(5000);
    }

    public void enterPhoneNumber(String phone) {
        mobileInput.sendKeys(phone);
        waitForElementPresent(2);
        testLogger.info("Entered phone Number: " + phone);
    }


    public void verifyEmailFromMailinator() {
        waitForElementPresent(5);
        mouseHoverClick(maxFashionRecord);

        waitForElementPresent(5);
        switchToIFrame(driver, "html_msg_body");
        if (config.getLanguage().equalsIgnoreCase("ar")) {
            waitForWebElementPresent(resetPasswordAR, driver, 15);
            String reset = resetPasswordAR.getText().trim();
            System.out.println("reset password text" + reset);
            mouseHoverClick(resetPasswordAR);
            System.out.println("click on arabic reset password link");
        } else {
            waitForWebElementPresent(resetPassword, driver, 15);
            String reset = resetPassword.getText().trim();
            System.out.println("reset password text" + reset);
            mouseHoverClick(resetPassword);
        }
        driver.switchTo().defaultContent();

    }

    public void verifyRequiredErrorMessages() {
        clickRegisterButton();
        Assert.assertEquals(firstNameRequired.getText().trim(), "Enter your first name", "Enter your first name text mismatch");
        Assert.assertEquals(lastNameRequired.getText().trim(), "Enter your last name", "Enter your last name text mismatch");
        Assert.assertEquals(emailRegisterRequired.getText().trim(), "Enter your email address", "Enter your email address text mismatch");
        Assert.assertEquals(passwordRequired.getText().trim(), "Enter your password", "Enter your password name text mismatch");
        Assert.assertEquals(confirmPasswordRequired.getText().trim(), "Please confirm your password", "EPleaser confirm your password text mismatch");
        Assert.assertEquals(mobileNumberRequired.getText().trim(), "Please enter your mobile number", "Please enter your mobile number text mismatch");


    }

    public void verifyUIComponents() {
        Assert.assertEquals(firstNameLabel.getText().trim(), "First Name", "First Name text mismatch");
        Assert.assertEquals(lastNameLabel.getText().trim(), "Last Name", "Last Name text mismatch");
        Assert.assertEquals(emailLabel.getText().trim(), "E-mail", "E-mail address text mismatch");
        Assert.assertEquals(passwordLabel.getText().trim(), "Password", "Password text mismatch");
        Assert.assertEquals(cofirmPasswordLabel.getText().trim(), "Confirm Password", "Confirm Password text mismatch");
        Assert.assertEquals(mobileLabel.getText().trim(), "Mobile", "Mobile text mismatch");
        Assert.assertEquals(createYourAccount.getText().trim(), "By creating your account you agree to our", "By creating your account you agree to our  text mismatch");
        Assert.assertEquals(termsAndConditions.getText().trim(), "Terms and Conditions", "Terms and Conditions text mismatch");
        Assert.assertEquals(alreadyHaveAnAccount.getText().trim(), "ALREADY HAVE AN ACCOUNT ?", "ALREADY HAVE AN ACCOUNT ? text mismatch");
        Assert.assertEquals(signInNowCTA.getText().trim(), "Sign In Now", "Sign In Now text mismatch");
    }

    public void firstNameInvalidMessage() {
        enterFirstName("23");
        waitForWebElementPresent(firstNameInValid, 15);
        Assert.assertEquals(firstNameInValid.getText().trim(), "Please enter alphabets only", "Please enter alphabets only text mismatch");

    }

    public void lastNameInvalidMessage() {
        enterLastName("45");
        waitForWebElementPresent(lastNameInvalid, 15);
        Assert.assertEquals(lastNameInvalid.getText().trim(), "Please enter alphabets only", "Please enter alphabets only text mismatch");

    }

    public void emailAddressValidation() {
        enterInvalidEmail("testemail");
        Assert.assertEquals(emailInvalid.getText().trim(), "Please Enter a valid email address", "Please Enter a valid email address text mismatch");

    }

    public void passwordStrengthValidation() {
        enterPassword("test");
        Assert.assertEquals(passwordStrength.getText().trim(), "Very weak", "Very weak text mismatch");
        Assert.assertEquals(passwordValidationShort.getText().trim(), "Must be at least 8 characters", "Must be at least 8 characters text mismatch");
        enterPassword("sh");
        Assert.assertEquals(passwordStrength.getText().trim(), "Weak", "Weak text mismatch");
        enterPassword("ort");
        Assert.assertEquals(passwordStrength.getText().trim(), "Medium", "Medium text mismatch");
        Assert.assertEquals(passwordValidationUpper.getText().trim(), "Must include at least 1 uppercase character", "must include at least 1 uppercase character text mismatch");
        Assert.assertEquals(passwordValidationSpecial.getText().trim(), "Must include at least 1 special character", "must include at least 1 special character text mismatch");
        Assert.assertEquals(passwordValidationDigit.getText().trim(), "Must include at least 1 number", "must include at least 1 number text mismatch");
        enterPassword("1");
        Assert.assertEquals(passwordStrength.getText().trim(), "Good", "Good text mismatch");
        waitForElementPresent(2);
        enterPassword("@A");
        Assert.assertEquals(passwordStrength.getText().trim(), "Strong", "Good text mismatch");


    }

    public void passwordDidNotMatchValidation() {
        confirmPassword("confirm");
        Assert.assertEquals(confirmPasswordMismatch.getText().trim(), "Passwords did not match", "Passwords did not match text mismatch");
    }

    public void mobileNumberInvalidText() {
        enterPhoneNumber("100000000");
        Assert.assertEquals(mobileNumberInvalid.getText().trim(), "Please enter a valid 10-digit mobile number", "Please enter a valid 10-digit mobile number text mismatch");


    }

    public void verifyRequiredInvalidErrorMessages() {
        firstNameInvalidMessage();
        lastNameInvalidMessage();
        enterInvalidEmail("invalid");
        passwordStrengthValidation();
        passwordDidNotMatchValidation();
        mobileNumberInvalidText();

    }

    public void showPassword() {
        Assert.assertTrue(showPassword.isDisplayed(), "show password is not displayed");
        Assert.assertTrue(confirmPassword.isDisplayed(), "confirm show password is not displayed");
        String bShowPssword = password.getText().trim();
        System.out.println("Before click show password ,password as" + bShowPssword);
        showPassword.click();
        confirmPassword.click();
        String aShowPssword = showPassword.getText().trim();
        System.out.println("Before click show password ,password as" + aShowPssword);

    }

    public void clickOnSignInNow() {
        waitForElementToBeClickable(signInNowCTA, driver, 15);
        signInNowCTA.click();
    }


    public void clickTermsAndConditions() {
        waitForElementToBeClickable(termsAndConditions, driver, 15);
        termsAndConditions.click();

    }

    public void chooseDateOfBirth() {
        scrollPageEnd();
        chooseDate();
        chooseMonth();
    }

    private void chooseMonth() {

        Select sel = new Select(month);
        sel.selectByValue("05");

    }

    private void chooseDate() {
        Select sel = new Select(date);
        sel.selectByValue("27");

    }

    public void clickOnSignUpWithFaceBook() {
        waitForElementToBeClickable(signUpWithFaceBookCTA, 15);
        signUpWithFaceBookCTA.click();

    }

    public void enterFullName() {
    }

    public void verifySignUpWithFaceBook() {
        waitForVisibilityOfElementLocated(signUpWithFaceBookCTA, driver, 15);
        waitForElementToBeClickable(signUpWithFaceBookCTA, driver, 15);
        signUpWithFaceBookCTA.click();
    }

    public void clickOnVerifyLink() {
        waitForElementPresent(2);
       // driver.findElementByAndroidUIAutomator("new UiScrollable(new UiSelector().scrollable(true).instance(0)).scrollIntoView(new UiSelector().textContains('Verify').instance(0))").click();
        mouseHoverClick(mobileVerificationButton);
        waitForElementPresent(5);

    }

    @FindBy(xpath = "//*[@id='verify-mobile-section']/div/div[3]/input")
    private List<WebElement> verifyYourNumberOTPTextBoxList;

    public void enterOTP(String otp) {
        enterOTP(verifyYourNumberOTPTextBoxList, otp);
    }

    @FindBy(xpath = "//*[@id='btn-confirm-verify-otp']")
    private WebElement confirmButtonInVerifyYourNumberPopUp;

    public void clickConfirmButtonInVerifyYourNumberPopUp() {
        confirmButtonInVerifyYourNumberPopUp.click();
    }
}
