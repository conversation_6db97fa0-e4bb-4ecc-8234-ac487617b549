package com.lmg.blc.web.PageObjects;

import com.landmarkshops.Web_Mweb.config.config;
import com.landmarkshops.Web_Mweb.utilities.StepContext;
import com.landmarkshops.Web_Mweb.utilities.WebUtils;
import org.assertj.core.api.SoftAssertions;
import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;

import java.util.List;

import static com.landmarkshops.Web_Mweb.launcher.TestHarness.configProperties;

public class OrderHistory extends WebUtils {
    public OrderHistory(WebDriver driver) {
        super(driver);
        PageFactory.initElements(driver,this);
    }

    @FindBy(id = "my-acc-head_QA")
    private WebElement ordersText;

    @FindBy(id = "my-acc-head-desc_QA")
    private WebElement ViewAndTrackYourOrders;

    @FindBy(xpath = "//*[@id='order-list-item_QA']/div[1]/div[1]")
    private WebElement orderDateAndTime;

    @FindBy(id = "orderListingThumbnail_QA")
    private WebElement orderedProductImage;

    @FindBy(xpath = "//*[@id='orderListingOrderNumber_QA']/div[1]")
    private WebElement orderNumberText;

    @FindBy(xpath = "(//*[@id='orderListingOrderNumber_QA']/div[2])[1]")
    private WebElement orderId;

    @FindBy(xpath = "//*[@id='orderListingOrderTotalMob_QA']/div[1]")
    private WebElement totalText;

    @FindBy(xpath = "//*[@id='orderListingOrderTotalMob_QA']/div[2]")
    private WebElement totalValue;

    @FindBy(xpath = "//*[@id='orderListingOrderStatusMob_QA']/div[1]")
    private WebElement statusText;

    @FindBy(xpath = "//*[@id='orderListingOrderStatusMob_QA']/div[2]/span")
    private WebElement orderStatusValue;

    @FindBy(xpath = "//*[@id='orderListingPaymentWrap_QA']/div[1]")
    private WebElement paymentText;

    @FindBy(id = "orderListingPaymentType-Card-Vi_QA")
    private WebElement paymentCardImage;

    @FindBy( id = "orderListingPaymenType-Card-Number_QA")
    private WebElement paymentCardNumber;

    @FindBy(xpath = "//*[@id='orderListingShipmentReceiver_QA']/div[1]")
    private WebElement shipToText;

    @FindBy(xpath = "//*[@id='orderListingShipmentReceiver_QA']/div[2]")
    private WebElement shipToUserName;

    @FindBy(xpath = "//*[@id='orderListingViewOrderDetailsLink_QA']")
    private WebElement viewDetails;

    @FindBy(xpath = "//*[@id='main-part']/div/div[1]/div[3]/div")
    private List<WebElement> placedOrdersPage;

    @FindBy(xpath = "//*[@id='order-list-item_QA']")
    private List<WebElement> placedOrdersCount;

    @FindBy(id = "orderHistoryNextBtnEnabled_QA")
    private WebElement next;

    @FindBy(id = "orderHistoryNextBtnEnabled_QA")
    private WebElement mNext;

    @FindBy(xpath = "//*[@id='myAcc-topNav_QA']")
    private WebElement myAccountHeader;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[2]/div[1]/img")
    private WebElement orderLogo;

    @FindBy(xpath = "//*[@id='order-history-select-interval_QA']")
    private WebElement pastOrdersOption;

    @FindBy(xpath = "//*[@id='menu-order-history-interval']/div[3]/ul")
    private List<WebElement> pastOrdersOptionDrownDown;

    @FindBy(id = "myAcc-topNav-history_QA")
    private WebElement purchases;

//    @FindBy(xpath = "//*[text()='In-store']")
//    private WebElement instoreTab;

    @FindBy(xpath = "//*[@id='main-part']/div/div[2]/div/div[1]/div/a[2]")
    private WebElement instoreTab;



    @FindBy(css = "button.viewDetailsBtn>span")
    private WebElement instoreViewDetails;

    @FindBy(id = "jnShukran")
    private WebElement shukranJoinNow;

    @FindBy(css = "div.dateTimePanel>p")
    private WebElement purchaseDateNtime;

    @FindBy(xpath = "//p[contains(@class,'locationName')]/preceding-sibling::*")
    private WebElement purchaseLocationText;

    @FindBy(xpath = "//p[contains(@class,'locationName')]")
    private WebElement purchaseLocation;
    
    @FindBy(xpath = "//p[contains(@class,'total')]/preceding-sibling::*")
    private WebElement purchaseTotalText;

    @FindBy(xpath = "//p[contains(@class,'total')]")
    private WebElement purchaseTotal;
    
    @FindBy(xpath = "//p[contains(@class,'status')]/preceding-sibling::*")
    private WebElement purchaseStatusText;

    @FindBy(xpath = "//p[contains(@class,'status')]")
    private WebElement purchaseStatus;

    @FindBy(xpath = "//h2")
    private WebElement nonShukraninfoTitle;

    @FindBy(css = "a.linkShukranBtn")
    private WebElement linkShukranButton;

    @FindBy(css = "a.registerLink")
    private WebElement registerLinkButton;

    @FindBy(xpath = "//h2")
    private WebElement ShukranUserinfoTitle;
    
    @FindBy(xpath = "//*[@id='main-part']//p")
    private WebElement ShukranUserinfosubTitle;

    @FindBy(css ="#main-part > div > div.MuiBox-root.jss98.jss70 > div > div.MuiBox-root.jss109.jss104 > div > div > div > ul > li:nth-child(1) > div > div.topStrip > div > p")
    private WebElement mCompleted;

    @FindBy(xpath= "//ul[contains(@class,'transactionList')]/li[1]/div/div[1]")
    private WebElement  mOfflineOrder_Images;

    @FindBy(xpath= "//ul[contains(@class,'transactionList')]/li[1]/div/div[2]/span")
    private WebElement mOfflineOrder_MallName;


    @FindBy(xpath= "//ul[contains(@class,'transactionList')]/li[1]/div/div[2]/div/p")
    private WebElement mOfflineOrder_completed;

    @FindBy(xpath= "//ul[contains(@class,'transactionList')]/li[1]/div/div[3]/div")
    private WebElement mOfflineOrder_purchaseDate;

    @FindBy(xpath= "//ul[contains(@class,'transactionList')]/li[1]/div/div[3]/div[2]/span")
    private WebElement mOfflineOrder_price;

    @FindBy(xpath= "//ul[contains(@class,'transactionList')]/li[1]/div/div[3]/div[2]/span[2]")
    private WebElement mOfflineOrder_VAT;


    @FindBy(xpath = "//ul[contains(@class,'transactionList')]/li[1]")
    private WebElement mFirstOrderInOH;








    public void verifyUserLandedToOrderHistorySection() {
        waitForElementPresent(4);
        waitForPageToLoad(15);
        SoftAssertions sa=new SoftAssertions();
        sa.assertThat(ordersText.getText().trim()).isEqualTo(configProperties.getProperty("landMarkMyAccountPage.header.user.my.orders"));
        sa.assertAll();
    }

    public void verifyAllUIElementsInOrderHistory() {
        waitForPageToLoad(25);
        SoftAssertions sa=new SoftAssertions();
        waitForWebElementVisibility(ordersText,25);
        sa.assertThat(ordersText.getText().trim()).isEqualTo(configProperties.getProperty("landMarkMyAccountPage.header.user.my.orders"));
        sa.assertThat(ViewAndTrackYourOrders.getText().trim()).isEqualTo(configProperties.getProperty("landMarkMyAccountPage.header.user.my.orders.description"));
        boolean status=verifyNoelement(orderNumberText);
        if(status){
            sa.assertThat(orderDateAndTime.isDisplayed());
            sa.assertThat(orderedProductImage.isDisplayed());
            sa.assertThat(orderNumberText.getText().trim()).isEqualTo(configProperties.getProperty("text.account.orderHistory.orderNumber"));
            sa.assertThat(orderId.isDisplayed());
            sa.assertThat(totalText.getText().trim()).isEqualTo(configProperties.getProperty("text.account.orderHistory.total"));
            sa.assertThat(totalValue.isDisplayed());
            sa.assertThat(statusText.getText().trim()).isEqualTo(configProperties.getProperty("text.account.orderHistory.status"));
            sa.assertThat(orderStatusValue.isDisplayed());
            if(config.getBrowser().trim().equalsIgnoreCase("CHROME") || config.getBrowser().trim().equalsIgnoreCase("CHROMEHEADLESS") || config.getBrowser().trim().equalsIgnoreCase("MOBILECHROME")) {
                sa.assertThat(orderLogo.isDisplayed());
                sa.assertThat(paymentText.getText().trim()).isEqualTo(configProperties.getProperty("text.account.paymentDetails.payment"));
                sa.assertThat(paymentCardImage.isDisplayed());
                sa.assertThat(paymentCardNumber.isDisplayed());
                sa.assertThat(shipToText.getText().trim()).isEqualTo(configProperties.getProperty("landMarkOrders.shipto.text"));
                sa.assertThat(shipToUserName.isDisplayed());
                sa.assertThat(viewDetails.getText().trim()).isEqualTo(configProperties.getProperty("landMarkOrders.viewDetails.text"));
            }
        }else{
            System.out.println("user not placed even one order");
        }
        sa.assertAll();
    }

    public void verifyPastOrders() {
        boolean status=verifyNoelement(orderNumberText);
        if(status){
            int count=placedOrdersCount.size();
            boolean statusNext= true;
            while(statusNext) {
                if (config.getBrowser().trim().equalsIgnoreCase("CHROME") || config.getBrowser().trim().equalsIgnoreCase("CHROMEHEADLESS") || config.getBrowser().trim().equalsIgnoreCase("MOBILECHROME")) {
                    statusNext = verifyNoelement(next);
                    if (!statusNext) {
                        break;
                    }
                    mouseHoverClick(next);
                    waitForElementPresent(3);
                    count++;

                }else{
                    statusNext = verifyNoelement(mNext);
                    if (!statusNext) {
                        break;
                    }
                    mouseHoverClick(mNext);
                    waitForElementPresent(3);
                    count++;
                }
            }
            System.out.println("user placed orders count is "+count);
            waitForElementPresent(1);
        }else{
            System.out.println("user not placed even one order");
        }
    }

    public void verifyPastOrders(String verifyOrders) {
        boolean status=verifyNoelement(orderNumberText);
        if(status){
            pastOrdersOption.click();
            int count=pastOrdersOptionDrownDown.size();
            WebElement viewPastOrders=null;
            String preFix = "//*[@id='menu-order-history-interval']/div[3]/ul/li/span[text()='";
            String postFix = "']";
            for (int i = 1; i <= count; i++) {
                waitForPageToLoad(35);
                String path = preFix + verifyOrders + postFix;
                viewPastOrders = driver.findElement(By.xpath(path));
                viewPastOrders.click();
                waitForPageToLoad(35);
            }
        }else{
            System.out.println("user not placed even one order");
        }
    }

    public void clickOnOrderMyAccountHeader() {
        waitForPageToLoad(25);
        mouseHover(myAccountHeader);
//        waitForElementToBeClickable(myAccountHeader,15);
        myAccountHeader.click();
        waitForElementPresent(1);
    }

    public void clickOnOrderId() {
        waitForElementPresent(2);
        waitForPageToLoad(25);
        waitForElementToBeClickable(orderId,15);
        orderId.click();
    }

   public void userClickOnInstoreTab() throws InterruptedException {
      waitForPageToLoad(25);
      waitForElementToBeClickable(instoreTab,15);
      instoreTab.click();
      Thread.sleep(3000);
      System.out.println("background colour is ==>"+instoreTab.getCssValue("background-color"));
      SoftAssertions sa=new SoftAssertions();
      sa.assertThat(instoreTab.getCssValue("background-color").contains("#FFFFFF"));
      sa.assertAll();
   }

   public void verifyAllUIElementsInPurchases() {
        waitForPageToLoad(25);
        SoftAssertions sa=new SoftAssertions();
        if(StepContext.web) {
            waitForWebElementVisibility(purchases, 25);
            boolean status=verifyNoelement(instoreViewDetails);
            boolean shukranStatus = verifyNoelement(shukranJoinNow);
            if(status){
                sa.assertThat(instoreViewDetails.getText().trim()).isEqualTo(configProperties.getProperty("text.account.purchases.instore.viewdetails"));
                sa.assertThat(purchaseDateNtime.isDisplayed());
                sa.assertThat(purchaseLocation.isDisplayed());
                sa.assertThat(purchaseStatus.isDisplayed());
                sa.assertThat(purchaseLocationText.getText().trim()).isEqualTo(configProperties.getProperty("text.account.purchases.instore.location"));
                sa.assertThat(purchaseStatusText.getText().trim()).isEqualTo(configProperties.getProperty("text.account.purchases.instore.status"));
                sa.assertThat(purchaseTotalText.getText().trim()).isEqualTo(configProperties.getProperty("text.account.purchases.instore.total"));

            }else{
                if(shukranStatus){
                    System.out.println("Not a Shukran user");
                    sa.assertThat(nonShukraninfoTitle.getText().trim()).isEqualTo(configProperties.getProperty("text.account.purchases.instore.nonShukraninfoTitle"));
                    sa.assertThat(linkShukranButton.getText().trim()).isEqualTo(configProperties.getProperty("text.account.purchases.instore.linkShukranButton"));
                    sa.assertThat(registerLinkButton.getText().trim()).isEqualTo(configProperties.getProperty("text.account.purchases.instore.registerLinkButton"));

                }else{
                    System.out.println("No Purchases for this Shukran user");
                    sa.assertThat(ShukranUserinfoTitle.getText().trim()).isEqualTo(configProperties.getProperty("text.account.purchases.instore.ShukranUserinfoTitle"));
                    sa.assertThat(ShukranUserinfosubTitle.getText().trim()).contains(configProperties.getProperty("text.account.purchases.instore.ShukranUserinfosubTitle"));
                }
        }

        }
        else{
            boolean status=verifyNoelement(mCompleted);
            boolean shukranStatus =StepContext.shukranJoinNow;
            if(status){
                sa.assertThat(mOfflineOrder_Images.isDisplayed());
                sa.assertThat(mOfflineOrder_MallName.isDisplayed());
                sa.assertThat(mOfflineOrder_completed.isDisplayed());
                sa.assertThat(mOfflineOrder_purchaseDate.isDisplayed());
                sa.assertThat(mOfflineOrder_price.isDisplayed());
                sa.assertThat(mOfflineOrder_VAT.isDisplayed());
                sa.assertThat(mOfflineOrder_completed.getText().trim()).isEqualTo(configProperties.getProperty("text.account.purchase.instore.completed"));
                sa.assertThat(mOfflineOrder_VAT.getText().trim()).isEqualTo(configProperties.getProperty("text.account.purchase.instore.vat"));
            }else{
                if(shukranStatus){
                    System.out.println("Not a Shukran user");
                    sa.assertThat(nonShukraninfoTitle.getText().trim()).isEqualTo(configProperties.getProperty("text.account.purchases.instore.nonShukraninfoTitle"));
                    sa.assertThat(linkShukranButton.getText().trim()).isEqualTo(configProperties.getProperty("text.account.purchases.instore.linkShukranButton"));
                    sa.assertThat(registerLinkButton.getText().trim()).isEqualTo(configProperties.getProperty("text.account.purchases.instore.registerLinkButton"));

                }else{
                    System.out.println("No Purchases for this Shukran user");
                    sa.assertThat(ShukranUserinfoTitle.getText().trim()).isEqualTo(configProperties.getProperty("text.account.purchases.instore.ShukranUserinfoTitle"));
                    sa.assertThat(ShukranUserinfosubTitle.getText().trim()).contains(configProperties.getProperty("text.account.purchases.instore.ShukranUserinfosubTitle"));
                }
            }
        }
        sa.assertAll();
        
   }

   public void userClickOnViewDetails() {
       if (StepContext.mWeb) {
           waitForPageToLoad(25);
           waitForElementPresent(10);
           mFirstOrderInOH.click();
           waitForElementPresent(25);
           //javaScriptExecuteClick(mFirstOrderInOH);
       } else {

           SoftAssertions sa = new SoftAssertions();
           waitForWebElementVisibility(purchases, 25);

           boolean status = verifyNoelement(instoreViewDetails);
           if (status) {
               instoreViewDetails.click();
           } else {
               System.out.println("No Purchases for this User");
           }

       }
   }

    public void verifyUIComponentsInReceipts() {
    }
}
