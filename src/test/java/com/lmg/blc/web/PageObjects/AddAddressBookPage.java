package com.lmg.blc.web.PageObjects;

import com.landmarkshops.Web_Mweb.config.config;
import com.landmarkshops.Web_Mweb.utilities.AddressBean;
import com.landmarkshops.Web_Mweb.utilities.StepContext;
import com.landmarkshops.Web_Mweb.utilities.WebUtils;
import org.assertj.core.api.SoftAssertions;
import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.testng.Assert;

import java.util.List;

import static com.landmarkshops.Web_Mweb.launcher.TestHarness.configProperties;

public class AddAddressBookPage extends WebUtils {


    public AddAddressBookPage(WebDriver driver) {
        super(driver);
        PageFactory.initElements(driver, this);

    }


    /**
     * Add your Address Now section
     */
    //@FindBy(xpath = "//*[@id='my-acc-head_QA']")
    @FindBy(xpath = "//*[@id='main-part']/div/div/div/h4")
    private WebElement addressBookTitle;

    @FindBy(xpath = "//*[@id='main-part']/div/div[2]/div[1]/p[2]")
    private WebElement addressBookSubTitle;

    @FindBy(xpath = "//*[@id='main-part']/div/div[3]/div/div/div[2]")
    private WebElement noShippingAddressTextMessage;

    @FindBy(xpath = "//*[@id='main-part']/div/div[3]/div/div/div[3]")
    private WebElement letsFindYouAHome;

    //@FindBy(xpath = "//*[@id='addFirstAddr_QA']/span")
    //@FindBy(xpath = "//*[@id='addressList']/div")
    @FindBy(xpath = "//*[@id='addFirstAddr_QA']")
    private WebElement addYourAddressNow;


    @FindBy(xpath = "//*[@id='main-part']/div/div[3]/div/form/div/div/div[1]")
    private WebElement cityLabel;

    @FindBy(xpath = "//*[@id='emirate'][1]")
    private WebElement cityInput;

    @FindBy(xpath = "//*[@id='menu-emirate']/div[3]/ul/li")
    private List<WebElement> cityNames;

    @FindBy(xpath = "//*[@id='menu-emirate']/div[3]/ul/li[3]")
    private WebElement cityDropDownItem;

    @FindBy(xpath = "//*[@id='menu-emirate']/div[3]/ul/li[4]")
    private WebElement cityDropDownItemUpdated;


    @FindBy(id = "checkout-homedelivery-area-label_QA")
    private WebElement areaLabel;

    @FindBy(xpath = "//*[@id='area']")
    private WebElement areaInput;

    @FindBy(xpath = "//*[@id='menu-area']/div[3]/ul/li")
    private List<WebElement> areaNames;

    @FindBy(xpath = "//*[@id='menu-area']/div[3]/ul/li[6]")
    private WebElement areaDropDownItem;

    @FindBy(xpath = "//*[@id='menu-area']/div[3]/ul/li[3]")
    private WebElement areaDropDownItemUpdated;

    @FindBy(id = "checkout-homedelivery-building-label_QA")
    private WebElement BuildingNameHouseNoFloorApartmentNumberLabel;

    //@FindBy(id = "building")
    @FindBy(xpath = "//*[@id='building']")
    private WebElement buildingInput;

    @FindBy(xpath = "//*[@id='floor']")
    private WebElement streetInput;

    @FindBy(id = "landmark")
    private WebElement landmarkInput;

    @FindBy(id = "checkout-homedelivery-floor-label_QA")
    private WebElement streetNameLabel;

    @FindBy(id = "checkout-homedelivery-makani-label_QA")
    private WebElement landmarkLabel;


    @FindBy(xpath = "//*[@id='main-part']/div/div[3]/div/form/div/div/div[11]")
    private WebElement personalInformationLabel;

    @FindBy(id = "checkout-homedelivery-name-label_QA")
    private WebElement fullNameLabel;

    @FindBy(id = "fullname")
    private WebElement fullNameInput;


    @FindBy(xpath = "//*[@id='main-part']/div/div[3]/div/form/div/div/div[14]")
    private WebElement mobileNumberLabel;

    @FindBy(xpath = "//*[@id='main-part']/div/div[3]/div/form/div/div/div[15]/div/p")
    private WebElement mobileCountryCode;

    @FindBy(id = "mobile_num")
    private WebElement mobileNumberInput;

    @FindBy(id = "checkout-homedelivery-address-type-label_QA")
    private WebElement addressTypeLabel;


    @FindBy(xpath ="//*[@id='address-type-home-work_QA']/div/div/label[1]/span[2]")
    private WebElement homeLabel;


    @FindBy(xpath ="//*[@id='address-type-home-work_QA']/div/div/label[2]/span[2]")
    private WebElement officeLabel;

    @FindBy(xpath = "//*[@id='address-type-home-work_QA']/div/div/div/div[1]/label/span[1]/span/input")
    private WebElement homeRadioButton;

    @FindBy(xpath = "//*[@id='address-type-home-work_QA']/div/div/label[2]/span[1]/span/input")
    private WebElement officeRadioButton;

    //@FindBy(xpath = "//*[@id='saveFirstAddr_QA']/span/span[2]")
    @FindBy(xpath = "//*[@id='saveFirstAddr_QA']/span[1]/span")
    private WebElement saveButton;

    @FindBy(xpath = "//*[@id='confirmAddrSave_QA']/span/span[2]")
    private WebElement saveButtonSecondAddress;

    @FindBy(xpath = "//*[@id='addNewAddr_QA']/div")
    private WebElement addNewAddressLink;

    @FindBy(xpath = " //*[@id='addFirstAddr_QA']/span[1]")
    private WebElement editLabel;

    //@FindBy(xpath = "//*[@id='deleteAddr_QA']/span/span[2]")
    @FindBy(xpath = "//*[@id='deleteAddr_QA']")
    private WebElement deleteLabel;

    @FindBy(xpath = "//*[@id='addNewAddr_QA']/div")
    private WebElement setAsDefaultLabel;

    @FindBy(xpath = "//*[@id='main-part']/div/div[1]/div[1]/div[1]/a")
    private WebElement myAccount;

    @FindBy(xpath = "//*[@id='addressList']/div")
    List<WebElement> addList;

    @FindBy(id = "saveFirstAddr_QA")
    WebElement editAddressBtn;


    //@FindBy(id = "deleteAddrConfirm_QA")
    @FindBy(xpath = "//*[@id='deleteAddrConfirm_QA']")
    WebElement deleteAddressConfirm;

/*    @FindBy(xpath = "//div[contains(text(),'Are you sure ?')]")
    WebElement areYouSure;*/

    @FindBy(xpath = "//*[@id='addressList']/div/div/div/div")
    WebElement areYouSure;



    @FindBy(id = "deleteAddrCancel_QA")
    WebElement neverMind;


    @FindBy(xpath = " //*[@id='deleteAddrConfirm_QA']/span")
    WebElement deleteAddressText;

    @FindBy(xpath = "//*[@id='deleteAddrCancel_QA']/span")
    WebElement neverMindText;

    @FindBy(id = "cancelAddrSave_QA")
    WebElement cancelUpdatedAddress;

    @FindBy(id = "confirmAddrSave_QA")
    WebElement saveUpdatedAddress;

//    @FindBy(id = "addNewAddr_QA")
//    WebElement addNewAddressLink;

    @FindBy(xpath = "//*[@id='addressList']/div[2]/div[2]/button[3]")
    WebElement setDefaultSecondAddress;
    //*[@id="main-part"]/div/div[3]/form/div[1]/div/div[4]/div[2]/em
    @FindBy(xpath = "//*[@id='main-part']/div/div/div/div/div/div/form/div[1]/div/div[2]/div[1]/div/div[2]/div[2]/em")
    WebElement selectCityErrorMsg;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div/div/div/div/form/div[1]/div/div[2]/div[2]/div/div[2]/div[2]/em")
    WebElement areaErrorMsg;;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div/div/div/div/form/div[1]/div/div[3]/div[1]/div/div[2]/div/div[2]/em")
    WebElement buildingNameErrorMsg;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div/div/div/div/form/div[1]/div/div[3]/div[2]/div/div[2]/div/div[2]/em")
    WebElement streetNameErrorMsg;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div/div/div/div/form/div[1]/div/div[1]/div[1]/div/div[2]/div/div[2]/em")
    WebElement fullNameErrorMsg;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div/div/div/div/form/div[1]/div/div[1]/div[1]/div/div[2]/div/div[2]/em")
    WebElement fullNameAlphaErrorMsg;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div/div/div/div/form/div[1]/div/div[1]/div[2]/div/div[2]/div/div[2]/em")
    WebElement mobileNumberErrorMsg;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div/div/div/div/form/div[1]/div/div[1]/div[2]/div/div[2]/div/div[2]/em")
    WebElement mobileNumberLimitErrorMsg;


    @FindBy(xpath="//*[@id='addNewAddr_QA']")
    WebElement addAddressBookLink;


    public void userClickOnAddYourAddressNow(){
        waitForPageToLoad(30);
        waitForElementPresent(2);
        boolean status=true;
        status=verifyNoelement(addYourAddressNow);
        if(status) {
            waitForElementToBeClickable(addYourAddressNow, 25);
            addYourAddressNow.click();
        }else{
            waitForElementToBeClickable(addNewAddressLink, 25);
            addNewAddressLink.click();
        }
    }

    public void verifyUIComponentsAddressNotYetAdded(){



    }
    public void verifyUserLandedToAddressBook() {
        //waitForPageToLoad(50);
        waitForElementPresent(15);
        //waitForWebElementPresent(addressBookTitle, 25);
        addressBookTitle.getText().trim();

    }

    public void backToMyAccountPage() {
        myAccount.click();
    }


    public void saveClick() {
        boolean status=verifyNoelement(saveButton);
        if(status) {
            waitForElementPresent(2);
            saveButton.click();
        }else{
            waitForElementPresent(2);
            saveButtonSecondAddress.click();
        }
    }


    public void addressListCount(int number) {
        waitForElementPresent(10);
        int addressList = addList.size();
        Assert.assertEquals(addressList, number, "address list mismatch");
    }

    public void verifyAddressDetailsInList(String fullName) {

        // //*[@id="addressList"]/div[1]/div[1]/div[1]

        waitForElementPresent(1);
        int addressList = addList.size();
        String preFix = "//*[@id='addressList']/div[";
        String mixFix1 = "]/div[";
        String mixFix2 = "]/div[";
        String postFix = "]";
        for (int i = 1; i <= addressList; i++) {
            String path = preFix + i + mixFix1 + 1 + mixFix2 + 1 + postFix;
            String addressName = driver.findElement(By.xpath(path)).getText().trim();
            addressName = addressName.replace("Default", "");
            Assert.assertEquals(addressName, fullName, "full name in address mismatch");
            System.out.println("Address details as :\t" + addressName);
        }


    }


    public void verifyUpdatedAddressDetailsInList(String fullName) {

        // //*[@id="addressList"]/div[1]/div[1]/div[1]

        waitForElementPresent(10);
        int addressList = addList.size();
        String preFix = "//*[@id='addressList']/div[";
        String mixFix1 = "]/div[";
        String mixFix2 = "]/div[";
        String postFix = "]";
        for (int i = 1; i <= addressList; i++) {
            for (int j = 1; j < 5; j++) {
                String path = preFix + i + mixFix1 + 1 + mixFix2 + j + postFix;
                if (j == 1) {
                    String fname = driver.findElement(By.xpath(path)).getText().trim();
                    fname = fname.replace("Default", "");
                    Assert.assertEquals(fname, AddressBean.getFullName().trim(), "full name in address mismatch");
                    System.out.println("full name  as :\t" + fname);
                } else if (j == 2) {
                    String building = driver.findElement(By.xpath(path)).getText().trim();
                    System.out.println("building name  as :\t" + building);

                } else if (j == 3) {
                    String area = driver.findElement(By.xpath(path)).getText().trim();
                    System.out.println("area name  as :\t" + area);

                } else if (j == 4) {
                    String makani = driver.findElement(By.xpath(path)).getText().trim();
                    System.out.println("makani  :\t" + makani);

                }
                if (j == 5) {
                    String mobile = driver.findElement(By.xpath(path)).getText().trim();
                    System.out.println("mobile  :\t" + mobile);

                }
            }
        }

    }

    public void verifyAddressDetails(String fullName) {

        // //*[@id="addressList"]/div[1]/div[1]/div[1]

        //  //*[@id="addressList"]/div[2]/div[2]/button[3] ------>set default second item
        waitForElementPresent(10);
        int addressList= addList.size();
        String preFix = "//*[@id='addressList']/div[";
        String mixFix1 = "]/div[";
        String mixFix2 ="]/div[";
        String postFix ="]";
        for(int i=1;i<=addressList;i++){

            for(int j=1;j<5;j++){
                String path = preFix+i+mixFix1+1+mixFix2+j+postFix;
                String addressDetail = driver.findElement(By.xpath(path)).getText().trim();
                System.out.println("Address details as :\t"+addressDetail);
            }

        }
    }


    public void deleteAddressBook() {
        waitForPageToLoad(10);
        waitForElementPresent(1);
        boolean status=verifyNoelement(deleteLabel);
//        javaScriptExecute(addNewAddressLink);
//        scrollToParticularElement(addNewAddressLink);
        if(status) {
            mouseHoverClick(deleteLabel);
        }
        //deleteLabel.click();
        waitForElementPresent(1);
    }

    public void verifyConformationMessage() {
        waitForWebElementPresent(areYouSure, 10);
        SoftAssertions sas =new SoftAssertions();
       // sas.assertThat(areYouSure.getText().trim()).isEqualTo(configProperties.getProperty("myAccountPage.addAddress.label.confirm.are"));
        sas.assertThat(deleteAddressText.getText().trim()).isEqualTo(configProperties.getProperty("myAccountPage.addAddress.label.confirm.deleteAddress"));
        sas.assertThat(neverMindText.getText().trim()).isEqualTo(configProperties.getProperty("myAccountPage.addAddress.label.confirm.neverMind"));
        sas.assertAll();


    }

    public void clickOnNeverMind() {
        waitForElementPresent(1);
        waitForElementToBeClickable(neverMind,10);
        mouseHoverClick(neverMind);
        //neverMind.click();
    }

    public void verifyAddressNotDeleted() {
        waitForElementPresent(10);
        int addressList = addList.size();
        Assert.assertEquals(addressList, 1, "address list mismatch");
    }

    public void confirmDeleteAddress() {
        waitForElementPresent(1);
        boolean status=verifyNoelement(deleteAddressConfirm);
        if(status) {
            waitForElementToBeClickable(deleteAddressConfirm, 10);
            mouseHoverClick(deleteAddressConfirm);
            //deleteAddressConfirm.click();
            waitForElementPresent(2);
        }
    }

    public void saveUpdateAddress() {
        waitForElementToBeClickable(saveUpdatedAddress,10);
        saveUpdatedAddress.click();
        waitForElementPresent(5);
    }

    public void cancelUpdateAddress() {
        waitForElementPresent(2);
        cancelUpdatedAddress.click();
    }


    public void clickOnEditAddressBtn() {
        waitForElementPresent(2);
        editAddressBtn.click();
    }

    public void addAddressLink() {
        waitForElementPresent(2);
        mouseHoverClick(addNewAddressLink);
        // addNewAddressLink.click();
    }

    public void changeAddressAsDefaultAndValidate() {

        // //*[@id="addressList"]/div[1]/div[1]/div[1]

        //  //*[@id="addressList"]/div[2]/div[2]/button[3] ------>set default second item
// //*[@id="addressList"]/div[1]/div[1]/div[1]

        //  //*[@id="addressList"]/div[2]/div[2]/button[3] ------>set default second item
        String[] str1 = new String[3];
        String[] str2 = new String[3];
        waitForElementPresent(2);
        int addressList = addList.size();
        String preFix = "//*[@id='addressList']/div[";
        String mixFix1 = "]/div[";
        String mixFix2 = "]/div[";
        String postFix = "]";
        for (int i = 1; i <= addressList; i++) {
            String path = preFix + i + mixFix1 + 1 + mixFix2 + 1 + postFix;
            String fname = driver.findElement(By.xpath(path)).getText().trim();
            System.out.println("Full Name details for list of address :\t" + fname);
            str1[i - 1] = fname;
        }
//        waitForElementPresent(2);
//        scrolldownElement(setDefaultSecondAddress);
        waitForElementPresent(2);
        mouseHoverClick(setDefaultSecondAddress);
        //setDefaultSecondAddress.click();
        waitForElementPresent(2);
        addressList = addList.size();
        for (int i = 1; i <= addressList; i++) {
            String path = preFix + i + mixFix1 + 1 + mixFix2 + 1 + postFix;
            String fname = driver.findElement(By.xpath(path)).getText().trim();
            System.out.println("Full Name after set as default, for second address :\t" + fname);
            str2[i - 1] = fname;


        }
        if(config.getLanguage().equalsIgnoreCase("AR")){
            System.out.println("Address one"+str2[0]);
            System.out.println("Address two"+str2[1]);
            StepContext.defaultAddress=str2[0].replaceAll("الأساسي","");
            Assert.assertTrue(str2[0].contains("الأساسي"), "Set Default not updated");
            Assert.assertFalse(str2[1].contains("الأساسي"), "Set Default not update");
            waitForElementPresent(2);
        }else{
            System.out.println("Address one"+str2[0]);
            System.out.println("Address two"+str2[1]);

            StepContext.defaultAddress=str2[0].replaceAll("DEFAULT","");
            Assert.assertTrue(str2[0].contains("DEFAULT"), "Set Default not updated");
            Assert.assertFalse(str2[1].contains("DEFAULT"), "Set Default not update");
            waitForElementPresent(2);
        }

    }


    public String chooseCityFromSelectDropDown() {
        waitForElementToBeClickable(cityInput,10);
        waitForPageToLoad(15);
        cityInput.click();
        elementMouseClick(cityDropDownItem);
        String cityName = cityInput.getText().trim();
        testLogger.info("Selected city value as:"+cityName);
        waitForElementPresent(1);
        return cityName;

    }

    public String updateCityFromSelectDropDown() {
        waitForPageToLoad(10);
        waitForElementToBeClickable(cityInput,10);
        javaScriptExecute(cityInput);
        //cityInput.click();
        elementMouseClick(cityDropDownItemUpdated);
        String cityName = cityInput.getText().trim();
        testLogger.info("Selected city value as:"+cityName);
        waitForElementPresent(1);
        return cityName;

    }

    public String chooseCityFromSelectDropDown(String city) {
        waitForElementPresent(2);
        waitForElementToBeClickable(cityInput,10);
        waitForPageToLoad(15);
        mouseHoverClick(cityInput);
        waitForPageToLoad(15);
        int count = cityNames.size();
        waitForElementPresent(1);
        WebElement updateCity=null;
        String preFix = "//*[@id='menu-emirate']/div[3]/ul/li/span[text()='";
        String postFix = "']";
        for (int i = 1; i <= count; i++) {
            String path = preFix + city + postFix;
            updateCity = driver.findElement(By.xpath(path));
        }
//        waitForElementToBeClickable(updateCity,25);
        updateCity.click();
        waitForPageToLoad(15);
        String selectedEmirateValue = cityInput.getText().trim();
        testLogger.info("Selected emirate value as:"+selectedEmirateValue);
        return selectedEmirateValue;
    }
    public String chooseAreaFromSelectDropDown() {
        waitForElementToBeClickable(areaInput,10);
        mouseHoverClick(areaInput);
        //areaInput.click();
        elementMouseClick(areaDropDownItem);
        String areaName = areaInput.getText().trim();
        testLogger.info("Selected area value as:"+areaName);
        waitForElementPresent(2);
        return areaName;

    }

    public String updatedAreaFromSelectDropDown() {
        waitForElementToBeClickable(areaInput,10);
        areaInput.click();
        elementMouseClick(areaDropDownItemUpdated);
        String areaName = areaInput.getText().trim();
        testLogger.info("Selected area value as:"+areaName);
        waitForElementPresent(2);
        return areaName;

    }

    public String chooseAreaFromSelectDropDown(String area) {
        waitForPageToLoad(15);
        waitForElementToBeClickable(areaInput,10);
        areaInput.click();
        waitForPageToLoad(15);
        int count = areaNames.size();
        WebElement updateArea=null;
        String preFix = "//*[@id='menu-area']/div[3]/ul/li/span[text()='";
        String postFix = "']";
        for (int i = 1; i <= count; i++) {
            String path = preFix + area + postFix;
            updateArea = driver.findElement(By.xpath(path));
        }
//        waitForElementToBeClickable(updateArea,12);
        updateArea.click();
        waitForPageToLoad(15);
        String selectedAreaValue = areaInput.getText().trim();
        testLogger.info("Selected area value as:"+selectedAreaValue);
        return selectedAreaValue;
    }

    public void clearBuildingDetails(){
        waitForPageToLoad(25);
        buildingInput.click();
        buildingInput.clear();
    }

    public void enterBuildingDetails(String building){
        waitForPageToLoad(25);
        buildingInput.click();
        buildingInput.sendKeys(building);
    }

    public void clearStreetName(){
        waitForPageToLoad(25);
        streetInput.click();
        streetInput.clear();
        waitForElementPresent(1);
    }

    public void enterStreetName(String streetName){
        waitForPageToLoad(25);
        streetInput.click();
        streetInput.sendKeys(streetName);
    }

    public void clearLandMarkDetails(){
        waitForPageToLoad(25);
        landmarkInput.click();
        landmarkInput.clear();
        waitForElementPresent(1);
    }


    public void enterLandMarkDetails(String landmark){
        scrollToParticularElement(landmarkInput);
        landmarkInput.click();
        landmarkInput.sendKeys(landmark);
    }

    public void clearFullName(){
        waitForPageToLoad(25);
        fullNameInput.click();
        fullNameInput.clear();
       waitForElementPresent(1);
    }
   public void enterFullName(String fullName) {
       if (StepContext.mWeb) {
           waitForPageToLoad(25);
           clickElement(fullNameInput, 15);
           fullNameInput.sendKeys(fullName);
           System.out.println("Full name as" + fullName);
       }
       else {
           waitForPageToLoad(25);
           scrollToParticularElement(fullNameInput);
           fullNameInput.click();
           fullNameInput.sendKeys(fullName);
           System.out.println("Full name as" + fullName);
       }
   }
   public void enterMobileNumber(String mobileNumber){
       waitForPageToLoad(25);
       mobileNumberInput.click();
       mobileNumberInput.sendKeys(mobileNumber);
   }


  public void checkOptionAsHome(){
        scrolldown();
        mouseHoverClick(homeRadioButton);
        //homeRadioButton.click();

  }

    public void checkOptionAsOffice(){
        officeRadioButton.click();

    }


    public void verifySavedAddressDetails(String fullName) {
        // //*[@id="addressList"]/div[1]/div[1]/div[1]
        waitForElementPresent(2);
        int addressList = addList.size();
        String preFix = "//*[@id='addressList']/div[";
        String mixFix1 = "]/div[";
        String mixFix2 = "]/div[";
        String postFix = "]";
        for (int i = 1; i <= addressList; i++) {
            String path = preFix + i + mixFix1 + 1 + mixFix2 + 1 + postFix;
            String addressName = driver.findElement(By.xpath(path)).getText().trim();
            if(config.getLanguage().equalsIgnoreCase("AR")){
                addressName = addressName.replace("الأساسي", "");
            }else {
                addressName = addressName.replace("DEFAULT", "");
            }
           // Assert.assertEquals(addressName, fullName, "full name in address mismatch");
            System.out.println("Address details as :\t" + addressName);
        }

    }

    public void clearMobileNumber() {
        mobileNumberInput.click();
        mobileNumberInput.clear();
        waitForElementPresent(2);
    }

    public void deleteAllAddresses() {
        waitForPageToLoad(25);
        waitForElementPresent(1);
        boolean status=true;
        status=verifyNoelement(addYourAddressNow);
        if(status){
            System.out.println("Address details are not available in Address section");
        }else {
            int addressList = addList.size();
            System.out.println("addressList"+addressList);
            System.out.println("number of address added to my account address" + addressList);
            for (int i = 0; i < addressList; i++) {
                deleteAddressBook();
                confirmDeleteAddress();
            }
        }
        waitForElementPresent(3);
    }

    public void verifyErrorMessageForAddAddressForm(String field) {
        if("city".equalsIgnoreCase(field)) {
            SoftAssertions sas = new SoftAssertions();
           // scrollToParticularElement(cityLabel);
            sas.assertThat(selectCityErrorMsg.getText().trim()).contains(configProperties.getProperty("myAccountPage.addAddress.error.city"));
            sas.assertThat(areaErrorMsg.getText().trim()).isEqualTo(configProperties.getProperty("myAccountPage.addAddress.error.area"));
            sas.assertThat(buildingNameErrorMsg.getText().trim()).contains(configProperties.getProperty("myAccountPage.addAddress.error.buildingName"));
            //sas.assertThat(streetNameErrorMsg.getText().trim()).isEqualTo(configProperties.getProperty("myAccountPage.addAddress.error.streetName"));
            sas.assertAll();
        }
    }

    public void verifyFullNameErrorMessages() {
        SoftAssertions sas =new SoftAssertions();
        sas.assertThat(fullNameErrorMsg.getText().trim()).contains(configProperties.getProperty("myAccountPage.addAddress.error.fullName"));
        enterFullName("t");
        waitForElementPresent(3);
        waitForPresenceOfElementLocated(fullNameAlphaErrorMsg,driver,15);
        sas.assertThat(fullNameAlphaErrorMsg.getText().trim()).isEqualTo(configProperties.getProperty("myAccountPage.addAddress.error.alpha.fullName"));
        sas.assertAll();
    }

    public void verifyMobileNumberErrorMessages(){
        SoftAssertions sas =new SoftAssertions();
        sas.assertThat(mobileNumberErrorMsg.getText().trim()).contains(configProperties.getProperty("myAccountPage.addAddress.error.mobileNumber"));
        enterMobileNumber("1");
        waitForElementPresent(3);
        waitForPresenceOfElementLocated(mobileNumberLimitErrorMsg,driver,15);
        //sas.assertThat(mobileNumberLimitErrorMsg.getText().trim()).isEqualTo(configProperties.getProperty("myAccountPage.addAddress.error.ten.mobileNumber"));
        sas.assertAll();
    }

    public void clickOnAddressBookLink() {
        if(StepContext.mWeb){
            addAddressBookLink.click();
        }
    }
}