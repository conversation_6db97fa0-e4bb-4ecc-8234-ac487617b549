package com.lmg.blc.web.PageObjects;

import com.landmarkshops.Web_Mweb.config.config;
import com.landmarkshops.Web_Mweb.utilities.OrderDetailsBean;
import com.landmarkshops.Web_Mweb.utilities.WebUtils;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;



public class ThankYouPage extends WebUtils {

	public ThankYouPage(WebDriver driver) {
		super(driver);
		PageFactory.initElements(driver, this);

	}


	@FindBy(xpath="//*[@id='main-part']/div/div[1]/div[1]/div[1]/div[2]/div[1]")
	WebElement thankYouConfirmation;

	@FindBy(xpath="//*[@id='main-part']/div/div[1]/div[1]/div[1]/div[2]/div[2]")
	WebElement weAppereciateYourOrder;

	@FindBy(id = "store-available-close")
	WebElement ratingCloseIcon;

	@FindBy(xpath = "/html/body/div[*]/div[3]/div/h2/div/ul/li[11]")
	WebElement rating10;

	@FindBy(xpath = "//*[@id='store-available-close']/span/div")
	WebElement mCloseRatingPopup;

	@FindBy(xpath = "//*[@id='main-part']/div/div[1]/div[1]/div[1]/div[2]/div[3]/*")
	WebElement orderConfirmationNumber;


	@FindBy(xpath = "//*[@id='main-part']/div/div[1]/div[1]/div[3]/div/form/fieldset/div/div/input")
	WebElement passwordInput;


	@FindBy(id= "mobile_num")
	WebElement mobileNumberInput;

	@FindBy(xpath = "//*[@id='main-part']/div/div[1]/div[1]/div[3]/div/form/button/span")
	WebElement createAnAccountButton;

	@FindBy(xpath = "//*[@id='main-part']/div/div[1]/div[1]/div[4]/a/span")
	WebElement becomeShukranMember;

	@FindBy(id = "orderId")
	WebElement orderNumber;


	public void verifyUserLandedToThankYouPage(){
		//waitForPageToLoad(45);
		implicitWait(60);
		waitForElementPresent(30);

		waitForWebElementPresent(thankYouConfirmation,driver,100);
	}

	public void enterPassword(String password){
		waitForWebElementPresent(passwordInput,driver,15);
		passwordInput.sendKeys(password);
	}

	public void clickOnCreateAnAccount(){
		waitForElementToBeClickable(createAnAccountButton,driver,15);
		createAnAccountButton.click();
	}

	public String getMobileNumber(){
		return mobileNumberInput.getAttribute("value").trim();
	}

	/*
	* This is created for sample
	* */

	public void closeRatingPopup() {
		//this is for demo
		//waitForPageToLoad(240);
		//implicitWait(230);
		implicitWait(25);
		if(config.getBrowser().trim().equalsIgnoreCase("CHROME") || config.getBrowser().trim().equalsIgnoreCase("CHROMEHEADLESS") || config.getBrowser().trim().equalsIgnoreCase("MOBILECHROME")) {

			waitForWebElementPresent(ratingCloseIcon,driver,35);
			ratingCloseIcon.click();
			testLogger.info("user clicked on rating close icon");
		}else{
			waitForWebElementPresent(rating10,driver,30);
			rating10.click();
			mCloseRatingPopup.click();
		}
	}

	public void orderConfirmation() {;
		waitForWebElementPresent(orderConfirmationNumber,driver,30);
		String orderNum = orderConfirmationNumber.getText().trim();
		testLogger.info("user successfully placed the order using guest user" + orderNum);

	}



	public  String orderNumber() {
		waitForWebElementPresent(orderNumber,driver,20);
		String orderNumberVal =orderNumber.getText().trim();
		return orderNumberVal;
	}


	public void validateMobileNumberInThankYou() {
		String mobileNumber = getMobileNumber();
		//assertThat(StepContext.thankYouMobileValue).as("Thank you page mobile number validation").isEqualTo(mobileNumber);




	}

    public void clickOnOrderNumber() {
		waitForVisibilityOfElementLocated(orderConfirmationNumber,driver,15);
		String orderNumber=orderConfirmationNumber.getText().trim();
		OrderDetailsBean.setOrderNumber(orderNumber);
		mouseHoverClick(orderConfirmationNumber);

    }
}
