package com.lmg.blc.web.PageObjects;

import com.landmarkshops.Web_Mweb.config.config;
import com.landmarkshops.Web_Mweb.utilities.CardDataBean;
import com.landmarkshops.Web_Mweb.utilities.TestDataBean;
import com.landmarkshops.Web_Mweb.utilities.WebUtils;
import org.assertj.core.api.SoftAssertions;
import org.json.JSONArray;
import org.json.JSONObject;
import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;

import java.io.FileWriter;
import java.io.IOException;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.List;
import java.util.Random;

import static com.landmarkshops.Web_Mweb.launcher.TestHarness.configProperties;


public class BasketPage extends WebUtils {


	public BasketPage(WebDriver driver) {
		super(driver);
		PageFactory.initElements(driver, this);
	}
	private static String concept = config.getConcept();



	@FindBy(xpath = "//*[@id='main-part']/div/div[1]")
	private WebElement yourShippingBasket;

	@FindBy(xpath = "//*[@id='cart-section_QA']/div/div[1]/div[1]/div[1]")
	private WebElement itemLabel;

	@FindBy(xpath = "//*[@id='cart-section_QA']/div/div[1]/div[1]/div[2]")
	private WebElement descriptionLabel;

	@FindBy(xpath = "//*[@id='cart-section_QA']/div/div[1]/div[1]/div[3]")
	private WebElement priceLabel;

	@FindBy(xpath = "//*[@id='cart-section_QA']/div/div[1]/div[1]/div[4]")
	private WebElement quantityLabel;

	@FindBy(xpath = "//*[@id='cart-list']//div[5]/div")
	private List<WebElement>  totalPriceLabel;

	@FindBy(xpath ="//img[@fetchpriority='high']")
	private WebElement productImage;

	@FindBy(xpath ="//*[@id='cart-list']/div/div/div/div[1]/a/img")
	private WebElement mProductImage;

	@FindBy(xpath = "//*[@id='cart-list']//div[2]/div/div[2]/a")
	private List<WebElement> productDescription;

	@FindBy(xpath = "//img[@fetchpriority='high']/../../../div[2]/div/a")
	private WebElement mProductDescription;

	@FindBy(xpath = "//*[@id='cart-list']//div[3]/div")
	private List<WebElement> productPrice;

	@FindBy(xpath = "(//img[@fetchpriority='high']/../../../div[2]/div[2]/div/div[1]/div)[1]")
	private WebElement mProductPrice;

	@FindBy(xpath = "//*[@id='changeQuantity_QA']/span/div[1]")
	private List<WebElement> quantityDropDownIcon;

	@FindBy(xpath = "//img[@fetchpriority='high']/../../..//*[@id='changeQuantity_QA']/span[1]/div[2]")
	private WebElement mQuantityDropDownIcon;


	@FindBy(xpath = "//*[@id='changeQuantity_QA']/span/div[1]")
	private WebElement quantityDropdown;

	@FindBy(xpath = "//*[@id='changeQuantity_QA']/span[1]/div[1]")
	private WebElement mQuantityDropdown;


	@FindBy(xpath = "//*[@id='cart-removeitem_QA']")
	private WebElement productRemove;

	@FindBy(xpath = "//img[@fetchpriority='high']/../../..//*[@id='cart-removeitem_QA']")
	private WebElement mProductRemove;

	@FindBy(xpath = "//*[@id='main-part']/div/div/div[2]")
	private WebElement productEmpty;

	@FindBy(xpath = "//*[@id='main-part']/div/div/div[2]")
	private WebElement mProductEmpty;

	@FindBy(xpath = "//*[@id='main-part']/div/div/div[3]")
	private WebElement goOnAddSomeThing;

	@FindBy(xpath = "//*[@id='main-part']/div/div/div[3]")
	private WebElement mGoOnAddSomeThing;

	@FindBy(xpath = "//*[@id='main-part']/div/div/div[4]/button/span")
	private WebElement startShoppingButton;

	@FindBy(xpath = "//*[@id='main-part']/div/div/div[4]/button/span[1]")
	private WebElement mStartShoppingButton;

	@FindBy(xpath = "//img[@fetchpriority='high']/../../../div[2]/a")
	private WebElement cartProductDescription;

	@FindBy(xpath = "//*[@id='mobile-pdp-title']/div/h1")
	private WebElement mCartProductDescription;

	@FindBy(xpath = "")
	private WebElement productLowInStockOrderNow;

	@FindBy(xpath = "//*[@id='cart-section_QA']/div[2]/div[1]/div[3]/div[2]/div/div[3]/div[2]")
	private WebElement productTotalPrice;

	@FindBy(xpath = "//*[@id='cart-section_QA']/div[3]/div[2]/div[2]/div[2]/div/div[1]/div[2]")
	private  WebElement mProductTotalPrice;

	@FindBy(xpath = "//*[@id='main-part']/div/div[2]/div[1]/ul/div/div[1]/div[5]")
	private WebElement productExtendWarrantyImg;

	@FindBy(xpath = "//*[@id='main-part']/div/div[2]/div[1]/ul/div/div[2]/div[2]/div[1]")
	private WebElement productExtendWarrantyTitle;

	@FindBy(xpath = "//*[@id='main-part']/div/div[2]/div[1]/ul/div/div[2]/div[2]/div[2]")
	private WebElement productExtendWarrantyDescription;

	@FindBy(xpath = "//*[@id='main-part']/div/div[2]/div[1]/ul/div/div[2]/div[3]")
	private WebElement productExtendWarrantyPrice;

	@FindBy(xpath = "//*[@id='main-part']/div/div[2]/div[1]/ul/div/div[2]/div[4]/div")
	private WebElement extendWarrantyEdit;

	@FindBy(xpath = "//*[@id='main-part']/div/div[2]/div[1]/ul/div/div[2]/div[4]/div")
	private WebElement extendWarrantyTotalPrice;

	@FindBy(xpath = "//*[@id='cart-section_QA']/div/div[1]/div[3]/div[1]/div/div/img")
	private WebElement goToPromoCodeIcon;

	@FindBy(xpath = "//*[@id='cart-promo-apply_QA']/span")
	private WebElement promosAndVoucherText;

	@FindBy(xpath = "//*[@id='voucherContainer']/div/div/div[1]")
	private WebElement promosAndVoucherText1;

	@FindBy(xpath = "/html/body/div[3]/div[3]/div/div/div[1]/div[2]/div[1]/div[2]/div[1]/img")
	private WebElement promoCodeApplied;

	@FindBy(xpath = "//*[@id='voucherContainer']/div/div/div[2]")
	private WebElement viewPromosEnterVoucherCodeText;

	@FindBy(xpath = "//*[@id='voucherContainer']/img")
	private WebElement viewPromosEnterVoucherCodeTextIcon;

	@FindBy(xpath = "//*[@id='offer-modal-close']/span/div/../../..//div[contains(text(),'Promos')]")
	private WebElement promosAndVoucherTextONPopup;

	@FindBy(xpath = "//*[@id='offer-modal-close']/span/div/../../../..//div[contains(text(),'Got a Promo')]")
	private WebElement gotAPromoCodeTextONPopup;

	@FindBy(xpath = "/html/body/div[3]/div[3]/div/div/div/div[1]/div")
	private WebElement promoCodeHeaderONPopup;


	@FindBy(xpath = "//div[contains(text(),'Available Promotions')]/../div[2]/div[2]")
	private WebElement promoCodeHeaderValueONPopup;


	@FindBy(xpath = "//div[contains(text(),'Available Promotions')]/../div[2]/div[2]")
	private WebElement promoCodeHeaderValueDateONPopup;


	@FindBy(xpath = "//div[contains(text(),'Available Promotions')]/../div[2]/div[4]/div[1]")
	private WebElement promoCodeHeaderValueCopyONPopup;


	@FindBy(xpath = "//div[contains(text(),'Available Promotions')]/../div[2]/div[4]/div[2]")
	private WebElement applyPromoVoucherTextONPopup;

	@FindBy(xpath = "//div[@id='cart-section_QA']/div[3]/div[2]/div[2]/div[1]/div/div/div[1]\n")
	private WebElement mPromosAndVoucherText;

	@FindBy(xpath = "//*[@id='voucherContainer']/div/div")
	private WebElement useItHere;

	@FindBy(xpath = "//*[@id='cart-promo-click_QA']")
	private WebElement mUseItHere;

	@FindBy(xpath = "//*[@id='voucherError_QA']")
	private WebElement duplicatePromoCodeErrorMessageText;

	@FindBy(xpath = "//*[@id='cart-section_QA']/div[3]/div[2]/div[2]/div[1]/div/div[2]/div/div[2]")
	private WebElement mDuplicatePromoCodeErrorMessageText;

	@FindBy(xpath = "//*[@id='main-part']/div/div[3]/div[1]/div[1]/img")
	private WebElement giftCardIcon;

	@FindBy(xpath = "//*[@id='main-part']/div/div[3]/div[1]/div[2]/div")
	private WebElement haveAGiftCardRedeemAtCheckout;

	@FindBy(xpath = "//*[@id='cart-section_QA']/div[2]/div[1]/div[3]/div[2]/div/div[1]/div[2]")
	private WebElement subtotal;

	@FindBy(xpath = "//*[@id='cart-section_QA']/div[3]/div[2]/div[2]/div[2]/div/div[1]/div[1]")
	private WebElement mSubtotal;

	@FindBy(xpath = "//*[@id='cart-section_QA']/div/div[1]/div[3]/div[2]/div/div[4]")
	private WebElement shippingChargesMayApply;

	@FindBy(xpath = "//*[@id='cart-section_QA']/div[3]/div[2]/div[2]/div[2]/div/div[4]")
	private WebElement mShippingChargesMayApply;

	@FindBy(xpath = "//*[@id='cart-section_QA']/div/div[1]/div[3]/div[2]/div/div[1]/div[2]")
	private WebElement subTotalAmount;

	@FindBy(xpath = "//div[@id='cart-section_QA']/div[3]/div[2]/div[2]/div[2]/div/div[1]/div[2]\n")
	private WebElement mSubTotalAmount;

	@FindBy(xpath = "//*[@id='cart-section_QA']/div/div[1]/div[3]/div[2]/div/div[3]/div[1]")
	private WebElement totalPriceText;

	@FindBy(xpath = "//*[@id='cart-section_QA']/div[3]/div[2]/div[2]/div[2]/div/div[3]/div[1]")
	private WebElement mTotalPriceText;

	@FindBy(xpath = "//*[@id='cart-section_QA']/div/div[1]/div[3]/div[2]/div/div[3]/div[2]/span")
	private WebElement includingVatText;

	@FindBy(xpath = "//*[@id='cart-section_QA']/div[3]/div[2]/div[2]/div[2]/div/div[3]/div[2]/span")
	private WebElement mIncludingVatText;

	@FindBy(xpath = "//*[@id='cart-section_QA']/div/div[1]/div[3]/div[2]/div/div[3]/div[2]")
	private WebElement totalPriceValue;

	@FindBy(xpath = "//*[@id='cart-section_QA']/div[3]/div[2]/div[2]/div[2]/div/div[3]/div[2]")
	private WebElement mTotalPriceValue;

	@FindBy(xpath = "//*[@id='cart-section_QA']/div/div[1]/div[3]/div[2]/div/div[2]/div[1]/div[1]")
	private WebElement standardDelivery;

	@FindBy(xpath = "//*[@id='cart-section_QA']/div[3]/div[2]/div[2]/div[2]/div/div[2]/div[1]/div[1]")
	private WebElement mStandardDelivery;

	@FindBy(xpath = "//*[@id='cart-section_QA']/div/div[1]/div[3]/div[2]/div/div[2]/div[1]/div[2]")
	private WebElement standardDeliverValue;

	@FindBy(xpath = "//*[@id='cart-section_QA']/div[3]/div[2]/div[2]/div[2]/div/div[2]/div[1]/div[2]")
	private WebElement mStandardDeliverValue;

	@FindBy(xpath = "//*[@id='cart-list']/div/div/div/div[3]/div[1]/div/div[1]")
	private WebElement mTotalText;

	@FindBy(xpath = "//*[@id='cart-list']/div/div/div/div[3]/div[1]/div/div[2]")
	private WebElement mTotalTextValue;

	@FindBy(xpath = "//*[@id='cart-subtotalsection_QA']/div[1]/div/img")
	private WebElement freeShippingIcon;

	@FindBy(xpath = "//div[@id='cart-section_QA']/div[1]/div/img")
	private WebElement mFreeShippingIcon;


	@FindBy(xpath = "//*[@id='cart-subtotalsection_QA']/div[1]")
	private WebElement freeShipping;

	@FindBy(xpath = "//*[@id='cart-section_QA']/div[1]/div")
	private WebElement mFreeShipping;

	@FindBy(xpath = "//*[@id='cart-subtotalsection_QA']/div[2]/div/div[1]")
	private WebElement subTotalWithNumberOfItemsAddedText;

	@FindBy(xpath = "//*[@id='cart-section_QA']/div[2]")
	private WebElement mSubTotalWithNumberOfItemsAddedText;

	@FindBy(xpath = "//*[@id='cart-checkoutnow_QA']/span")
	private WebElement checkOutNow;

	@FindBy(xpath = "//*[@id='proceedBtn_QA']")
	private WebElement mCheckOutNow;


	@FindBy(xpath = "//*[@id='cart-subtotalsection_QA']/div[2]/div/div[3]/div/img[2]")
	private WebElement masterCardIcon;

	@FindBy(xpath = "//div[@id='cart-subtotalsection_QA']/div/div/div/div/img[2]\n")
	private WebElement mMasterCardIcon;

	@FindBy(xpath = "//*[@id='cart-subtotalsection_QA']/div[2]/div/div[3]/div/img[1]")
	private WebElement visCardIcon;

	@FindBy(xpath = "//div[@id='cart-subtotalsection_QA']/div/div/div/div/img[1]\n")
	private WebElement mVisCardIcon;

	@FindBy(xpath = "//*[@id='cart-subtotalsection_QA']/div[2]/div/div[3]/div/img[3]")
	private WebElement cashOnDeliveryIcon;

	@FindBy(xpath = "//div[@id='cart-subtotalsection_QA']/div/div/div/div/img[3]\n")
	private WebElement mCashOnDeliveryIcon;

	@FindBy(xpath = "//*[@id='cart-subtotalsection_QA']/div[2]/div/div[3]/div/img[4]")
	private WebElement shukranIcon;

	@FindBy(xpath = "//div[@id='cart-subtotalsection_QA']/div/div/div/div/img[4]\n")
	private WebElement mShukranIcon;



	@FindBy(xpath = "//*[@id='main-part']/div/div[2]/div[2]/div/div[1]/div[1]")
	private WebElement subtotalAmountValue;


	@FindBy(xpath = "//*[@id='main-part']/div/div[2]/div[2]/div/div[2]/div[1]/img")
	private WebElement calendarIcon;

	@FindBy(xpath = "//*[@id='main-part']/div/div[2]/div[2]/div/div[2]/div[2]")
	private WebElement congratsMessage;


	@FindBy(xpath = "")
	private WebElement emiConversionMessage;

	@FindBy(xpath = "//*[@id='cart-helpinfo_QA']/div[1]")
	private WebElement needHelp;

	@FindBy(xpath = "//div[@id='cart-helpinfo_QA']/div[1]\n")
	private WebElement mNeedHelp;

	@FindBy(xpath = "//*[@id='cart-helpinfo_QA']/div[2]/a")
	private WebElement callCustomerSupport;

	@FindBy(xpath = "//div[@id='cart-helpinfo_QA']/div[2]/a\n")
	private WebElement mCallCustomerSupport;

	@FindBy(xpath = "//*[@id='cart-helpinfo_QA']/div[3]/a")
	private WebElement emailCustomerSupport;

	@FindBy(xpath = "//div[@id='cart-helpinfo_QA']/div[3]/a\n")
	private WebElement mEmailCustomerSupport;

	@FindBy(xpath = "//*[@id='cart-helpinfo_QA']/div[4]/a")
	private WebElement returnsPolicy;

	@FindBy(xpath = "//div[@id='cart-helpinfo_QA']/div[4]/a\n")
	private WebElement mReturnsPolicy;

	@FindBy(xpath = "//*[@id='promoInput']/div/div/input")
	private WebElement promoInput;

	@FindBy(xpath = "//*[@id='cart-section_QA']/div[3]/div[2]/div[2]/div[1]/div/div/div/div[1]/div/div/input")
	private WebElement mPromoInput;

	@FindBy(xpath = "//*[@id='cart-promo-apply_QA']")
	private WebElement applyPromo;

	@FindBy(xpath = "//*[@id='cart-promo-apply_QA']/span[1]")
	private WebElement mApplyPromo;

	@FindBy(xpath = "")
	private WebElement basketIconItems;



	@FindBy(xpath = "")
	private WebElement precart;

	@FindBy(xpath = "//*[@id='cart-list']//div[2]//div[1]/span[2]")
	private List<WebElement>  size;

	@FindBy(xpath = "//*[@id='cart-list']//div[2]//div[2]/span[2]")
	private List<WebElement>  color;

	@FindBy(xpath = "")
	private WebElement addToBasket;

	@FindBy(xpath = "")
	private WebElement precartMax;

	@FindBy(id = "cart-checkoutnow_QA")
	private WebElement checkoutNowButton;

	@FindBy(xpath = "")
	private WebElement conceptLogo;

	@FindBy(xpath = "")
	private WebElement productName;

	@FindBy(xpath = "")
	private WebElement productCount;

	@FindBy(xpath = "//*[@id='changeQuantity_QA']/span/div[1]/../../../div/div/div/div/div/ul")
	private List<WebElement> quantityValues;

	@FindBy(xpath = "//*[@id='changeQuantity_QA']/span/div[1]/../../../div/div/div/div/div/ul")
	private List<WebElement> mQuantityValues;

	@FindBy(xpath = "//*[@id='changeQuantity_QA']/span/div[1]")
	private WebElement selectedQuantity;

	@FindBy(xpath = "//*[@id='changeQuantity_QA']/span[1]")
	private WebElement mSelectedQuantity;

	@FindBy(xpath="//*[@id='cart-list']/div/div/div[4]/div[1]/div/div/div/div/div/div/ul/div[2]")
	private WebElement updateQuantity;

	@FindBy(xpath="//*[@id='cart-list']/div/div/div[2]/div[2]/div[2]/div[1]/div/div/div/div/div/div/div/ul/div[2]")
	private WebElement mUpdateQuantity;

	@FindBy(xpath = "//*[@id='cart-list']/div/div/div[5]")
	private WebElement productPriceTotalPrice;

	@FindBy(xpath = "//*[@id='cart-section_QA']/div[3]/div[2]/div[2]/div[2]/div/div[1]/div[2]")
	private WebElement mProductPriceTotalPrice;

	@FindBy(xpath = "//*[@id='cart-section_QA']/div/div[1]/div[3]/div[2]/div/div[3]/div[2]")
	private WebElement totalPrice;

	@FindBy(xpath = "//*[@id='cart-section_QA']/div[3]/div[2]/div[2]/div[2]/div/div[3]/div[2]")
	private WebElement mTotalPrice;

	@FindBy(xpath = "//*[@id='cart-list']/div/div[1]/div[1]/span")
	private WebElement climedOfferText;

	@FindBy(xpath = "//*[@id='cart-list']/div/div/div[2]/div[2]/div[4]/div/span[1]")
	private WebElement mClimedOfferText;

	@FindBy(xpath = "//img[@fetchpriority='high']/../../../div[3]/div")
	private WebElement productOriginalPrice;

	@FindBy(xpath = "(//img[@fetchpriority='high']/../../../div[2]/div[2]/div/div[1]/div)[1]")
	private WebElement mProductOriginalPrice;

	@FindBy(xpath = "/html/body/div[3]/div[3]/div/div/div[1]/div[2]/div[1]/div[2]/div[2]/button/span")
	private WebElement removePromoCode;

	@FindBy(xpath = "//*[@id='offer-modal-close']/span/div")
	private WebElement closePromoCodePopup;

	@FindBy(xpath = "")
	private WebElement emptyCart;

	@FindBy(xpath = "")
	private List<WebElement> rails;

	@FindBy(xpath = "")
	private WebElement checkoutRails;

	@FindBy(xpath = "")
	private WebElement referalPromoDiscountLabel;

	@FindBy(xpath = "")
	private WebElement referalPromoDiscountPrice;

	@FindBy(xpath = "")
	private WebElement referalamount;

	@FindBy(xpath = "")
	private WebElement referal_Discount_Applied;

	@FindBy(xpath = "")
	private WebElement removed;

	@FindBy(xpath = "")
	private WebElement referalToolTip;

	@FindBy(xpath = "")
	private WebElement referalModalBoxTitle;

	@FindBy(xpath = "")
	private WebElement referalModalBoxSubTitle;

	@FindBy(xpath = "")
	private WebElement referalModalBoxBrand;

	@FindBy(xpath = "")
	private WebElement referalModalBoxDiscount;

	@FindBy(xpath = "")
	private WebElement referalmodalBoxConcept;

	@FindBy(xpath = "")
	private WebElement referalmodalBoxConceptDiscountAmount;

	@FindBy(xpath = "")
	private WebElement referalmodalBoxGotIt;


	@FindBy(xpath = "")
	private WebElement basketNotification;

	@FindBy(xpath = "")
	private List<WebElement> saveForLater;

	@FindBy(id = "")
	private WebElement undoBtn;

	@FindBy(xpath = "")
	private WebElement notificationProductName;

	@FindBy(id = "")
	private WebElement bundlePromoShowAllLink;

	@FindBy(xpath = "")
	private WebElement MultiplyText;

	@FindBy(xpath = "")
	private WebElement promotionDetailsFlyOutBox;

	@FindBy(xpath = "")
	private WebElement promoOldPriceLabel;

	@FindBy(xpath = "")
	private WebElement missedOfferLabel;

	@FindBy(xpath = "")
	private WebElement offerClaimedLabel;

	//EMI
	@FindBy(xpath = "")
	private WebElement emiText;

	@FindBy(xpath = "")
	private WebElement emiClanderIcon;

	@FindBy(xpath = "")
	private WebElement emiLearnLink;


	@FindBy(xpath = "")
	private WebElement oldPriceLabel;

	@FindBy(xpath = "")
	private WebElement newPriceLabl;

	@FindBy(xpath = "")
	private WebElement youSavedLabel;

	@FindBy(xpath = "")
	private WebElement youSavedAmount;

	@FindBy(xpath="")
	private WebElement promocodeSection;

	@FindBy(xpath="")
	private WebElement useHereLink;

	@FindBy(xpath="")
	private WebElement giftIcon;

	@FindBy(xpath="")
	private WebElement enterPromoCode;


	@FindBy(xpath="")
	private WebElement invalidPromo;

	@FindBy(xpath="")
	private WebElement retry;

	@FindBy(xpath="")
	private WebElement closeError;

	@FindBy(xpath="")
	private WebElement againApplyPromo;

	@FindBy(xpath="")
	private WebElement gotAnotherPromo;

	@FindBy(xpath = "")
	private WebElement closeButton;


	@FindBy(xpath = "")
	private WebElement basket;

	/**
	 * This method is used validate BasketPage URL
	 */
	public void verifyBasketPage() {
		verifyProductInBasketPage();


	}

	/**
	 * This method is used to validate same product or not in PDP page and basket page
	 * */

	public void verifyProductInBasketPage() {
		SoftAssertions sa=new SoftAssertions();
		if (config.getBrowser().trim().equalsIgnoreCase("CHROME") || config.getBrowser().trim().equalsIgnoreCase("CHROMEHEADLESS") || config.getBrowser().trim().equalsIgnoreCase("MOBILECHROME")) {
			implicitWait(10);
			waitForPresenceOfElementLocated(cartProductDescription,driver,50);
			String cartProductDescriptionText = null;
			cartProductDescriptionText = cartProductDescription.getText().trim();
			sa.assertThat((TestDataBean.getProductTitle()).equals(cartProductDescriptionText));
		}else{
			String mCartProductDescriptionText = null;
			waitForVisibilityOfElementLocated(mProductDescription,driver,25);
			mCartProductDescriptionText = mProductDescription.getText().trim();
			sa.assertThat((TestDataBean.getProductTitle()).equals(mCartProductDescriptionText));
		}
		sa.assertAll();
	}

	/**
	 * This method is used for product header in Basket page
	 * */

	public void verifyProductHeaderSection() {
		SoftAssertions sa=new SoftAssertions();
		waitForPageToLoad(50);
		if(config.getBrowser().trim().equalsIgnoreCase("CHROME") || config.getBrowser().trim().equalsIgnoreCase("CHROMEHEADLESS") || config.getBrowser().trim().equalsIgnoreCase("MOBILECHROME") ) {
				sa.assertThat(itemLabel.getText().trim()).isEqualTo(configProperties.getProperty("basket.page.title"));
				sa.assertThat(descriptionLabel.getText().trim()).isEqualTo(configProperties.getProperty("basket.page.description"));
				sa.assertThat(priceLabel.getText().trim()).isEqualTo(configProperties.getProperty("basket.page.price"));
				sa.assertThat(quantityLabel.getText().trim()).isEqualTo(configProperties.getProperty("basket.page.quantity"));
				//sa.assertThat(totalPriceLabel.getText().trim()).isEqualTo(configProperties.getProperty("basket.page.totalPrice"));
				sa.assertThat(productImage.isDisplayed());
				sa.assertThat(cartProductDescription.isDisplayed());
				sa.assertThat(productPrice.size());
				sa.assertThat(productRemove.isDisplayed());
				sa.assertThat(quantityDropDownIcon.size());
				sa.assertThat(quantityDropdown.isDisplayed());
				sa.assertThat(productTotalPrice.isDisplayed());
		}else{
				sa.assertThat(mProductImage.isDisplayed());
				sa.assertThat(mProductDescription.isDisplayed());
				sa.assertThat(mProductPrice.isDisplayed());
				sa.assertThat(mProductRemove.isDisplayed());
				sa.assertThat(mQuantityDropDownIcon.isDisplayed());
				sa.assertThat(mQuantityDropdown.isDisplayed());
		}
		sa.assertAll();
	}

	/**
	 * This method used for update product quantity
	 * */

	public void updateQuantity(String quantity)  {
		if(config.getBrowser().trim().equalsIgnoreCase("CHROME") || config.getBrowser().trim().equalsIgnoreCase("CHROMEHEADLESS") || config.getBrowser().trim().equalsIgnoreCase("MOBILECHROME") ) {
			//waitForElementToBeClickable(quantityDropDownIcon,driver,5);
			quantityDropDownIcon.get(0).click();
			testLogger.info("Clicked On Quantity Dropdown");
			int count = quantityValues.size();
			WebElement updateQty=null;
			String preFix = "//*[@id='changeQuantity_QA']/span/div[1]/../../../div/div/div/div/div/ul/div[";
			String postFix = "]";
			for (int i = 1; i <= count; i++) {
				String path = preFix + quantity + postFix;
				updateQty = driver.findElement(By.xpath(path));
			}

			if (count > 0) {
				waitForElementToBeClickable(updateQty,driver,5);
				updateQty.click();
				testLogger.info("Quantity Updated");
			} else {
				testLogger.error("No Value To Update Quantity");
			}
		}
		else {
			waitForElementToBeClickable(mQuantityDropDownIcon, driver, 5);
			scrolldown();
			mQuantityDropDownIcon.click();
			testLogger.info("Clicked On Quantity Dropdown");
			int count = mQuantityValues.size();
			WebElement updateQty = null;
			String preFix = "(//*[@id='changeQuantity_QA']/span/div[1]/../../../div/div/div/div/div/ul/div[";
			String postFix = "])[1]";
			for (int i = 1; i <= count; i++) {
				String path = preFix + quantity + postFix;
				updateQty = driver.findElement(By.xpath(path));
			}

			if (count > 0) {
				waitForElementToBeClickable(updateQty, driver, 5);
				updateQty.click();
				testLogger.info("Quantity Updated");
			} else {
				testLogger.error("No Value To Update Quantity");
			}
		}
	}

	/**
	 * This method used for get product quantity
	 * */
	public String getQuantityValue() {
		String value = null;
		waitForElementPresent(1);
		if(config.getBrowser().trim().equalsIgnoreCase("CHROME") || config.getBrowser().trim().equalsIgnoreCase("CHROMEHEADLESS") || config.getBrowser().trim().equalsIgnoreCase("MOBILECHROME") ) {
			waitForVisibilityOfElementLocated(selectedQuantity,driver,5);
			value = selectedQuantity.getText();
			testLogger.info("Quantity Selected Is " + value);
		}else{
			waitForVisibilityOfElementLocated(mSelectedQuantity,driver,5);
			waitForPageToLoad(10);
			value = mSelectedQuantity.getText();
			waitForPageToLoad(10);
			testLogger.info("Quantity Selected Is " + value);
		}
		return value;
	}

	/**
	 * this method used for offer applied or not on particular product
	 * */
	public void verifyOfferAmountAppliedBasketPage(){
		SoftAssertions sa=new SoftAssertions();
		boolean flag=true;
		boolean offerFlag=verifyNoelement(climedOfferText);
		String productOriginalPriceValue=null;
		String TotalPriceValue=null;
		String TotalPriceValue2[]=null;
		double num1=0;
		double num2=0;
		if (config.getBrowser().trim().equalsIgnoreCase("CHROME") || config.getBrowser().trim().equalsIgnoreCase("CHROMEHEADLESS") || config.getBrowser().trim().equalsIgnoreCase("MOBILECHROME")) {
			if(flag==offerFlag) {
				if (config.getLanguage().equalsIgnoreCase("ar") ) {
					productOriginalPriceValue = productOriginalPrice.getText().trim();
					String[] part = productOriginalPriceValue.split(" ");
					String totalEGPValue = removeText(part[0]);
					num1 = Double.parseDouble(removeText(totalEGPValue));
					TotalPriceValue = productTotalPrice.getText().trim();
					String[] part1 = TotalPriceValue.split(" ");
					String totalEGPValue1 = removeText(part1[0]);
					num2 = Double.parseDouble(removeText(totalEGPValue1));
				}else{
					productOriginalPriceValue = productOriginalPrice.getText().trim();
					num1 = Double.parseDouble(removeText(productOriginalPriceValue));
					TotalPriceValue2 = productTotalPrice.getText().trim().split("\n");
					num2 = Double.parseDouble(removeText(TotalPriceValue2[0]));
				}
				if (num1 < num2) {
					sa.assertThat(climedOfferText.isDisplayed());
					testLogger.info("Product offer price is applied on Total Value");
				}
			}
		}else{
			if(flag==offerFlag) {
				if (config.getLanguage().equalsIgnoreCase("ar") ) {
					productOriginalPriceValue = mProductOriginalPrice.getText().trim();
					String[] part = productOriginalPriceValue.split(" ");
					String totalEGPValue = removeText(part[0]);
					num1 = Double.parseDouble(removeText(totalEGPValue));
					TotalPriceValue = mProductTotalPrice.getText().trim();
					String[] part1 = TotalPriceValue.split(" ");
					String totalEGPValue1 = removeText(part1[0]);
					num2 = Double.parseDouble(removeText(totalEGPValue1));
				}else{
					productOriginalPriceValue = mProductOriginalPrice.getText().trim();
					num1 = Double.parseDouble(removeText(productOriginalPriceValue));
					TotalPriceValue2 = mProductTotalPrice.getText().trim().split("\n");
					num2 = Double.parseDouble(removeText(TotalPriceValue2[0]));
				}
				if (num1 < num2) {
					sa.assertThat(mClimedOfferText.isDisplayed());
					testLogger.info("Product offer price is applied on Total Value");
				}
			}
		}
		sa.assertAll();

	}
	/**
	 * This method used for validate total amount in basket page
	 * */

	public void verifyTotalAmountBasketPage(){
		SoftAssertions sa=new SoftAssertions();
		String productPriceTotalPriceValue=null;
		String TotalPriceValue=null;
		if (config.getBrowser().trim().equalsIgnoreCase("CHROME") || config.getBrowser().trim().equalsIgnoreCase("CHROMEHEADLESS") || config.getBrowser().trim().equalsIgnoreCase("MOBILECHROME")) {
			productPriceTotalPriceValue=productPriceTotalPrice.getText().trim();
			String val1=removeText(productPriceTotalPriceValue);
			TotalPriceValue=totalPrice.getText().trim();
			String val2=removeText(TotalPriceValue);
			sa.assertThat(val1).isEqualTo(val2);
		}else{
			productPriceTotalPriceValue=mProductPriceTotalPrice.getText().trim();
			String val1=removeText(productPriceTotalPriceValue);
			TotalPriceValue=mTotalPrice.getText().trim();
			String val2=removeText(TotalPriceValue);
			sa.assertThat(val1).isEqualTo(val2);
		}
		sa.assertAll();
	}
	/**
	 * This method used for verify subtotal section
	 * */

	public void verifySubtotalSection() {
		SoftAssertions sa=new SoftAssertions();
		if(config.getBrowser().trim().equalsIgnoreCase("CHROME") || config.getBrowser().trim().equalsIgnoreCase("CHROMEHEADLESS") || config.getBrowser().trim().equalsIgnoreCase("MOBILECHROME") ) {
				sa.assertThat(freeShippingIcon.isDisplayed());
				sa.assertThat(subTotalWithNumberOfItemsAddedText.isDisplayed());
				sa.assertThat(checkOutNow.getText().trim()).isEqualTo(configProperties.getProperty("basket.page.checkout.now"));
				sa.assertThat(subtotal.getText().trim()).isEqualTo(configProperties.getProperty("basket.page.totals.subtotal"));
				sa.assertThat(subTotalAmount.isDisplayed());
				sa.assertThat(totalPriceText.getText().trim()).isEqualTo(configProperties.getProperty("basket.page.total"));
//				sa.assertThat(includingVatText.getText().trim()).isEqualTo(configProperties.getProperty("basket.page.total.includingVat"));
				sa.assertThat(totalPriceValue.isDisplayed());
//				sa.assertThat(standardDelivery.getText().trim()).isEqualTo(configProperties.getProperty("basket.page.totals.standard.delivery"));
//				sa.assertThat(standardDeliverValue.isDisplayed());
				sa.assertThat(shippingChargesMayApply.getText().trim()).isEqualTo(configProperties.getProperty("basket.page.shipping.charges.may.apply"));
		}
		else{
				sa.assertThat(mFreeShippingIcon.isDisplayed());
				sa.assertThat(mSubTotalWithNumberOfItemsAddedText.getText().trim().isEmpty());
				sa.assertThat(mCheckOutNow.getText().trim()).isEqualTo(configProperties.getProperty("basket.page.proceed.to.shipping"));
				sa.assertThat(mSubtotal.getText().trim()).isEqualTo(configProperties.getProperty("basket.page.totals.subtotal"));
				sa.assertThat(mSubTotalAmount.isDisplayed());
//				sa.assertThat(mStandardDelivery.getText().trim()).isEqualTo(configProperties.getProperty("basket.page.totals.standard.delivery"));
//				sa.assertThat(mStandardDeliverValue.isDisplayed());
				sa.assertThat(mTotalPriceText.getText().trim()).isEqualTo(configProperties.getProperty("basket.page.total"));
//				sa.assertThat(mIncludingVatText.getText().trim()).isEqualTo(configProperties.getProperty("basket.page.total.includingVat"));
				sa.assertThat(mTotalPriceValue.isDisplayed());
				sa.assertThat(mShippingChargesMayApply.getText().trim()).isEqualTo(configProperties.getProperty("basket.page.shipping.charges.may.apply"));
				sa.assertThat(mTotalText.getText().trim()).isEqualTo(configProperties.getProperty("basket.page.totals.total"));
				sa.assertThat(mTotalTextValue.isDisplayed());
				sa.assertThat(mCheckOutNow.isDisplayed());
		}
		sa.assertAll();
	}

	/**
	 * This method used for verify card section
	 * */

	public void verifyCardSection() {
		SoftAssertions sa=new SoftAssertions();
		if(config.getBrowser().trim().equalsIgnoreCase("CHROME") || config.getBrowser().trim().equalsIgnoreCase("CHROMEHEADLESS") || config.getBrowser().trim().equalsIgnoreCase("MOBILECHROME") ) {
			sa.assertThat(masterCardIcon.isDisplayed());
			sa.assertThat(visCardIcon.isDisplayed());
			sa.assertThat(cashOnDeliveryIcon.isDisplayed());
			sa.assertThat(shukranIcon.isDisplayed());
		}
		else{
			sa.assertThat(mMasterCardIcon.isDisplayed());
			sa.assertThat(mVisCardIcon.isDisplayed());
			sa.assertThat(mCashOnDeliveryIcon.isDisplayed());
			sa.assertThat(mShukranIcon.isDisplayed());
		}
		sa.assertAll();
	}

	/**
	 * This method used for verify Promo and Gift card section
	 * */

	public void verifyPromoAndGiftCardSection() {
		waitForPageToLoad(100);
		scrolldown();
		SoftAssertions sa=new SoftAssertions();
		sa.assertThat(promosAndVoucherText1.getText().trim()).isEqualTo(configProperties.getProperty("basket.page.footer.promoCode"));
		sa.assertThat(viewPromosEnterVoucherCodeText.getText().trim()).isEqualTo(configProperties.getProperty("basket.page.footer.promoCode.view.enter.voucher.code.text"));
		sa.assertThat(viewPromosEnterVoucherCodeTextIcon.isDisplayed());
		promosAndVoucherText1.click();
		boolean status=verifyNoelement(promoCodeApplied);
		if(status){
			removePromoCode.click();
		}
		sa.assertThat(promosAndVoucherTextONPopup.getText().trim()).isEqualTo(configProperties.getProperty("basket.page.footer.promoCode"));
		sa.assertThat(gotAPromoCodeTextONPopup.getText().trim()).isEqualTo(configProperties.getProperty("basket.page.footer.promoCode.got.promocode.text.popup"));
		enterPromoTextBoxPresent();
		applyButtonPresent();
		sa.assertThat(promoCodeHeaderONPopup.isDisplayed());
//		sa.assertThat(promoCodeHeaderValueONPopup.isDisplayed());
//		sa.assertThat(promoCodeHeaderValueDateONPopup.isDisplayed());
//		sa.assertThat(promoCodeHeaderValueCopyONPopup.isDisplayed());
		//sa.assertThat(applyPromoVoucherTextONPopup.getText().trim()).isEqualTo(configProperties.getProperty("basket.enter.apply.voucher"));
		closePromoCodePopup.click();
		sa.assertAll();
	}

	/**
	 * This method used for verify need help section
	 * */

	public void verifyNeedHelpSection() {
		SoftAssertions sa=new SoftAssertions();
		if(config.getBrowser().trim().equalsIgnoreCase("CHROME") || config.getBrowser().trim().equalsIgnoreCase("CHROMEHEADLESS") || config.getBrowser().trim().equalsIgnoreCase("MOBILECHROME") ) {
				sa.assertThat(needHelp.getText().trim()).isEqualTo(configProperties.getProperty("basket.page.footer.contact.needHelp"));
				sa.assertThat(callCustomerSupport.getText().trim()).isEqualTo(configProperties.getProperty("basket.page.footer.contact.callCustomer.egypt"));
				sa.assertThat(emailCustomerSupport.getText().trim()).isEqualTo(configProperties.getProperty("write.to.us"));
				sa.assertThat(returnsPolicy.getText().trim()).isEqualTo(configProperties.getProperty("help.title.return"));
		}
		else{
				sa.assertThat(mNeedHelp.getText().trim()).isEqualTo(configProperties.getProperty("basket.page.footer.contact.needHelp"));
				sa.assertThat(mCallCustomerSupport.getText().trim()).isEqualTo(configProperties.getProperty("basket.page.footer.contact.callCustomer.egypt"));
				sa.assertThat(mEmailCustomerSupport.getText().trim()).isEqualTo(configProperties.getProperty("write.to.us"));
				sa.assertThat(mReturnsPolicy.getText().trim()).isEqualTo(configProperties.getProperty("help.title.return"));
		}
		sa.assertAll();
	}

	/**
	 * This method used remove product in basket page
	 * */

	public void removeProduct() {
			waitForPageToLoad(10);
			closePromoCodePopup.click();
			waitForElementToBeClickable(productRemove, 10);
			productRemove.click();
	}

	public void removeAllProductInBasket() {

		while(verifyNoelement(productRemove)){
			waitForElementToBeClickable(productRemove, 10);
			productRemove.click();
			pageRefresh();
			waitForPageToLoad(10);
		}
	}

	/**
	 * This method used for click on Use It Here in Basket page
	 * */
	public void clickUseItHereLink() {
			waitForElementToBeClickable(useItHere, 3);
			useItHere.click();
			waitForPageToLoad(15);
		testLogger.info("clicked on Use it here link on Basket page ");
	}
	/**
	 * This method used for promo text box is available or not
	 * */
	public void enterPromoTextBoxPresent() {
		boolean enterPromoBox=false;
		enterPromoBox = promoInput.isDisplayed();
			if(enterPromoBox){
				testLogger.info("enter promo code text box present in basket page");
			}else{
				testLogger.info("enter promo code text box not present in basket page");
			}

		//return enterPromoBox;
	}
	/**
	 * This method used for apply button is available or not
	 * */
	public void applyButtonPresent() {
		boolean apply=false;
		if (config.getBrowser().trim().equalsIgnoreCase("CHROME") || config.getBrowser().trim().equalsIgnoreCase("CHROMEHEADLESS") || config.getBrowser().trim().equalsIgnoreCase("MOBILECHROME")) {
			apply = applyPromo.isDisplayed();
			if(apply){
				testLogger.info("apply button promocode is present in basket page");
			}else{
				testLogger.info("apply button promocode is not present in basket page");
			}
		}
		else{
			apply = mApplyPromo.isDisplayed();
			if(apply){
				testLogger.info("apply button promocode is present in basket page");
			}else{
				testLogger.info("apply button promocode is not present in basket page");
			}
		}
		testLogger.info("apply button promocode is present in basket page");
		//return apply;
	}
	/**
	 * This method used for enter promo code in basket page
	 * */

	public void enterPromoCode(String promoCodeText, String promoCode) {
		promoCode(promoCodeText, promoCode);
		String promoCodeValue=CardDataBean.getPromoCode();
			waitForElementToBeClickable(promoInput, 3);
			enterPromoCode(promoInput, promoCodeValue);
	}

	/**
	 * This method used for enter apply promo voucher in basket page
	 * */
	public void applyVoucher() {
		waitForElementToBeClickable(promosAndVoucherText,driver,10);
		promosAndVoucherText.click();
		testLogger.info("clicked on apply promo voucher");
	}

	/**
	 * This method used for verify for duplicate promo code error message
	 * */

	public void verifyDuplicatePromoCodeErrorMessage() {
		waitForPageToLoad(25);
		waitForElementPresent(2);
		SoftAssertions sa=new SoftAssertions();
		String duplicatePromoCodeErrorMessageTextValue = null;
		waitForVisibilityOfElementLocated(duplicatePromoCodeErrorMessageText,driver,5);
		duplicatePromoCodeErrorMessageTextValue = duplicatePromoCodeErrorMessageText.getText().trim();
		sa.assertThat(duplicatePromoCodeErrorMessageTextValue).isEqualTo(configProperties.getProperty("basket.page.footer.promoCode.already.used.this.voucher.egypt"));
		sa.assertAll();
	}
	/**
	 * This method used for product removed or not in basket page
	 * */

	public void verifyProductRemovedFromCartPage() {
		waitForPageToLoad(10);
		waitForElementPresent(1);
		SoftAssertions sa=new SoftAssertions();
		if(config.getBrowser().trim().equalsIgnoreCase("CHROME") || config.getBrowser().trim().equalsIgnoreCase("CHROMEHEADLESS") || config.getBrowser().trim().equalsIgnoreCase("MOBILECHROME") ) {
			waitForVisibilityOfElementLocated(productEmpty,driver,15);
			sa.assertThat(productEmpty.getText().trim()).isEqualTo(configProperties.getProperty("basket.page.empty.cart"));
			waitForPresenceOfElementLocated(goOnAddSomeThing,driver,10);
			sa.assertThat(goOnAddSomeThing.getText().trim()).isEqualTo(configProperties.getProperty("basket.page.empty.cart.add.something"));
		}else{
			waitForVisibilityOfElementLocated(mProductEmpty,driver,15);
			sa.assertThat(mProductEmpty.getText().trim()).isEqualTo(configProperties.getProperty("basket.page.empty.cart"));
			waitForPresenceOfElementLocated(mGoOnAddSomeThing,driver,10);
			sa.assertThat(mGoOnAddSomeThing.getText().trim()).isEqualTo(configProperties.getProperty("basket.page.empty.cart.add.something"));
		}
		sa.assertAll();
	}
	/**
	 * This method used for click on checkout button in basket page
	 * */
	public void clickOnStartShopping(){
		if(config.getBrowser().trim().equalsIgnoreCase("CHROME") || config.getBrowser().trim().equalsIgnoreCase("CHROMEHEADLESS") || config.getBrowser().trim().equalsIgnoreCase("MOBILECHROME") ) {
			waitForElementToBeClickable(startShoppingButton, 5);
			startShoppingButton.click();
		}else{
			waitForElementToBeClickable(mStartShoppingButton, 5);
			mStartShoppingButton.click();
		}
	}

	/**
	 * This method used for click on checkout button in basket page
	 * */

	public void checkoutButton() {

		if(config.getBrowser().trim().equalsIgnoreCase("CHROME") || config.getBrowser().trim().equalsIgnoreCase("CHROMEHEADLESS") || config.getBrowser().trim().equalsIgnoreCase("MOBILECHROME") ) {
			waitForPageToLoad(10);
			setCartData();
			waitForElementToBeClickable(checkOutNow, 15);
			checkOutNow.click();


		}
		else {
			waitForElementToBeClickable(mCheckOutNow, 15);
			mCheckOutNow.click();
		}
	}

	private JSONObject setCartData() {

		JSONArray productDescriptionData = setListData(productDescription);
		JSONArray sizeData  = setListData(size);
		JSONArray colorData  = setListData(color);
		JSONArray productPriceData  = setListData(productPrice);
		JSONArray quantityDropDownIconData  = setListData(quantityDropDownIcon);
		JSONArray totalPriceLabelData  = setListData(totalPriceLabel);

		System.out.println("test");

		JSONObject cart = new JSONObject();
		for (int i = 0; i < productDescriptionData.length(); i++) {
			JSONObject product = new JSONObject();
			product.put("productName", productDescriptionData.get(i).toString());
			product.put("size", sizeData.get(i).toString());
			product.put("color", colorData.get(i).toString());
			product.put("price", (productPriceData.get(i).toString().replaceAll("[^0-9]", "")));
			product.put("quantity", quantityDropDownIconData.get(i).toString());
			product.put("actualPrice", totalPriceLabelData.get(i).toString().replaceAll("[^0-9]", ""));
			//product.put("discountPrice", productsData.get(i).toString());
			cart.put("product" + (i + 1), product);
		}

		cart.put("subTotal", subtotal.getText().trim().replaceAll("[^0-9]", ""));
		cart.put("total", productTotalPrice.getText().trim().replaceAll("[^0-9]", ""));
//		cart.put("voucher", subtotal.getText().trim());
//		cart.put("totalItem", subtotal.getText().trim());
//		cart.put("totalQuantity", subtotal.getText().trim());
//		cart.put("ShukranEarnPoints", subtotal.getText().trim());
		//System.out.println("test");

		try (FileWriter file = new FileWriter("output.json")) {
			file.write(cart.toString());
			System.out.println("JSON data has been saved to output.json");
		} catch (IOException e) {
			e.printStackTrace();
		}

		return cart;
	}



	public void clickMiniBasket() {
		basket.click();
		checkoutNowButton.click();
	}



	public boolean verifyItemLabel() {
		boolean res;
		res = itemLabel.isDisplayed();
		testLogger.info("Item Label Is Displayed " + res);
		return res;
	}

	public boolean verifyDescriptionLabel() {
		boolean res;
		res = descriptionLabel.isDisplayed();
		testLogger.info("Description Label Is Displayed " + res);
		return res;
	}

	public boolean verifyPriceLabel() {
		boolean res;
		res = priceLabel.isDisplayed();
		testLogger.info("Price Label Is Displayed " + res);
		return res;
	}

	public boolean verifyQuantityLabel() {
		boolean res;
		res = quantityLabel.isDisplayed();
		testLogger.info("Quantity Label Is Displayed " + res);
		return res;
	}

	public boolean verifyTotalPriceLabel() {
		boolean res;
		res = totalPriceLabel.isEmpty();
		testLogger.info("Total Price Label Is Displayed " + res);
		return res;
	}

	public boolean verifyConceptLogo() {
		boolean res;
		res = conceptLogo.isDisplayed();
		testLogger.info("Concept Logo Is Displayed On Basket Page " + res);
		return res;
	}

	public boolean verifyProductName() {
		boolean res;
		res = productName.isDisplayed();
		testLogger.info("Product Name Is Displayed On Basket Page " + res);
		return res;
	}

	public boolean verifyProductImage() {
		boolean res;
		res = productImage.isDisplayed();
		testLogger.info("Product Image Is Displayed On Basket Page " + res);
		return res;
	}

	public boolean verifyProductPrice() {
		boolean res;
		res = productPrice.get(0).isDisplayed();
		testLogger.info("Product Price Is Displayed On Basket Page " + res);
		return res;
	}

	public boolean verifyProductCount() {
		boolean res;
		res = productCount.isDisplayed();
		testLogger.info("Product Count Is Displayed On Basket Page " + res);
		return res;
	}

	public boolean verifyProductTotalPrice() {
		boolean res;
		res = productTotalPrice.isDisplayed();
		testLogger.info("Product Total Price Is Displayed On Basket Page "
				+ res);
		return res;
	}


	public void updateQuantityBackToNormal() {
		quantityDropdown.click();
		testLogger.info("Clicked On Quantity Dropdown");
		int count = quantityValues.size();
		if (count > 0) {
			quantityValues.get(0).click();
			waitForPageToLoad(10);
			testLogger.info("Quantity Updated To Normal");
		} else {
			testLogger.error("No Value To Update Quantity");
		}
	}

	public boolean verifyQuantityUpdated(String val1, String val2) {
		boolean res;
		res = (!val1.equalsIgnoreCase(val2));
		return res;
	}


	public boolean verifyProductRemoved() {
		boolean res;
		res = emptyCart.isDisplayed();
		testLogger.info("Product Removed And Cart Is Empty " + res);
		return res;
	}

	public void preCart() {
		testLogger.info("Pre cart Button is :" + "precart");
		{
			if (concept.equalsIgnoreCase("Max")) {
				waitForElementToBeClickable(precartMax, 20);
				precartMax.click();
			}
			else {

				waitForElementToBeClickable(precart, 20);
				precart.click();
			}
		}
	}

	public String youMayAlsoLike_Rail() throws InterruptedException {
		String rail;
		rail = rails.get(0).getText().trim();
		testLogger.info("You May Also Like Rails is : " + rails);
		return rail;
	}

	public String customers_also_viewed_Rail() throws InterruptedException {
		String rail;
		rail = rails.get(1).getText().trim();
		testLogger.info("Customers Also Viewed Rails is: " + rails);
		return rail;
	}

	public String youMayAlsoLike_Rail_Checkout() throws InterruptedException {
		String rail;
		waitForVisibilityOfElementLocated(checkoutRails,driver,3);
		rail = checkoutRails.getText().trim();
		testLogger.info("You May Also Like Rails is  In checkout page: "
				+ rails);
		return rail;
	}

	public String referalPromoDiscountLabel() throws InterruptedException {
		String title;
		title = referalPromoDiscountLabel.getText().trim();
		testLogger.info("Referal Promo Discount Label is: "
				+ referalPromoDiscountLabel);
		return title;
	}

	public boolean referalPromoAmount() {
		boolean res;
		res = referalPromoDiscountPrice.isDisplayed();
		testLogger.info("Promo Discount Price Is Displayed " + res);
		return res;
	}

	public boolean referalPromoAmount_Applied() {
		boolean res;
		res = referalamount.isDisplayed();
		testLogger.info("Promo Applied Amount Price Is Displayed" + res);
		return res;
	}

	public String referralDiscountApplied_Tag() throws InterruptedException {
		String title;
		waitForVisibilityOfElementLocated(referal_Discount_Applied,driver,3);
		title = referal_Discount_Applied.getText().trim();
		testLogger.info("Referal Promo Discount Applied  Label is: "
				+ referal_Discount_Applied);
		return title;
	}

	public String referral_Remove() throws InterruptedException {
		String title;
		waitForVisibilityOfElementLocated(removed,driver,3);
		title = removed.getText().trim();
		testLogger.info("Referal Promo Remove Link is: "
				+ removed);
		return title;
	}

	public void toolTip() {
		waitForElementToBeClickable(referalToolTip,5);
		referalToolTip.click();
	}

	public String referalModalBoxTitle() throws InterruptedException {
		String title;
		title = referalModalBoxTitle.getText().trim();
		testLogger.info("Referal Modal Box Title is: " + referalModalBoxTitle);
		return title;
	}

	public String referalModalBoxSubTitle() throws InterruptedException {
		String title;
		title = referalModalBoxSubTitle.getText().trim();
		testLogger.info("Referal Modal Box Sub Title is: "
				+ referalModalBoxSubTitle);
		return title;
	}

	public String referalModalBoxBrand() throws InterruptedException {
		String title;
		title = referalModalBoxBrand.getText().trim();
		testLogger.info("Referal Modal Box Brand is: " + referalModalBoxBrand);
		return title;
	}

	public String referalModalBoxDiscount() throws InterruptedException {
		String title;
		title = referalModalBoxDiscount.getText().trim();
		testLogger.info("Referal Modal Box Discount is: "
				+ referalModalBoxDiscount);
		return title;
	}

	public boolean referalmodalBoxConcept() {
		boolean res;
		res = referalmodalBoxConcept.isDisplayed();
		testLogger.info("Referal Model Box Concept : Displayed" + res);
		return res;
	}

	public boolean referalmodalBoxConcepDiscountAmount() {
		boolean res;
		res = referalmodalBoxConceptDiscountAmount.isDisplayed();
		testLogger.info("Referal Model Box Concept Discount Amount : Displayed"
				+ res);
		return res;
	}

	public String referalModalBoxGotit_Button() throws InterruptedException {
		String title;
		title = referalmodalBoxGotIt.getText().trim();
		testLogger.info("Referal Modal Box Okay Got It Button is: "
				+ referalmodalBoxGotIt);
		waitForWebElementVisibility(referalmodalBoxGotIt,5);
		referalmodalBoxGotIt.click();
		return title;
	}


	public boolean isUndoNotificationPanelShown() {
		boolean res;
		res = basketNotification.isDisplayed();
		testLogger.info("Undo / remove element from basket notification box : Displayed"
				+ res);
		return res;
	}

	public boolean saveItemForLater() {
		int productsCount = saveForLater.size();
		testLogger.info("Count of Products in the basket to save for later is: " + productsCount);
		List<WebElement> products = saveForLater;
		products.get(new Random().nextInt(products.size())).click();
		return true;
	}

	public void undoProductAgainToBasket() {
		waitForElementToBeClickable(basketNotification, 10);
		undoBtn.click();
	}

	public String getProductNameFromUndoNotification() {
		waitForVisibilityOfElementLocated(notificationProductName,driver,5);
		String name = notificationProductName.getText().trim();
		return name;
	}
	public boolean verifyProductBackToBasketOnUndo() {
		boolean res;
		String product_Name = productName.getText().trim();
		String notification_name = getProductNameFromUndoNotification();
		res = product_Name.equalsIgnoreCase(notification_name);
		return res;
	}

	public boolean isBundlePromoShowAllDetailsLinkShown() {
		waitForVisibilityOfElementLocated(bundlePromoShowAllLink,driver,5);
		boolean isShown;
		isShown = bundlePromoShowAllLink.isDisplayed();
		testLogger.info("bundle promotion show all deatils link is shown: "
				+ isShown);

		return isShown;
	}

	public void clickOnShowAllDetails() {
		waitForElementToBeClickable(bundlePromoShowAllLink, 2);
		bundlePromoShowAllLink.click();

	}

	public boolean verifyBundlePromoQuanitiy(String expectedText) {
		String text;
		boolean isShown = true;
		waitForVisibilityOfElementLocated(MultiplyText,driver,5);
		text = MultiplyText.getText().trim();
		if (text.equalsIgnoreCase(expectedText)) {
			testLogger.info("bundle promotion quantity text is displayed " + text);

		} else {
			testLogger.info("bundle promotion quantity text is displayed " + text);
		}

		return isShown;
	}

	public boolean isPromotionDetailsFlyOutBoxShown() {
		waitForVisibilityOfElementLocated(promotionDetailsFlyOutBox,driver,5);
		boolean isShown;
		isShown = promotionDetailsFlyOutBox.isDisplayed();
		testLogger.info("promotion details flyout box is shown: "
				+ isShown);

		return isShown;
	}

	public boolean isPromoOldPriceStriked() {
		waitForVisibilityOfElementLocated(promoOldPriceLabel,driver,5);
		boolean res = true;
		String cssText = promoOldPriceLabel.getCssValue("text-decoration");
		if (cssText.equalsIgnoreCase("line-through;")) {
			testLogger.info("old price label text css is " + cssText);
		} else {
			testLogger.info("old price label text css is  " + cssText);
		}

		return res;
	}

	//emi
	public String emi_Text() throws InterruptedException {
		String text;
		text = emiText.getText().trim();
		testLogger.info("Emi Text Under Basket Page : " + text);
		return text;
	}

	public boolean emi_Claender() {
		boolean res;
		res = emiClanderIcon.isDisplayed();
		testLogger.info("Emi Calendar Label Is Displayed " + res);
		return res;
	}

	public void learnLink() {
		waitForVisibilityOfElementLocated(emiLearnLink,driver,5);
		javaScriptExecute(emiLearnLink);
	}




	public boolean isMissedOfferLabelShown() {
		waitForVisibilityOfElementLocated(missedOfferLabel,driver,5);
		boolean isShown = true;;
		isShown = missedOfferLabel.isDisplayed();
		testLogger.info("missed offer promotion label is displayed " + isShown);
		return isShown;
	}

	public boolean isOfferClaimedLabelShown() {
		waitForVisibilityOfElementLocated(offerClaimedLabel,driver,5);
		boolean isShown = true;
		isShown = offerClaimedLabel.isDisplayed();
		testLogger.info("missed offer claimed label is displayed " + isShown);
		return isShown;
	}

	public String getOfferClaimedLabelTextColor() {
		waitForVisibilityOfElementLocated(offerClaimedLabel,driver,5);
		String textColor = offerClaimedLabel.getCssValue("color");
		testLogger.info("Offer claimed label has Text color " + textColor);
		return textColor;
	}

	public String getOfferClaimedLabelBackgroundColor() {
		waitForVisibilityOfElementLocated(offerClaimedLabel,driver,5);
		String backgroundColor = offerClaimedLabel.getCssValue("background");
		testLogger.info("Offer claimed label has Background color " + backgroundColor);
		return backgroundColor;
	}

	public boolean isOldPriceLabelShown() {
		waitForVisibilityOfElementLocated(oldPriceLabel,driver,5);
		boolean isShown = oldPriceLabel.isDisplayed();
		testLogger.info("old price label is shown " + isShown);
		return isShown;
	}

	public boolean isYouSavedLabelShown() {
		waitForVisibilityOfElementLocated(youSavedLabel,driver,5);
		boolean isShown = youSavedLabel.isDisplayed();
		testLogger.info("you saved label is shown " + isShown);
		return isShown;
	}

	public int getProductPrice() {
		int p = 0;
		String price = productPrice.get(0).getText();
		String[] data = price.split(" ");
		p = Integer.parseInt(data[1]);
		return p;
	}

	public int getProductTotalPrice() {
		int p = 0;
		String price = totalPriceLabel.get(0).getText();
		String[] data = price.split(" ");
		p = Integer.parseInt(data[1]);
		return p;
	}

	public int getWasPrice() {
		int p = 0;
		String price = promoOldPriceLabel.getText();
		String[] data = price.split(" ");
		p = Integer.parseInt(data[1]);
		return p;
	}

	public int getNewPrice() {
		int p = 0;
		String price = newPriceLabl.getText();
		String[] data = price.split(" ");
		p = Integer.parseInt(data[2]);
		return p;
	}

	public int getYouSavedAmount() {
		int p = 0;
		String price = youSavedAmount.getText();
		String[] data = price.split(" ");
		p = Integer.parseInt(data[1]);
		return p;
	}

	public boolean giftIconPresent() {
		boolean gift=giftIcon.isDisplayed();
		return gift;}

	public boolean promoCodeSectionPresent() {
		boolean promocode=promocodeSection.isDisplayed();
		return promocode;}

	public boolean useHereLinkPresent() {
		boolean useHere=useHereLink.isDisplayed();
		return useHere;}



	public void enterVoucher() {
		enterPromoCode.clear();
		//enterPromoCode.sendKeys("PROMO50");
		testLogger.info("promocode entered in bastet page");}

	public String verifyYourShoppingBasket() {
		waitForWebElementPresent(yourShippingBasket,10);
		return yourShippingBasket.getText().trim();
	}

	public void verifyWarrantySection() {
		SoftAssertions sa=new SoftAssertions();
		sa.assertThat(productExtendWarrantyImg.isDisplayed());
		sa.assertThat(extendWarrantyTotalPrice.getText().trim().isEmpty());
		sa.assertThat(productExtendWarrantyDescription.getText().trim().isEmpty());
		sa.assertThat(productExtendWarrantyPrice.getText().trim().isEmpty());
		sa.assertThat(productExtendWarrantyTitle.getText().trim().isEmpty());
		sa.assertThat(extendWarrantyEdit.getText().trim().isEmpty()).isEqualTo("Edit");
		sa.assertAll();
	}
	public double verifyOrderValue() {
		waitForPageToLoad(40);
		String TotalPriceValue = null;
		double doubleFortotalEGPValue=0;
		if (config.getBrowser().trim().equalsIgnoreCase("CHROME") || config.getBrowser().trim().equalsIgnoreCase("CHROMEHEADLESS") || config.getBrowser().trim().equalsIgnoreCase("MOBILECHROME")) {
			TotalPriceValue = totalPrice.getText().trim();
			if (config.getLanguage().equalsIgnoreCase("ar")){
				String[] part = TotalPriceValue.split(" ");
				String totalEGPValue = removeText(part[0]);
				doubleFortotalEGPValue = Double.parseDouble(totalEGPValue);
			}else {
				String totalEGPValue = removeText(TotalPriceValue);
				doubleFortotalEGPValue = Double.parseDouble(totalEGPValue);
			}
		}
		else{
			TotalPriceValue = mTotalPrice.getText().trim();
			if (config.getLanguage().equalsIgnoreCase("ar")){
				String[] part = TotalPriceValue.split(" ");
				String totalEGPValue = removeText(part[0]);
				doubleFortotalEGPValue = Double.parseDouble(totalEGPValue);
			}else {
				String totalEGPValue = removeText(TotalPriceValue);
				doubleFortotalEGPValue = Double.parseDouble(totalEGPValue);
			}
		}
		return  doubleFortotalEGPValue;
	}

	public void verifyShippingMessage() {
		scrollUp();
		int above200;
		int below139;
		int above140;
		int below200;
		double orderValue=verifyOrderValue();
		int orderValue1 = 0;
		if ((orderValue % 1) == 0){
			orderValue1=  (int)orderValue;
		}
			above200 = Integer.parseInt(configProperties.getProperty("basket.page.free.shipping.checkout.now.above200"));
			below139 = Integer.parseInt(configProperties.getProperty("basket.page.free.shipping.charges.below139"));
			above140 = Integer.parseInt(configProperties.getProperty("basket.page.still.need.free.shipping.first.value"));
			below200 = Integer.parseInt(configProperties.getProperty("basket.page.still.need.free.shipping.last.value"));
		if (config.getBrowser().trim().equalsIgnoreCase("CHROME") || config.getBrowser().trim().equalsIgnoreCase("CHROMEHEADLESS") || config.getBrowser().trim().equalsIgnoreCase("MOBILECHROME")) {
			String msg = freeShipping.getText().replace("\n", "");
			SoftAssertions sa = new SoftAssertions();
			if (orderValue >= above200 || orderValue1 >= above200)
			{
					sa.assertThat(msg).isEqualTo(configProperties.getProperty("basket.page.free.shipping.checkout.now"));
			} else if (orderValue>=above140 && orderValue <= below200 || orderValue1>=above140 && orderValue1 <= below200) {
				System.out.println("msg"+msg);
				double TotalPriceValue = 0;
				String finalString=null;
				TotalPriceValue = verifyOrderValue();
				double egpVal=200-TotalPriceValue;
					String firstString=configProperties.getProperty("basket.page.still.need.free.shipping.first.string");
					String lastString=configProperties.getProperty("basket.page.still.need.free.shipping.last.string");
					if ((egpVal % 1) == 0){
						int egpVal1=  (int)egpVal;
						finalString=firstString+egpVal1+lastString;
					}else {
						DecimalFormat numberFormat = new DecimalFormat("#.##");
						numberFormat.setRoundingMode(RoundingMode.FLOOR);
						finalString = firstString + numberFormat.format(egpVal) + lastString;
					}
					sa.assertThat(msg).isEqualTo(finalString);
			} else if(orderValue <= below139 || orderValue1 <= below139){
					sa.assertThat(msg).isEqualTo(configProperties.getProperty("basket.page.free.shipping.charges.over.egp200"));
			} else{
				testLogger.info("user not added proper value product");
			}
			sa.assertAll();
		}
		else {
			String msg = mFreeShipping.getText().replace("\n", "");
			SoftAssertions sa = new SoftAssertions();
			if (orderValue >= above200 || orderValue1 >= above200) {
					sa.assertThat(msg).isEqualTo(configProperties.getProperty("basket.page.free.shipping.checkout.now"));
			} else if (orderValue >= above140 && orderValue <= below200 || orderValue1 >= above140 && orderValue1 <= below200) {
				System.out.println("msg" + msg);
				double TotalPriceValue = 0;
				String finalString = null;
				TotalPriceValue = verifyOrderValue();
				double egpVal = 200 - TotalPriceValue;
					String firstString = configProperties.getProperty("basket.page.still.need.free.shipping.first.string");
					String lastString = configProperties.getProperty("basket.page.still.need.free.shipping.last.string");
					if ((egpVal % 1) == 0) {
						int egpVal1 = (int) egpVal;
						finalString = firstString + egpVal1 + lastString;
					} else {
						DecimalFormat numberFormat = new DecimalFormat("#.##");
						numberFormat.setRoundingMode(RoundingMode.FLOOR);
						finalString = firstString + numberFormat.format(egpVal) + lastString;
					}
					sa.assertThat(msg).isEqualTo(finalString);
			} else if (orderValue <= below139 || orderValue1 <= below139) {
					sa.assertThat(msg).isEqualTo(configProperties.getProperty("basket.page.free.shipping.charges.over.egp200"));
			} else {
				testLogger.info("user not added proper value product");
			}
			sa.assertAll();
		}
	}
	public void removePromoCode() {
		waitForPageToLoad(10);
		waitForElementToBeClickable(removePromoCode,driver,10);
		removePromoCode.click();
		testLogger.info("user not added proper value product");
	}

}