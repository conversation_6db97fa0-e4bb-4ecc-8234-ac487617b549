package com.lmg.blc.web.PageObjects;

import com.landmarkshops.Web_Mweb.config.ProductURLConfigBean;
import com.landmarkshops.Web_Mweb.config.config;
import com.landmarkshops.Web_Mweb.utilities.*;
import cucumber.api.DataTable;
import cucumber.api.java.en.And;
import org.assertj.core.api.SoftAssertions;
import org.openqa.selenium.Keys;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;

import java.util.List;
import java.util.Map;

import static com.landmarkshops.Web_Mweb.launcher.TestHarness.configProperties;

public class ProductDetailPage extends WebUtils {

    public ProductDetailPage(WebDriver driver) {
        super(driver);
        PageFactory.initElements(driver, this);
    }


    @FindBy(xpath="//*[@id='root-nav-mini-basket']/div/button/span/span/span")
    private WebElement countOnBasketIcon;

    /**
     * PDP header section
     */
    @FindBy(xpath = "//*[@id='langChange']/a/span")
    private WebElement PDPLangChange;

    /**
     * Product title and image
     */
    @FindBy(xpath = "//*[@id='desk-prod-layout']//h1")
    private WebElement PDPProductTitle;

    @FindBy(xpath = "//*[@id='mobile-pdp-details']/div[1]/div[3]/h1")
    private WebElement mPDPProductTitle;

    @FindBy(xpath = "//*[@id='prod-img_QA']")
    private WebElement PDPProductImageSection;

    @FindBy(xpath = "//*[@id='root-mob-top-images']")
    private WebElement mPDPProductImageSection;

    @FindBy(xpath = "//*[@id='prod-img_QA']//img")
    private List<WebElement> PDPProductImage;

    @FindBy(xpath = "//*[@id='root-mob-top-images']//img")
    private List<WebElement> mPDPProductImage;

    /**
     * Product Price
     */
    @FindBy(xpath = "//*[@id='details-price']")
    private WebElement PDPProductDetailsPriceSection;

    @FindBy(xpath = "//*[@id='details-price']/div")
    private WebElement PDPProductDetailsPrice;

    @FindBy(xpath = "//*[@id='details-price']/div[2]")
    private WebElement PDPProductDetailsPriceVatText;

    /**
     * Product Free Shipping and Shukran
     */
    @FindBy(xpath = "//*[@id='free-shipping']/button")
    private WebElement PDPProductDetailsFreeShippingHyperlink;

    @FindBy(xpath = "//*[@id='details-shipping-return']/button")
    private WebElement mPDPProductDetailsFreeShippingHyperlink;

    @FindBy(xpath = "//*[@id='free-shipping']/div")
    private WebElement PDPProductDetailsShukransSection;

    @FindBy(xpath = "//*[@id='details-loyalty']")
    private WebElement mPDPProductDetailsShukransSection;

    @FindBy(xpath = "//*[@id='free-shipping']/div/div[1]")
    private WebElement PDPProductDetailsShukranIcon;

    @FindBy(xpath = "//*[@id='details-loyalty']/div[1]")
    private WebElement mPDPProductDetailsShukranIcon;

    @FindBy(xpath = "//*[@id='free-shipping']/div/div[2]")
    private WebElement PDPProductDetailsShukranText;

    @FindBy(xpath = "//*[@id='details-loyalty']/div[2]")
    private WebElement mPDPProductDetailsShukranText;

    /**
     * Product Color
     */
    @FindBy(xpath = "//*[@id='details-color']")
    private WebElement PDPColorSection;

    @FindBy(xpath = "//*[@id='details-color']/div/div[1]/div")
    private WebElement PDPColorLabel;

    @FindBy(xpath = "//*[@id='details-color']/div/div[1]/div/span")
    private WebElement PDPColorLabelValue;

    @FindBy(xpath = "//*[@id='details-color']/div/div[2]/div/div/button")
    private List<WebElement> PDPColorLabelThumbnailImage;

    /**
     * Product Size
     */
    @FindBy(xpath = "//*[@id='details-memory']/div/div[1]/div")
    private WebElement PDPSizeSection;

    //@FindBy(xpath = "//*[@id='details-memory']/div/div[2]/div/div")
    @FindBy(xpath = "//*[@id='details-memory']/div/div[2]//button")

    private List<WebElement> PDPSizeValue;

    @FindBy(xpath = "//*[@id='details-memory']/div/div[1]/div")
    private WebElement mPDPSizeSection;

    @FindBy(xpath = "//*[@id='details-memory']/div/div[2]/div/div")
    private List<WebElement> mPDPSizeValue;

    /**
     * Product Add to Basket
     */
    @FindBy(id = "addToCart_QA")
    private WebElement PDPAddToBasketButton;

    @FindBy(xpath = "//*[@id='successDialog']/div[3]//div[3]/button[2]")
    private WebElement PDPViewBasketButtonInPopUp;

    @FindBy(xpath = "//*[@id='addToCart_QA']/span")
    private WebElement PDPAddToBasketButtonText;

    /**
     * Product customer Location Section
     */
    @FindBy(xpath = "//*[@id='main-customer-location-tof']/div/div[3]")
    private WebElement PDPCustomerLocationSection;

    @FindBy(xpath = "//*[@id='main-customer-location-tof']/div/div[3]/img[2]")
    private WebElement PDPCustomerLocationExitButton;

    @FindBy(xpath = "//*[@id='root-header']/div/div/div[5]")
    private WebElement mPDPCustomerLocationSection;

    @FindBy(xpath = "//*[@id='root-header']/div/div/div[5]/div/img[2]")
    private WebElement mPDPCustomerLocationExitButton;

    /**
     * Mini Basket
     */
    @FindBy(xpath = "//*[@id='root-nav-mini-basket']/div/button/span/span/div")
    private WebElement PDPMiniBasketButton;

//    @FindBy(xpath = "//*[@id='mob-top-header']/div/div[3]/*/span[1]/span/div")
//    private WebElement mPDPMiniBasketButton;

    @FindBy(xpath = "//*[@id='mob-top-header']/div/div[3]/a[2]")
    private WebElement mPDPMiniBasketButton;



    /**
     * Review section
     */
    @FindBy(xpath = "//*[@id='desk-reviews-inner-layout']")
    private WebElement PDPReviewSection;

    @FindBy(xpath = "//*[@id='desk-reviews-inner-layout']//div[3]/div/button/span")
    private WebElement PDPWriteAReviewButton;

    @FindBy(xpath = "/html/body/div[4]/div[3]/div/div")
    private WebElement PDPWriteAReviewPopUp;

    @FindBy(xpath = "")
    private WebElement PDPWriteAReviewPopUpTitle;

    @FindBy(xpath = "")
    private WebElement PDPWriteAReviewPopUpSubTitle;

    @FindBy(xpath = "")
    private WebElement PDPWriteAReviewPopUpSubProductReview;

    @FindBy(xpath = "")
    private WebElement PDPWriteAReviewPopUpYourRatingHeader;

    @FindBy(xpath = "")
    private List<WebElement> PDPWriteAReviewPopUpYourRatingStar;

    @FindBy(xpath = "")
    private WebElement PDPWriteAReviewPopUpShareYourExperienceHeader;

    @FindBy(xpath = "")
    private WebElement PDPWriteAReviewPopUpShareYourExperienceTextBox;

    @FindBy(xpath = "")
    private WebElement PDPWriteAReviewPopUpGiveItATitleHeader;

    @FindBy(xpath = "")
    private WebElement PDPWriteAReviewPopUpGiveItATitleBody;

    @FindBy(xpath = "")
    private WebElement PDPWriteAReviewPopUpCancelButton;

    @FindBy(xpath = "/html/body/div[4]/div[3]/div/div/div[2]/form/div[2]/div[5]/div/div[1]/button/span")
    private WebElement PDPWriteAReviewPopUpSaveButton;


    @FindBy(xpath = "//*[@id='desk-prod-layout']/div/div[1]/div/div/div/div/div/div")
    private WebElement PDPAddToListSection;
    @FindBy(xpath = "//*[@id='desk-prod-layout']/div/div[1]//img")
    private WebElement PDPAddToListIcon;
    @FindBy(xpath = "//*[@id='desk-prod-layout']/div/div[1]//span")
    private WebElement PDPAddToListLabel;
    @FindBy(xpath = "//*[@id='root-mob-top-images']/div[1]//button")
    private WebElement mPDPAddToListButton;


    @FindBy(xpath = "//*[@id='desk-prod-layout']/div/div[1]//div[2]//div[2]/h6")
    private WebElement PDPAddToListPopUpTitle;
    @FindBy(xpath = "//*[@id='desk-prod-layout']/div/div[1]//div[2]//div[2]/a")
    private WebElement PDPAddToListPopUpCreateANewListHyperlink;

    @FindBy(xpath = "//*[@id='desk-prod-layout']//form/div[1]")
    private WebElement PDPAddToListPopUpCreateNewList;
    @FindBy(xpath = "//*[@id='wishlist-name']")
    private WebElement PDPAddToListPopUpCreateNewListEnterNewList;
    @FindBy(xpath = "//*[@id='desk-prod-layout']//form/div[1]/div[2]/button")
    private WebElement PDPAddToListPopUpCreateNewListSubmitNewList;
    @FindBy(xpath = "//*[@id='desk-prod-layout']//form/div[1]/div[2]/span")
    private WebElement PDPAddToListPopUpCreateNewListCancelNewList;

    @FindBy(xpath = "//*[@id='desk-prod-layout']//div[3]/ul/li")
    private List<WebElement> PDPAddToListPopUpList;
    @FindBy(xpath = "/html/body//div[3]/ul/li")
    private List<WebElement> mPDPAddToListPopUpList;
    @FindBy(xpath = "//*[@id='desk-prod-layout']//div[3]/ul/li/div/img")
    private WebElement PDPAddToListPopUpListImage;
    @FindBy(xpath = "//*[@id='desk-prod-layout']//div[3]/ul/li//h3")
    private WebElement PDPAddToListPopUpListName;
    @FindBy(xpath = "//*[@id='desk-prod-layout']//div[3]/ul/li//p")
    private WebElement PDPAddToListPopUpListItemCount;
    @FindBy(xpath = "/html/body/div[4]/div[3]/div/div/div[4]")
    private WebElement mPDPAddToListPopUpListDoneButton;
    @FindBy(xpath = "/html/body/div[4]/div[3]/div/div/div[1]/img")
    private WebElement mPDPAddToListPopUpListCloseButton;

    /**
     * This method is used to verify product Add To List section
     */
    public boolean verifyAddToListSection() {
        boolean flag = false;
        SoftAssertions sa = new SoftAssertions();
        if(config.getBrowser().trim().equalsIgnoreCase("CHROME") || config.getBrowser().trim().equalsIgnoreCase("CHROMEHEADLESS") || config.getBrowser().trim().equalsIgnoreCase("MOBILECHROME") ) {
            waitForElementPresent(2);
            waitForElementToBeClickable(PDPAddToListSection,30);
            scrollDown(50);
            flag = PDPAddToListSection.isDisplayed();
            sa.assertThat(PDPAddToListSection.isDisplayed());
            sa.assertThat(PDPAddToListIcon.isDisplayed());
            sa.assertThat(PDPAddToListLabel.isDisplayed());
        }else{
            flag = mPDPAddToListButton.isDisplayed();
        }
        testLogger.info("Is product Add To List section displayed: " + flag);
        return flag;
    }

    /**
     * This method is used to click on Add to List
     */
    public void clickAddToListButton() {
        waitThread(5000);
        if(config.getBrowser().trim().equalsIgnoreCase("CHROME") || config.getBrowser().trim().equalsIgnoreCase("CHROMEHEADLESS") || config.getBrowser().trim().equalsIgnoreCase("MOBILECHROME") ) {
            PDPAddToListLabel.click();
        }else{
            mPDPAddToListButton.click();
        }
        waitForElementPresent(5);
    }
    public void addProductToList(String listName) {

        if(config.getBrowser().trim().equalsIgnoreCase("CHROME") || config.getBrowser().trim().equalsIgnoreCase("CHROMEHEADLESS") || config.getBrowser().trim().equalsIgnoreCase("MOBILECHROME") ) {
            clickElementsList(PDPAddToListPopUpList, listName);
        }else{
            clickElementsList(mPDPAddToListPopUpList, listName);
        }
        waitForElementPresent(5);
    }


    public void clickDoneButtonInAddToList() {

        if(config.getBrowser().trim().equalsIgnoreCase("CHROME") || config.getBrowser().trim().equalsIgnoreCase("CHROMEHEADLESS") || config.getBrowser().trim().equalsIgnoreCase("MOBILECHROME") ) {

        }else{
            //mPDPAddToListPopUpListDoneButton.click();
            mPDPAddToListPopUpListCloseButton.click();

        }
        waitForElementPresent(5);
    }




    /**
     * This method is used to navigate to PDP
     */
    public void userNavigateToPDP() {
        waitForPageToLoad(100);
        navigateToParticularPage(ProductURLConfigBean.getMaxEgyptStandardProduct());
        waitForDomToLoad(10);
        setProductData();
    }

    public void userNavigateToVariantPDP() {
        navigateToParticularPage(ProductURLConfigBean.getMaxEgyptVariantProductUrl());
        waitForDomToLoad(40);
        setProductData();
    }

    public void verifyUserNavigateToPDP() {
        waitForDomToLoad(10);
        //waitForPageToLoad(1000);
    }

    @FindBy(xpath = "//*[@id='mob-top-header']/div/div[1]/button")
    WebElement hamburgerMenu;
    @FindBy(xpath = "//*[@id='footer-language-switch']/span")
    WebElement mCountrySwitcher;

    public void setProductData(){
        if (config.getLanguage().equalsIgnoreCase("ar") && !(PDPLangChange.getText().trim().equalsIgnoreCase("EN"))){
            waitForVisibilityOfElementLocated(PDPLangChange,driver,20);
            PDPLangChange.click();
        }
        waitForElementPresent(2);
        if (TestDataBean.getProductTitle() == null) {
            if (config.getBrowser().trim().equalsIgnoreCase("CHROME") || config.getBrowser().trim().equalsIgnoreCase("CHROMEHEADLESS")) {
                waitForPresenceOfElementLocated(PDPProductTitle,driver,35);
                TestDataBean.setProductTitle(PDPProductTitle.getText().trim());
            } else {
                waitForPresenceOfElementLocated(mPDPProductTitle,driver,35);
                    TestDataBean.setProductTitle(mPDPProductTitle.getText().trim());
            }
        }
        if (TestDataBean.getProductActualPrice() == null) {
            waitForPresenceOfElementLocated(PDPProductDetailsPrice,driver,35);
            TestDataBean.setProductActualPrice(PDPProductDetailsPrice.getText().trim());
        }
    }

    /**
     * This method is used to verify product Title
     */
    public boolean verifyProductTitle() {
        boolean flag = false;
        if (TestDataBean.getProductTitle() != null) {
            flag = verifyProductTitle(TestDataBean.getProductTitle());
        } else {
            SoftAssertions sa = new SoftAssertions();
            waitForPageToLoad(15);

            if (config.getBrowser().trim().equalsIgnoreCase("CHROME") ||config.getBrowser().trim().equalsIgnoreCase("MOBILECHROME") || config.getBrowser().trim().equalsIgnoreCase("CHROMEHEADLESS")) {

                flag = PDPProductTitle.isDisplayed();
                sa.assertThat(PDPProductTitle.isDisplayed());
            } else {
                waitForWebElementPresent(mPDPProductTitle, driver, 15);
                flag = mPDPProductTitle.isDisplayed();
                sa.assertThat(mPDPProductTitle.isDisplayed());
            }
            testLogger.info("Is product title displayed: " + flag);
        }
        return flag;
    }

    /**
     * This method is used to verify product Title
     * * @param title
     */
    public boolean verifyProductTitle(String title) {
        boolean flag = false;
        SoftAssertions sa = new SoftAssertions();
        waitForPageToLoad(25);

        if(config.getBrowser().trim().equalsIgnoreCase("CHROME")||config.getBrowser().trim().equalsIgnoreCase("MOBILECHROME") || config.getBrowser().trim().equalsIgnoreCase("CHROMEHEADLESS") ) {


            flag = PDPProductTitle.isDisplayed();
            sa.assertThat(PDPProductTitle.isDisplayed());
            sa.assertThat(PDPProductTitle.getText().trim()).isEqualTo(title);
        }else{
            flag = mPDPProductTitle.isDisplayed();
            sa.assertThat(mPDPProductTitle.isDisplayed());
            sa.assertThat(mPDPProductTitle.getText().trim()).isEqualTo(title);
        }
        testLogger.info("Is product title displayed: " + flag);
        return flag;
    }

    /**
     * This method is used to verify product Image
     */
    public boolean verifyProductImageSection() {
        boolean flag = false;
        SoftAssertions sa = new SoftAssertions();
        if(config.getBrowser().trim().equalsIgnoreCase("CHROME")||config.getBrowser().trim().equalsIgnoreCase("MOBILECHROME") || config.getBrowser().trim().equalsIgnoreCase("CHROMEHEADLESS") ) {

            flag = PDPProductImageSection.isDisplayed();
            sa.assertThat(PDPProductImageSection.isDisplayed());
        }else{
            flag = mPDPProductImageSection.isDisplayed();
            sa.assertThat(mPDPProductImageSection.isDisplayed());
        }
        testLogger.info("Is product image displayed: " + flag);
        return flag;
    }

    /**
     * This method is used to verify product price
     */
    public boolean verifyProductPrice() {
        boolean flag = false;
        if (TestDataBean.getProductActualPrice() != null) {
            flag = verifyProductTitle(TestDataBean.getProductActualPrice());
        } else {
            SoftAssertions sa = new SoftAssertions();
            waitForWebElementPresent(PDPProductDetailsPriceSection, driver, 15);
            flag = PDPProductDetailsPriceSection.isDisplayed();
            sa.assertThat(PDPProductDetailsPriceSection.isDisplayed());
            sa.assertThat(PDPProductDetailsPriceVatText.getText().trim()).isEqualTo(configProperties.getProperty("pdp.pricrVAT"));
            testLogger.info("Is product price section displayed: " + flag);
        }
        return flag;
    }

    /**
     * This method is used to verify product price
     * * @param price
     */
    public boolean verifyProductPrice(String price) {
        boolean flag = false;
        SoftAssertions sa = new SoftAssertions();
        waitForWebElementPresent(PDPProductDetailsPriceSection,driver,15);
        flag = PDPProductDetailsPriceSection.isDisplayed();
        sa.assertThat(PDPProductDetailsPriceSection.isDisplayed());
        sa.assertThat(PDPProductDetailsPrice.getText().trim()).isEqualTo("EGP "+price);
        sa.assertThat(PDPProductDetailsPriceVatText.getText().trim()).isEqualTo(configProperties.getProperty("pdp.pricrVAT"));
        testLogger.info("Is product price section displayed: " + flag);
        return flag;
    }

    /**
     * This method is used to verify product free shipping
     */
    public boolean verifyProductFreeShipping() {
        boolean flag = false;
        SoftAssertions sa = new SoftAssertions();

        if(config.getBrowser().trim().equalsIgnoreCase("CHROME") ||config.getBrowser().trim().equalsIgnoreCase("MOBILECHROME") || config.getBrowser().trim().equalsIgnoreCase("CHROMEHEADLESS") ) {

            flag = PDPProductDetailsFreeShippingHyperlink.isDisplayed();
            sa.assertThat(PDPProductDetailsFreeShippingHyperlink.isDisplayed());
            sa.assertThat(PDPProductDetailsPriceVatText.getText().trim()).isEqualTo(configProperties.getProperty("pdp.freeShopping"));

        }else{
            flag = mPDPProductDetailsFreeShippingHyperlink.isDisplayed();
            sa.assertThat(mPDPProductDetailsFreeShippingHyperlink.isDisplayed());
            sa.assertThat(PDPProductDetailsPriceVatText.getText().trim()).isEqualTo(configProperties.getProperty("pdp.freeShopping"));
        }
        testLogger.info("Is product free shipping section displayed: " + flag);
        return flag;
    }

    /**
     * This method is used to verify product Shukran section
     */
    public boolean verifyProductsShukran() {
        boolean flag = false;
        SoftAssertions sa = new SoftAssertions();
        if(config.getBrowser().trim().equalsIgnoreCase("CHROME") || config.getBrowser().trim().equalsIgnoreCase("CHROMEHEADLESS") || config.getBrowser().trim().equalsIgnoreCase("MOBILECHROME") || config.getBrowser().trim().equalsIgnoreCase("MOBILECHROME")) {
            flag = PDPProductDetailsShukransSection.isDisplayed();
            sa.assertThat(PDPProductDetailsShukransSection.isDisplayed());
            sa.assertThat(PDPProductDetailsShukranIcon.isDisplayed());
            sa.assertThat(PDPProductDetailsShukranText.isDisplayed());
        }else{
            flag = mPDPProductDetailsShukransSection.isDisplayed();
            sa.assertThat(mPDPProductDetailsShukransSection.isDisplayed());
            sa.assertThat(mPDPProductDetailsShukranIcon.isDisplayed());
            sa.assertThat(mPDPProductDetailsShukranText.isDisplayed());
        }
        testLogger.info("Is product Shukran section displayed: " + flag);
        return flag;
    }

    /**
     * This method is used to verify product color
     */
    public boolean verifyProductColor() {
        boolean flag = false;
        if (TestDataBean.getProductColor() != null) {
            flag = verifyProductColor(TestDataBean.getProductColor());
        } else {
            SoftAssertions sa = new SoftAssertions();
            waitForWebElementPresent(PDPColorSection, driver, 15);
            flag = PDPColorSection.isDisplayed();
            sa.assertThat(PDPColorSection.isDisplayed());
            sa.assertThat(PDPColorLabel.getText().trim()).isEqualTo(configProperties.getProperty("pdp.color.label"));
            sa.assertThat(PDPColorLabelValue.isDisplayed());
            testLogger.info("Is product color displayed: " + flag);
        }
        return flag;
    }

    /**
     * This method is used to verify product color
     * * @param color
     */
    public boolean verifyProductColor(String color) {
        boolean flag = false;
        SoftAssertions sa = new SoftAssertions();
        waitForWebElementPresent(PDPColorSection,driver,15);
        flag = PDPColorSection.isDisplayed();
        sa.assertThat(PDPColorSection.isDisplayed());
        sa.assertThat(PDPColorLabel.getText().trim()).isEqualTo(configProperties.getProperty("pdp.color.label"));
        sa.assertThat(PDPColorLabelValue.getText().trim()).isEqualTo(color);
        testLogger.info("Is product color displayed: " + flag);
        return flag;
    }

    /**
     * This method is used to click product color
     * * @param color
     */
    public void clickColorByName(String color){
        implicitWait(15);
        waitForWebElementPresent(PDPColorSection,driver,25);
        if (!config.getBrowser().trim().equalsIgnoreCase("CHROME") || config.getBrowser().trim().equalsIgnoreCase("CHROMEHEADLESS") || config.getBrowser().trim().equalsIgnoreCase("MOBILECHROME")) {
            scrollElement(PDPColorSection);
        }
        //clickElementsList(PDPColorLabelThumbnailImage, PDPColorLabelValue, color);
        clickElementsListByAttributeValue(PDPColorLabelThumbnailImage, color);
    }

    /**
     * This method is used to verify product size
     */
    public boolean verifyProductSize() {
        boolean flag = false;
        if (TestDataBean.getProductSize() != null) {
            flag = verifyProductColor(TestDataBean.getProductSize());
        } else {
            SoftAssertions sa = new SoftAssertions();
            if (config.getBrowser().trim().equalsIgnoreCase("CHROME") || config.getBrowser().trim().equalsIgnoreCase("CHROMEHEADLESS") || config.getBrowser().trim().equalsIgnoreCase("MOBILECHROME")) {
                flag = PDPSizeSection.isDisplayed();
                sa.assertThat(PDPSizeSection.isDisplayed());
            } else {
                flag = mPDPSizeSection.isDisplayed();
                sa.assertThat(mPDPSizeSection.isDisplayed());
            }
            testLogger.info("Is product size displayed: " + flag);
        }
        return flag;
    }

    /**
     * This method is used to verify product size
     * * @param size
     */
    public boolean verifyProductSize(String size) {
        boolean flag = false;
        SoftAssertions sa = new SoftAssertions();
        if (config.getBrowser().trim().equalsIgnoreCase("CHROME") || config.getBrowser().trim().equalsIgnoreCase("CHROMEHEADLESS") || config.getBrowser().trim().equalsIgnoreCase("MOBILECHROME")) {
            flag = PDPSizeSection.isDisplayed();
            sa.assertThat(PDPSizeSection.isDisplayed());
            sa.assertThat(PDPSizeSection.getText().trim()).isEqualTo(configProperties.getProperty("pdp.size.lable"));
        } else {
            flag = mPDPSizeSection.isDisplayed();
            sa.assertThat(mPDPSizeSection.isDisplayed());
            sa.assertThat(PDPSizeSection.getText().trim()).isEqualTo(configProperties.getProperty("pdp.size.lable"));
        }
        testLogger.info("Is product size displayed: " + flag);
        return flag;
    }

    /**
     * This method is used to click primary Product Variant Section by name in PDP
     * * @param size
     */
    public void clickSizeByName(String size){
        implicitWait(50);
        if(config.getBrowser().trim().equalsIgnoreCase("CHROME") || config.getBrowser().trim().equalsIgnoreCase("CHROMEHEADLESS") || config.getBrowser().trim().equalsIgnoreCase("MOBILECHROME") ) {
            if(config.getConcept().equalsIgnoreCase("max")) {
                scrolldownElement(PDPSizeSection);
                clickElementsList(PDPSizeValue, size);
            } else if(config.getConcept().equalsIgnoreCase("cp")){
                if(verifyNoelement(PDPSizeSection)) {
                    scrolldownElement(PDPProductDetailsPriceSection);
//                    if(!verifyNoelement(PDPSizeValue)) {
//                        clickElementsList(PDPSizeValue, size);
//                    }
                   // if(!verifyNoelement(PDPSizeValue)) {
                        clickElementsList(PDPSizeValue, size);
                   // }
                }
            }
        }else{
            scrollElement(mPDPProductTitle);
            clickElementsList(mPDPSizeValue, size);
        }
    }

    @FindBy(xpath = "//*[@id='root-prod-details-inner']/div[4]/div/div[1]")
    private WebElement PDPRecipientDetailsSection;
    @FindBy(xpath = "//*[@id='root-prod-details-inner']/div[4]/div/div[2]/div/label")
    private WebElement PDPFullNameLabelInRecipientDetailsSection;
    @FindBy(xpath = "//*[@id='recipientName_QA']")
    private WebElement PDPFullNameTextBoxInRecipientDetailsSection;
    @FindBy(xpath = "//*[@id='note_QA']")
    private WebElement PDPEmailLabelInRecipientDetailsSection;
    @FindBy(xpath = "//*[@id='recipientEmail_QA']")
    private WebElement PDPEmailTextBoxInRecipientDetailsSection;
    @FindBy(xpath = "//*[@id='root-prod-details-inner']/div[4]/div/div[4]/div/label/div")
    private WebElement PDPWriteANoteLabelInRecipientDetailsSection;
    @FindBy(xpath = "//*[@id='note_QA']")
    private WebElement PDPWriteANoteTextBoxInRecipientDetailsSection;

    public boolean verifyRecipientDetailsSection() {
        boolean flag = false;
        SoftAssertions sa = new SoftAssertions();
        if (config.getBrowser().trim().equalsIgnoreCase("CHROME") || config.getBrowser().trim().equalsIgnoreCase("CHROMEHEADLESS") || config.getBrowser().trim().equalsIgnoreCase("MOBILECHROME")) {
            scrollDown(20);
            flag = PDPRecipientDetailsSection.isDisplayed();
            sa.assertThat(PDPRecipientDetailsSection.isDisplayed());
            sa.assertThat(PDPRecipientDetailsSection.getText().trim()).isEqualTo(configProperties.getProperty("pdp.recipientDetailsSection.label"));
            sa.assertThat(PDPFullNameLabelInRecipientDetailsSection.getText().trim()).isEqualTo(configProperties.getProperty("pdp.fullNameInRecipientDetailsSection.label"));
            sa.assertThat(PDPEmailLabelInRecipientDetailsSection.getText().trim()).isEqualTo(configProperties.getProperty("pdp.emailInRecipientDetailsSection.label"));
            sa.assertThat(PDPWriteANoteLabelInRecipientDetailsSection.getText().trim()).isEqualTo(configProperties.getProperty("pdp.writeANoteInRecipientDetailsSection.label"));
        } else {
            flag = true;
        }
        testLogger.info("Is product size displayed: " + flag);
        return flag;
    }

    /**
     * This method is used to click primary Product Variant Section by name in PDP
     * * @param size
     */
    public void enterFullNameInRecipientDetailsSection(String name){
        implicitWait(30);
        if(config.getBrowser().trim().equalsIgnoreCase("CHROME") || config.getBrowser().trim().equalsIgnoreCase("CHROMEHEADLESS") || config.getBrowser().trim().equalsIgnoreCase("MOBILECHROME") ) {
            //scrollToParticularElement(PDPFullNameTextBoxInRecipientDetailsSection);
            //scrollToParticularElement(PDPSizeSection);
            if(config.getConcept().equalsIgnoreCase("max") || config.getConcept().equalsIgnoreCase("cp")) {
                scrolldownElement(PDPSizeSection);
            }else if (config.getConcept().equalsIgnoreCase("hc") ) {
                scrolldownElement(PDPFullNameTextBoxInRecipientDetailsSection);
            }
//            PDPSizeSection.sendKeys(Keys.DOWN);
//            PDPSizeSection.sendKeys(Keys.DOWN);
            moveToElementAndClickAndSendKeys(PDPFullNameTextBoxInRecipientDetailsSection, name);
        }else{
//            scrollElement(PDPFullNameTextBoxInRecipientDetailsSection);
//            PDPFullNameTextBoxInRecipientDetailsSection.sendKeys(name);
        }
    }

    public void enterEmailInRecipientDetailsSection(String email){
        implicitWait(30);
        if(config.getBrowser().trim().equalsIgnoreCase("CHROME") || config.getBrowser().trim().equalsIgnoreCase("CHROMEHEADLESS") || config.getBrowser().trim().equalsIgnoreCase("MOBILECHROME") ) {
            //scrollToParticularElement(PDPAddToBasketButton);
            //scrollDown(20);
            moveToElementAndClickAndSendKeys(PDPEmailTextBoxInRecipientDetailsSection, email);
//            PDPEmailTextBoxInRecipientDetailsSection.click();
//            PDPEmailTextBoxInRecipientDetailsSection.sendKeys(email);
        }else{
//            scrollElement(PDPEmailTextBoxInRecipientDetailsSection);
//            PDPEmailTextBoxInRecipientDetailsSection.sendKeys(email);
        }
    }

    public void enterWriteNoteInRecipientDetailsSection(String note){
        implicitWait(30);
        if(config.getBrowser().trim().equalsIgnoreCase("CHROME") || config.getBrowser().trim().equalsIgnoreCase("CHROMEHEADLESS") || config.getBrowser().trim().equalsIgnoreCase("MOBILECHROME") ) {
            //scrollToParticularElement(PDPAddToBasketButton);
//            PDPWriteANoteTextBoxInRecipientDetailsSection.click();
//            PDPWriteANoteTextBoxInRecipientDetailsSection.sendKeys(note);
            moveToElementAndClickAndSendKeys(PDPWriteANoteTextBoxInRecipientDetailsSection, note);
        }else{
//            scrollElement(PDPWriteANoteTextBoxInRecipientDetailsSection);
//            PDPWriteANoteTextBoxInRecipientDetailsSection.sendKeys(note);
        }
    }

    /**
     * This method is used to verify add to basket
     */
    public void verifyAddToCartButton(){
        scrollToParticularElement(PDPSizeSection);
        SoftAssertions sa = new SoftAssertions();
        sa.assertThat(PDPAddToBasketButton.isDisplayed());
        String logMessage = PDPAddToBasketButton.isDisplayed() ? "is displayed" : "is not displayed";
        testLogger.info("Add to Basket section "+logMessage+" in Product Detail screen");
        sa.assertThat(PDPAddToBasketButtonText.getText().trim()).isEqualTo(configProperties.getProperty("pdp.addToBasket"));
        testLogger.info("Button displayed message - "+PDPAddToBasketButtonText.getText().trim());
        sa.assertAll();
    }

    @FindBy(xpath = "//*[@id='details-overview']")
    private WebElement PDPOverviewSection;
    @FindBy(xpath = "//*[@id='you-may-also-like']")
    private WebElement PDPYouMayAlsoKnowSection;
    @FindBy(xpath = "//*[@id='customers-also-viewed']")
    private WebElement PDPCustomerAlsoViewedSection;

    /**
     * This method is used to verify add to basket
     */
    public void verifyCustomerAlsoViewedSection(){
        scrollToParticularElement(PDPSizeSection);
        SoftAssertions sa = new SoftAssertions();
        sa.assertThat(PDPCustomerAlsoViewedSection.isDisplayed());
        String logMessage = PDPCustomerAlsoViewedSection.isDisplayed() ? "is displayed" : "is not displayed";
        testLogger.info("Customer Also Viewed section "+logMessage+" in Product Detail screen");
        sa.assertAll();
    }

    /**
     * This method is used to verify add to basket
     */
    public void verifyYouMayAlsoKnowSection(){
        scrollToParticularElement(PDPSizeSection);
        SoftAssertions sa = new SoftAssertions();
        sa.assertThat(PDPYouMayAlsoKnowSection.isDisplayed());
        String logMessage = PDPYouMayAlsoKnowSection.isDisplayed() ? "is displayed" : "is not displayed";
        testLogger.info("You May Also Know section "+logMessage+" in Product Detail screen");
        sa.assertAll();
    }


    /**
     * This method is used to click on Add to Basket label
     */
    public void userClickOnAddToBasket() {
        if(StepContext.mWeb){
            PDPAddToBasketButton.click();
        }else {
            waitForElementToBeClickable(PDPAddToBasketButton, 15);
            //scrollToParticularElement(PDPSizeSection);
            //scrollDown(20);
            scrollUp();
            mouseHoverClick(PDPAddToBasketButton);
            if (!(config.getConcept().equalsIgnoreCase("MAX") && config.getCountry().equalsIgnoreCase("Egypt"))) {
                mouseHoverClick(PDPViewBasketButtonInPopUp);
            }
            //Temp fix for MAX Egypt, getting continue shopping popup
            if (verifyNoelement(PDPViewBasketButtonInPopUp)) {
                mouseHoverClick(PDPViewBasketButtonInPopUp);
            }
            verifyItemCountDisplayedInBasket();
        }
    }



    public void verifyItemCountDisplayedInBasket(){
        if(config.getBrowser().trim().equalsIgnoreCase("CHROME") || config.getBrowser().trim().equalsIgnoreCase("CHROMEHEADLESS") || config.getBrowser().trim().equalsIgnoreCase("MOBILECHROME") ) {
            waitForVisibilityOfElementLocated(countOnBasketIcon,driver,15);
        }else{

        }
        //waitForWebElementVisibility(PDPAddToBasketButton,10);
        //waitClick(PDPAddToBasketButton,driver,25);


    }
    @FindBy(xpath = "//*[@id='main-part']/div[1]/div/div[2]")
    private WebElement emptyBasket;

    @FindBy(xpath = "//*[@id='cart-removeitem_QA']")
    private WebElement productRemove;

    @FindBy(xpath = "//*[@id='root-nav-mini-basket']/div/button/span/span/span")
    private WebElement nonEmptyBasket;

    /**
     * This method is used to click on mini basket
     */
    public void clickMiniBasket() {

        //implicitWait(50);
        if(config.getBrowser().trim().equalsIgnoreCase("CHROME") || config.getBrowser().trim().equalsIgnoreCase("CHROMEHEADLESS") || config.getBrowser().trim().equalsIgnoreCase("MOBILECHROME") ) {
            waitForElementToBeClickable(PDPMiniBasketButton,15);
           mouseHoverClick(PDPMiniBasketButton);
           //Workaround for empty basket page
            waitForPageToLoad(10);
            if(verifyNoelement(nonEmptyBasket)) {
                while (!verifyNoelement(productRemove)) {
                    //waitForElementPresent(20);
                    mouseHoverClick(PDPMiniBasketButton);
                    //elementMouseClick(PDPMiniBasketButton);
                    //javaScriptExecuteClick(PDPMiniBasketButton);
                    //PDPMiniBasketButton.click();
                    pageRefresh();
                    waitForPageToLoad(5);
                }
            }
        }else{
            waitForVisibilityOfElementLocated(mPDPMiniBasketButton,driver,15);
            mPDPMiniBasketButton.click();
        }
        waitForElementPresent(1);
    }

    public void productType(String productType) {
        System.out.println("Product Type: "+productType);
        productsURL(productType);
        BaseURLBean.getUrl();
        navigateToParticularPage(ProductURLsDataBean.getURL());
        setProductData();
        clickColorByName(ProductURLsDataBean.getColor());
        if(productType.contains("gift")) {
            clickSizeByName(ProductURLsDataBean.getSize());
        } else if (config.getConcept().equalsIgnoreCase("max") || config.getConcept().equalsIgnoreCase("cp")) {
            clickSizeByName(ProductURLsDataBean.getSize());
        }
//        if(productType.equalsIgnoreCase("variant MEN")){
//            String url=ProductURLsDataBean.getURL();
//            navigateToParticularPage(url);
//            setProductData();
//            clickColorByName(ProductURLsDataBean.getColor());
//            clickSizeByName(ProductURLsDataBean.getSize());
//        }else if(productType.equalsIgnoreCase("variant WOMEN")){
//            navigateToParticularPage(ProductURLsDataBean.getURL());
//            setProductData();
//            clickColorByName(ProductURLsDataBean.getColor());
//            clickSizeByName(ProductURLsDataBean.getSize());
//        }else if(productType.equalsIgnoreCase("variant KIDS")){
//            navigateToParticularPage(ProductURLsDataBean.getURL());
//            setProductData();
//            clickColorByName(ProductURLsDataBean.getColor());
//            clickSizeByName(ProductURLsDataBean.getSize());
//        }else{
//            waitForPageToLoad(20);
//            navigateToParticularPage(ProductURLsDataBean.getURL());
//            setProductData();
//        }
    }
    @FindBy(xpath = "//*[@id='changeQuantity_QA']")
    private WebElement PDPMaxQuantity;

    @FindBy(xpath = "//*[@id='changeQuantity_QA']/span")
    private WebElement PDPMaxSelectedQuantity;
    @FindBy(xpath = "//*[@id='prod-detail_QA']//div[2]/div[2]/div[1]//ul/div")
    private List<WebElement> PDPMaxQuantityList;


    @FindBy(xpath = "//*[@id='root-prod-details-inner']//div[2]/div[2]/div[2]/div[1]/button")
    private WebElement PDPHCQuantityMinus;

    @FindBy(xpath = "//*[@id='root-prod-details-inner']//div[2]/div[2]/button")
    private WebElement PDPHCQuantityLabelHC;

    @FindBy(xpath = "//*[@id='root-prod-details-inner']/div[1]/div[2]/div/div[1]/div[2]/div[1]")
    private WebElement PDPHCQuantityLabelCP;

    @FindBy(xpath = "//*[@id='root-prod-details-inner']//div[2]/div[3]/button")
    private WebElement PDPHCQuantityPlus;


    public void updateQuantity(String qty) {
        if(config.getConcept().toLowerCase().trim().equalsIgnoreCase("hc")){
            clickElementTillDisplayedText(PDPHCQuantityPlus, PDPHCQuantityLabelHC, qty);
        } else if(config.getConcept().toLowerCase().trim().equalsIgnoreCase("cp")){
            scrollElement(PDPProductTitle);
            clickElementTillDisplayedText(PDPHCQuantityPlus, PDPHCQuantityLabelCP, qty);
        } else if (config.getConcept().toLowerCase().trim().equalsIgnoreCase("max")) {
            if(!PDPMaxSelectedQuantity.getText().trim().equalsIgnoreCase(qty)) {
                if(verifyNoelement(PDPAddYourLocationCLose)){
                    PDPAddYourLocationCLose.click();
                }
                if (verifyNoelement(PDPBrowseMoreProd)){
////                    scrollToParticularElement(PDPBrowseMoreProd);
////                    scrolldownElement(PDPBrowseMoreProd);
//                    scrollElement(PDPBrowseMoreProd);
//                    PDPMaxQuantity.sendKeys(Keys.UP);
                    scrolldownElement(PDPBrowseMoreProd);
                    scrolldownElement(PDPSizeSection);
//                    PDPMaxQuantity.sendKeys(Keys.DOWN);
//                    PDPMaxQuantity.sendKeys(Keys.DOWN);
                    //PDPMaxQuantity.sendKeys(Keys.UP);
                } else {
                    PDPMaxQuantity.sendKeys(Keys.DOWN);
                    PDPMaxQuantity.sendKeys(Keys.DOWN);
                    PDPMaxQuantity.sendKeys(Keys.DOWN);
                }

                PDPMaxQuantity.click();
               // PDPMaxQuantity.sendKeys(Keys.DOWN);
                clickElementsListWithOutScrollDown(PDPMaxQuantityList, qty);
                //clickElementsList(PDPMaxQuantityList, qty);
            }
        }
    }

    @FindBy(xpath = "//*[@id='main-customer-location-tof']//div[3]/img[2]")
    private WebElement PDPAddYourLocationCLose;

    @FindBy(xpath = "//*[@id='browse-more-prod']")
    private WebElement PDPBrowseMoreProd;


    public void mwebUpdateQuantity() {
    }
}