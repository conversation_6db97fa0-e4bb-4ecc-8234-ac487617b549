package com.lmg.blc.web.PageObjects;

import com.landmarkshops.Web_Mweb.utilities.CardDataBean;
import com.landmarkshops.Web_Mweb.utilities.WebUtils;
import org.assertj.core.api.SoftAssertions;
import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.testng.Assert;

import java.util.List;

import static com.landmarkshops.Web_Mweb.launcher.TestHarness.configProperties;

public class MyAccountPaymentPage extends WebUtils {
    public MyAccountPaymentPage(WebDriver driver) {
        super(driver);
        PageFactory.initElements(driver,this);
    }

    @FindBy(xpath = "//div[2]/button[2]/span")
    private List<WebElement> addList;

    @FindBy(xpath = "//div[2]/button[2]/span")
    private WebElement deleteLabel;

    @FindBy(id = "deletePaymtConfirm_QA")
    private WebElement deletePaymentCardConfirm;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div/h4")
    private WebElement paymentTitle;

    @FindBy(xpath = "//*[@id='addFirstCard_QA']")
    private WebElement addACard;

    @FindBy(xpath = "//*[@id='addNewCard_QA']/div")
    private WebElement addSecondCard;

    @FindBy(xpath = "//*[@id='save-new-card_QA']")
    private WebElement clickOnContinueButton;

    @FindBy(id = "cardnum")
    private WebElement cardnumInput;

    @FindBy(id = "namecard")
    private WebElement namecardInput;

    @FindBy(id = "expMonthYear")
    private WebElement expiryMonth;

    @FindBy(xpath = "//*[@id='menu-expirymonth']/div[3]/ul")
    private List<WebElement> expiryMonthValues;

    @FindBy(xpath = "//*[@id='menu-expirymonth']/div[3]/ul/li[4]")
    private WebElement selectExpiryMonthValue;

    @FindBy(xpath = "//*[@class='MuiButtonBase-root MuiListItem-root MuiMenuItem-root MuiMenuItem-gutters MuiListItem-gutters MuiListItem-button']")
    private WebElement selectExpiryMonthText;

    @FindBy(id = "expiryyear")
    private WebElement expiryYear;

    @FindBy(xpath = "//*[@id='menu-expiryyear']/div[3]/ul")
    private List<WebElement> expiryYearValues;


    @FindBy(xpath = "//*[@id='menu-expiryyear']/div[3]/ul/li[4]")
    private WebElement selectExpiryYearValue;

    @FindBy(id = "cvv")
    private WebElement cvvInput;

    @FindBy(id = "cvv_0")
    private WebElement cvvInput1;

    @FindBy(xpath = "//*[@id='namecard']")
    private WebElement paymentCardName;

    @FindBy(xpath = "//*[@id='addNewCard_QA']/div")
    private WebElement addANewCardLink;

    @FindBy(xpath = "//*[@id='delCnfrmTitle_QA']")
    private WebElement areYouSure;

    @FindBy(id = "deletePaymtConfirm_QA")
    private WebElement deleteAddressText;

    @FindBy(id = "deleteAddrCancel_QA")
    private WebElement neverMind;

    @FindBy(xpath = "//*[@id='setDefaultAddr-0_QA']")
    private WebElement setDefaultSecondPaymentCard;

    @FindBy(xpath = "//*[@id='defaultCardBadge_QA']")
    private WebElement defaultOption;

    @FindBy(xpath = " //*[@id='setDefaultAddr-0_QA']/span")
    private WebElement setDefaultOption;




    @FindBy(xpath = "//*[@id='main-part']/div/div/div/div/div/div[2]/div[1]/div[1]/img")
    private WebElement paymentLogo;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div/h4")
    private WebElement paymentText;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div/div/div/div[1]")
    private WebElement manageYourPaymentText;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div/div/div/div[1]")
    private WebElement weProudlyAcceptText;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div[2]/div[1]/div[1]/img")
    private WebElement creditDebitCardLogo;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div/div/div/div[2]/div[1]/div[2]")
    private WebElement creditDebitCardText;

    @FindBy(xpath = "//*[@id='addFirstCard_QA']")
    private WebElement addCardButton;

    @FindBy(xpath = "//*[@id='ddFirstAddr_QA']/span/span/div/img")
    private WebElement addCardButtonPlusImg;

    @FindBy(xpath = "//*[@id='tabby-close']/span/div")
    private WebElement AddACardText;

    @FindBy(xpath = "//*[@id='tabby-close']/img")
    private WebElement closeIcon;

    @FindBy(xpath = "/html/body/div[3]/div[3]/div/div/div/div/h4")
    private WebElement AddANewCardText;


    @FindBy(xpath = "//*[@id='expMonthYear']")
    private WebElement expiryDate;

    @FindBy(xpath = "//*[@id='checkout-homedelivery-payment-number-label_QA']/span[1]")
    private WebElement cardNumberText;

    @FindBy(xpath = "//*[@id='checkout-homedelivery-payment-expirydate-label_QA']")
    private WebElement expiryDateText;

    @FindBy(xpath = "//*[@id='checkout-homedelivery-payment-cvv-label_QA']")
    private WebElement cvvText;

    @FindBy(xpath = "//*[@id='checkout-homedelivery-payment-name-label_QA']")
    private WebElement nameOnCardText;

    @FindBy(xpath = "//*[@id='cardnum-errorText_QA']")
    private WebElement pleaseEnterATrueDigitNumberText;

    @FindBy(id = "expMonthYear-errorText_QA")
    private WebElement pleaseSelectAValidMonthText;

    @FindBy(id = "cvv-errorText_QA")
    private WebElement pleaseSelectAValidYearText;

    @FindBy(xpath = "//*[@id='cvv-errorText_QA']")
    private WebElement enterCVVText;

    @FindBy(xpath = "//*[@id='namecard-errorText_QA']")
    private WebElement pleaseEnterYourFullNameText;

    public void verifyAllUIElement() {
        SoftAssertions sa=new SoftAssertions();
        sa.assertThat(paymentLogo.isDisplayed());
        sa.assertThat(paymentText.getText().trim()).isEqualTo(configProperties.getProperty("landMarkMyAccountPage.header.user.my.payment"));
        //sa.assertThat(manageYourPaymentText.getText().trim()).isEqualTo(configProperties.getProperty("landMarkMyAccountPage.header.user.my.payment.description"));
        sa.assertThat(weProudlyAcceptText.getText().trim()).isEqualTo(configProperties.getProperty("landMarkPayment.header.user.payment.proudlyacceptfollowingmethods"));
       // sa.assertThat(creditDebitCardLogo.isDisplayed());
        sa.assertThat(creditDebitCardText.getText().trim()).isEqualTo(configProperties.getProperty("landMarkMyAccountPage.header.user.my.payment.CreditOrDebit"));
        //sa.assertThat(addCardButton.isDisplayed());
        waitForElementPresent(5);
        addCardButton.click();
        sa.assertThat(closeIcon.isDisplayed());
        sa.assertThat(cardNumberText.getText().trim()).isEqualTo(configProperties.getProperty("paymentPage.cardNumber"));
        sa.assertThat(expiryDateText.getText().trim()).isEqualTo(configProperties.getProperty("paymentPage.expiryDate"));
        sa.assertThat(cvvText.getText().trim()).isEqualTo(configProperties.getProperty("paymentPage.cvv"));
        sa.assertThat(nameOnCardText.getText().trim()).isEqualTo(configProperties.getProperty("paymentPage.nameOnCard"));
        sa.assertAll();
        closeIcon.click();
    }

    public void deletePaymentCard() {
        waitForPageToLoad(10);
        waitForElementPresent(1);
        mouseHoverClick(deleteLabel);
        waitForElementPresent(2);
    }

    public void confirmDeletePaymentCard() {
        waitForElementPresent(1);
        waitForElementToBeClickable(deletePaymentCardConfirm, 10);
        mouseHoverClick(deletePaymentCardConfirm);
        waitForElementPresent(1);
    }

    public void deleteAllPayments() {
        waitForPageToLoad(25);
        waitForElementPresent(2);
        boolean status=true;
        status=verifyNoelement(addACard);
        if(status){
            System.out.println("CC details are not available in Payment section");
        }else {
            waitForPageToLoad(25);
            int paymentList = addList.size();
            for (int i = 0; i < paymentList; i++) {
                deletePaymentCard();
                confirmDeletePaymentCard();
                waitForPageToLoad(25);
            }
        }
        waitForElementPresent(3);
    }

    public void verifyUserLandedToPaymentCardPage() {
        waitForWebElementPresent(paymentTitle, 8);
        paymentTitle.getText().trim();

    }

    public void userClickOnAddACardButton(){
        waitForPageToLoad(15);
        waitForElementToBeClickable(addACard,15);
        addACard.click();
    }

    /**
     * This method is used to enter debit or credit card details
     * */

    public void enterCreditDebitCardNumber(String cardType) {
        CreditDebitCardDetails(cardType);
        waitForPageToLoad(25);
        waitForElementPresent(3);
        boolean status=verifyNoelement(cvvInput1);
        if(cardType.equalsIgnoreCase("visa")){
            if(status){
                cvvInput1.click();
                cvvInput1.sendKeys(CardDataBean.getCvvNumber());
            }else{
            waitForVisibilityOfElementLocated(cardnumInput,driver,15);
            cardnumInput.click();
            cardnumInput.sendKeys(CardDataBean.getCardNumber());
            waitForPageToLoad(15);

//
//            waitForElementToBeClickable(expiryMonth,driver,15);
//            expiryMonth.click();
//            waitForPageToLoad(15);
//            int count = expiryMonthValues.size();
//            WebElement updateExpiryMonth=null;
//            String preFix = "//*[@id='menu-expirymonth']/div[3]/ul/li/span[text()='";
//            String postFix = "']";
//            for (int i = 1; i <= count; i++) {
//                String path = preFix + CardDataBean.getExpiryMonth() + postFix;
//                updateExpiryMonth = driver.findElement(By.xpath(path));
//            }
//            waitForElementToBeClickable(updateExpiryMonth,driver,15);
//            updateExpiryMonth.click();
//            waitForPageToLoad(15);
//            expiryYear.click();
//            waitForPageToLoad(15);
//            int count1 = expiryYearValues.size();
//            WebElement updateExpiryYear=null;
//            String preFix1 = "//*[@id='menu-expiryyear']/div[3]/ul/li/span[text()='";
//            String postFix1 = "']";
//            for (int i = 1; i <= count1; i++) {
//                String path = preFix1 + CardDataBean.getExpiryYear() + postFix1;
//                updateExpiryYear = driver.findElement(By.xpath(path));
//            }
//            waitForElementToBeClickable(updateExpiryYear,driver,15);
//            updateExpiryYear.click();
//            waitForPageToLoad(15);

            String  expiryDatecInput = CardDataBean.getExpiryMonth() + CardDataBean.getExpiryYear().substring(CardDataBean.getExpiryYear().length() -2);
            expiryDate.sendKeys(expiryDatecInput);

            waitForElementToBeClickable(cvvInput,driver,15);
            cvvInput.click();
            cvvInput.sendKeys(CardDataBean.getCvvNumber());
            waitForElementToBeClickable(namecardInput,driver,15);
            namecardInput.click();
            namecardInput.sendKeys(CardDataBean.getNameOnCard());
            }
        }
        else if(cardType.equalsIgnoreCase("master")){
            if(status){
                cvvInput1.click();
                cvvInput1.sendKeys(CardDataBean.getCvvNumber());
            }else{
                waitForVisibilityOfElementLocated(cardnumInput,driver,15);
                cardnumInput.click();
                cardnumInput.sendKeys(CardDataBean.getCardNumber());
                waitForPageToLoad(15);
                waitForElementToBeClickable(expiryMonth,driver,15);
                expiryMonth.click();
                waitForPageToLoad(15);
                int count = expiryMonthValues.size();
                WebElement updateExpiryMonth=null;
                String preFix = "//*[@id='menu-expirymonth']/div[3]/ul/li/span[text()='";
                String postFix = "']";
                for (int i = 1; i <= count; i++) {
                    String path = preFix + CardDataBean.getExpiryMonth() + postFix;
                    updateExpiryMonth = driver.findElement(By.xpath(path));
                }
                waitForElementToBeClickable(updateExpiryMonth,driver,15);
                updateExpiryMonth.click();
                waitForPageToLoad(15);
                expiryYear.click();
                int count1 = expiryYearValues.size();
                WebElement updateExpiryYear=null;
                String preFix1 = "//*[@id='menu-expiryyear']/div[3]/ul/li/span[text()='";
                String postFix1 = "']";
                for (int i = 1; i <= count1; i++) {
                    String path = preFix1 + CardDataBean.getExpiryYear() + postFix1;
                    updateExpiryYear = driver.findElement(By.xpath(path));
                }
                waitForElementToBeClickable(updateExpiryYear,driver,15);
                updateExpiryYear.click();
                waitForPageToLoad(15);
                waitForElementToBeClickable(cvvInput,driver,15);
                cvvInput.click();
                cvvInput.sendKeys(CardDataBean.getCvvNumber());
                waitForElementToBeClickable(namecardInput,driver,15);
                namecardInput.click();
                namecardInput.sendKeys(CardDataBean.getNameOnCard());
            }
        }
        else{
            testLogger.info("Card Type is Invalid" + cardType);
        }

    }

    public void clickOnContinue() {
        //waitForElementToBeClickable(clickOnContinueButton,driver,20);
        mouseHoverClick(clickOnContinueButton);
       // clickOnContinueButton.click();
    }

    public void verifySavedCardDetails(String nameOnCard){
//        SoftAssertions sa=new SoftAssertions();
//        waitForPageToLoad(100);
//        waitForElementPresent(3);
//        int cardList = addList.size();
//        String preFix="//*[@id='paymentList']/div/div[1]/div/div[1]/p[text()='";
//        String postFix="']";
//        for (int i = 1; i <= cardList; i++) {
//            String paymentCardNamePath=preFix+nameOnCard+postFix;
//            sa.assertThat(driver.findElement(By.xpath(paymentCardNamePath)).getText().trim()).isEqualTo(nameOnCard);
//        }
//        sa.assertAll();
    }

    public void verifyConformationMessage() {
        waitForWebElementPresent(areYouSure, 10);
        SoftAssertions sas =new SoftAssertions();
        sas.assertThat(areYouSure.getText().trim()).isEqualTo(configProperties.getProperty("landMarkPayment.header.user.payment.sure"));
        sas.assertThat(deleteAddressText.getText().trim()).isEqualTo(configProperties.getProperty("landMarkPayment.header.user.payment.delete.text"));
        sas.assertThat(neverMind.getText().trim()).isEqualTo(configProperties.getProperty("landMarkPayment.header.user.payment.nevermind"));
        sas.assertAll();
    }

    public void clickOnNeverMind() {
        waitForElementToBeClickable(neverMind,5);
        mouseHoverClick(neverMind);
    }

    public void verifyAddressNotDeleted() {
        waitForElementPresent(1);
        int addressList = addList.size();
        Assert.assertEquals(addressList, 1, "payment card list mismatch");
    }

    public void deletePaymentCardConfirm() {
        waitForElementPresent(1);
        waitForElementToBeClickable(deleteAddressText, 10);
        mouseHoverClick(deleteAddressText);
        waitForElementPresent(1);
    }

    public void paymentCardListCount() {
        waitForElementPresent(1);
        int addressList = addList.size();
        Assert.assertEquals(addressList, 2, "payment cards count mismatch");
    }

    public void changePaymentAsDefaultAndValidate() {
//        waitForElementPresent(1);
//        int cardList = addList.size();
//        for (int i = 1; i <= cardList; i++) {
//            String cardName=paymentCardName.getText().trim();
//        }
        mouseHoverClick(setDefaultSecondPaymentCard);
        waitForElementPresent(1);
//        cardList = addList.size();
//        for (int i = 1; i <= cardList; i++) {
//            String cardName=paymentCardName.getText().trim();
//        }
        SoftAssertions sa=new SoftAssertions();
        sa.assertThat(setDefaultOption.getText().trim()).contains(configProperties.getProperty("landMarkPayment.header.user.payment.default.set"));
        sa.assertAll();
        waitForElementPresent(1);
    }

    public void userClickOnAddASecondCardButton() {
        waitForPageToLoad(30);
        waitForElementToBeClickable(addSecondCard,25);
        mouseHoverClick(addSecondCard);
        //addSecondCard.click();
    }

    public void verifyErrorMessage() {
        addACard.click();
        clickOnContinue();
        SoftAssertions sa=new SoftAssertions();
        sa.assertThat(pleaseEnterATrueDigitNumberText.getText().trim()).isEqualTo(configProperties.getProperty("payment.valid.card.error1"));
        //sa.assertThat(pleaseSelectAValidMonthText.getText().trim()).isEqualTo(configProperties.getProperty("payment.select.month"));
        //sa.assertThat(pleaseSelectAValidYearText.getText().trim()).isEqualTo(configProperties.getProperty("payment.select.year"));
        sa.assertThat(enterCVVText.getText().trim()).isEqualTo(configProperties.getProperty("payment.enter.cvv"));
        sa.assertThat(pleaseEnterYourFullNameText.getText().trim()).isEqualTo(configProperties.getProperty("payment.enter.fullname"));
        sa.assertAll();
        closeIcon.click();
        waitForElementPresent(5);
    }
}
