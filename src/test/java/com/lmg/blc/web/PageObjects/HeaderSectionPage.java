package com.lmg.blc.web.PageObjects;

import com.landmarkshops.Web_Mweb.config.config;
import com.landmarkshops.Web_Mweb.utilities.WebUtils;
import org.assertj.core.api.SoftAssertions;
import org.openqa.selenium.By;
import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;

import java.util.Iterator;
import java.util.List;

import static com.landmarkshops.Web_Mweb.launcher.TestHarness.configProperties;

public class HeaderSectionPage extends WebUtils {

    HeaderSectionPage hsp;
    public HeaderSectionPage(WebDriver driver) {
        super(driver);
        PageFactory.initElements(driver, this);
    }


    @FindBy(xpath = "//*[@id='root-desk-top-inner']/div[1]/div/span/p")
    WebElement freeShipping;


    @FindBy(xpath = "//*[@id='page-header']/div/ul/span[1]/li[1]/p/span")
    WebElement freeShipping_toolTipMessage;

    @FindBy(xpath = "//*[@id='root-desk-top-inner']/div[1]/span[1]/p")
    WebElement clickAndCollect;

    @FindBy(xpath = "//*[@id='root-desk-top-inner']/div[1]/span[2]/p")
    WebElement homeDelivery;

    @FindBy(xpath = "//*[@id='customer-location-tof']/span/div[2]")
    WebElement getDeliveryToArea;

    @FindBy(xpath ="//*[@id='headerShukran']/img")
    WebElement shukranLogo;

    @FindBy(xpath ="//*[@id='shukranHeader']/a[2]")
    WebElement giftCard;

    @FindBy(xpath ="//*[@id='shukranHeader']/a[3]")
    WebElement aboutShukran;

    @FindBy(id ="jnShukran")
    WebElement joinNow;

    @FindBy(xpath = "//*[@id='DownloadourApps']")
    WebElement downLoadOurApps;

//    @FindBy(xpath = "//*[@id='StoreLocator']")
//    WebElement storeLocator;

    @FindBy(css = "#root-desk-top-right-inner > div.MuiBox-root.jss55.desk-top-storelocator")
    WebElement storeLocator;

    @FindBy(xpath = "//*[@id='Help']")
    WebElement help;

    @FindBy(xpath = "//*[@id='page-header']/a")
    WebElement siteLogo;

    @FindBy(id = "account-actions-signup")
    WebElement signUp;


    @FindBy(id = "account-actions-signin")
    WebElement signIn;


    @FindBy(xpath = "//*[@id='root-nav-mini-basket']/div/button/span/div[1]")
    WebElement basket;


    @FindBy(id = "js-site-search-input")
    WebElement searchInput;

    @FindBy(xpath = "//*[@id='Help']/a/span")
    WebElement helpHeader;



    @FindBy(xpath = "//*[@id='jnShukran']")
    WebElement joinUs;


    @FindBy(xpath = "//*[@id='customer-location-tof']/span/div[2]")
    WebElement deliveryToArea;


    @FindBy(xpath = "//*[@id='page-header']/div/div[1]/div")
    WebElement women;

    @FindBy(xpath = "//*[@id='page-header']/div/div[2]/div")
    WebElement kids;

    @FindBy(xpath = "//*[@id='page-header']/div/div[3]/div")
    WebElement men;

    @FindBy(xpath = "//html/body/div[4]/div[3]/div[1]/nav/div[1]/div/div[3]/button/span[1]")
    WebElement m_men;


    @FindBy(xpath = "//html/body/div[4]/div[3]/div[1]/nav/div[1]/div/div[1]/button/span[1]")
    WebElement m_women;

    @FindBy(xpath = "//html/body/div[4]/div[3]/div[1]/nav/div[2]/ul/div[4]/div/div/div/span")
    WebElement m_women_submenu;


    @FindBy(xpath = "//html/body/div[4]/div[3]/div[1]/div[3]/div/nav/div[2]/div[1]/a/div/span")
    WebElement m_women_submenu_1;

    @FindBy(xpath = "//html/body/div[4]/div[3]/div[1]/nav/div[1]/div/div[2]/button/span[1]")
    WebElement m_kids;


    @FindBy(xpath = "//html/body/div[4]/div[3]/div[1]/nav/div[4]/ul/div[4]/div/div/div/span")
    WebElement m_men_submenu;


    @FindBy(xpath = "//html/body/div[4]/div[3]/div[3]/div[3]/div/nav/div[2]/a/div/span")
    WebElement m_men_submenu_1;

    @FindBy(xpath = "//*[@id='root-header']/div/div/div[2]/button/span[1]")
    WebElement hamburgerMenuClose;

    @FindBy(xpath = "//html/body/div[4]/div[3]/div[1]/div[3]/div/nav/div[1]/button")
    WebElement hamburgerMenuNavigateBack;


    @FindBy(xpath = "//*[@id='category-menu-0']/div[1]")
    WebElement subMenu_0;

    @FindBy(xpath = "//*[@id='category-menu-1']/div[1]")
    WebElement subMenu_1;

    @FindBy(xpath = "//*[@id='category-menu-2']/div[1]")
    WebElement subMenu_2;

  /*  @FindBy(xpath= "//*[@id='dept-men']//a[contains(text(),'T-shirt')]")
    WebElement subMenu_men_2;*/

    @FindBy(xpath = "//*[@id='category-menu-2']//div[@id='-0']/div/div/div/div[1]/div[2]/div/div[1]")
    WebElement subMenu_men_2;



/*    @FindBy(xpath= "//*[@id='dept-women']//a[contains(text(),'All Clothing')]")
    WebElement subMenu_women_2;*/

    @FindBy(xpath = "//*[@id='category-menu-0']//div[@id='-0']/div/div/div/div[1]/div[2]/div/div[1]")
    WebElement subMenu_women_2;


    @FindBy(xpath = "//*[@id='category-menu-0']//div[@id='-0']/div/div/div/div[2]/div/a/img")
    WebElement subMenu_women_Img;

/*    @FindBy(xpath= "//*[@id='dept-kids']//a[contains(text(),'Summer Edit')]")
    WebElement subMenu_kids_2;*/

    @FindBy(xpath = "//*[@id='category-menu-1']//div[@id='-0']/div/div/div/div[1]/div[2]/div/div[1]")
    WebElement subMenu_kids_2;


    @FindBy(id = "langChange")
    WebElement countrySwitcher;

    @FindBy(xpath = "//*[@id='footer-language-switch']/span")
    WebElement mCountrySwitcher;


    @FindBy(xpath = "//*[@id='footer-country-switch']/div/div/div[1]/button/span[1]/div[2]")
    WebElement countryDropDownIconMweb;

    @FindBy(xpath = "//*[@id='mob-top-header']/div/div[1]/button")
    WebElement hamburgerMenu;

//    @FindBy(xpath = "//*[@id='root-desk-top-right-inner']/div[1]/div/div/div[1]/button")
//    private WebElement countryDropDown;

    @FindBy(xpath = "//*[@id='root-desk-top-right-inner']/div[10]/div/div/div[1]/button/span/div[2]")
    private WebElement countryDropDown;

    @FindBy(xpath = "//*[@id='root-desk-top-right-inner']/div[10]/div/div/div[2]/div/div/div/div/nav/div[3]")
    private WebElement Egypt;

    @FindBy(xpath = "//*[@id='Banner-1']/a/span[1]/div/img")
    private WebElement department;

    @FindBy(xpath = "//*[@id='customer-location-tof']")
    private WebElement deliveryToSection;

    @FindBy(xpath = "//*[@id='customer-location-tof']/span/div[1]")
    private WebElement deliveryToSectionIcon;

    @FindBy(xpath = "//*[@id='customer-location-tof']/span/div[2]")
    private WebElement deliveryToSectionText;
    //*[@id='customer-location-tof']/span/div[3]
    @FindBy(xpath = "//*[@id='customer-location-tof']/span")
    private WebElement deliveryToSectionDropDownButton;

    @FindBy(xpath = "//div[@class='MuiCollapse-root MuiCollapse-entered']//button/span")
    private WebElement deliveryToSectionSignInToSeeYourAddressesButton;

    @FindBy(xpath = "//div[@class='MuiCollapse-root MuiCollapse-entered']//div[2]/div[1]/div")
    private WebElement deliveryToSectionWhereShouldWeDeliverToIcon;

    @FindBy(xpath = "//div[@class='MuiCollapse-root MuiCollapse-entered']//div[2]/div/div[1]/div")
    private WebElement deliveryToSectionWhereShouldWeDeliverToIconRegUser;

    @FindBy(xpath = "//div[@class='MuiCollapse-root MuiCollapse-entered']//div[2]/div[1]/span")
    private WebElement deliveryToSectionWhereShouldWeDeliverToText;

    @FindBy(xpath = "//div[@class='MuiCollapse-root MuiCollapse-entered']//div[2]/div/div[1]/span")
    private WebElement deliveryToSectionWhereShouldWeDeliverToTextRegUser;

    @FindBy(xpath = "//div[@class='MuiCollapse-root MuiCollapse-entered']//div[2]/div/div[2]")
    private WebElement deliveryToSectionWhereShouldWeDeliverToSubTitleTextRegUser;

    @FindBy(xpath = "//div[@class='MuiCollapse-root MuiCollapse-entered']//div[2]/div[4]/div[1]/div")
    private WebElement deliveryToSectionSearchForYourAreaIcon;

    @FindBy(xpath = "//div[@class='MuiCollapse-root MuiCollapse-entered']//input[@id='search-area-input']")
    private WebElement deliveryToSectionSearchForYourAreaTextBox;

    @FindBy(xpath = "//div[@class='MuiCollapse-root MuiCollapse-entered']//*[@id='desk-change-area']")
    private WebElement deliveryToSectionChangeYourAreaHyperlink;

    @FindBy(xpath = "//div[@class='MuiCollapse-root MuiCollapse-entered']//div[2]/div[3]/div/div[1]/div")
    private WebElement deliveryToSectionWeAreCurrentlyDeliveringToIcon;

    @FindBy(xpath = "//div[@class='MuiCollapse-root MuiCollapse-entered']//div[2]/div/div/div[1]/div")
    private WebElement deliveryToSectionWeAreCurrentlyDeliveringToIconRegUser;

    @FindBy(xpath = "//div[@class='MuiCollapse-root MuiCollapse-entered']//div[2]/div[3]/div/div[2]/div")
    private WebElement deliveryToSectionWeAreCurrentlyDeliveringToText;

    @FindBy(xpath = "//div[@class='MuiCollapse-root MuiCollapse-entered']//div[2]/div/div/div[2]/div[1]")
    private WebElement deliveryToSectionWeAreCurrentlyDeliveringToTextRegUser;

    @FindBy(xpath = "//div[@class='MuiCollapse-root MuiCollapse-entered']//div[2]/div[3]/div/div[2]/div[2]/div[1]")
    private WebElement deliveryToSectionSelectedCity;

    @FindBy(xpath = "//div[@class='MuiCollapse-root MuiCollapse-entered']//div[2]/div[2]/div[1]")
    private WebElement deliveryToSectionSelectedCityRegUser;

    @FindBy(xpath = "//div[@class='MuiCollapse-root MuiCollapse-entered']//div[2]/div[3]/div/div[2]/div[2]/div[1]")
    private WebElement deliveryToSectionSelectedPlace;

    @FindBy(xpath = "//div[@class='MuiCollapse-root MuiCollapse-entered']//div[2]/div[2]/div[2]")
    private WebElement deliveryToSectionSelectedPlaceRegUser;

    @FindBy(xpath = "//div[@class='MuiCollapse-root MuiCollapse-entered']//div[3]/button/span")
    private WebElement deliveryToSectionAddANewLocationManuallyRegUser;

    @FindBy(xpath = "//*[@id='desk-header-layout']/div/div[2]/div[1]")
    private WebElement accountName;

    @FindBy(xpath = "//div[@class='MuiCollapse-root MuiCollapse-entered']//*[@id='area-list']/li")
    private List<WebElement> deliveryToSectionWhereShouldWeDeliverToList;

    @FindBy(xpath = "//div[@class='MuiCollapse-root MuiCollapse-entered']//div[4]/div")
    private List<WebElement> deliveryToSectionWhereShouldWeDeliverToListRegUser;


    @FindBy(xpath = "//*[@id='footer-language-switch']/span[1]/div")
    private WebElement mWebLangSwitcher;
    /**
     * This method is used to click on country switcher dropdown icon
     */
    public void clickOnCountrySwitcher() {
        waitForElementToBeClickable(countryDropDown, 10);
        countryDropDown.click();
    }

    /**
     * Used to change country from country dropdown
     */
    public void changeCountry() {
        if (config.getCountry().equalsIgnoreCase("Egypt")) {
            waitForElementToBeClickable(Egypt, 15);
            elementMouseClick(Egypt);
            waitForPageToLoad(20);

        }
    }


    public void languageSwitcher(String language) {

        if (config.getBrowser().trim().equalsIgnoreCase("CHROME") || config.getBrowser().trim().equalsIgnoreCase("CHROMEHEADLESS") || config.getBrowser().trim().equalsIgnoreCase("MOBILECHROME")) {
            waitForElementToBeClickable(countrySwitcher, 15);
            javaScriptExecuteClick(countrySwitcher);
            waitForPageToLoad(20);
            System.out.println("Language was changed based on config property value   :-->" + language);
        }else{
            waitForElementToBeClickable(mCountrySwitcher, 15);
            mCountrySwitcher.click();
            waitForPageToLoad(20);
            System.out.println("Language was changed based on config property value   :-->" + language);
        }

    }
    public void languageSwitcher() {
        waitForPageToLoad(20);
        String text = mWebLangSwitcher.getText().trim();
        System.out.println("Language switcher"+text);
        waitForElementToBeClickable(mWebLangSwitcher, 15);
        mWebLangSwitcher.click();
    }


    public void chooseCountryFromHeader(String countryInput) {

        String country_pre = "//*[@id='root-desk-top-right-inner']/div[10]/div/div/div[2]/div/div/div/div/nav/div[";
        String country_post = "]/div";

        String countryName;
        for (int i = 1; i <= 6; i++) {
            String countryLocator = country_pre + i + country_post;
            waitForElementPresent(3);
            countryName = driver.findElement(By.xpath(countryLocator)).getText().trim();
            System.out.println("iterate country as:------>  " + countryName);
            if (countryName.equalsIgnoreCase(countryInput.trim())) {
                driver.findElement(By.xpath(countryLocator)).click();
                waitForPageToLoad(15);
                //waitForVisibilityOfElementLocated(footerCountryValue,driver,15);
                // assertThat(footerCountryValue.getText().trim()).isEqualTo(countryName);
                break;
            }

        }

    }

    public void chooseCountryMobileWeb(String countryInput) {
        String country_pre = "//*[@id='footer-country-switch']/div/div/div[2]/div/div/div/nav/div[";
        String country_post = "]/div";

        String countryName;
        for (int i = 1; i <= 6; i++) {
            String countryLocator = country_pre + i + country_post;
            waitForElementPresent(3);
            countryName = driver.findElement(By.xpath(countryLocator)).getText().trim();
            System.out.println("iterate country as:------>  " + countryName);
            if (countryName.equalsIgnoreCase(countryInput.trim())) {
                driver.findElement(By.xpath(countryLocator)).click();
                waitForPageToLoad(15);
                //waitForVisibilityOfElementLocated(footerCountryValue,driver,15);
                // assertThat(footerCountryValue.getText().trim()).isEqualTo(countryName);
                break;
            }

        }

    }

    public void clickOnHamburgerMenu() {
        //if (config.getBrowser().trim().equalsIgnoreCase("CHROME") || config.getBrowser().trim().equalsIgnoreCase("CHROMEHEADLESS") || config.getBrowser().trim().equalsIgnoreCase("MOBILECHROME")) {
            waitForElementToBeClickable(hamburgerMenu, 15);
            hamburgerMenu.click();
            System.out.println("user clicked on hamburger menu");




    }

    public void clickOnMwebCountrySwitcher() {
        waitForElementPresent(6);
        visibilityOf(countryDropDownIconMweb, 15);
        mouseHoverClick(countryDropDownIconMweb);

    }

    public void clickOnDepartment() {
        waitForElementToBeClickable(department, 15);
        department.click();


    }

    public void landedToHomePageByReLaunchURL() {
       // waitForElementPresent(20);
        navigateToParticularPage(config.getReLaunchURL());
        implicitWait(45);

    }

    public void clickOnSiteLogoToLandHomePage() {
        siteLogo.click();
        waitForPageToLoad(10);
    }


    public void verifyUserlandToGuestUserHomePage() {
        SoftAssertions sa = new SoftAssertions();
        mouseHoverClick(siteLogo);
        waitForPageToLoad(60);
        implicitWait(60);
        sa.assertThat(siteLogo.isDisplayed());
        sa.assertAll();
    }

    public String getBrowserURL() {
        return driver.getCurrentUrl().trim();
    }


    public void verifyAllUIComponentsInGuestHeader() {
        if (config.getBrowser().trim().equalsIgnoreCase("CHROME") || config.getBrowser().trim().equalsIgnoreCase("CHROMEHEADLESS") || config.getBrowser().trim().equalsIgnoreCase("MOBILECHROME")) {
            SoftAssertions sas = new SoftAssertions();
            sas.assertThat(signUp.isDisplayed());
            sas.assertThat(signIn.isDisplayed());
            sas.assertThat(basket.isDisplayed());
            sas.assertAll();
        } else {
//            SoftAssertions sas = new SoftAssertions();
////            scrollToParticularElement(mSubscribeTitle);
////            sas.assertThat(mSubscribeTitle.getText().trim()).isEqualTo(configProperties.getProperty("footer.subscription.title"));
////            sas.assertThat(mSubscribeInfo.getText().trim()).isEqualTo(configProperties.getProperty("footer.subscription.info"));
////            sas.assertThat(subscribeButton.isDisplayed());
//            // sas.assertThat(downLoadOurAppsText.getText().trim()).isEqualTo(configProperties.getProperty("footer.download.app.text"));
//            //sas.assertThat(downLoadOurAppsSubTitle.getText().trim()).isEqualTo(configProperties.getProperty("footer.download.app.subtext"));
//            sas.assertAll();
        }
    }

    public void clickonHelpHeaderSection() {
        waitForElementToBeClickable(helpHeader, 40);
        waitForElementPresent(5);
        helpHeader.click();
    }

    public void mouseHoverOnMenSection() {
        mouseHover(men);
    }

    public void mouseHoverOnWomenSection() {
        mouseHover(women);
    }

    public void mouseHoverOnKidsSection() {

        mouseHover(kids);
    }

    public void chooseShirtsFromMen() {
        if (!(config.getBrowser().equalsIgnoreCase("CHROME"))) {
            clickOnHamburgerMenu();
            waitForElementToBeClickable(m_men, 15);
            m_men.click();
            waitForWebElementPresent(m_men_submenu, 15);
            m_men_submenu.click();
            waitForWebElementPresent(m_men_submenu_1, 15);
            m_men_submenu_1.click();
        } else {
            mouseHover(men);
            waitForElementPresent(2);
            mouseHover(subMenu_2);
            waitForElementPresent(2);
            System.out.println("Get text" + subMenu_men_2.getText().trim());
            subMenu_men_2.click();

        }
    }

    public void chooseSubMenuFromWomen() {
        if(!(config.getBrowser().equalsIgnoreCase("CHROME"))){
            clickOnHamburgerMenu();
        }else {
            mouseHover(women);
            waitForElementPresent(2);
            mouseHover(subMenu_0);
            waitForElementPresent(2);
            System.out.println("Get text" + subMenu_women_2.getText().trim());
            subMenu_women_2.click();
        }
    }

    public void chooseSubMenuFromKids() {
        if(!(config.getBrowser().equalsIgnoreCase("CHROME"))) {
            clickOnHamburgerMenu();
        } else {
            mouseHover(kids);
            waitForElementPresent(2);
            mouseHover(subMenu_1);
            waitForElementPresent(2);
            System.out.println("Get text" + subMenu_kids_2.getText().trim());
            subMenu_kids_2.click();
        }
    }

    public void clickOnImageFromWomenSubmenu() {
        if(!(config.getBrowser().equalsIgnoreCase("CHROME"))) {
            clickOnHamburgerMenu();

        } else {
            mouseHover(women);
            waitForElementPresent(2);
            mouseHover(subMenu_0);
            waitForElementPresent(2);
            subMenu_women_Img.click();
        }
    }

    public void verifyAllUiComponentsInDeliverySection() {
        SoftAssertions sas = new SoftAssertions();
        if (config.getBrowser().trim().equalsIgnoreCase("CHROME") || config.getBrowser().trim().equalsIgnoreCase("CHROMEHEADLESS") || config.getBrowser().trim().equalsIgnoreCase("MOBILECHROME")) {
            sas.assertThat(deliveryToSection.isDisplayed());
            sas.assertThat(deliveryToSectionIcon.isDisplayed());
            sas.assertThat(deliveryToSectionText.isDisplayed());
            sas.assertThat(deliveryToSectionDropDownButton.isDisplayed());
            if (deliveryToSectionText.getText().trim().equalsIgnoreCase("Deliver to area")) {
                deliveryToSectionDropDownButton.click();
                waitForWebElementPresent(deliveryToSectionSignInToSeeYourAddressesButton,10);
                sas.assertThat(deliveryToSectionSignInToSeeYourAddressesButton.isDisplayed());
                sas.assertThat(deliveryToSectionWhereShouldWeDeliverToIcon.isDisplayed());
                sas.assertThat(deliveryToSectionWhereShouldWeDeliverToText.isDisplayed());
                sas.assertThat(deliveryToSectionSearchForYourAreaIcon.isDisplayed());
                sas.assertThat(deliveryToSectionSearchForYourAreaTextBox.isDisplayed());
            } else if (accountName.getText().trim().contains("Sign Up")) {
                deliveryToSectionDropDownButton.click();
                waitForWebElementPresent(deliveryToSectionSignInToSeeYourAddressesButton,10);
                sas.assertThat(deliveryToSectionSignInToSeeYourAddressesButton.isDisplayed());
                sas.assertThat(deliveryToSectionWeAreCurrentlyDeliveringToIcon.isDisplayed());
                sas.assertThat(deliveryToSectionWeAreCurrentlyDeliveringToText.isDisplayed());
                sas.assertThat(deliveryToSectionSelectedCity.isDisplayed());
                sas.assertThat(deliveryToSectionSelectedPlace.isDisplayed());
                sas.assertThat(deliveryToSectionChangeYourAreaHyperlink.isDisplayed());
                deliveryToSectionChangeYourAreaHyperlink.click();
                sas.assertThat(deliveryToSectionSignInToSeeYourAddressesButton.isDisplayed());
                sas.assertThat(deliveryToSectionWhereShouldWeDeliverToIcon.isDisplayed());
                sas.assertThat(deliveryToSectionWhereShouldWeDeliverToText.isDisplayed());
                sas.assertThat(deliveryToSectionSearchForYourAreaIcon.isDisplayed());
                sas.assertThat(deliveryToSectionSearchForYourAreaTextBox.isDisplayed());
            } else {

                deliveryToSectionDropDownButton.click();
                waitForWebElementPresent(deliveryToSectionWeAreCurrentlyDeliveringToIconRegUser,10);
                sas.assertThat(deliveryToSectionWeAreCurrentlyDeliveringToIconRegUser.isDisplayed());
                sas.assertThat(deliveryToSectionWeAreCurrentlyDeliveringToTextRegUser.isDisplayed());
                sas.assertThat(deliveryToSectionSelectedCityRegUser.isDisplayed());
                sas.assertThat(deliveryToSectionSelectedPlaceRegUser.isDisplayed());
                sas.assertThat(deliveryToSectionChangeYourAreaHyperlink.isDisplayed());
                deliveryToSectionChangeYourAreaHyperlink.click();
                waitForWebElementPresent(deliveryToSectionWhereShouldWeDeliverToIconRegUser,10);
                sas.assertThat(deliveryToSectionWhereShouldWeDeliverToIconRegUser.isDisplayed());
                sas.assertThat(deliveryToSectionWhereShouldWeDeliverToTextRegUser.isDisplayed());
                sas.assertThat(deliveryToSectionWhereShouldWeDeliverToSubTitleTextRegUser.isDisplayed());
                sas.assertThat(deliveryToSectionAddANewLocationManuallyRegUser.isDisplayed());
            }
        }
        sas.assertAll();
    }

    public void enterAreaSearchTextAndPressEnterKey(String search) {
        deliveryToSectionSearchForYourAreaTextBox.sendKeys(search);
        testLogger.info("Entered Search: " + deliveryToSectionSearchForYourAreaTextBox.getText().trim());
        //deliveryToSectionSearchForYourAreaTextBox.sendKeys(Keys.ENTER);
        clickElementsListContains(deliveryToSectionWhereShouldWeDeliverToList, search);

    }

    public void selectDeliveryToAddressFromSavedAddress(String search) {
        clickElementsListContains(deliveryToSectionWhereShouldWeDeliverToListRegUser, search);
    }


    public void clickOnLanguageSwitcher(String lang,String browser) {
        if((lang.equalsIgnoreCase("AR")) &&(!(browser.equalsIgnoreCase("CHROME"))) ){
          waitForElementPresent(3);
            mouseHover(mWebLangSwitcher);

        }
    }

    public void userClickOnStoreLocator() {

        waitForElementToBeClickable(storeLocator,driver,15);
       storeLocator.click();

    }

    public void verifyFreeShippingOnHeader() {
        SoftAssertions sas =new SoftAssertions();
        sas.assertThat(freeShipping.getText().trim()).isEqualTo(configProperties.getProperty("header.strip.freeShipping"));
        sas.assertAll();

    }

    public void verifyDarkHeaderStripSectionComponents() {
        SoftAssertions sas =new SoftAssertions();
        sas.assertThat(freeShipping.getText().trim()).isEqualTo(configProperties.getProperty("header.strip.freeShipping"));
        sas.assertThat(clickAndCollect.getText().trim()).isEqualTo(configProperties.getProperty("header.strip.clickAndCollect"));
        sas.assertThat(homeDelivery.getText().trim()).isEqualTo(configProperties.getProperty("header.strip.homeDelivery"));
        sas.assertThat(getDeliveryToArea.getText().trim()).contains(configProperties.getProperty("header.strip.deliveryToArea"));
        sas.assertThat(giftCard.getText().trim()).isEqualTo(configProperties.getProperty("header.strip.giftCard"));
        sas.assertThat(aboutShukran.getText().trim()).isEqualTo(configProperties.getProperty("header.strip.aboutShukran"));
        sas.assertThat(joinNow.getText().trim()).isEqualTo(configProperties.getProperty("header.strip.joinNow"));
//      sas.assertThat(downLoadOurApps.getText().trim()).isEqualTo(configProperties.getProperty("header.strip.downLoadOurApps"));
        //sas.assertThat(storeLocator.getText().trim()).isEqualTo(configProperties.getProperty("header.strip.storeLocator"));
//        sas.assertThat(help.getText().trim()).isEqualTo(configProperties.getProperty("header.strip.help"));
        sas.assertAll();
    }

    public void clickOnDownLoadOurApps() {
        waitForElementToBeClickable(downLoadOurApps,driver,15);
        downLoadOurApps.click();
    }

    public void clickOnGiftCard() {
        waitForElementToBeClickable(giftCard,driver,15);
        giftCard.click();
    }

    public void clickOnShukran() {
        waitForElementToBeClickable(aboutShukran,driver,15);
        aboutShukran.click();
    }

    public void userClickOnSignUpLinkFromHeader() {
        waitForPageToLoad(100);
        if((config.getBrowser().equalsIgnoreCase("MOBILEWEBCHROMEANDROID"))||(config.getBrowser().equalsIgnoreCase("MOBILECHROME"))) {
            hsp = new HeaderSectionPage(driver);
            hsp.clickOnHamburgerMenu();
            waitForElementToBeClickable(signUp, 30);
            signUp.click();
        }else {
            waitForElementToBeClickable(signUp, 30);
            signUp.click();
        }
    }

    public void clickOnSignInLinkFromHeader() {
        waitForElementToBeClickable(signIn,15);
        signIn.click();
    }

    public void verifyAllLinksForGuestUserHomePage() {

        String url="";
        List<WebElement> allURLs = driver.findElements(By.tagName("a"));
        System.out.println("Total links on the Wb Page: " + allURLs.size());

        //We will iterate through the list and will check the elements in the list.
        Iterator<WebElement> iterator = allURLs.iterator();
        while (iterator.hasNext()) {
            url = iterator.next().getText();
            System.out.println(url);
        }

    }

    public void verifyBrokenLinksForHomePage(){
        List<WebElement> links = driver.findElements(By.tagName("a"));
        System.out.println("Number of links in home page "+ links.size());
        for(int i=0;i<links.size();i++)
        {
            WebElement E1= links.get(i);
            String url= E1.getAttribute("href");
            verifyLinks(url);
        }
    }

    public void verifyBrokenImagesForHomePage() {
        // Storing all elements with img tag in a list of WebElements
        List<WebElement> images = driver.findElements(By.tagName("img"));
        System.out.println("Total number of Images on the Page are " + images.size());

        //checking the links fetched.
        for(int index=0;index<images.size();index++)
        {
            WebElement image= images.get(index);
            String imageURL= image.getAttribute("src");
            System.out.println("URL of Image " + (index+1) + " is: " + imageURL);
            verifyLinks(imageURL);

            //Validate image display using JavaScript executor
            try {
                boolean imageDisplayed = (Boolean) ((JavascriptExecutor) driver).executeScript("return (typeof arguments[0].naturalWidth !=\"undefined\" && arguments[0].naturalWidth > 0);", image);
                if (imageDisplayed) {
                    System.out.println("DISPLAY - OK");
                }else {
                    System.out.println("DISPLAY - BROKEN");
                }
            }
            catch (Exception e) {
                System.out.println("Error Occured");
            }
        }
    }

    public void userNavigateToHomePage() {
        waitForPageToLoad(50);
        navigateToParticularPage(config.getAuthenticationURL());
        waitThread(2000);
    }
}