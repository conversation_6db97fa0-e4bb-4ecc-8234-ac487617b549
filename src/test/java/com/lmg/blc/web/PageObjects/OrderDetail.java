package com.lmg.blc.web.PageObjects;

import com.landmarkshops.Web_Mweb.config.config;
import com.landmarkshops.Web_Mweb.utilities.OrderDetailsBean;
import com.landmarkshops.Web_Mweb.utilities.StepContext;
import com.landmarkshops.Web_Mweb.utilities.WebUtils;
import org.assertj.core.api.SoftAssertions;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;

import static com.landmarkshops.Web_Mweb.launcher.TestHarness.configProperties;

public class OrderDetail extends WebUtils {
    public OrderDetail(WebDriver driver) {
        super(driver);
        PageFactory.initElements(driver,this);
    }

    @FindBy(xpath = "//*[@id='orderStatusTitleblock_QA']/p[1]")
    private WebElement orderDetailsText;

    @FindBy(xpath="//*[@id='main-part']/div/div[2]/div/div/div[1]/div[1]/p[1]")
    private WebElement purchasedDateAndTime;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div/div[*]/div[1]/p")
    private WebElement mOrderDetailsText;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[2]/div[1]/img")
    private WebElement orderLogo;

    @FindBy(id = "my-acc-head_QA" )
    private WebElement orderNumber;

    @FindBy(id = "my-acc-head-desc_QA")
    private WebElement orderDateTime;

    @FindBy(id = "cancelItemsEnablerBtn_QA")
    private WebElement cancelItemsText;

    @FindBy(id = "orderDetailsCancelOrder_QA")
    private WebElement mCancelItemsText;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div[1]/div[*]/div/div[1]/div[1]/p[1]")
    private WebElement deliveryText;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div/div[*]/div[2]/div/div[1]/div[1]/p[1]")
    private WebElement mDeliveryText;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div[1]/div[*]/div/div[1]/div[1]/p[2]")
    private WebElement deliveryValue;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div/div[*]/div[2]/div/div[1]/div[1]/p[2]")
    private WebElement mDeliveryValue;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div[1]/div[*]/div/div[1]/div[2]/p[1]")
    private WebElement courierTrackingText;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div/div[*]/div[2]/div/div[1]/div[2]/p[1]")
    private WebElement mCourierTrackingText;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div[1]/div[*]/div/div[1]/div[2]/p[2]")
    private WebElement courierTrackingTextValue;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div/div[*]/div[2]/div/div[1]/div[2]/p[2]")
    private WebElement mCourierTrackingTextValue;

    @FindBy(xpath = "//*[@id='orderStatusSubmitted_QA']")
    private WebElement submittedText;

    @FindBy(xpath = "//*[@id='orderStatusSubmitted_QA']")
    private WebElement mSubmittedText;

    @FindBy(xpath = "//*[@id='orderStatusProcressing_QA']")
    private WebElement processingText;

    @FindBy(xpath = "//*[@id='orderStatusProcessing_QA']")
    private WebElement mProcessingText;

    @FindBy(xpath = "//*[@id='orderStatusShipped_QA']")
    private WebElement shippedText;

    @FindBy(xpath = "//*[@id='orderStatusShipped_QA']")
    private WebElement mShippedText;

    @FindBy(xpath = "//*[@id='orderStatusDelivered_QA']")
    private WebElement deliveredText;

    @FindBy(xpath = "//*[@id='orderStatusDelivered_QA']")
    private WebElement mDeliveredText;

    @FindBy(xpath = "(//*[@id='orderStatusItemHeader_QA']/div)[1]")
    private WebElement itemText;

    @FindBy(xpath = "//*[@id='orderStatusItemDesc_QA']/div")
    private WebElement descriptionText;

    @FindBy(xpath = "(//*[@id='orderStatusItemHeader_QA']/div)[2]")
    private WebElement totalPrice;

    @FindBy(xpath = "//*[@id='orderDetailsItemImg-item0_QA']/a/img")
    private WebElement productImage;

    @FindBy(xpath = "//*[@id='orderDetailsItemImg-item0_QA']/a/img")
    private WebElement mProductImage;

    @FindBy(xpath = "//*[@id='orderDetailsItemName-item0_QA']/span")
    private WebElement productDescription;

    @FindBy(xpath = "//*[@id='orderDetailsItemName-item0_QA']/a/span/span")
    private WebElement mProductDescription;

    @FindBy(xpath = "//*[@id='orderDetailsItemColor-item0_QA']")
    private WebElement colorWithValue;

    @FindBy(xpath = "//*[@id='orderDetailsItemSize-item0_QA']")
    private WebElement sizeWithValue;

    @FindBy(xpath = "//*[@id='orderDetailsItemQnty-item0_QA']")
    private WebElement qtyTextWithNumber;

    @FindBy(xpath = "//*[@id='orderDetailsItemQnty-item0_QA']")
    private WebElement mQtyTextWithNumber;

    @FindBy(xpath = "//*[@id='orderDetailsItemPrice-item0_QA']")
    private WebElement productPrice;

    @FindBy(xpath = "//*[@id='orderDetailItemPrice-item0_QA']")
    private WebElement mProductPrice;

    @FindBy(xpath = "//*[@id='orderDetailsAddrCont-HD_QA']/p/span[1]")
    private WebElement shippedToText;

    @FindBy(xpath = "//*[@id='orderDetailsHdTitleBlockMob_QA']/span[1]")
    private WebElement mShippedToText;

    @FindBy(xpath = "//*[@id='orderDetailsAddrCont-HD_QA']/p/span[2]")
    private WebElement shippedToUserName;

    @FindBy(xpath = "//*[@id='orderDetailsAddrMob-HD_QA']/div/h3")
    private WebElement mShippedToUserName;

    @FindBy(xpath = "//*[@id='orderDetailsAddrCont-HD_QA']/div/span[1]")
    private WebElement placedOrderBuildingName;

    @FindBy(xpath = "//*[@id='orderDetailsAddrMob-HD_QA']/div/span[1]")
    private WebElement mPlacedOrderBuildingName;

    @FindBy(xpath = "//*[@id='orderDetailsAddrCont-HD_QA']/div/span[2]")
    private WebElement placedOrderStreetName;

    @FindBy(xpath = "//*[@id='orderDetailsAddrMob-HD_QA']/div/span[2]")
    private WebElement mPlacedOrderStreetName;

    @FindBy(xpath = "//*[@id='orderDetailsAddrCont-HD_QA']/div/p[1]")
    private WebElement placedOrderCity;

    @FindBy(xpath = "//*[@id='orderDetailsAddrMob-HD_QA']/div/p[1]")
    private WebElement mPlacedOrderCity;

    @FindBy(xpath = "//*[@id='orderDetailsAddrCont-HD_QA']/div/p[2]")
    private WebElement placedOrderArea;

    @FindBy(xpath = "//*[@id='orderDetailsAddrMob-HD_QA']/div/p[2]")
    private WebElement mPlacedOrderArea;

    @FindBy(xpath = "")
    private WebElement placedOrderLandmark;

    @FindBy(xpath = "//*[@id='orderDetailsAddrMob-HD_QA']/div/p[3]")
    private WebElement mPlacedOrderLandmark;

    @FindBy(xpath = "//*[@id='orderDetailsAddrCont-HD_QA']/div/p[3]")
    private WebElement mobileNumberTextAndMobileNumber;

    @FindBy(xpath = "//*[@id='orderDetailsAddrMob-HD_QA']/div/p[3]")
    private WebElement mMobileNumberTextAndMobileNumber;

    @FindBy(xpath = "//*[@id='orderDetailsAddrTitleCont_QA']/img")
    private WebElement cncPickupLocationLogo;

    @FindBy(xpath = "//*[@id='orderDetailsAddrTitleCncMob_QA']/img")
    private WebElement mCncPickupLocationLogo;

    @FindBy(xpath = "//*[@id='orderDetailsAddrTitle_QA']")
    private WebElement cncPickupFromText;

    @FindBy(xpath = "//*[@id='orderDetailsAddrTitleCncMob_Q']/div/p[1]")
    private WebElement mCncPickupFromText;

    @FindBy(xpath = "//*[@id='orderDetailsAddr-CNC-store_QA']")
    private WebElement cncMaxFashionText;

    @FindBy(xpath = "//*[@id='orderDetailsAddrTitleCncMob_QA']/div/p[2]")
    private WebElement mCncMaxFashionText;

    @FindBy(xpath = "//*[@id='orderDetailsAddr-CNC-store_QA']")
    private WebElement cncAddressText;

    @FindBy(xpath = "//*[@id='orderDetailsAddrCnCMob_QA']/div[2]/span")
    private WebElement mCncAddressText;

    @FindBy(xpath = "//*[@id='orderDetailsAddress-CnC-QA']/span[1]")
    private WebElement cncFullAddress;

    @FindBy(xpath = "//*[@id='orderDetailsAddrCnCMob_QA']/div[2]/div[1]/span[1]")
    private WebElement mCncFullAddress;

    @FindBy(xpath = "//*[@id='orderDetailsAddress-CnC-QA']/span[4]")
    private WebElement cncCityName;

    @FindBy(xpath = "//*[@id='orderDetailsAddrCnCMob_QA']/div[2]/div[1]/span[5]")
    private WebElement mCncCityName;

    @FindBy(xpath = "//*[@id='orderDetailsAddrMainCont-CnC_QA']/div[2]/span[1]")
    private WebElement cncWorkingHoursText;

    @FindBy(xpath = "//*[@id='orderDetailsAddrCnCMob_QA']/div[2]/div[2]/span[1]")
    private WebElement mCncWorkingHoursText;

    @FindBy(xpath = "//*[@id='orderDetailsAddrMainCont-CnC_QA']/div[2]/span[2]")
    private WebElement cncWorkingHoursTimings;

    @FindBy(xpath = "//*[@id='orderDetailsAddrCnCMob_QA']/div[2]/div[2]/span[2]")
    private WebElement mCncWorkingHoursTimings;

    @FindBy(xpath = "//*[@id='orderDetailsCnCPickupDirMob_QA']")
    private WebElement mCncGetDirectionsButton;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div[2]/div[1]/div/div/div[2]/img")
    private WebElement paymentLogo;

    @FindBy(xpath = "//*[@id='orderDetailsPaymentTypeMob-Card-Vi_QA']")
    private WebElement mPaymentLogo;

    @FindBy(xpath = "//*[@id='orderDetailsPaymentTypeCODDesk_QA']/span")
    private WebElement cashOnDeliveryText;

    @FindBy(xpath = "//*[@id='orderDetailsSummaryCODPaymentMob_QA']/span")
    private WebElement mCashOnDeliveryText;

    @FindBy(xpath = "//*[@id='orderDetailsMaskedCardNumDesk_QA']")
    private WebElement creditDebitCardNumber;

    @FindBy(xpath = "//*[@id='orderDetailMaskedCardNumMob_QA']")
    private WebElement mCreditDebitCardNumber;

    @FindBy(xpath = "//*[@id='orderDetailsCardDetailsDesk_QA']")
    private WebElement creditDebitCardDetails;

    @FindBy(xpath = "//*[@id='orderDetailsCardholderNameCvvExpMob_QA']")
    private WebElement mCreditDebitCardDetails;

    @FindBy(xpath = "//*[@id='orderDetailsPricingSubtotal_QA']/div[1]")
    private WebElement subTotalText;

    @FindBy(xpath = "//*[@id='orderDetailsPricingSubtotalMob_QA']/div[1]")
    private WebElement mSubTotalText;

    @FindBy(xpath = "//*[@id='orderDetailsPricingSubtotal_QA']/div[2]")
    private WebElement subTotalValue;

    @FindBy(xpath = "//*[@id='orderDetailsPricingSubtotalMob_QA']/div[2]")
    private WebElement mSubTotalValue;

    @FindBy(xpath = "//*[@id='orderDetailsPricingStndrdShppng_QA']/div[1]/p")
    private WebElement standardGroundShippingText;

    @FindBy(xpath = "//*[@id='orderDetailsStndrdShpngMob_QA']/div[1]/p")
    private WebElement mStandardGroundShippingText;

    @FindBy(xpath = "//*[@id='orderDetailsPricingStndrdShppng_QA']/div[1]/p")
    private WebElement standardGroundShippingTextCOD;

    @FindBy(xpath = "//*[@id='orderDetailsStndrdShpngMob_QA']/div[1]/p")
    private WebElement mStandardGroundShippingTextCOD;

    @FindBy(xpath = "//*[@id='orderDetailsPricingStndrdShppng_QA']/div[2]")
    private WebElement standardGroundShippingValueCOD;

    @FindBy(xpath = "//*[@id='orderDetailsStndrdShpngMob_QA']/div[2]/div")
    private WebElement mStandardGroundShippingValueCOD;

    @FindBy(xpath = "//*[@id='orderDetailsPricingStndrdShppng_QA']/div[2]")
    private WebElement standardGroundShippingValue;

    @FindBy(xpath = "//*[@id='orderDetailsStndrdShpngMob_QA']/div[2]/div")
    private WebElement mStandardGroundShippingValue;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div[2]/div[1]/div/div/div[3]/div[4]/div[1]/p")
    private WebElement standardGroundShippingText1;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div/div[3]/div[4]/div[4]/div[1]/p")
    private WebElement mStandardGroundShippingText1;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div[2]/div[1]/div/div/div[3]/div[3]/div[1]/p")
    private WebElement standardGroundShippingText2;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div/div[3]/div[4]/div[3]/div[1]/p")
    private WebElement mStandardGroundShippingText2;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div[2]/div[1]/div/div/div[3]/div[4]/div[2]")
    private WebElement standardGroundShippingValue1;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div/div[3]/div[4]/div[4]/div[2]/div")
    private WebElement mStandardGroundShippingValue1;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div[2]/div[1]/div/div/div[3]/div[3]/div[2]")
    private WebElement standardGroundShippingValue2;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div/div[3]/div[4]/div[3]/div[2]/div")
    private WebElement mStandardGroundShippingValue2;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div[2]/div[1]/div/div/div[3]/div[*]/div/p")
    private WebElement buy2Get1FreeText;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div/div[*]/div[4]/div[2]/div/p")
    private WebElement mBuy2Get1FreeText;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div[2]/div[1]/div/div/div[3]/div[*]/div/div")
    private WebElement buy2Get1FreeValue;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div/div[2]/div[4]/div[2]/div/div")
    private WebElement mBuy2Get1FreeValue;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div[2]/div[1]/div/div/div[3]/div[3]/div[1]/p")
    private WebElement buy2Get1FreeStandardGroundShippingText;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div/div[3]/div[4]/div[3]/div[1]/p")
    private WebElement mBuy2Get1FreeStandardGroundShippingText;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div[2]/div[1]/div/div/div[3]/div[3]/div[2]")
    private WebElement buy2Get1FreeStandardGroundShippingValue;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div/div[3]/div[4]/div[3]/div[2]/div")
    private WebElement mBuy2Get1FreeStandardGroundShippingValue;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div[2]/div[1]/div/div/div[3]/div[4]/p")
    private WebElement buy2Get1FreeCodChargesText;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div/div[3]/div[4]/div[4]/p")
    private WebElement mBuy2Get1FreeCodChargesText;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div[2]/div[1]/div/div/div[3]/div[4]/div")
    private WebElement buy2Get1FreeCodChargesValue;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div/div[3]/div[4]/div[4]/div")
    private WebElement mBuy2Get1FreeCodChargesValue;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div[2]/div[1]/div/div/div[3]/div[4]/div[1]/p[1]")
    private WebElement buy2Get1FreeTotalTextCOD;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div/div[2]/div[4]/div[4]/div[1]/p[1]")
    private WebElement mBuy2Get1FreeTotalTextCOD;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div[2]/div[1]/div/div/div[3]/div[5]/div[1]/p[1]")
    private WebElement buy2Get1FreeTotalTextCOD1;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div/div[3]/div[4]/div[5]/div[1]/p[1]")
    private WebElement mBuy2Get1FreeTotalTextCOD1;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div[2]/div[1]/div/div/div[3]/div[4]/div[1]/p[2]")
    private WebElement buy2Get1FreeInclusiveOfAnyApplicableVATTextCOD;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div/div[2]/div[4]/div[4]/div[1]/p[2]")
    private WebElement mBuy2Get1FreeInclusiveOfAnyApplicableVATTextCOD;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div[2]/div[1]/div/div/div[3]/div[5]/div[1]/p[2]")
    private WebElement buy2Get1FreeInclusiveOfAnyApplicableVATTextCOD1;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div/div[3]/div[4]/div[5]/div[1]/p[2]")
    private WebElement mBuy2Get1FreeInclusiveOfAnyApplicableVATTextCOD1;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div[2]/div[1]/div/div/div[3]/div[4]/div[2]")
    private WebElement buy2Get1FreeTotalValueCOD;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div/div[2]/div[4]/div[4]/div[2]")
    private WebElement mBuy2Get1FreeTotalValueCOD;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div[2]/div[1]/div/div/div[3]/div[5]/div[2]")
    private WebElement buy2Get1FreeTotalValueCOD1;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div/div[3]/div[4]/div[5]/div[2]")
    private WebElement mBuy2Get1FreeTotalValueCOD1;

    @FindBy(xpath = "//*[@id='orderDetailsPricingCODChargesDesk_QA']/p")
    private WebElement codChargesText;

    @FindBy(xpath = "//*[@id='orderDetailsPricingCodMob_QA']/p")
    private WebElement mCodChargesText;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div[2]/div[1]/div/div/div[3]/div[5]/p")
    private WebElement codChargesText1;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div/div[3]/div[4]/div[5]/p")
    private WebElement mCodChargesText1;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div[2]/div[1]/div/div/div[3]/div[4]/p")
    private WebElement codChargesText2;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div/div[3]/div[4]/div[4]/p")
    private WebElement mCodChargesText2;

    @FindBy(xpath = "//*[@id='orderDetailsPricingCODChargesDesk_QA']/div")
    private WebElement codChargesValue;

    @FindBy(xpath = "//*[@id='orderDetailsPricingCodMob_QA']/p")
    private WebElement mCodChargesValue;

    @FindBy(xpath = "//*[@id='orderDetailsPricingCODChargesDesk_QA']/div")
    private WebElement codChargesValue1;

    @FindBy(xpath = "//*[@id='orderDetailsPricingCodMob_QA']/p")
    private WebElement mCodChargesValue1;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div[2]/div[1]/div/div/div[3]/div[4]/div")
    private WebElement codChargesValue2;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div/div[3]/div[4]/div[4]/div")
    private WebElement mCodChargesValue2;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div[2]/div[1]/div/div/div[3]/div[3]/div/p")
    private WebElement steppedMultiBuyText;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div/div[3]/div[4]/div[3]/div/p")
    private WebElement mSteppedMultiBuyText;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div[2]/div[1]/div/div/div[3]/div[3]/div/div")
    private WebElement steppedMultiBuyValue;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div/div[3]/div[4]/div[3]/div/div")
    private WebElement mSteppedMultiBuyValue;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div[2]/div[1]/div/div/div[3]/div[4]/div[1]/p[1]")
    private WebElement TotalTextCOD;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div/div[3]/div[4]/div[4]/div[1]/p[1]")
    private WebElement mTotalTextCOD;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div[2]/div[1]/div/div/div[3]/div[4]/div[1]/p[2]")
    private WebElement inclusiveOfAnyApplicableVATTextCOD;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div/div[3]/div[4]/div[4]/div[1]/p[2]")
    private WebElement mInclusiveOfAnyApplicableVATTextCOD;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div[2]/div[1]/div/div/div[3]/div[4]/div[2]")
    private WebElement TotalValueCOD;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div/div[2]/div[4]/div[4]/div[2]")
    private WebElement mTotalValueCOD;

    @FindBy(xpath = "//*[@id='orderDetailsPricingTotal_QA']/div[1]/p[1]")
    private WebElement TotalText;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div/div[2]/div[4]/div[4]/div[1]/p[1]")
    private WebElement mTotalText;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div[2]/div[1]/div/div/div[3]/div[6]/div[1]/p[1]")
    private WebElement TotalText1;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div/div[3]/div[4]/div[6]/div[1]/p[1]")
    private WebElement mTotalText1;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div[2]/div[1]/div/div/div[3]/div[5]/div[1]/p[1]")
    private WebElement TotalText2;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div/div[2]/div[4]/div[5]/div[1]/p[1]")
    private WebElement mTotalText2;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div[2]/div[1]/div/div/div[3]/div[4]/div[1]/p[1]")
    private WebElement TotalText3;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div/div[2]/div[4]/div[4]/div[1]/p[1]")
    private WebElement mTotalText3;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div[2]/div[1]/div/div/div[3]/div[3]/div[1]/p[2]")
    private WebElement inclusiveOfAnyApplicableVATText;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div/div[2]/div[4]/div[3]/div[1]/p[2]")
    private WebElement mInclusiveOfAnyApplicableVATText;


    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div[2]/div[1]/div/div/div[3]/div[6]/div[1]/p[2]")
    private WebElement inclusiveOfAnyApplicableVATText1;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div/div[3]/div[4]/div[6]/div[1]/p[2]")
    private WebElement mInclusiveOfAnyApplicableVATText1;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div[2]/div[1]/div/div/div[3]/div[5]/div[1]/p[2]")
    private WebElement inclusiveOfAnyApplicableVATText2;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div/div[2]/div[4]/div[5]/div[1]/p[2]")
    private WebElement mInclusiveOfAnyApplicableVATText2;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div[2]/div[1]/div/div/div[3]/div[4]/div[1]/p[2]")
    private WebElement inclusiveOfAnyApplicableVATText3;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div/div[2]/div[4]/div[4]/div[1]/p[2]")
    private WebElement mInclusiveOfAnyApplicableVATText3;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div[2]/div[1]/div/div/div[3]/div[3]/div[2]")
    private WebElement TotalValue;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div/div[2]/div[4]/div[4]/div[2]")
    private WebElement mTotalValue;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div[2]/div[1]/div/div/div[3]/div[6]/div[2]")
    private WebElement TotalValue1;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div/div[3]/div[4]/div[6]/div[2]")
    private WebElement mTotalValue1;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div[2]/div[1]/div/div/div[3]/div[5]/div[2]")
    private WebElement TotalValue2;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div/div[2]/div[4]/div[5]/div[2]")
    private WebElement mTotalValue2;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div[2]/div[1]/div/div/div[3]/div[4]/div[2]")
    private WebElement TotalValue3;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div/div[2]/div[4]/div[4]/div[2]")
    private WebElement mTotalValue3;

    @FindBy(xpath = "//*[@id='returnPolicyBlocklTitle_QA']")
    private WebElement returnPolicyText;

    @FindBy(xpath = "//*[@id='returnPolicy-returnWindowImg_QA']")
    private WebElement returnPolicyCalenderIcon;

    @FindBy(xpath = "//*[@id='main-part']/div/div/div[3]/div/div/div[2]/div[2]/div/div/div[1]")
    private WebElement ReturnYourProductWithin7DaysAfterTheDeliveryText;

    @FindBy(xpath = "//*[@id='returnPolicy-returnEligbilityImg_QA']")
    private WebElement cartOrReturnIcon;

    @FindBy(xpath = "//*[@id='orders-return_QA']")
    private WebElement forMoreReturnsRelatedInformationReadTheCompleteText;

    @FindBy(css = "div.purchaseSummaryLeftPart>div>div>h5")
    private WebElement instore_purchaseSummary;

    @FindBy(xpath = "//*[@id='orders-return_QA']/a")
    private WebElement returnPolicyLink;


    @FindBy(id= "myAcc-topNav_QA")
    private  WebElement  mMyAccountBack;

    @FindBy(xpath="//*[@id='main-part']/div/div[2]/div/div/p")
    private WebElement mBackToPurchase;

    @FindBy(xpath = "//*[@id='main-part']/div/div[2]/div/div/div[1]/div[1]/div/h5")
    private WebElement mReceipt;

    @FindBy(xpath = "//*[@id='main-part']/div/div[2]/div/div/div[1]/div[1]/div/p")
    private WebElement mCompleted;

    @FindBy(xpath = "//*[@id='main-part']/div/div[2]/div/div/div[1]/div[1]/p[1]")
    private WebElement mDate;

    @FindBy(id="viewReceipt_QA")
    private WebElement mViewReceipt;

    @FindBy(xpath= "//*[@id='main-part']/div/div[2]/div/div/div[2]/div[1]/div/div/h5")
    private WebElement mPurchaseSummary;

    @FindBy(xpath = "//*[@id='shopping-basket-product-co-img']")
    private WebElement mProductDescriptionName;

    @FindBy(xpath = "//*[@id='shopping-basket-product-total']/h5")
    private WebElement mPriceValue;

    @FindBy(xpath = "//*[@id='main-part']/div/div[2]/div/div/div[2]/div[2]/div[1]/h5")
    private WebElement mReturnPolicy;

    @FindBy(xpath = "//*[@id='main-part']/div/div[2]/div/div/div[2]/div[2]/div[2]/h5")
    private WebElement mPaymentDetails;

    @FindBy(xpath = "//*[@id='main-part']/div/div[2]/div/div/div[2]/div[2]/div[2]/div")
    private WebElement  mPaymentSection;

    @FindBy(xpath = "//*[@id='main-part']/div/div[2]/div/div/div[2]/div[2]/div[2]/div/div[1]/div")
    private WebElement mCashSection;

    @FindBy(xpath = "//*[@id='main-part']/div/div[2]/div/div/div[2]/div[2]/div[2]/div/div[1]/div")
    private WebElement mCash;

    @FindBy(xpath ="//*[@id='main-part']/div/div[2]/div/div/div[2]/div[2]/div[2]/div/div[1]/div/h5")
    private WebElement mCashText;

    @FindBy(xpath ="//*[@id='main-part']/div/div[2]/div/div/div[2]/div[2]/div[2]/div/div[2]/div/h5")
    private WebElement mPointsText;

    @FindBy(xpath = "//*[@id='main-part']/div/div[2]/div/div/div[2]/div[2]/div[2]/div/div[2]")
    private WebElement mPoints;

    @FindBy(xpath = "//*[@id='main-part']/div/div[2]/div/div/div[2]/div[2]/div[3]/table/tbody/tr[1]/th")
    private WebElement mPurchaseSummaryOverall;

    @FindBy(xpath ="//*[@id='main-part']/div/div[2]/div/div/div[2]/div[2]/div[3]/table/tbody/tr[2]/th")
    private WebElement mQuantity;

    @FindBy(xpath ="//*[@id='main-part']/div/div[2]/div/div/div[2]/div[2]/div[3]/table/tbody/tr[2]/td")
    private WebElement mQuantityValue;

    @FindBy(xpath ="//*[@id='main-part']/div/div[2]/div/div/div[2]/div[2]/div[3]/table/tbody/tr[3]/th")
    private WebElement mSubTotal;

    @FindBy(xpath ="//*[@id='main-part']/div/div[2]/div/div/div[2]/div[2]/div[3]/table/tbody/tr[3]/td")
    private WebElement mSubTotalVal;

    @FindBy(xpath ="//*[@id='main-part']/div/div[2]/div/div/div[2]/div[2]/div[3]/table/tbody/tr[4]/th")
    private WebElement mVAT;

    @FindBy(xpath ="//*[@id='main-part']/div/div[2]/div/div/div[2]/div[2]/div[3]/table/tbody/tr[3]/td")
    private WebElement mVATVal;


    @FindBy(xpath ="//*[@id='main-part']/div/div[2]/div/div/div[2]/div[2]/div[3]/table/tbody/tr[5]/th")
    private WebElement mTotal;

    @FindBy(xpath ="//*[@id='main-part']/div/div[2]/div/div/div[2]/div[2]/div[3]/table/tbody/tr[5]/td")
    private WebElement mTotalVal;

        @FindBy(xpath = "//*[@id='main-part']/div/div[2]/div/div/p")
    private WebElement backToPurchase;


    @FindBy(xpath = "//*[@id='main-part']/div/div[2]/div/div/div[1]/div[1]/div/h5")
    private WebElement receiptNumber;

    @FindBy(xpath = "//*[@id='main-part']/div/div[2]/div/div/div[1]/div[1]/div/p")
    private WebElement completed;

    @FindBy(id = "viewReceipt_QA")
    private WebElement viewReceiptText;

    @FindBy(xpath="//*[@id='store-available-close']/span/div")
    private WebElement closeViewReceiptAlert;

    @FindBy(xpath= "//h4[@class='receiptTitle']")
    private WebElement receiptNumberPopUp;

    @FindBy(xpath = "//h5[@class='storeName']")
    private WebElement purchaseDateAndTimeReceipts;

    @FindBy(xpath = " //p[@class='territory']")
    private WebElement territoryReceipts;

    @FindBy(xpath = " //p[@class='date']")
    private WebElement dateReceipts;

    @FindBy(id = "receipt_barcode")
    private WebElement receiptBarcode;

    @FindBy(xpath="//*[@id='offline-view-receipt']/div[3]/div/div/div[2]/div[3]/div/table[@class='MuiTable-root']")
    private WebElement itemsTable    ;

    @FindBy(xpath="//*[@id='offline-view-receipt']/div[3]/div/div/div[2]/div[4]/div/table")
    private WebElement calculationSection;


    @FindBy(xpath="//div/ul[1][@class='tenderDetails']")
    private WebElement tenderDetailsReceipt;

    @FindBy(xpath="//div/ul[2][@class='shukranDetails']")
    private WebElement shukranDetailsReceipt;

    @FindBy(xpath = "//*[@id='main-part']/div/div[2]/div/div/div[1]/div[1]/p[2]")

    private WebElement storeName;

    @FindBy(xpath ="//*[@id='main-part']/div/div[2]/div/div/div[2]/div[1]/div/div/h5")
    private WebElement purchaseSummaryText;

    @FindBy(id = "shopping-basket-product-img")
    private WebElement offlineProductImage;

    @FindBy(id="shopping-basket-product")
    private WebElement offlineProductDescription;

    @FindBy(id = "shopping-basket-product-qty-title")
    private WebElement offlineQtyName;

    @FindBy(id="shopping-basket-product-qty")
    private WebElement offlineQtyVal;

    @FindBy(id ="shopping-basket-product-total")
    private WebElement offLineProductPrice;

    @FindBy(xpath= "//*[@id='main-part']/div/div[2]/div/div/div[2]/div[2]/div[1]/div[1]/img")
    private WebElement offLineCashImg;

    @FindBy(xpath ="//*[@id='main-part']/div/div[2]/div/div/div[2]/div[2]/div[1]/div[1]/div/h5")
    private WebElement offLineCashText;

    @FindBy(xpath ="//*[@id='main-part']/div/div[2]/div/div/div[2]/div[2]/div[1]/div[1]/div/div")
    private WebElement offLineCashValue;

    @FindBy(xpath ="//*[@id='main-part']/div/div[2]/div/div/div[2]/div[2]/div[1]/div[2]/img")
    private WebElement offLineShukranImg;

    @FindBy(xpath="//*[@id='main-part']/div/div[2]/div/div/div[2]/div[2]/div[1]/div[2]/div/h5")
    private WebElement offlinePointsText;

    @FindBy(xpath="//*[@id='main-part']/div/div[2]/div/div/div[2]/div[2]/div[1]/div[2]/div/div")
    private WebElement offlinePointsValue;

    @FindBy(xpath ="//*[@id='main-part']/div/div[2]/div/div/div[2]/div[2]/div[2]/table/tbody/tr[1]/th")
    private WebElement offlinePurchaseSummaryTitle;

    @FindBy(xpath="//*[@id='main-part']/div/div[2]/div/div/div[2]/div[2]/div[2]/table/tbody/tr[2]/th")
    private WebElement offlineQtyText;

    @FindBy(xpath="//*[@id='main-part']/div/div[2]/div/div/div[2]/div[2]/div[2]/table/tbody/tr[2]/td")
    private WebElement offlineQtyValue;

    @FindBy(xpath="//*[@id='main-part']/div/div[2]/div/div/div[2]/div[2]/div[2]/table/tbody/tr[3]/th")
    private WebElement offlineSubtotalText;

    @FindBy(xpath="//*[@id='main-part']/div/div[2]/div/div/div[2]/div[2]/div[2]/table/tbody/tr[3]/td")
    private WebElement offlineSubtotalValue;

    @FindBy(xpath ="//*[@id='main-part']/div/div[2]/div/div/div[2]/div[2]/div[2]/table/tbody/tr[4]/th")
    private WebElement offlineVATTitle;


    @FindBy(xpath ="//*[@id='main-part']/div/div[2]/div/div/div[2]/div[2]/div[2]/table/tbody/tr[4]/td")
    private WebElement offlineVATTitleValue;

    @FindBy(xpath="//*[@id='main-part']/div/div[2]/div/div/div[2]/div[2]/div[2]/table/tbody/tr[5]/th")
    private WebElement offlineTotalText;

    @FindBy(xpath="//*[@id='main-part']/div/div[2]/div/div/div[2]/div[2]/div[2]/table/tbody/tr[5]/td")
    private WebElement offlineTotalValue;

    @FindBy(xpath="//*[@id='returnPolicy_QA']/span")
    private WebElement offLineViewReturnPolicyText;

    @FindBy(xpath=" //*[@id='returnPolicy_QA']/img[2]")
    private WebElement offlineReturnPolicyImag;

    @FindBy(id="returnPolicy_QA")
    private WebElement offLineReturnPolicy;





    @FindBy(xpath="//*[@id='store-available-close']/span/div")
    private WebElement mCloseViewReceiptAlert;

    @FindBy(xpath="//h5[@class='storeName']")
    private WebElement mStoreNameReceipts;

    @FindBy(xpath="//p[@class='territory']")
    private WebElement mTerritoryReceipts;

    @FindBy(xpath = "//p[2][@class='date']")
    private WebElement mPurchaseDateAndTimeReceipts;


    @FindBy(xpath= "//h4[@class='invoiceTitle']")
    private WebElement mReceiptNumberPopUp;

    @FindBy(xpath="//*[@id='offline-view-receipt']/div[3]/div/div/div[2]/div[2]/div/table/thead/tr/th[1]")
    private WebElement mItemLabelReceipt;

    @FindBy(xpath="//*[@id='offline-view-receipt']/div[3]/div/div/div[2]/div[2]/div/table/thead/tr/th[2]")
    private WebElement mQtyLabelReceipt;


    @FindBy(xpath="//*[@id='offline-view-receipt']/div[3]/div/div/div[2]/div[2]/div/table/thead/tr/th[3]")
    private WebElement mRateLabelReceipt;

    @FindBy(xpath="//*[@id='offline-view-receipt']/div[3]/div/div/div[2]/div[2]/div/table/thead/tr/th[4]")
    private WebElement valueLabelReceipt;

    @FindBy(xpath = "//h5[@class='itemId']")
    private WebElement mSkuItemReceipt;

    @FindBy(xpath = "//h5[@class='itemName']")
    private WebElement mProductDescriptionReceipt;

    @FindBy(xpath="//*[@id='offline-view-receipt']/div[3]/div/div/div[2]/div[3]/div/table/tr[1]/td[1]")
    private WebElement msubtotalReceipt;

    @FindBy(xpath="//*[@id='offline-view-receipt']/div[3]/div/div/div[2]/div[3]/div/table/tr[2]/td[1]")
    private WebElement mVATReceipt;

    @FindBy(xpath="//*[@id='offline-view-receipt']/div[3]/div/div/div[2]/div[3]/div/table/tr[3]/td[1]")
    private WebElement mTotalReceipt;


    @FindBy(xpath="//*[@id='offline-view-receipt']/div[3]/div/div/div[2]/div[3]/div/table/tr[1]/td[2]")
    private WebElement mSubtotalValueReceipt;

    @FindBy(xpath="//*[@id='offline-view-receipt']/div[3]/div/div/div[2]/div[3]/div/table/tr[2]/td[2]")
    private WebElement mVATValueReceipt;


    @FindBy(xpath="//*[@id='offline-view-receipt']/div[3]/div/div/div[2]/div[3]/div/table/tr[3]/td[2]")
    private WebElement mTotalValueReceipt;

    @FindBy(xpath="//*[@id='offline-view-receipt']/div[3]/div/div/div[2]/div[3]/div/ul[1][@class='tenderDetails']")
    private WebElement mTenderDetailsReceipt;

    @FindBy(xpath= "//ul[2][@class='shukranDetails']")
    private WebElement mShukranDetailsSectionReceipt;

    @FindBy(xpath="//ul[2][@class='shukranDetails']/li[1]/dt[1]")
    private WebElement mShukranIdLabelReceipt;

    @FindBy(xpath="//ul[2][@class='shukranDetails']/li[1]/dd")
    private WebElement mShukranIdValueReceipt;

    @FindBy(xpath="//ul[2][@class='shukranDetails']/li[2]/dt[1]")
    private WebElement mPointsEarnedReceipt;

    @FindBy(xpath="//ul[2][@class='shukranDetails']/li[2]/dd")
    private WebElement mPointsEarnedValueReceipt;

    @FindBy(xpath="//ul[2][@class='shukranDetails']/li[3]/dt[1]")
    private WebElement mPointsRedeemedReceipt;

    @FindBy(xpath="//ul[2][@class='shukranDetails']/li[3]/dd")
    private WebElement mPointsRedeemedValueReceipt;

    @FindBy(xpath= "//*[@id='offline-view-receipt']/div[3]/div/div/div[2]/div[4]")
    private WebElement mFooterDescr;

    @FindBy(id = "receipt_barcode")
    private WebElement mReceiptBarcode;





    public void verifyUserLandedToOrderDetailsPage() {
        waitForPageToLoad(25);
        waitForElementPresent(7);
        SoftAssertions sa=new SoftAssertions();
        if(config.getBrowser().trim().equalsIgnoreCase("CHROME") || config.getBrowser().trim().equalsIgnoreCase("CHROMEHEADLESS") || config.getBrowser().trim().equalsIgnoreCase("MOBILECHROME") ) {
            waitForWebElementPresent(orderDetailsText,driver,25);
            sa.assertThat(orderDetailsText.getText().trim()).isEqualTo(configProperties.getProperty("landMarkOrders.header.orderDetails"));
        }else{
            sa.assertThat(mOrderDetailsText.getText().trim()).isEqualTo(configProperties.getProperty("landMarkOrders.header.orderSummary"));
        }
        sa.assertAll();
    }

    public void verifyAllUIElementsInOrderDetails() {
        waitForPageToLoad(15);
        SoftAssertions sa=new SoftAssertions();
        sa.assertThat(orderLogo.isDisplayed());
        sa.assertThat(orderNumber.isDisplayed());
        sa.assertThat(orderDateTime.isDisplayed());
        if(config.getBrowser().trim().equalsIgnoreCase("CHROME") || config.getBrowser().trim().equalsIgnoreCase("CHROMEHEADLESS") || config.getBrowser().trim().equalsIgnoreCase("MOBILECHROME") ) {
            sa.assertThat(orderDetailsText.getText().trim()).isEqualTo(configProperties.getProperty("landMarkOrders.header.orderDetails"));
            sa.assertThat(cancelItemsText.getText().trim()).isEqualTo(configProperties.getProperty("landMarkOrders.cancel.items.button.text"));
            sa.assertThat(deliveryText.isDisplayed());
            sa.assertThat(deliveryValue.isDisplayed());
            sa.assertThat(courierTrackingText.isDisplayed());
            sa.assertThat(courierTrackingTextValue.isDisplayed());
            sa.assertThat(submittedText.getText().trim()).isEqualTo(configProperties.getProperty("text.account.order.status.display.submitted"));
            sa.assertThat(processingText.getText().trim()).isEqualTo(configProperties.getProperty("text.account.order.status.display.processing"));
            sa.assertThat(shippedText.getText().trim()).isEqualTo(configProperties.getProperty("text.account.order.consignment.status.shipped"));
            sa.assertThat(deliveredText.getText().trim()).isEqualTo(configProperties.getProperty("text.account.order.consignment.status.delivered"));
        }else{
            sa.assertThat(mOrderDetailsText.getText().trim()).isEqualTo(configProperties.getProperty("lmgcheckout.order.review.summary"));
//            sa.assertThat(mCancelItemsText.getText().trim()).isEqualTo(configProperties.getProperty("landMarkOrders.cancel.items.button.text"));
            sa.assertThat(mDeliveryText.isDisplayed());
            sa.assertThat(mDeliveryValue.isDisplayed());
            sa.assertThat(mCourierTrackingText.isDisplayed());
            sa.assertThat(mCourierTrackingTextValue.isDisplayed());
            sa.assertThat(mSubmittedText.getText().trim()).isEqualTo(configProperties.getProperty("text.account.order.status.display.submitted"));
            sa.assertThat(mProcessingText.getText().trim()).isEqualTo(configProperties.getProperty("text.account.order.status.display.processing"));
            sa.assertThat(mShippedText.getText().trim()).isEqualTo(configProperties.getProperty("text.account.order.consignment.status.shipped"));
            sa.assertThat(mDeliveredText.getText().trim()).isEqualTo(configProperties.getProperty("text.account.order.consignment.status.delivered"));
        }
        verifyProductUIElementsInOrderDetails();
        verifyShippingDetailsUIElementsInOrderDetails();
        verifyPaymentMethodUIElements();
//        verifyTotalAmountUIElements();
        verifyReturnsPolicyUIElements();
        sa.assertAll();
    }

    public void verifyProductUIElementsInOrderDetails() {
        SoftAssertions sa=new SoftAssertions();
        if(config.getBrowser().trim().equalsIgnoreCase("CHROME") || config.getBrowser().trim().equalsIgnoreCase("CHROMEHEADLESS") || config.getBrowser().trim().equalsIgnoreCase("MOBILECHROME") ) {
            sa.assertThat(itemText.getText().trim()).isEqualTo(configProperties.getProperty("landMarkOrders.header.item"));
            sa.assertThat(descriptionText.getText().trim()).isEqualTo(configProperties.getProperty("landMarkOrders.header.description"));
            sa.assertThat(totalPrice.getText().trim()).isEqualTo(configProperties.getProperty("landMarkOrders.header.totalPrice"));
            sa.assertThat(productImage.isDisplayed());
            sa.assertThat(productDescription.isDisplayed());
//            sa.assertThat(colorWithValue.isDisplayed());
//            sa.assertThat(sizeWithValue.isDisplayed());
            sa.assertThat(qtyTextWithNumber.isDisplayed());
            sa.assertThat(productPrice.isDisplayed());
        }else{
            sa.assertThat(mProductImage.isDisplayed());
            sa.assertThat(mProductDescription.isDisplayed());
//            sa.assertThat(colorWithValue.isDisplayed());
//            sa.assertThat(sizeWithValue.isDisplayed());
            sa.assertThat(mQtyTextWithNumber.isDisplayed());
            sa.assertThat(mProductPrice.isDisplayed());
        }
        sa.assertAll();
    }

    public void verifyShippingDetailsUIElementsInOrderDetails(){
        SoftAssertions sa=new SoftAssertions();
            if (config.getBrowser().trim().equalsIgnoreCase("CHROME") || config.getBrowser().trim().equalsIgnoreCase("CHROMEHEADLESS") || config.getBrowser().trim().equalsIgnoreCase("MOBILECHROME")) {
                boolean status=verifyNoelement(cncPickupLocationLogo);
                if(status) {
                        sa.assertThat(cncPickupLocationLogo.isDisplayed());
                        sa.assertThat(cncPickupFromText.getText().trim()).isEqualTo(configProperties.getProperty("landMarkOrders.header.pickupFrom"));
                        sa.assertThat(cncMaxFashionText.getText().trim()).isEqualTo(configProperties.getProperty("landMarkOrders.header.maxFashion"));
                        sa.assertThat(cncAddressText.getText().trim()).isEqualTo(configProperties.getProperty("landMarkOrders.header.address"));
                        sa.assertThat(cncFullAddress.isDisplayed());
                        sa.assertThat(cncCityName.isDisplayed());
                        sa.assertThat(cncWorkingHoursText.getText().trim()).isEqualTo(configProperties.getProperty("lmgcheckout.reviewpage.workinghours.label"));
                        sa.assertThat(cncWorkingHoursTimings.isDisplayed());
                    } else {
                        sa.assertThat(shippedToText.getText().trim()).isEqualTo(configProperties.getProperty("landMarkOrders.header.shippedTo"));
                        sa.assertThat(shippedToUserName.isDisplayed());
                        sa.assertThat(placedOrderBuildingName.isDisplayed());
                        sa.assertThat(placedOrderStreetName.isDisplayed());
                        sa.assertThat(placedOrderCity.isDisplayed());
                        sa.assertThat(placedOrderArea.isDisplayed());
//                      sa.assertThat(placedOrderLandmark.isDisplayed());
                        sa.assertThat(mobileNumberTextAndMobileNumber.isDisplayed());
                    }

            } else {
                    boolean mStatus=verifyNoelement(mCncPickupLocationLogo);
                    if(mStatus) {
                            sa.assertThat(mCncPickupLocationLogo.isDisplayed());
                            sa.assertThat(mCncPickupFromText.getText().trim()).isEqualTo(configProperties.getProperty("landMarkOrders.header.pickupFrom"));
                            sa.assertThat(mCncMaxFashionText.getText().trim()).isEqualTo(configProperties.getProperty("landMarkOrders.header.maxFashion"));
                            sa.assertThat(mCncAddressText.getText().trim()).isEqualTo(configProperties.getProperty("landMarkOrders.header.address"));
                            sa.assertThat(mCncFullAddress.isDisplayed());
                            sa.assertThat(mCncCityName.isDisplayed());
                            sa.assertThat(mCncWorkingHoursText.getText().trim()).isEqualTo(configProperties.getProperty("lmgcheckout.reviewpage.workinghours.label"));
                            sa.assertThat(mCncWorkingHoursTimings.isDisplayed());
                            sa.assertThat(mCncGetDirectionsButton.isDisplayed());
                        } else {
                            sa.assertThat(mShippedToText.getText().trim()).isEqualTo(configProperties.getProperty("landMarkOrders.header.shippedTo"));
                            sa.assertThat(mShippedToUserName.isDisplayed());
                            sa.assertThat(mPlacedOrderBuildingName.isDisplayed());
                            sa.assertThat(mPlacedOrderStreetName.isDisplayed());
                            sa.assertThat(mPlacedOrderCity.isDisplayed());
                            sa.assertThat(mPlacedOrderArea.isDisplayed());
                            sa.assertThat(mPlacedOrderLandmark.isDisplayed());
                            sa.assertThat(mMobileNumberTextAndMobileNumber.isDisplayed());
                        }
            }

        sa.assertAll();
    }

    public void verifyPaymentMethodUIElements(){
        waitForElementPresent(2);
        SoftAssertions sa=new SoftAssertions();
        if(config.getBrowser().trim().equalsIgnoreCase("CHROME") || config.getBrowser().trim().equalsIgnoreCase("CHROMEHEADLESS") || config.getBrowser().trim().equalsIgnoreCase("MOBILECHROME") ) {
            sa.assertThat(paymentLogo.isDisplayed());
            boolean status = verifyNoelement(creditDebitCardDetails);
            if (status) {
                sa.assertThat(creditDebitCardNumber.isDisplayed());
                sa.assertThat(creditDebitCardDetails.isDisplayed());
            } else {
                sa.assertThat(cashOnDeliveryText.getText().trim()).isEqualTo(configProperties.getProperty("landMarkPayment.header.user.payment.cashondelivery"));
            }
        }else{
//            sa.assertThat(mPaymentLogo.isDisplayed());
            boolean status = verifyNoelement(mCreditDebitCardDetails);
            if (status) {
                sa.assertThat(mCreditDebitCardNumber.isDisplayed());
                sa.assertThat(mCreditDebitCardDetails.isDisplayed());
            } else {
                sa.assertThat(mCashOnDeliveryText.getText().trim()).isEqualTo(configProperties.getProperty("landMarkPayment.header.user.payment.cashondelivery"));
            }
        }
        sa.assertAll();
    }

    /**
    *
    * */

    public void verifyTotalAmountUIElements(){
        waitForElementPresent(2);
        SoftAssertions sa=new SoftAssertions();
        if(config.getBrowser().trim().equalsIgnoreCase("CHROME") || config.getBrowser().trim().equalsIgnoreCase("CHROMEHEADLESS") || config.getBrowser().trim().equalsIgnoreCase("MOBILECHROME") ) {
            sa.assertThat(subTotalText.getText().trim()).isEqualTo(configProperties.getProperty("landMarkOrders.header.subTotal"));
            sa.assertThat(subTotalValue.isDisplayed());
            boolean buy2Get1FreeStatus = verifyNoelement(buy2Get1FreeValue);
            boolean steppedMultiBuyStatus = verifyNoelement(steppedMultiBuyValue);
            boolean CODChargesTextStatus = verifyNoelement(codChargesValue1);

            if (buy2Get1FreeStatus && steppedMultiBuyStatus && CODChargesTextStatus){
                sa.assertThat(buy2Get1FreeText.getText().trim()).isEqualTo(configProperties.getProperty("landMarkOrders.header.Buy2Get1Free"));
                sa.assertThat(buy2Get1FreeValue.isDisplayed());
                sa.assertThat(steppedMultiBuyText.isDisplayed());
                sa.assertThat(steppedMultiBuyValue.isDisplayed());
                sa.assertThat(standardGroundShippingText1.getText().trim()).isEqualTo(configProperties.getProperty("landMarkOrders.header.standardGroundShipping"));
                sa.assertThat(standardGroundShippingValue1.isDisplayed());
                sa.assertThat(codChargesText1.getText().trim()).isEqualTo(configProperties.getProperty("basket.page.cod.charges"));
                sa.assertThat(codChargesValue1.isDisplayed());
                sa.assertThat(TotalText1.getText().trim()).isEqualTo(configProperties.getProperty("landMarkOrders.header.total"));
                sa.assertThat(inclusiveOfAnyApplicableVATText1.getText().trim()).isEqualTo(configProperties.getProperty("vat.included.message"));
                sa.assertThat(TotalValue1.isDisplayed());
            }else if(buy2Get1FreeStatus && steppedMultiBuyStatus){
                sa.assertThat(buy2Get1FreeText.getText().trim()).isEqualTo(configProperties.getProperty("landMarkOrders.header.Buy2Get1Free"));
                sa.assertThat(buy2Get1FreeValue.isDisplayed());
                sa.assertThat(steppedMultiBuyText.isDisplayed());
                sa.assertThat(steppedMultiBuyValue.isDisplayed());
                sa.assertThat(standardGroundShippingText1.getText().trim()).isEqualTo(configProperties.getProperty("landMarkOrders.header.standardGroundShipping"));
                sa.assertThat(standardGroundShippingValue1.isDisplayed());
                sa.assertThat(TotalText2.getText().trim()).isEqualTo(configProperties.getProperty("landMarkOrders.header.total"));
                sa.assertThat(inclusiveOfAnyApplicableVATText2.getText().trim()).isEqualTo(configProperties.getProperty("vat.included.message"));
                sa.assertThat(TotalValue2.isDisplayed());
            }else if(buy2Get1FreeStatus && CODChargesTextStatus){
                sa.assertThat(buy2Get1FreeText.getText().trim()).isEqualTo(configProperties.getProperty("landMarkOrders.header.Buy2Get1Free"));
                sa.assertThat(buy2Get1FreeValue.isDisplayed());
                sa.assertThat(standardGroundShippingText2.getText().trim()).isEqualTo(configProperties.getProperty("landMarkOrders.header.standardGroundShipping"));
                sa.assertThat(standardGroundShippingValue2.isDisplayed());
                sa.assertThat(codChargesText2.getText().trim()).isEqualTo(configProperties.getProperty("basket.page.cod.charges"));
                sa.assertThat(codChargesValue2.isDisplayed());
                sa.assertThat(TotalText2.getText().trim()).isEqualTo(configProperties.getProperty("landMarkOrders.header.total"));
                sa.assertThat(inclusiveOfAnyApplicableVATText2.getText().trim()).isEqualTo(configProperties.getProperty("vat.included.message"));
                sa.assertThat(TotalValue2.isDisplayed());
            }else if(buy2Get1FreeStatus){
                sa.assertThat(buy2Get1FreeText.getText().trim()).isEqualTo(configProperties.getProperty("landMarkOrders.header.Buy2Get1Free"));
                sa.assertThat(buy2Get1FreeValue.isDisplayed());
                sa.assertThat(standardGroundShippingText2.getText().trim()).isEqualTo(configProperties.getProperty("landMarkOrders.header.standardGroundShipping"));
                sa.assertThat(standardGroundShippingValue2.isDisplayed());
                sa.assertThat(TotalText3.getText().trim()).isEqualTo(configProperties.getProperty("landMarkOrders.header.total"));
                sa.assertThat(inclusiveOfAnyApplicableVATText3.getText().trim()).isEqualTo(configProperties.getProperty("vat.included.message"));
                sa.assertThat(TotalValue3.isDisplayed());

            }else if(steppedMultiBuyStatus && CODChargesTextStatus){

            }else if(steppedMultiBuyStatus){

            }else if(CODChargesTextStatus){
                sa.assertThat(standardGroundShippingTextCOD.getText().trim()).isEqualTo(configProperties.getProperty("landMarkOrders.header.standardGroundShipping"));
                sa.assertThat(standardGroundShippingValueCOD.isDisplayed());
                sa.assertThat(codChargesText.getText().trim()).isEqualTo(configProperties.getProperty("basket.page.cod.charges"));
                sa.assertThat(codChargesValue.isDisplayed());
                sa.assertThat(TotalTextCOD.getText().trim()).isEqualTo(configProperties.getProperty("landMarkOrders.header.total"));
                sa.assertThat(inclusiveOfAnyApplicableVATTextCOD.getText().trim()).isEqualTo(configProperties.getProperty("vat.included.message"));
                sa.assertThat(TotalValueCOD.isDisplayed());

            }else{
                sa.assertThat(standardGroundShippingText.getText().trim()).isEqualTo(configProperties.getProperty("landMarkOrders.header.standardGroundShipping"));
                sa.assertThat(standardGroundShippingValue.isDisplayed());
                sa.assertThat(TotalText.getText().trim()).isEqualTo(configProperties.getProperty("landMarkOrders.header.total"));
                sa.assertThat(inclusiveOfAnyApplicableVATText.getText().trim()).isEqualTo(configProperties.getProperty("vat.included.message"));
                sa.assertThat(TotalValue.isDisplayed());
            }
        }else {
            sa.assertThat(mSubTotalText.getText().trim()).isEqualTo(configProperties.getProperty("landMarkOrders.header.subTotal"));
            sa.assertThat(mSubTotalValue.isDisplayed());
            boolean buy2Get1FreeStatus = verifyNoelement(mBuy2Get1FreeValue);
            boolean steppedMultiBuyStatus = verifyNoelement(mSteppedMultiBuyValue);
            boolean chargesTextStatusCOD = verifyNoelement(mCodChargesValue1);

            if (buy2Get1FreeStatus && steppedMultiBuyStatus && chargesTextStatusCOD) {
                sa.assertThat(mBuy2Get1FreeText.getText().trim()).isEqualTo(configProperties.getProperty("landMarkOrders.header.Buy2Get1Free"));
                sa.assertThat(mBuy2Get1FreeValue.isDisplayed());
                sa.assertThat(mSteppedMultiBuyText.isDisplayed());
                sa.assertThat(mSteppedMultiBuyValue.isDisplayed());
                sa.assertThat(mStandardGroundShippingText1.getText().trim()).isEqualTo(configProperties.getProperty("landMarkOrders.header.standardGroundShipping"));
                sa.assertThat(mStandardGroundShippingValue1.isDisplayed());
                sa.assertThat(mCodChargesText1.getText().trim()).isEqualTo(configProperties.getProperty("basket.page.cod.charges"));
                sa.assertThat(mCodChargesValue1.isDisplayed());
                sa.assertThat(mTotalText1.getText().trim()).isEqualTo(configProperties.getProperty("landMarkOrders.header.total"));
                sa.assertThat(mInclusiveOfAnyApplicableVATText1.getText().trim()).isEqualTo(configProperties.getProperty("vat.included.message"));
                sa.assertThat(mTotalValue1.isDisplayed());
            } else if (buy2Get1FreeStatus && steppedMultiBuyStatus) {
                sa.assertThat(mBuy2Get1FreeText.getText().trim()).isEqualTo(configProperties.getProperty("landMarkOrders.header.Buy2Get1Free"));
                sa.assertThat(mBuy2Get1FreeValue.isDisplayed());
                sa.assertThat(mSteppedMultiBuyText.isDisplayed());
                sa.assertThat(mSteppedMultiBuyValue.isDisplayed());
                sa.assertThat(mStandardGroundShippingText1.getText().trim()).isEqualTo(configProperties.getProperty("landMarkOrders.header.standardGroundShipping"));
                sa.assertThat(mStandardGroundShippingValue1.isDisplayed());
                sa.assertThat(mTotalText2.getText().trim()).isEqualTo(configProperties.getProperty("landMarkOrders.header.total"));
                sa.assertThat(mInclusiveOfAnyApplicableVATText2.getText().trim()).isEqualTo(configProperties.getProperty("vat.included.message"));
                sa.assertThat(mTotalValue2.isDisplayed());
            } else if (buy2Get1FreeStatus && chargesTextStatusCOD) {
                sa.assertThat(mBuy2Get1FreeText.getText().trim()).isEqualTo(configProperties.getProperty("landMarkOrders.header.Buy2Get1Free"));
                sa.assertThat(mBuy2Get1FreeValue.isDisplayed());
                sa.assertThat(mStandardGroundShippingText2.getText().trim()).isEqualTo(configProperties.getProperty("landMarkOrders.header.standardGroundShipping"));
                sa.assertThat(mStandardGroundShippingValue2.isDisplayed());
                sa.assertThat(mCodChargesText2.getText().trim()).isEqualTo(configProperties.getProperty("basket.page.cod.charges"));
                sa.assertThat(mCodChargesValue2.isDisplayed());
                sa.assertThat(mTotalText2.getText().trim()).isEqualTo(configProperties.getProperty("landMarkOrders.header.total"));
                sa.assertThat(mInclusiveOfAnyApplicableVATText2.getText().trim()).isEqualTo(configProperties.getProperty("vat.included.message"));
                sa.assertThat(mTotalValue2.isDisplayed());
            } else if (buy2Get1FreeStatus) {
                sa.assertThat(mBuy2Get1FreeText.getText().trim()).isEqualTo(configProperties.getProperty("landMarkOrders.header.Buy2Get1Free"));
                sa.assertThat(mBuy2Get1FreeValue.isDisplayed());
                sa.assertThat(mStandardGroundShippingText2.getText().trim()).isEqualTo(configProperties.getProperty("landMarkOrders.header.standardGroundShipping"));
                sa.assertThat(mStandardGroundShippingValue2.isDisplayed());
                sa.assertThat(mTotalText3.getText().trim()).isEqualTo(configProperties.getProperty("landMarkOrders.header.total"));
                sa.assertThat(mInclusiveOfAnyApplicableVATText3.getText().trim()).isEqualTo(configProperties.getProperty("vat.included.message"));
                sa.assertThat(mTotalValue3.isDisplayed());

            } else if (steppedMultiBuyStatus && chargesTextStatusCOD) {

            } else if (steppedMultiBuyStatus) {

            } else if (chargesTextStatusCOD) {
                sa.assertThat(mStandardGroundShippingTextCOD.getText().trim()).isEqualTo(configProperties.getProperty("landMarkOrders.header.standardGroundShipping"));
                sa.assertThat(mStandardGroundShippingValueCOD.isDisplayed());
                sa.assertThat(mCodChargesText.getText().trim()).isEqualTo(configProperties.getProperty("basket.page.cod.charges"));
                sa.assertThat(mCodChargesValue.isDisplayed());
                sa.assertThat(mTotalTextCOD.getText().trim()).isEqualTo(configProperties.getProperty("landMarkOrders.header.total"));
                sa.assertThat(mInclusiveOfAnyApplicableVATTextCOD.getText().trim()).isEqualTo(configProperties.getProperty("vat.included.message"));
                sa.assertThat(mTotalValueCOD.isDisplayed());

            } else {
                sa.assertThat(mStandardGroundShippingText.getText().trim()).isEqualTo(configProperties.getProperty("landMarkOrders.header.standardGroundShipping"));
                sa.assertThat(mStandardGroundShippingValue.isDisplayed());
                sa.assertThat(mTotalText.getText().trim()).isEqualTo(configProperties.getProperty("landMarkOrders.header.total"));
                sa.assertThat(mInclusiveOfAnyApplicableVATText.getText().trim()).isEqualTo(configProperties.getProperty("vat.included.message"));
                sa.assertThat(mTotalValue.isDisplayed());
            }
        }
        sa.assertAll();
    }
    public void verifyReturnsPolicyUIElements(){
        SoftAssertions sa=new SoftAssertions();
        if(config.getBrowser().trim().equalsIgnoreCase("CHROME") || config.getBrowser().trim().equalsIgnoreCase("CHROMEHEADLESS") || config.getBrowser().trim().equalsIgnoreCase("MOBILECHROME") ) {
            sa.assertThat(returnPolicyText.getText().trim()).isEqualTo(configProperties.getProperty("help.section.return.header"));
            sa.assertThat(returnPolicyCalenderIcon.isDisplayed());
//        sa.assertThat(ReturnYourProductWithin7DaysAfterTheDeliveryText.getText().trim()).isEqualTo(configProperties.getProperty(""));
            sa.assertThat(cartOrReturnIcon.isDisplayed());
//        sa.assertThat(forMoreReturnsRelatedInformationReadTheCompleteText.getText().trim()).isEqualTo(configProperties.getProperty(""));
            sa.assertThat(returnPolicyLink.isDisplayed());
        }
        sa.assertAll();
    }


    public void verifyOrderNumberInOrderDetails() {
        String orderNumberFroUI=orderNumber.getText().trim();
        String actualOrderNumber=removeText(orderNumberFroUI);
        SoftAssertions sa=new SoftAssertions();
        sa.assertThat(actualOrderNumber.equals(OrderDetailsBean.getOrderNumber()));
        sa.assertAll();
    }

    public void clickOnProductImage() {
        if(config.getBrowser().trim().equalsIgnoreCase("CHROME") || config.getBrowser().trim().equalsIgnoreCase("CHROMEHEADLESS") || config.getBrowser().trim().equalsIgnoreCase("MOBILECHROME") ) {
            productImage.click();
        }else{
            mProductImage.click();
        }
    }

   public void verifyUserLandedToPurchaseDetailPage() {
      waitForPageToLoad(25);
        waitForElementPresent(7);
        SoftAssertions sa=new SoftAssertions();
        if(config.getBrowser().trim().equalsIgnoreCase("CHROME") || config.getBrowser().trim().equalsIgnoreCase("CHROMEHEADLESS") ) {
            waitForWebElementPresent(instore_purchaseSummary,driver,25);
            sa.assertThat(instore_purchaseSummary.getText().trim()).isEqualTo(configProperties.getProperty("landMarkOrders.header.purchaseSummary"));
        }else{
            sa.assertThat(mPurchaseSummary.getText().trim()).isEqualTo(configProperties.getProperty("landMarkOrders.header.purchaseSummary"));
        }
        sa.assertAll();
   }

    public void verifyAllOfflineUIElementsInOrderDetailsPage() {
        waitForPageToLoad(25);
        SoftAssertions sa= new SoftAssertions();
        if(StepContext.mWeb){

            sa.assertThat(mBackToPurchase.isDisplayed());
            sa.assertThat(mReceipt.isDisplayed());
            sa.assertThat(mCompleted.isDisplayed());
            sa.assertThat(mDate.isDisplayed());
            sa.assertThat(mViewReceipt.isDisplayed());
            sa.assertThat(mPurchaseSummary.isDisplayed());
            sa.assertThat(mProductDescriptionName.isDisplayed());
            sa.assertThat(mPriceValue.isDisplayed());
            sa.assertThat(mReturnPolicy.isDisplayed());
            sa.assertThat(mPaymentDetails.isDisplayed());
            sa.assertThat(mPaymentSection.isDisplayed());
            sa.assertThat(mCashSection.isDisplayed());
            sa.assertThat(mCash.isDisplayed());
            sa.assertThat(mPoints.isDisplayed());
            sa.assertThat(mPurchaseSummaryOverall.isDisplayed());
            sa.assertThat(mQuantity.isDisplayed());
            sa.assertThat(mQuantityValue.isDisplayed());
            sa.assertThat(mSubTotal.isDisplayed());
            sa.assertThat(mSubTotalVal.isDisplayed());
            sa.assertThat(mVAT.isDisplayed());
            sa.assertThat(mVATVal.isDisplayed());
            sa.assertThat(mTotal.isDisplayed());
            sa.assertThat(mTotalVal.isDisplayed());
            sa.assertThat(mViewReceipt.isDisplayed());
            sa.assertThat(mPurchaseSummaryOverall.isDisplayed());
            sa.assertThat(mQuantity.isDisplayed());
            sa.assertThat(mQuantityValue.isDisplayed());
            sa.assertThat(mSubTotal.isDisplayed());
            sa.assertThat(mSubTotalVal.isDisplayed());
            sa.assertThat(mVAT.isDisplayed());
            sa.assertThat(mVATVal.isDisplayed());
            sa.assertThat(mTotal.isDisplayed());
            sa.assertThat(mTotalVal.isDisplayed());
            sa.assertThat(mBackToPurchase.getText().trim()).isEqualTo(configProperties.getProperty("text.orderdetails.instore.backToPurchases"));
            sa.assertThat(mCompleted.getText().trim()).isEqualTo(configProperties.getProperty("text.orderdetails.instore.completed"));
            sa.assertThat(mViewReceipt.getText().trim()).isEqualTo(configProperties.getProperty("text.orderdetails.instore.viewReceipt"));
            sa.assertThat(mPurchaseSummary.getText().trim()).isEqualTo(configProperties.getProperty("text.orderdetails.instore.purchaseSummary"));
            sa.assertThat(mReturnPolicy.getText().trim()).isEqualTo(configProperties.getProperty("text.orderdetails.instore.returnsPolicy"));
            sa.assertThat(mPaymentDetails.getText().trim()).isEqualTo(configProperties.getProperty("text.orderdetails.instore.paymentDetails"));
            sa.assertThat(mCashText.getText().trim()).isEqualTo(configProperties.getProperty("text.orderdetails.instore.cash"));
            sa.assertThat(mPointsText.getText().trim()).isEqualTo(configProperties.getProperty("text.orderdetails.instore.Points"));
            sa.assertThat(mQuantity.getText().trim()).isEqualTo(configProperties.getProperty("text.orderdetails.instore.quanity"));
            sa.assertThat(mPurchaseSummaryOverall.getText().trim()).isEqualTo(configProperties.getProperty("text.orderdetails.instore.purchaseSummarytitle"));
            sa.assertThat(mQuantity.getText().trim()).isEqualTo(configProperties.getProperty("text.orderdetails.instore.quanity"));
            sa.assertThat(mSubTotal.getText().trim()).isEqualTo(configProperties.getProperty("text.orderdetails.instore.subtotal"));
            sa.assertThat(mVAT.getText().trim()).isEqualTo(configProperties.getProperty("text.orderdetails.instore.VAT"));
            sa.assertThat(mTotal.getText().trim()).isEqualTo(configProperties.getProperty("text.orderdetails.instore.Total"));


            sa.assertAll();
        }else{
            sa.assertThat(backToPurchase.isDisplayed());
            sa.assertThat(receiptNumber.isDisplayed());
            sa.assertThat(completed.isDisplayed());
            sa.assertThat(viewReceiptText.isDisplayed());
            sa.assertThat(purchasedDateAndTime.isDisplayed());
            sa.assertThat(storeName.isDisplayed());
            sa.assertThat(purchaseSummaryText.isDisplayed());
            sa.assertThat(offlineProductImage.isDisplayed());
            sa.assertThat(offlineProductDescription.isDisplayed());
            sa.assertThat(offlineQtyName.isDisplayed());
            sa.assertThat(offlineQtyText.isDisplayed());
            sa.assertThat(offlineQtyVal.isDisplayed());
            sa.assertThat(offLineProductPrice.isDisplayed());
            sa.assertThat(offLineCashImg.isDisplayed());
            sa.assertThat(offLineCashText.isDisplayed());
            sa.assertThat(offLineCashValue.isDisplayed());
            sa.assertThat(offLineShukranImg.isDisplayed());
            sa.assertThat(offlinePointsText.isDisplayed());
            sa.assertThat(offlinePointsValue.isDisplayed());
            sa.assertThat(offlinePurchaseSummaryTitle.isDisplayed());
            sa.assertThat(offlineQtyText.isDisplayed());
            sa.assertThat(offlineQtyName.isDisplayed());
            sa.assertThat(offlineQtyVal.isDisplayed());
            sa.assertThat(offlineQtyValue.isDisplayed());
            sa.assertThat(offlineSubtotalText.isDisplayed());
            sa.assertThat(offlineSubtotalValue.isDisplayed());
            sa.assertThat(offlineVATTitle.isDisplayed());
            sa.assertThat(offlineVATTitle.isDisplayed());
            sa.assertThat(offlineTotalText.isDisplayed());
            sa.assertThat(offlineTotalValue.isDisplayed());
            sa.assertThat(offLineViewReturnPolicyText.isDisplayed());
            sa.assertThat(offLineReturnPolicy.isDisplayed());
            sa.assertThat(offlineReturnPolicyImag.isDisplayed());
            sa.assertThat(backToPurchase.getText().trim()).isEqualTo(configProperties.getProperty("text.orderdetails.instore.backToPurchases"));
            sa.assertThat(completed.getText().trim()).isEqualTo(configProperties.getProperty("text.orderdetails.instore.completed"));
            sa.assertThat(viewReceiptText.getText().trim()).isEqualTo(configProperties.getProperty("text.orderdetails.instore.viewReceipt"));
            sa.assertThat(purchaseSummaryText.getText().trim()).isEqualTo(configProperties.getProperty("text.orderdetails.instore.purchaseSummary"));
            sa.assertThat(offlinePurchaseSummaryTitle.getText().trim()).isEqualTo(configProperties.getProperty("text.orderdetails.instore.purchaseSummarytitle"));
            sa.assertThat(offLineCashText.getText().trim()).isEqualTo(configProperties.getProperty("text.orderdetails.instore.cash"));
            sa.assertThat(offlinePointsText.getText().trim()).isEqualTo(configProperties.getProperty("text.orderdetails.instore.Points"));
            sa.assertThat(offlineQtyText.getText().trim()).isEqualTo(configProperties.getProperty("text.orderdetails.instore.quanity"));
            sa.assertThat(offlinePurchaseSummaryTitle.getText().trim()).isEqualTo(configProperties.getProperty("text.orderdetails.instore.purchaseSummarytitle"));
            sa.assertThat(offlineQtyText.getText().trim()).isEqualTo(configProperties.getProperty("text.orderdetails.instore.quanity"));
            sa.assertThat(offlineSubtotalText.getText().trim()).isEqualTo(configProperties.getProperty("text.orderdetails.instore.subtotal"));
            sa.assertThat(offlineVATTitle.getText().trim()).isEqualTo(configProperties.getProperty("text.orderdetails.instore.VAT"));
            sa.assertThat(offlineTotalText.getText().trim()).isEqualTo(configProperties.getProperty("text.orderdetails.instore.Total"));
            sa.assertAll();
        }
    }

    public void clickOnViewReceipt() {
        viewReceiptText.click();
    }

    public void verifyUIComponentsInReceipts(){
        SoftAssertions sa=new SoftAssertions();
        if(StepContext.web){
            waitForWebElementVisibility(closeViewReceiptAlert,25);



            sa.assertThat(closeViewReceiptAlert.isDisplayed());
            sa.assertThat(receiptNumberPopUp.isDisplayed());
            sa.assertThat(purchaseDateAndTimeReceipts.isDisplayed());
            sa.assertThat(territoryReceipts.isDisplayed());
            sa.assertThat(dateReceipts.isDisplayed());
            sa.assertThat(receiptBarcode.isDisplayed());
            sa.assertThat(itemsTable.isDisplayed());
            sa.assertThat(calculationSection.isDisplayed());
            sa.assertThat(tenderDetailsReceipt.isDisplayed());
            sa.assertThat(shukranDetailsReceipt.isDisplayed());

        }else{
            waitForWebElementVisibility(mCloseViewReceiptAlert,25);
            sa.assertThat(mCloseViewReceiptAlert.isDisplayed());
            sa.assertThat(mStoreNameReceipts.isDisplayed());
            sa.assertThat(mTerritoryReceipts.isDisplayed());
            sa.assertThat(mTerritoryReceipts.isDisplayed());
            sa.assertThat(mPurchaseDateAndTimeReceipts.isDisplayed());
            sa.assertThat(mReceiptNumberPopUp.isDisplayed());
            sa.assertThat(mItemLabelReceipt.isDisplayed());
            sa.assertThat(mQtyLabelReceipt.isDisplayed());
            sa.assertThat(mRateLabelReceipt.isDisplayed());
            sa.assertThat(valueLabelReceipt.isDisplayed());
            sa.assertThat(mSkuItemReceipt.isDisplayed());
            sa.assertThat(msubtotalReceipt.isDisplayed());
            sa.assertThat(mTotalReceipt.isDisplayed());
            sa.assertThat(mSubtotalValueReceipt.isDisplayed());
            sa.assertThat(mVATValueReceipt.isDisplayed());
            sa.assertThat(mVATReceipt.isDisplayed());
            sa.assertThat(mVATVal.isDisplayed());
            sa.assertThat(mTotalValueReceipt.isDisplayed());
            sa.assertThat(mTenderDetailsReceipt.isDisplayed());
            sa.assertThat(mShukranDetailsSectionReceipt.isDisplayed());
            sa.assertThat(mShukranIdLabelReceipt.isDisplayed());
            sa.assertThat(mShukranIdValueReceipt.isDisplayed());
            sa.assertThat(mPointsEarnedReceipt.isDisplayed());
            sa.assertThat(mPointsEarnedValueReceipt.isDisplayed());
            sa.assertThat(mPointsRedeemedReceipt.isDisplayed());
            sa.assertThat(mPointsRedeemedValueReceipt.isDisplayed());
            sa.assertThat(mFooterDescr.isDisplayed());
            sa.assertThat(mReceiptBarcode.isDisplayed());
            sa.assertAll();
        }


    }

    public void closeReceiptPopUp(){
        closeViewReceiptAlert.click();
    }


}
