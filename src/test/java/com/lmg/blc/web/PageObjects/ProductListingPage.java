package com.lmg.blc.web.PageObjects;

import com.landmarkshops.Web_Mweb.config.config;
import com.landmarkshops.Web_Mweb.utilities.WebUtils;
import org.assertj.core.api.SoftAssertions;
import org.openqa.selenium.Keys;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;

import java.util.List;

import static com.landmarkshops.Web_Mweb.launcher.TestHarness.configProperties;

public class ProductListingPage extends WebUtils {

    public ProductListingPage(WebDriver driver) {
        super(driver);
        PageFactory.initElements(driver, this);
    }

    /**
     * PLP Search
     */
    @FindBy(xpath = "//*[@id='js-site-search-input']")
    private WebElement PLPSearchBox;

    @FindBy(xpath = "//*[@id='page-header']//form/div[1]/div/div")
    private WebElement PLPSearchBoxIcon;

    @FindBy(xpath = "//*[@id='search-top-layout']/div/div[1]/div/div/div[1]/h1")
    private WebElement PLPSearchHeaderText;

    @FindBy(xpath = "//*[@id='search-top-layout']/div/div[2]")
    private WebElement PLPSearchForSection;

    @FindBy(xpath = "//*[@id='search-top-layout']//div[2]/div/div[1]")
    private WebElement PLPSearchForSectionLabel;

    @FindBy(xpath = "//*[@id='search-top-layout']/div/div[2]/div/div/a/span/div[2]")
    private List<WebElement> PLPSearchForSectionItemList;

    @FindBy(xpath = "//*[@id='search-silter-layout']")
    private WebElement PLPFilterSection;

    @FindBy(xpath = "//*[@id='search-silter-layout']//div[1]//button")
    private List<WebElement> PLPSearchFilterItemList;

    @FindBy(xpath = "//*[@id='search-list-layout']")
    private WebElement PLPSearchProductListSection;

    @FindBy(xpath = "/html/body/div[2]/div[2]/div/div[4]/div/div/div")
    private List<WebElement> PLPSearchProductList;

    @FindBy(xpath = "/html/body/div[2]/div[2]//div[4]//div[1]/a/div[1]/button/span/img")
    private List<WebElement> PLPProductAddToList;

    @FindBy(xpath = "/html/body/div[2]/div[2]//div[4]/div/div/div/div/div[1]/a/img")
    private List<WebElement> PLPProductImage;

    @FindBy(xpath = "/html/body/div[2]/div[2]//div[4]/div/div/div/div/div[2]")
    private List<WebElement> PLPProductPrice;

    @FindBy(xpath = "/html/body/div[2]/div[2]//div[4]/div/div/div/div/div[3]/a")
    private List<WebElement> PLPProductTitle;

    @FindBy(xpath = "/html/body/div[2]/div[2]//div[4]/div/div/div/div/div[4]/div[1]/a/div/div[1]/div[2]/div/div[4]/div/div/img")
    private WebElement PLPMouseOverProductImage;

    @FindBy(xpath = "//img[contains(@src,'white-arrow-left')]")
    private WebElement PLPMouseOverProductImageArrowLeft;

    @FindBy(xpath = "//img[contains(@src,'white-arrow-right')]")
    private WebElement PLPMouseOverProductImageArrowRight;

    @FindBy(xpath = "/html/body/div[2]/div[2]//div[4]/div/div/div/div/div[4]/div[2]")
    private WebElement PLPMouseOverProductPrice;

    @FindBy(xpath = "/html/body/div[2]/div[2]//div[4]/div/div/div/div/div[4]/div[3]/a")
    private WebElement PLPMouseOverProductTitle;

    @FindBy(xpath = "/html/body/div[2]/div[2]//div[4]/div/div/div/div/div[4]/button")
    private WebElement PLPMouseOverAddToBask;

    @FindBy(xpath = "//*[@id='page-header']//form/div[2]/div/div")
    private WebElement PLPSearchResultDropdown;

    @FindBy(xpath = "//*[@id='page-header']//form/div[2]/div/div/div/div[1]")
    private WebElement PLPSearchResultDropdownCategoriesSection;

    @FindBy(xpath = "//*[@id='page-header']//form/div[2]/div/div/div/div[2]")
    private WebElement PLPSearchResultDropdownTopProductsSection;

    @FindBy(xpath = "//*[@id='page-header']//form/div[2]/div/div/div/div[1]/div/div[1]")
    private WebElement PLPSearchResultDropdownCategoriesTitle;

    @FindBy(xpath = "//*[@id='product-list--departments']/a")
    private List<WebElement> PLPSearchResultDropdownCategoriesList;

    @FindBy(xpath = "//*[@id='page-header']//form/div[2]/div/div/div/div[2]/div[1]/div[1]")
    private WebElement PLPSearchResultDropdownTopProductsTitle;

    @FindBy(xpath = "//*[@id='product-list']/a")
    private List<WebElement> PLPSearchResultDropdownTopProductsList;

    @FindBy(xpath = "//*[@id='page-header']//form//div[2]/a/span")
    private WebElement PLPSearchResultDropdownSeeAllMatchedProductsHyperlink;

    @FindBy(xpath = "//*[@id='page-header']//form/div[2]/div/div/div/div")
    private WebElement PLPSearchResultDropdownRecentSearchesSection;

    @FindBy(xpath = "//*[@id='page-header']//form/div[2]/div/div/div/div/div/div[1]/div[1]")
    private WebElement PLPSearchResultDropdownRecentSearchesTitle;

    @FindBy(xpath = "//*[@id='page-header']/div[3]/div/form/div[2]/div/div/div/div/div/div[2]/ul/a")
    private List<WebElement> PLPSearchResultDropdownRecentSearchesList;

    @FindBy(xpath = "//*[@id='page-header']//form/div[2]//div[1]/div[2]/button/span")
    private WebElement PLPSearchResultDropdownRecentSearchesClearButton;

    @FindBy(xpath = "//*[@id='search-sortby-layout']")
    private WebElement PLPSortBySection;

    @FindBy(xpath = "//*[@id='search-sortby-layout']/div/div[1]")
    private WebElement PLPSortBySectionSearchHits;

    @FindBy(xpath = "//*[@id='search-sortby-layout']/div/div/div[1]")
    private WebElement PLPSortBylabel;

    @FindBy(xpath = "//*[@id='search-sortby-layout']/div/div/div[2]/button")
    private WebElement PLPSortByDropdownButton;

    @FindBy(xpath = "//*[@id='search-sortby-layout']/div/div/div[2]//ul")
    private WebElement PLPSortByDropdownSection;

    @FindBy(xpath = "//*[@id='search-sortby-layout']/div/div[2]/div[2]//ul/div")
    private List<WebElement> PLPSortByDropdownList;

    @FindBy(xpath = "//*[@id='success-block']")
    private WebElement PLPAddToBasketSuccessPopUp;

    @FindBy(xpath = "//*[@id='success-block']/div/div[4]/img")
    private WebElement PLPAddToBasketSuccessPopUpCloseIcon;

    @FindBy(xpath = "//*[@id='root-desk-top-inner']/div[1]/span[2]/p")
    private WebElement PLPHeaderHomeDelivery;

    @FindBy(xpath = "//*[@id='langChange']/a/span")
    private WebElement PLPLangChange;



    public void verifyUserNavigateToPLP(){
        waitForDomToLoad(10);
        setProductData();
    }
    public void setProductData() {
        if (config.getLanguage().equalsIgnoreCase("ar") && !(PLPLangChange.getText().trim().equalsIgnoreCase("EN"))) {
            waitForElementToBeClickable(PLPLangChange, driver, 20);
            PLPLangChange.click();
        }
    }

    /**
     * This method is for searching given string value
     *
     * @param searchText input value for searching
     */
    public void userEntersKeyInSearchBox(String searchText) {
        waitForElementToBeClickable(PLPSearchBox,15);
        visibilityOf(PLPSearchBox, 25);
        waitForElementPresent(5);
        mouseHoverClick(PLPSearchBox);
        for (int i = 0; i < 50; i++) {
            PLPSearchBox.sendKeys(Keys.BACK_SPACE);
            PLPSearchBox.sendKeys(Keys.DELETE);
        }
        PLPSearchBox.sendKeys(searchText);
        PLPSearchBox.sendKeys(Keys.ENTER);

    }

    /**
     * This method is for clearing the search text
     */
    public void clearTextSearchBox() {
        waitThread(5000); //for demo
        mouseHoverClick(PLPSearchBox);
        PLPSearchBox.clear();
        for (int i = 0; i < 50; i++) {
            PLPSearchBox.sendKeys(Keys.BACK_SPACE);
            PLPSearchBox.sendKeys(Keys.DELETE);
        }
        testLogger.info("clear entered search result");
    }

    /**
     * This method is for verifying the category section
     */
    public boolean verifyCategorySection() {
        boolean flag = false;
        SoftAssertions sa = new SoftAssertions();
        if (config.getBrowser().trim().equalsIgnoreCase("CHROME") || config.getBrowser().trim().equalsIgnoreCase("CHROMEHEADLESS") || config.getBrowser().trim().equalsIgnoreCase("MOBILECHROME")) {
            PLPSearchBox.sendKeys( " ");
            waitForElementPresent(5);
            flag = PLPSearchResultDropdown.isDisplayed();
            sa.assertThat(PLPSearchResultDropdown.isDisplayed());
            sa.assertThat(PLPSearchResultDropdownCategoriesSection.isDisplayed());
            sa.assertThat(PLPSearchResultDropdownCategoriesTitle.isDisplayed());
            sa.assertThat(PLPSearchResultDropdownCategoriesList.isEmpty());
            sa.assertThat(PLPSearchResultDropdownCategoriesTitle.getText().trim()).isEqualTo("Categories");
        } else {
            //Yet to implement
        }
        testLogger.info("PLP Search Category section is displayed" + flag);
        return flag;
    }

    /**
     * This method is for verifying Top Products
     */
    public boolean verifyTopProductSection() {
        boolean flag = false;
        SoftAssertions sa = new SoftAssertions();
        if (config.getBrowser().trim().equalsIgnoreCase("CHROME") || config.getBrowser().trim().equalsIgnoreCase("CHROMEHEADLESS") || config.getBrowser().trim().equalsIgnoreCase("MOBILECHROME")) {
            flag = PLPSearchResultDropdown.isDisplayed();
            sa.assertThat(PLPSearchResultDropdown.isDisplayed());
            sa.assertThat(PLPSearchResultDropdownTopProductsSection.isDisplayed());
            sa.assertThat(PLPSearchResultDropdownTopProductsTitle.isDisplayed());
            sa.assertThat(PLPSearchResultDropdownTopProductsList.isEmpty());
            sa.assertThat(PLPSearchResultDropdownSeeAllMatchedProductsHyperlink.isDisplayed());
            sa.assertThat(PLPSearchResultDropdownTopProductsTitle.getText().trim()).isEqualTo("Top Products");
        } else {
            //Yet to implement
        }
        testLogger.info("PLP Search Top Products section is displayed" + flag);
        return flag;
    }

    /**
     * This method is for recent search section
     */
    public boolean verifyRecentSearchesSection() {
        boolean flag = false;
        SoftAssertions sa = new SoftAssertions();
        if (config.getBrowser().trim().equalsIgnoreCase("CHROME") || config.getBrowser().trim().equalsIgnoreCase("CHROMEHEADLESS") || config.getBrowser().trim().equalsIgnoreCase("MOBILECHROME")) {
            flag = PLPSearchResultDropdownRecentSearchesSection.isDisplayed();
            //sa.assertThat(PLPSearchResultDropdown.isDisplayed());
            sa.assertThat(PLPSearchResultDropdownRecentSearchesSection.isDisplayed());
            sa.assertThat(PLPSearchResultDropdownRecentSearchesTitle.isDisplayed());
            sa.assertThat(PLPSearchResultDropdownRecentSearchesList.isEmpty());
            sa.assertThat(PLPSearchResultDropdownRecentSearchesClearButton.getText().trim()).isEqualTo("Recent Searches");
        } else {
            //Yet to implement
        }
        testLogger.info("PLP Search Recent Search section is displayed" + flag);
        return flag;
    }

    /**
     * This method is for verifying recent search list
     *
     * @param searchKeyList input value for searching
     */
    public void verifyRecentSearchList(String searchKeyList) {
        String[] strSplit = searchKeyList.split(":");
        for (String s : strSplit)
            VerifyElementsList(PLPSearchResultDropdownRecentSearchesList,s);
    }
    /**
     * This method is for click clear button in recent search
     */
    public void clickClearButtonInRecentSearches(){
        if(config.getBrowser().trim().equalsIgnoreCase("CHROME") || config.getBrowser().trim().equalsIgnoreCase("CHROMEHEADLESS") || config.getBrowser().trim().equalsIgnoreCase("MOBILECHROME") ) {
            PLPSearchResultDropdownRecentSearchesClearButton.click();
        }else {
            //Yet to implement
        }
        waitForElementPresent(5);
    }

    /**
     * This method is for verifying UI component
     */
    public void verifyUIComponentInPLP(){
        verifySearchHeaderInPLP();
        verifyShopForSectionInPLP();
        verifySearchFilterSectionInPLP();
    }

    /**
     * This method is for verifying search header
     */
    public void verifySearchHeaderInPLP(){
        SoftAssertions sa = new SoftAssertions();
        sa.assertThat(PLPSearchHeaderText.isDisplayed());
        sa.assertAll();
    }

    /**
     * This method is for verifying shop for section
     */
    public void verifyShopForSectionInPLP(){
        SoftAssertions sa = new SoftAssertions();
        sa.assertThat(PLPSearchForSection.isDisplayed());
        sa.assertThat(PLPSearchForSectionLabel.isDisplayed());
        sa.assertThat(PLPSearchForSectionLabel.getText().trim()).contains(configProperties.getProperty("plp.shopFor.label"));
        sa.assertAll();
    }

    /**
     * This method is for click on Shop for section
     *
     * @param shopForText input value for filtering products
     */
    public void userClickOnShopForSection(String shopForText){
        waitThread(3000);
        scrollUp();
        clickElementsList(PLPSearchForSectionItemList, shopForText);
        waitThread(3000);
    }

    /**
     * This method is for verifying search filter section
     */
    public void verifySearchFilterSectionInPLP(){
        SoftAssertions sa = new SoftAssertions();
        sa.assertThat(PLPFilterSection.isDisplayed());
        sa.assertAll();
    }

    /**
     * This method is for verifying sort by function
     */
    public void verifySortBySectionInPLP(){
        SoftAssertions sa = new SoftAssertions();
        sa.assertThat(PLPSortBySection.isDisplayed());
        sa.assertThat(PLPSortBySectionSearchHits.isDisplayed());
        sa.assertThat(PLPSortBylabel.isDisplayed());
        sa.assertThat(PLPSortByDropdownButton.isDisplayed());
        sa.assertThat(PLPSortBylabel.getText().trim()).contains(configProperties.getProperty("plp.sortBy.label"));
        sa.assertAll();
    }

    /**
     * This method is for verifying listing section
     */
    public void verifyProductListingSectionInPLP(){
        SoftAssertions sa = new SoftAssertions();
        sa.assertThat(PLPSearchProductListSection.isDisplayed());
        sa.assertAll();
    }

    /**
     * This method is for verifying mousehover on first facet in the list
     */
    public void userMouseOverFirstProductInPLP(){
        mouseHover(PLPProductImage,1);
    }

    /**
     * This method is for clicking on first product
     */
    public void userClickOnFirstProduct(){
        mouseHoverClick(PLPProductImage,1);

    }

    /**
     * This method is for verifying success pop up message on adding to basket
     */
    public void addToBasketSuccessPopUpClose(){
        waitThread(3000);//for demo
        PLPAddToBasketSuccessPopUpCloseIcon.click();
        PLPSearchHeaderText.click();
        waitThread(3000);//for demo
    }

    /**
     * This method is for verifying sort by section
     */
    public void verifySortBySection() {
        SoftAssertions sa = new SoftAssertions();
        sa.assertThat(PLPSortBylabel.isDisplayed());
        sa.assertThat(PLPSortBylabel.getText().trim()).isEqualTo("");
        sa.assertAll();
    }

    /**
     * This method is for verifying sortBy dropdown
     */
    public void verifySortByDropDownDefaultValue() {
        SoftAssertions sa = new SoftAssertions();
        sa.assertThat(PLPSortBySection.isDisplayed());
        sa.assertThat(PLPSortByDropdownButton.getText().trim()).contains(configProperties.getProperty("plp.sortBy.value.relevance"));
        sa.assertAll();
    }

    /**
     * This method is for click on sort by dropdown
     */
    public void clickOnSortByDropDown() {
        SoftAssertions sa = new SoftAssertions();
        sa.assertThat(PLPSortByDropdownButton.isDisplayed());
        sa.assertThat(PLPSortBylabel.getText().trim()).contains(configProperties.getProperty("plp.sortBy.label"));
        sa.assertAll();
        mouseHoverClick(PLPSortByDropdownButton);
    }

    /**
     * This method is for verifying all values in dory by dropdown
     */
    public void verifyAllValueInSortByDropDown() {
        String sortByDropDownValue = configProperties.getProperty("plp.sortBy.value.list");
        String[] strSplit = sortByDropDownValue.split(":");
        for (String s : strSplit)
            VerifyElementsList(PLPSortByDropdownList,s);
    }

    /**
     * This method is for click sortby dropdown value by name
     *
     * @param value input value for searching
     */
    public void clicksSortByDropDownByValue(String value) {
        waitThread(5000);
        clickElementsList(PLPSortByDropdownList, value);
        waitThread(5000);
    }

    /**
     * This method is for verifying product display after the sorting by value
     *
     * @param value input value for searching
     */
    public void verifyProductDisplayedAfertSortEN(String value){
        waitForElementPresent(3);
        SoftAssertions sa = new SoftAssertions();
        switch(value) {
            case "New Arrivals":
                sa.assertThat(PLPSortByDropdownList);
                sa.assertThat(PLPSortByDropdownButton.getText().trim()).isEqualTo(configProperties.getProperty("plp.sortBy.value.newArrivals"));
                break;
            case "Discount":
                sa.assertThat(PLPSortByDropdownList);
                sa.assertThat(PLPSortByDropdownButton.getText().trim()).isEqualTo(configProperties.getProperty("plp.sortBy.value.discount"));
                break;
            case "Price - Low to High":
                sa.assertThat(PLPSortByDropdownList);
                sa.assertThat(PLPSortByDropdownButton.getText().trim()).isEqualTo(configProperties.getProperty("plp.sortBy.value.priceLowToHigh"));
                break;
            case "Price - High to Low":
                sa.assertThat(PLPSortByDropdownList);
                sa.assertThat(PLPSortByDropdownButton.getText().trim()).isEqualTo(configProperties.getProperty("plp.sortBy.value.priceHighToLow"));
                break;
            case "Relevance":
                sa.assertThat(PLPSortByDropdownList);
                sa.assertThat(PLPSortByDropdownButton.getText().trim()).isEqualTo(configProperties.getProperty("plp.sortBy.value.relevance"));
                break;
            default:
                sa.assertThat(PLPSortByDropdownList);
        }
        sa.assertAll();
    }

    public void verifyProductDisplayedAfertSortAR(String value){
        waitForElementPresent(3);
        SoftAssertions sa = new SoftAssertions();
        switch(value) {
            case "الأحدث":
                sa.assertThat(PLPSortByDropdownList);
                sa.assertThat(PLPSortByDropdownButton.getText().trim()).isEqualTo(configProperties.getProperty("plp.sortBy.value.newArrivals"));
                break;
            case "الخصم":
                sa.assertThat(PLPSortByDropdownList);
                sa.assertThat(PLPSortByDropdownButton.getText().trim()).isEqualTo(configProperties.getProperty("plp.sortBy.value.discount"));
                break;
            case " - من الأقل للأعلى":
                sa.assertThat(PLPSortByDropdownList);
                sa.assertThat(PLPSortByDropdownButton.getText().trim()).isEqualTo(configProperties.getProperty("plp.sortBy.value.priceLowToHigh"));
                break;
            case "السعر - من الأعلى إلى ":
                sa.assertThat(PLPSortByDropdownList);
                sa.assertThat(PLPSortByDropdownButton.getText().trim()).isEqualTo(configProperties.getProperty("plp.sortBy.value.priceHighToLow"));
                break;
            case " الصلة":
                sa.assertThat(PLPSortByDropdownList);
                sa.assertThat(PLPSortByDropdownButton.getText().trim()).isEqualTo(configProperties.getProperty("plp.sortBy.value.relevance"));
                break;
            default:
                sa.assertThat(PLPSortByDropdownList);
        }
        sa.assertAll();
    }
}