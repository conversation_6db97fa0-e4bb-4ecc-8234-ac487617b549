package com.lmg.blc.web.stepDefinition;

import com.landmarkshops.Web_Mweb.helper.LoggerHelper;
import com.landmarkshops.Web_Mweb.launcher.TestHarness;
import com.landmarkshops.Web_Mweb.utilities.AddressDataBean;
import com.landmarkshops.Web_Mweb.utilities.GiftCardBalanceCheckAPI;
import com.landmarkshops.Web_Mweb.utilities.GiftCardCreation;
import com.landmarkshops.Web_Mweb.utilities.StepContext;
import com.lmg.blc.web.PageObjects.BasketPage;
import com.lmg.blc.web.PageObjects.CheckoutPage;
import com.lmg.blc.web.PageObjects.ProductDetailPage;
import cucumber.api.DataTable;
import cucumber.api.PendingException;
import cucumber.api.java.en.And;
import cucumber.api.java.en.Then;
import io.restassured.response.Response;

import java.util.Map;

import static com.landmarkshops.Web_Mweb.utilities.WebUtils.address;

public class CheckoutStepDefination extends TestHarness {

    CheckoutPage cs;
    BasketPage bs;
    StepContext sc;
    protected static org.apache.log4j.Logger log = LoggerHelper.getLogger(CheckoutPage.class);

    @And("^Verify user landed to checkout page$")
    public void userLandedToCheckoutPage() {
        cs = new CheckoutPage(driver);
        cs.verifyUserLandedToCheckOutPage();
    }

    @And("^Verify product details in checkout page$")
    public void verifyProductDetailsInCheckoutPage() {
        cs = new CheckoutPage(driver);
        cs.verifyProductInCheckoutPage();
    }

    @And("^Verify all UI components in$")
    public void verifyAllUIComponentsIn(DataTable deliveryType) {
        cs = new CheckoutPage(driver);
        bs= new BasketPage(driver);
        address("Address");
        for (Map<String, String> data : deliveryType.asMaps(String.class, String.class)) {
            cs.deliveryTypeMethod(data.get("deliveryType"));
            cs.verifySelectPaymentSection();
            cs.verifyYourOrderSummaryDetails(data.get("deliveryType"));
            bs.verifyPromoAndGiftCardSection();
            cs.verifyCheckoutCardSection();
        }
    }

    @Then("^enter shipping \"([^\"]*)\" details for guest user$")
    public void enterShippingDetailsForGuestUser(String Address) {
        cs = new CheckoutPage(driver);
        address(Address);
//        String fullName= AddressDataBean.getFirstName() +" "+ WebUtils.generateRandomString(15);
        cs.selectYOurEmirate(AddressDataBean.getCity());
        cs.selectedArea(AddressDataBean.getArea());
        cs.enterBuildingName(AddressDataBean.getFloorNo());
        cs.enterStreetNameOrNumber(AddressDataBean.getStreetNo());
       // cs.enterLandmarkOptional(AddressDataBean.getLandmark());
//        cs.enterFullName(AddressDataBean.getFirstName());
//        System.out.println("mobile number is"+AddressDataBean.getMobileNumber());
//        cs.enterMobileNumber(AddressDataBean.getMobileNumber());
//        cs.enterEmailId();
    }

    @Then("^enter shipping \"([^\"]*)\" details for register user$")
    public void enterShippingDetailsForRegisterUser(String Address) {
        cs = new CheckoutPage(driver);
        address(Address);
//        String fullName= AddressDataBean.getFirstName() +" "+ WebUtils.generateRandomString(15);
        cs.selectYOurEmirate(AddressDataBean.getCity());
        cs.selectedArea(AddressDataBean.getArea());
        cs.enterBuildingName(AddressDataBean.getFloorNo());
        cs.enterStreetNameOrNumber(AddressDataBean.getStreetNo());
        cs.enterLandmarkOptional(AddressDataBean.getLandmark());
//        System.out.println("full name is"+fullName);
        cs.enterFullName(AddressDataBean.getFirstName());
        System.out.println("mobile number is"+AddressDataBean.getMobileNumber());
        cs.enterMobileNumber(AddressDataBean.getMobileNumber());
        cs.enterEmailId();
    }

    @Then("^User change address type as \"([^\"]*)\" for guest user$")
    public void userChangeAddressTypeAsForGuestUser(String addressType) throws Throwable {
        cs = new CheckoutPage(driver);
        cs.changeAddressTypeAs(addressType);
    }

    @Then("^User verify all UI components in select payment section$")
    public void userVerifyAllUIComponentsInSelectPaymentSection() {
        cs = new CheckoutPage(driver);
        cs.verifySelectPaymentSection();
    }

    @Then("^enter payment details for guest user$")
    public void enterPaymentDetailsForGuestUser() {
        cs = new CheckoutPage(driver);
        cs.enterCreditCardNumber("****************");
        cs.selectExpiryMonth();
        cs.setSelectExpiryYear();
        cs.enterCVV("123");
        cs.enterNameOnCard("test user");
    }

    @Then("^User enter \"([^\"]*)\" card details$")
    public void userEnterCardDetails(String cardType) throws Throwable {
        cs = new CheckoutPage(driver);
        cs.enterCreditDebitCardNumber(cardType);
    }

    @Then("^User selects only Shukran pay$")
    public void userPayUsingShukranPoints()  {
        cs = new CheckoutPage(driver);
        cs.userPayUsingShukranPoints();
    }

    @Then("^User select MyCredit as payment mode$")
    public void userPayUsingMyCredit()  {
        cs = new CheckoutPage(driver);
        cs.userPayUsingMyCredit();
    }

    @Then("^User enter Gift Card details$")
    public void userEnterGiftCardDetails(DataTable testData)  {
        cs = new CheckoutPage(driver);
        cs.clickGiftCardSectionUseItHereHyperlink();
        Map<String, String> data = testData.asMap(String.class, String.class);
        cs.enterGiftCardNumber(data.get("Gift Card Number"));
        cs.enterGiftCardPIN(data.get("Gift Card Pin"));
        cs.clickGiftCardApplyButton();
    }
//    String gcCardNumber;
//    String pinNumber;
    @Then("^User create \"([^\"]*)\" worth Gift Card and enters gift card number and pin$")
    public void userEnterGiftCardDetails(String amount) {
        cs = new CheckoutPage(driver);
        cs.clickGiftCardSectionUseItHereHyperlink();
        cs.generateGiftCardUsingAPI(amount);
//        Response response = GiftCardCreation.generateGiftCard(amount);
//        gcCardNumber = response.jsonPath().getString("barcodeNumber").trim();
//        pinNumber = response.jsonPath().getString("pinCode").trim();
        cs.enterGiftCardNumber(cs.getLastGiftCardNo());
        cs.enterGiftCardPIN(cs.getLastGiftCardPin());
        cs.clickGiftCardApplyButton();
    }

    @And("^User create total worth Gift Card and enters gift card number and pin$")
    public void userCreateTotalWorthGiftCardAndEntersGiftCardNumberAndPin() {
        cs = new CheckoutPage(driver);
        cs.clickGiftCardSectionUseItHereHyperlink();
        cs.generateGiftCardUsingAPI();
//        Response response = GiftCardCreation.generateGiftCard(amount);
//        gcCardNumber = response.jsonPath().getString("barcodeNumber").trim();
//        pinNumber = response.jsonPath().getString("pinCode").trim();
        cs.enterGiftCardNumber(cs.getLastGiftCardNo());
        cs.enterGiftCardPIN(cs.getLastGiftCardPin());
        cs.clickGiftCardApplyButton();

    }

//    @And("^Check the Gift Card balance$")
//    public void checkTheGiftCardBalance() {
//
//        Response giftCardBalance =  GiftCardBalanceCheckAPI.getGiftCardBalance(gcCardNumber,pinNumber);
//    }

    @And("^click on Pay now for guest user$")
    public void clickOnPaymentNowForGuestUser() {
        cs = new CheckoutPage(driver);
        cs.payNowClick();
    }

    @Then("^User search and select \"([^\"]*)\" city$")
    public void userSearchAndSelectCity(String city) {
        cs = new CheckoutPage(driver);
        cs.enterDataOnClickAndCollectSearchBox(city);
        cs.clickOnClickAndCollectSearchBoxList();
        cs.clickOnClickAndCollectConfirmPickupPointButton();
    }
    public static String saveStoreCityNameSaveRuntime = "";

    @Then("^Search \"([^\"]*)\" and select the address in map$")
    public void searchAndSelectTheAddressInMap(String storeCityName) throws Throwable {
        saveStoreCityNameSaveRuntime = storeCityName;
        cs = new CheckoutPage(driver);
        cs.enterDataOnClickAndCollectSearchBoxFromJson(storeCityName);
        cs.clickOnClickAndCollectSearchBoxList();
        cs.clickOnClickAndCollectConfirmPickupPointButton();
    }

    public static String saveContactRuntime = "";

    @And("^User enter \"([^\"]*)\" details$")
    public void userEnterContactDetails(String Contact) {
        saveContactRuntime = Contact;
        cs = new CheckoutPage(driver);
        address(Contact);
//        String fullName= AddressDataBean.getFirstName() +" "+ WebUtils.generateRandomString(15);
        cs.enterFullNameInClickAndCollectAddYourContactDetails(AddressDataBean.getFirstName());
        cs.enterMobileInClickAndCollectAddYourContactDetails(AddressDataBean.getMobileNumber());
        cs.enterEmailInClickAndCollectAddYourContactDetails();
    }
    @Then("^User click delivery mode as$")
    public void userClickDeliveryModeAs(DataTable deliveryType) {
        cs = new CheckoutPage(driver);
        for (Map<String, String> data : deliveryType.asMaps(String.class, String.class)) {
            cs.deliveryTypeMethodCOD(data.get("deliveryType"));
        }
    }

    @Then("^User choose Cash on Delivery$")
    public void userChooseCashOnDelivery() {
        cs = new CheckoutPage(driver);
        cs.clickOnCashOnDeliveryRadioButton();
    }

    @And("^click on Pay now Button$")
    public void clickOnPayNowButton() {
        cs = new CheckoutPage(driver);
        cs.payNowClick();
    }

    @Then("^Verify Cash on Delivery option disabled$")
    public void verifyCashOnDeliveryOptionDisabled() {
        cs = new CheckoutPage(driver);
        cs.clickandCollectCashOnDelivery();
    }

    @Then("^verify user default my account address same in checkout page$")
    public void verifyUserDefaultMyAccountAddressSameInCheckoutPage() {
        cs = new CheckoutPage(driver);
        cs.verifyDefaultAddressNameFromCheckoutPage();
    }

    @And("^User click on Proceed To Payment$")
    public void userClickOnProceedToPayment() {
        cs = new CheckoutPage(driver);
        cs.selectGdmsSlot();
        cs.clickOnProceedToPayment();
    }

    @And("^Verify error message components in$")
    public void verifyErrorMessageComponentsIn(DataTable deliveryType) {
        cs = new CheckoutPage(driver);
        for (Map<String, String> data : deliveryType.asMaps(String.class, String.class)) {
            cs.deliveryTypeMethodValidateErrorMessage(data.get("deliveryType"));
        }
    }

    @And("^Check the Gift Card balance$")
    public void checkTheGiftCardBalance() {
        //Need to implement dynamic balance check
    }

    @And("^Enter Valid OTP and click on confirm button$")
    public void enterValidOTPAndClickOnConfirmButton() {
        cs = new CheckoutPage(driver);
        cs.enterValidOTPAndClickOnConfirmButton();

    }

}