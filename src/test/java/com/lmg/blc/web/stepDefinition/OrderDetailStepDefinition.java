package com.lmg.blc.web.stepDefinition;

import com.landmarkshops.Web_Mweb.launcher.TestHarness;
import com.lmg.blc.web.PageObjects.OrderDetail;

import cucumber.api.PendingException;
import cucumber.api.java.en.And;
import cucumber.api.java.en.Given;
import cucumber.api.java.en.Then;

public class OrderDetailStepDefinition extends TestHarness {

   OrderDetail od;

   @Then("^Verify user landed to Details page$")
   public void verifyUserLandedToDetailsPage() {
      od = new OrderDetail(driver);
      od.verifyUserLandedToOrderDetailsPage();
   }

   @And("^Verify all UI elements in Order Details page$")
   public void verifyAllUIElementsInOrderDetailsPage() {
      od = new OrderDetail(driver);
      od.verifyAllUIElementsInOrderDetails();
   }

   @And("^Verify order number in Details page$")
   public void verifyOrderNumberInDetailsPage() {
      od = new OrderDetail(driver);
      od.verifyOrderNumberInOrderDetails();
   }

   @Then("^User Click on Product in Details page$")
   public void userClickOnProductInDetailsPage() {
      od = new OrderDetail(driver);
      od.clickOnProductImage();
   }

   @Given("^verify customer landed to purchase details section page$")
   public void verify_customer_landed_to_purchase_details_section_page() throws Throwable {
      od = new OrderDetail(driver);
      od.verifyUserLandedToPurchaseDetailPage();
   }

   @Then("^Verify all UI elements in Purchase Details for Instore Purchases$")
   public void verify_all_UI_elements_in_Purchase_Details_for_Instore_Purchases() throws Throwable {
      od = new OrderDetail(driver);
      od.verifyAllOfflineUIElementsInOrderDetailsPage();
   }

   @And("^user click on view receipt from order details page$")
   public void userClickOnViewReceiptFromOrderDetailsPage() {
      od = new OrderDetail(driver);
      od.clickOnViewReceipt();
   }

   @Then("^verify all UI components in view receipts$")
   public void verifyAllUIComponentsInViewReceipts() {
      od = new OrderDetail(driver);
      od.verifyUIComponentsInReceipts();
   }

   @And("^close receipt popup$")
   public void closeReceiptPopup() {
      od = new OrderDetail(driver);
      od.closeReceiptPopUp();
   }
}
