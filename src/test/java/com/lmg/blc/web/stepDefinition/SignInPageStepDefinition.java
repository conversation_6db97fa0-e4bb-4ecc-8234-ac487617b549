package com.lmg.blc.web.stepDefinition;

import com.landmarkshops.Web_Mweb.helper.LoggerHelper;
import com.landmarkshops.Web_Mweb.launcher.TestHarness;
import com.landmarkshops.Web_Mweb.stepdefinition.SignInStepDef;
import com.landmarkshops.Web_Mweb.utilities.StepContext;
import com.lmg.blc.web.PageObjects.SignInPage;
import cucumber.api.DataTable;
import cucumber.api.PendingException;
import cucumber.api.java.en.And;
import cucumber.api.java.en.Then;

import java.util.Map;

import static com.landmarkshops.Web_Mweb.utilities.WebUtils.signInSignUpFacebookLogin;


public class SignInPageStepDefinition extends TestHarness {

    SignInPage signIn;
    protected static org.apache.log4j.Logger log = LoggerHelper.getLogger(SignInStepDef.class);
    private StepContext contex;

    public SignInPageStepDefinition(
            StepContext context) {
        this.contex = context;
    }


    @And("^customer enter invalid \"([^\"]*)\" and password \"([^\"]*)\"$")
    public void customerEnterInValidAndPassword(String email, String password) throws Throwable {
        signIn = new SignInPage(driver);
        signIn.signInWithValidCredentials(email, password);
    }

    @And("^customer enter valid \"([^\"]*)\" and password \"([^\"]*)\"$")
    public void customerEnterValidAndPassword(String email, String password) throws Throwable {
        signIn = new SignInPage(driver);
        signIn.signInWithValidCredentials(email, password);

    }

    @Then("^User enters valid credential \"([^\"]*)\"$")
    public void userEntersValidCredential(String user) throws Throwable {
        // Write code here that turns the phrase above into concrete actions
        //throw new PendingException();
        signIn = new SignInPage(driver);
        signIn.signInWithValidCredentials(user);
    }

    @Then("^Customer enters valid user id and password$")
    public void enterCustomerUserIdAndPassword(DataTable testData) throws Throwable {
        signIn = new SignInPage(driver);
        Map<String, String> data = testData.asMap(String.class, String.class);
        signIn.signInWithValidCredentials(data.get("email"), data.get("password"));
    }

    @Then("^customer enter valid \"([^\"]*)\" and password as '([^']*)'$")
    public void customerEnterValidAndPasswordAs(String email, String password) throws Throwable {
        signIn = new SignInPage(driver);
        signInSignUpFacebookLogin("EMAIL", email, password);

    }

    @Then("^Verify customer not able to signIn$")
    public void verifyCustomerNotAbleToSignIn() {
        signIn = new SignInPage(driver);
        signIn.verifyInvalidSignInErrorMessage();
    }

    @Then("^verify all UI components in sign in screen$")
    public void verifyAllUIComponentsInSignInScreen() {
        signIn = new SignInPage(driver);
        signIn.verifyUIComponentsInSignInPage();
    }

    @And("^validate error message in signIn screen$")
    public void validateErrorMessageInSignInScreen() {
        signIn = new SignInPage(driver);
        signIn.validateErrorMessagesInSignIn();
    }

    @And("^customer click on forgot password link$")
    public void userClickOnForgotPasswordLink() {
        signIn = new SignInPage(driver);
        signIn.forgotPasswordClick();
    }

    @And("^customer landed to sign in page$")
    public void CustomerLandedToSignInPage() {
        signIn = new SignInPage(driver);
        signIn.clearBrowserCache();
        signIn.navigateToParticularPage("https://uat1emaxme.lmsin.net/auth/login?client_id=E_MAX");
    }

    @Then("^Verify customer sign in with registered email and password$")
    public void verifyCustomerSignInWithRegisteredEmailAndPassword() {
        signIn = new SignInPage(driver);
        signIn.enterEmail(contex.signUpEmail);
        signIn.enterPassword("Test@1234");
        signIn.clickOnSignInButton();
    }

    @Then("^verify customer sign in with facebook label in sign in page$")
    public void verifyCustomerSignInWithFacebookLabelInSignInPage() {
        signIn = new SignInPage(driver);
        signIn.signInWithFaceBook();

    }


    @And("^customer click on sign in with facebook label$")
    public void customerClickOnSignInWithFacebookLabel() {
        signIn = new SignInPage(driver);
        signIn.clickOnSignInWithFaceBook();
    }


    @Then("^User click on '([^']*)' to navigate to sign up page$")
    public void userClickOnToNavigateToSignUpPage(String arg0) throws Throwable {
        signIn = new SignInPage(driver);
        signIn.clickOnSignUpForOne();
    }

    @Then("^user enter valid email in sign in page$")
    public void userEnterValidEmailInSignInPage() {
        signIn = new SignInPage(driver);
        signIn.enterEmail(StepContext.emailIdValidation);
    }

    @And("^user enter valid password in sign in page$")
    public void userEnterValidPasswordInSignInPage() {
        signIn = new SignInPage(driver);
        signIn.enterPassword(StepContext.thankYouPasswordValue);
    }


    @Then("^user click on sign in button in Sign in page$")
    public void userClickOnSignInButtonInSignInPage() {
        signIn = new SignInPage(driver);
        signIn.signInClickButton();

    }


    @Then("^User click on '([^']*)'$")
    public void userClickOn(String checkoutAs) {
        signIn = new SignInPage(driver);
        signIn.clickOnCheckOutAsGuestUser();
    }


    @Then("^user entered sign up completed credentials to verify user able to sign in$")
    public void userEnteredSignUpCompletedCredentialsToVerifyUserAbleToSignIn() {
        signIn = new SignInPage(driver);
        signIn.enterEmail(StepContext.signUpEmail);
        signIn.enterPassword(StepContext.pwdValidation);
        signIn.clickOnSignInButton();

    }

    @Then("^verify user landed to signIn page$")
    public void verifyUserLandedToSignInPage() {
        signIn = new SignInPage(driver);
        signIn.verifyUserLandedSignInPage();
    }

    @And("^enter valid email id to sign in as '([^']*)'$")
    public void enterValidEmailIdToSignInAs(String email) throws Throwable {
        signIn = new SignInPage(driver);
        signIn.enterEmail(email);
    }

    @Then("^enter reset password as '([^']*)'$")
    public void enterResetPasswordAs(String pwd) throws Throwable {
        signIn = new SignInPage(driver);
        signIn.enterPassword(pwd);
    }

    @And("^user click on sign in to login button$")
    public void userClickOnSignInToLoginButton() {
        signIn = new SignInPage(driver);
        signIn.clickOnSignInButton();
    }



}