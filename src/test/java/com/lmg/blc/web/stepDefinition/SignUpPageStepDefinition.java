package com.lmg.blc.web.stepDefinition;

import com.landmarkshops.Web_Mweb.config.config;
import com.landmarkshops.Web_Mweb.helper.LoggerHelper;
import com.landmarkshops.Web_Mweb.launcher.TestHarness;
import com.landmarkshops.Web_Mweb.pageobjects.SignUpScreen;
//import com.landmarkshops.Web_Mweb.utilities.OAuthTokenExample;
import com.landmarkshops.Web_Mweb.utilities.StepContext;
import com.landmarkshops.Web_Mweb.utilities.WebUtils;
import com.lmg.blc.web.PageObjects.SignUpPage;
import cucumber.api.java.en.And;
import cucumber.api.java.en.Then;

import static com.landmarkshops.Web_Mweb.utilities.fetchOTPByAPI.fetchOTPBasedOnMobileNumber;

public class SignUpPageStepDefinition extends TestHarness {

    SignUpPage signUp;
    protected static org.apache.log4j.Logger log = LoggerHelper.getLogger(SignUpScreen.class);
    private StepContext context;
    public SignUpPageStepDefinition(StepContext context){
        this.context = context;
    }

    @Then("^Verify customer landed to sign up screen$")
    public void verifyCustomerLandedToSignUpScreen() {
        signUp =new SignUpPage(driver);
        signUp.verifyUserLandedToSignUpScreen();
    }

    @And("^customer enter valid emailId$")
    public void customerEnterValidEmailId() {
        if(!(signUp instanceof SignUpPage))
            signUp =new SignUpPage(driver);
        context.signUpEmail=signUp.enterEmail();
    }

    @And("^customer enter last Name$")
    public void customerEnterLastName() {
        if(!(signUp instanceof SignUpPage))
            signUp =new SignUpPage(driver);
        signUp.enterLastName("Automation");

    }

    @And("^customer enter valid password$")
    public void customerEnterValidPassword() {
        if(!(signUp instanceof SignUpPage))
            signUp =new SignUpPage(driver);
        StepContext.pwdValidation = "Pass1word!";
        signUp.enterPassword(StepContext.pwdValidation );
    }

    @And("^customer enter confirm password$")
    public void customerEnterConfirmPassword() {
        if(!(signUp instanceof SignUpPage))
            signUp =new SignUpPage(driver);
        signUp.confirmPassword(StepContext.pwdValidation );
    }

    @Then("^click on signUp btn for signUp$")
    public void clickOnSignUpBtnForsignUP() {
        if(!(signUp instanceof SignUpPage))
            signUp =new SignUpPage(driver);
        signUp.clickRegisterButton();

    }

    @And("^customer enter first Name$")
    public void customerEnterFirstName() {
        signUp =new SignUpPage(driver);
        signUp.enterFirstName("max");
    }


    @Then("^customer verify email from mailinator$")
    public void customerVerifyEmailFromMailinator() {
        signUp =new SignUpPage(driver);
        String email = context.signUpEmail.replace("@mailinator.com","");
        String mailinator ="https://www.mailinator.com/v4/public/inboxes.jsp?to=";
        String url = mailinator+email;
        signUp.navigateToMailinatorSite(url);
        signUp.verifyEmailFromMailinator();
        WebUtils.newWindowTabClose(driver);

    }
    String mobile;
    @Then("^enter random phone number$")
    public void enterRandomPhoneNumber() {
        signUp =new SignUpPage(driver);
        int num = WebUtils.generateRandomDigits(6);
         mobile ="77"+ String.valueOf(num);
       // mobile= WebUtils.generateRandomPhoneNumberString();
        //signUp.enterPhoneNumber("1000007001");
        signUp.enterPhoneNumber(mobile);


    }

    @And("^verify all UIComponents in sing up page$")
    public void verifyAllUIComponentsInSingUpPage() {
        signUp =new SignUpPage(driver);
        signUp.verifyUIComponents();
    }

    @Then("^verify required error messages for sign up page$")
    public void verifyRequiredErrorMessagesForSignUpPage() {
        signUp =new SignUpPage(driver);
        signUp.verifyRequiredErrorMessages();
    }

    @And("^Verify invalid error messages in sign up page$")
    public void verifyInvalidErrorMessagesInSignUpPage() {
        signUp =new SignUpPage(driver);
        signUp.verifyRequiredInvalidErrorMessages();
    }

    @Then("^verify show password and confirm show password in sign up page$")
    public void verifyShowPasswordAndConfirmShowPasswordInSignUpPage() {
        signUp =new SignUpPage(driver);
        signUp.showPassword();
    }

    @And("^customer enter full Name$")
    public void userEnterFullName() {
        signUp =new SignUpPage(driver);
        signUp.enterFullName();
    }

    @And("^choose date of birth in sign up$")
    public void chooseDateOfBirthInSignUp() {
        signUp =new SignUpPage(driver);
        signUp.chooseDateOfBirth();
    }

    @And("^validate labels in sing up page$")
    public void validateLabelsInSingUpPage() {
        signUp =new SignUpPage(driver);
        signUp.verifyUIComponents();
    }


    @Then("^validate required error message for sign up page$")
    public void verifyRequiredErrorMessageForSignUpPage() {
        signUp =new SignUpPage(driver);
        signUp.verifyRequiredErrorMessages();
    }

    @And("^validate invalid error message in sign up page$")
    public void verifyInvalidErrorMessageInSignUpPage() {
        signUp =new SignUpPage(driver);
        signUp.verifyRequiredInvalidErrorMessages();
    }

    @Then("^validate show password and confirm show password$")
    public void verifyShowPasswordAndConfirmShowPassword() {
        signUp =new SignUpPage(driver);
        signUp.showPassword();
    }

    @Then("^verify customer sign up with facebook label in sign up page$")
    public void verifyCustomerSignUpWithFacebookLabelInSignUpPage() {
        signUp =new SignUpPage(driver);
        signUp.verifySignUpWithFaceBook();
    }

    @And("^customer click on sign up with facebook label$")
    public void customerClickOnSignUpWithFacebookLabel() {
        signUp =new SignUpPage(driver);
        signUp.clickOnSignUpWithFaceBook();
    }

    @Then("^User clicking on verify button$")
    public void userClickingOnVerifyButton() {
        signUp =new SignUpPage(driver);
        signUp.clickOnVerifyLink();
    }
    String otp;
    //String countryCode = "20";
    String countryCode = "968";
    @And("^Fetch the otp from API call$")
    public void fetchTheOtpFromAPICall() {
        otp = fetchOTPBasedOnMobileNumber( config.getConcept().trim(),config.getCountry(),countryCode+mobile);
    }

    @And("^Enter the OTP in verify your number popup$")
    public void enterOTPValue() {
        signUp =new SignUpPage(driver);
        signUp.enterOTP(otp);
    }

    @And("^Click on Confirm button$")
    public void clickConfirmButtonInVerifyYourNumberPopUp() {
        signUp =new SignUpPage(driver);
        signUp.clickConfirmButtonInVerifyYourNumberPopUp();
    }


}
