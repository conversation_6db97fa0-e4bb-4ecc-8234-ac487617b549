package com.lmg.blc.web.stepDefinition;

import com.landmarkshops.Web_Mweb.launcher.TestHarness;
import com.landmarkshops.Web_Mweb.utilities.AddressBean;
import com.landmarkshops.Web_Mweb.utilities.AddressDataBean;
import com.landmarkshops.Web_Mweb.utilities.StepContext;
import com.landmarkshops.Web_Mweb.utilities.WebUtils;
import com.lmg.blc.web.PageObjects.AddAddressBookPage;
import cucumber.api.DataTable;
import cucumber.api.java.en.And;
import cucumber.api.java.en.Given;
import cucumber.api.java.en.Then;
import org.json.simple.parser.ParseException;

import java.io.IOException;
import java.util.List;

import static com.landmarkshops.Web_Mweb.utilities.WebUtils.address;

public class AddAddressStepDefinition extends TestHarness {

    AddAddressBookPage adb;

    @Then("^user click on add address from add address page$")
    public void userClickOnAddAddressFromAddAddressPage() {
        adb = new AddAddressBookPage(driver);
        adb.userClickOnAddYourAddressNow();
    }

    @And("^verify user landed to add address page$")
    public void verifyUserLandedToAddAddressPage() {
        adb = new AddAddressBookPage(driver);
        if(StepContext.mWeb) {
            adb.verifyUserLandedToAddressBook();
        }
    }

//    @And("^user add new address details in add address page$")
//    public void userAddNewAddressDetailsInAddAddressPage() {
//        adb = new AddAddressBookPage(driver);
//        address("UserDetails1");
//        String fullName = "Automation" + WebUtils.generateRandomString(15);
//        AddressBean.setFullName(fullName);
//        adb.enterFullName(AddressDataBean.getFirstName());
//        adb.enterMobileNumber(AddressDataBean.getMobileNumber());
//        adb.chooseCityFromSelectDropDown(AddressDataBean.getCity());
//        adb.chooseAreaFromSelectDropDown(AddressDataBean.getArea());
//        adb.enterBuildingDetails(AddressDataBean.getFloorNo());
//        //adb.enterStreetName(AddressDataBean.getStreetNo());
//        adb.enterStreetName("Test street name,********");
//        //adb.enterLandMarkDetails(AddressDataBean.getLandmark());
//        //adb.enterLandMarkDetails("landmark");
//        adb.checkOptionAsHome();

    @And("^user add new address details in add address page$")
    public void userAddNewAddressDetailsInAddAddressPage() {
        adb = new AddAddressBookPage(driver);
        address("UserDetails1");
        String fullName = "Automation" + WebUtils.generateRandomString(15);
        AddressBean.setFullName(fullName);
        adb.enterFullName(AddressDataBean.getFirstName());
        adb.enterMobileNumber(AddressDataBean.getMobileNumber());
        adb.chooseCityFromSelectDropDown(AddressDataBean.getCity());
        adb.chooseAreaFromSelectDropDown(AddressDataBean.getArea());
        adb.enterBuildingDetails(AddressDataBean.getFloorNo());
        adb.enterStreetName(AddressDataBean.getStreetNo());
        adb.enterStreetName("Test street name,********");
        //adb.enterLandMarkDetails(AddressDataBean.getLandmark());
        // adb.enterLandMarkDetails("landmark");
         adb.checkOptionAsHome();
         }
         @And("^User add \"([^\"]*)\" details in add address page$")
         public void userAddNewAddressDetailsInAddAddressPage(String userDetails) {
        adb = new AddAddressBookPage(driver);
        address(userDetails);
        String fullName = "Automation" + WebUtils.generateRandomString(15);
        AddressBean.setFullName(fullName);
        adb.enterFullName(AddressDataBean.getFirstName());
        adb.enterMobileNumber(AddressDataBean.getMobileNumber());
        adb.chooseCityFromSelectDropDown(AddressDataBean.getCity());
        adb.chooseAreaFromSelectDropDown(AddressDataBean.getArea());
        adb.enterBuildingDetails(AddressDataBean.getFloorNo());
        adb.enterStreetName(AddressDataBean.getStreetNo());
        adb.enterStreetName("Test street name,********");
        //adb.enterLandMarkDetails(AddressDataBean.getLandmark());
             // adb.enterLandMarkDetails("landmark");
              adb.checkOptionAsHome();

    }

    @Then("^user click on save button for add address$")
    public void userClickOnSaveButtonForAddAddress() {
        adb = new AddAddressBookPage(driver);
        adb.saveClick();
    }

    @And("^Verify saved address details in address list in myAccount$")
    public void verifySavedAddressDetailsInAddressListInMyAccount() {
        adb = new AddAddressBookPage(driver);
        String fullName = AddressDataBean.getFirstName();
        adb.verifySavedAddressDetails(fullName);
    }

    @Then("^click on delete address from address book in Address Page$")
    public void clickOnDeleteAddressFromAddressBookInAddressPage() {
        adb = new AddAddressBookPage(driver);
        adb.deleteAddressBook();
    }

    @And("^verify delete conformation message and validation in Address page$")
    public void verifyDeleteConformationMessageAndValidationInAddressPage() {
        adb = new AddAddressBookPage(driver);
        adb.verifyConformationMessage();
    }

    @Then("^User click on neverMind not to delete address$")
    public void userClickOnNeverMindNotToDeleteAddress() {
        adb = new AddAddressBookPage(driver);
        adb.clickOnNeverMind();
    }

    @And("^verify address not deleted from address book in my account$")
    public void verifyAddressNotDeletedFromAddressBookInMyAccount() {
        adb = new AddAddressBookPage(driver);
        adb.verifyAddressNotDeleted();
    }

    @And("^confirm deletion of address from address book in my account$")
    public void confirmDeletionOfAddressFromAddressBookInMyAccount() {
        adb = new AddAddressBookPage(driver);
        adb.confirmDeleteAddress();
    }

    @Then("^validate added address details in addresses list$")
    public void validateAddedAddressDetailsInAddressesList() {
        adb = new AddAddressBookPage(driver);
        String fullName = AddressBean.getFullName();
        adb.verifyAddressDetailsInList(fullName);
    }

    @And("^update address from address book in my account$")
    public void updateAddressFromAddressBookInMyAccount() throws IOException, ParseException {
        adb = new AddAddressBookPage(driver);
        address("Address1");
        adb.clickOnEditAddressBtn();
        adb.chooseCityFromSelectDropDown(AddressDataBean.getCity());
        adb.chooseAreaFromSelectDropDown(AddressDataBean.getArea());
        adb.clearBuildingDetails();
        adb.enterBuildingDetails(AddressDataBean.getFloorNo());
        adb.clearStreetName();
        adb.enterStreetName(AddressDataBean.getStreetNo());
        adb.clearLandMarkDetails();
        adb.enterLandMarkDetails(AddressDataBean.getLandmark());
        adb.clearFullName();
        adb.enterFullName(AddressDataBean.getFirstName()+"updated");
        adb.clearMobileNumber();
        adb.enterMobileNumber(AddressDataBean.getMobileNumber());
        adb.checkOptionAsOffice();
        adb.saveUpdateAddress();
    }

    @Then("^verify updated address details in address book in my account$")
    public void verifyUpdatedAddressDetailsInAddressBookInMyAccount() {
        adb = new AddAddressBookPage(driver);
//        String fullName = AddressBean.getFullName();
        adb.verifySavedAddressDetails(AddressDataBean.getFirstName());
    }

    @Given("^user click on add address link in Address page$")
    public void userClickOnAddAddressLinkInAddressPage() {
        adb = new AddAddressBookPage(driver);
        adb.addAddressLink();
    }

    @And("^verify number of address added to address book in my account$")
    public void verifyNumberOfAddressAddedToAddressBookInMyAccount() {
        adb = new AddAddressBookPage(driver);
        adb.addressListCount(2);
    }

    @Then("^change address as default one from address list in my account$")
    public void changeAddressAsDefaultOneFromAddressListInMyAccount() {
        adb = new AddAddressBookPage(driver);
        adb.changeAddressAsDefaultAndValidate();
    }

    @Then("^User click on confirm save button in my account address$")
    public void userClickOnConfirmSaveButtonInMyAccountAddress() {
        adb = new AddAddressBookPage(driver);
        adb.saveUpdateAddress();

    }

    @Then("^delete all address from address list from my account addresses$")
    public void deleteAllAddressFromAddressListFromMyAccountAddresses() {
        adb = new AddAddressBookPage(driver);
        adb.deleteAllAddresses();
    }


    @And("^verify error messages for add address form$")
    public void verifyErrorMessagesForAddAddressForm(DataTable addressForm) {
        adb = new AddAddressBookPage(driver);
        List<List<String>> formFieldName = addressForm.raw();
        if (formFieldName.get(0).get(0).equalsIgnoreCase("city")) {
            adb.verifyErrorMessageForAddAddressForm("city");

        }
        adb.verifyFullNameErrorMessages();
        adb.verifyMobileNumberErrorMessages();

        }

    @Then("^User click on save button in my account address book$")
    public void userClickOnSaveButtonInMyAccountAddressBook() {
        adb = new AddAddressBookPage(driver);
        adb.saveClick();
        //ab.verifyAddressDetailsInList();
    }

    @Then("^user click on Address Book link$")
    public void userClickOnAddressBookLink() {
        adb = new AddAddressBookPage(driver);
        adb.clickOnAddressBookLink();
    }
}

