package com.lmg.blc.web.stepDefinition;

import com.landmarkshops.Web_Mweb.launcher.TestHarness;
import com.lmg.blc.web.PageObjects.MyAccountDropDownItems;
import cucumber.api.java.en.And;
import cucumber.api.java.en.Then;

public class MyAccountDropDownStepDefinition extends TestHarness {


    MyAccountDropDownItems  mdit;
    @Then("^customer click on myAccount section from dropdown icon$")
    public void userClickOnMyAccountSectionFromDropdownIcon() {
        mdit = new MyAccountDropDownItems(driver);
        mdit.clickOnMyAccount();
    }


    @Then("^customer click on myList from drop down$")
    public void userClickOnMyListFromDropDown() {
        mdit = new MyAccountDropDownItems(driver);
        mdit.myLists();
    }

    @Then("^customer click on order history item from dropdown icon$")
    public void userClickOnOrderHistoryItemFromDropdownIcon() {
        mdit = new MyAccountDropDownItems(driver);
        mdit.clickOnMyOrders();
    }

    @Then("^customer click on my addresses Item from my account dropdown$")
    public void userClickOnMyAddressesItemFromMyAccountDropdown() {
        mdit = new MyAccountDropDownItems(driver);
        mdit.clickOnMyAddresses();
    }

    @Then("^customer click on saved cards Item from my account dropdown$")
    public void customerClickOnSavedCardsItemFromMyAccountDropdown() {
        mdit = new MyAccountDropDownItems(driver);
        mdit.clickOnSavedCards();
    }


    @Then("^customer click on mycredit from drop down$")
    public void userClickOnMyCreditFromDropDown() {
        mdit = new MyAccountDropDownItems(driver);
        mdit.clickOnMyCredit();
    }

    @Then("^customer click on reviews item from my account dropdown$")
    public void userClickOnReviewsItemFromMyAccountDropdown() {
        mdit = new MyAccountDropDownItems(driver);
        mdit.clickOnReviews();
    }

    @Then("^customer click on my shukran card item from drop down$")
    public void userClickOnMyShukranCardItemFromDropDown() {
        mdit = new MyAccountDropDownItems(driver);
        mdit.clickOnMyShukranCard();
    }

    @Then("^customer click on sign out item from drop down$")
    public void userClickOnMySignOutItemFromDropDown() {
        mdit = new MyAccountDropDownItems(driver);
        mdit.clickOnSignOut();
    }


    @And("^verify all UI components in my account dropdown$")
    public void verifyAllUIComponentsInMyAccountDropdown() {
        mdit = new MyAccountDropDownItems(driver);
        mdit.verifyAllUIComponentsOfMyAccountDropDownItems();
    }

    @And("^customer click on myAccount drop down icon for drop down items$")
    public void customerClickOnMyAccountDropDownIconForDropDownItems() {
        mdit = new MyAccountDropDownItems(driver);
        mdit.clickOnMyAccountDropDownIcon();
    }


}
