package com.lmg.blc.web.stepDefinition;

import org.checkerframework.checker.guieffect.qual.UI;

import com.google.common.base.Verify;
import com.landmarkshops.Web_Mweb.launcher.TestHarness;
import com.lmg.blc.web.PageObjects.OrderHistory;
import cucumber.api.java.en.And;
import cucumber.api.java.en.Then;

public class OrderHistoryStepDefinition extends TestHarness {

    OrderHistory oh;



    @And("^verify customer landed to orders section page$")
    public void verifyCustomerLandedToOrdersSectionPage() {
        oh = new OrderHistory(driver);
        oh.verifyUserLandedToOrderHistorySection();
    }

    @Then("^Verify all UI elements in Order History$")
    public void verifyAllUIElementsInOrderHistory() {
        oh = new OrderHistory(driver);
        oh.verifyAllUIElementsInOrderHistory();
    }

    @Then("^Verify User able to check past orders$")
    public void verifyUserAbleToCheckPastOrders() {
        oh = new OrderHistory(driver);
        oh.verifyPastOrders();
    }

    @And("^User click on My Account$")
    public void userClickOnMyAccount() {
        oh = new OrderHistory(driver);
        oh.clickOnOrderMyAccountHeader();
    }

    @Then("^User Click on Order Id in Order History page$")
    public void userClickOnOrderIdInOrderHistoryPage() {
        oh = new OrderHistory(driver);
        oh.clickOnOrderId();
    }

    @Then("^User Able to check \"([^\"]*)\" past orders$")
    public void userAbleToCheckPastOrders(String pastOrders){
        oh = new OrderHistory(driver);
        oh.verifyPastOrders(pastOrders);
    }

    @Then("^customer click on instore tab$")
    public void userClickOnInstoreTab() throws InterruptedException {
        oh = new OrderHistory(driver);
        oh.userClickOnInstoreTab();
    }

    @Then("^Verify all UI elements in Purchases instore Tab$")
    public void verifyAllUIElementsInPurchases() throws InterruptedException {
        oh = new OrderHistory(driver);
        oh.verifyAllUIElementsInPurchases();
    }

    @Then("^Customer click on viewDetails Button in instore purchases$")
    public void userClickOnviewDetails() throws InterruptedException {
        oh = new OrderHistory(driver);
        oh.userClickOnViewDetails();
    }


}
