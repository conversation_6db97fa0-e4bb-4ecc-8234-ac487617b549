package com.lmg.blc.web.stepDefinition;

import com.landmarkshops.Web_Mweb.launcher.TestHarness;
import com.lmg.blc.web.PageObjects.MyAccountDropDownItems;
import com.lmg.blc.web.PageObjects.MyAccountPage;

import cucumber.api.PendingException;
import cucumber.api.java.en.And;
import cucumber.api.java.en.Then;

public class MyAccountPageStepDefinitions extends TestHarness {

   MyAccountPage map;
   MyAccountDropDownItems mdi;

   @Then("^verify customer landed to myAccount page$")
   public void verifyCustomerLandedToMyAccountPage() {
      map = new MyAccountPage(driver);
      map.verifyUserLandedToMyAccountPage();
   }

   @And("^verify all ui components in myAccount page$")
   public void verifyAllUiComponentsInMyAccountPage() {
      map = new MyAccountPage(driver);
      map.verifyUIComponentsInMyAccountPage();
   }

   @Then("^user click on myAccount section from dropdown icon$")
   public void userClickOnMyAccountSectionFromDropdownIcon() {
      mdi = new MyAccountDropDownItems(driver);
      mdi.clickOnMyAccount();
   }

   @Then("^customer click on reviews section from my account dropdown$")
   public void customerClickOnReviewsSectionFromMyAccountDropdown() {
      mdi = new MyAccountDropDownItems(driver);
      mdi.clickOnReviews();
   }

   @Then("^customer click on add address section my account$")
   public void customerClickOnAddAddressSectionMyAccount() {
      map = new MyAccountPage(driver);
      map.myAddressesSectionClick();
   }

   @Then("^customer click on profile section in my account$")
   public void customerClickOnProfileSectionInMyAccount() {
      map = new MyAccountPage(driver);
      map.profileSectionClick();
   }

   @Then("^customer click on orders section in my account$")
   public void customerClickOnOrdersSectionInMyAccount() {
      map = new MyAccountPage(driver);
      map.orderHistorySectionClick();
   }

   @Then("^customer click on payment section in my account$")
   public void customerClickOnPaymentSectionInMyAccount() {
      map = new MyAccountPage(driver);
      map.paymentSectionClick();
   }

   @Then("^customer click on myList section in my account$")
   public void customerClickOnMyListSectionInMyAccount() {
      map = new MyAccountPage(driver);
      map.myListsSectionClick();
   }

   @Then("^customer click on reviews section in my account$")
   public void customerClickOnReviewsSectionInMyAccount() {
      map = new MyAccountPage(driver);
      map.reviewsSectionClick();
   }

   @And("^customer click on myAccount drop icon for drop down items$")
   public void customerClickOnMyAccountDropIconForDropDownItems() {

      map = new MyAccountPage(driver);

   }

   @Then("^customer click on my shukran card section in my account$")
   public void customerClickOnMyShukranCardSectionInMyAccount() {
      map = new MyAccountPage(driver);
      map.clickOnMyShukranCard();
   }

   @And("^customer navigate to previous page$")
   public void customerNavigateToPreviousPage() {
      map = new MyAccountPage(driver);
      map.navigateBack();
   }

   @Then("^customer click on my credit section from my account page$")
   public void userClickOnMyCreditSectionFromMyAccountPage() {
      map = new MyAccountPage(driver);
      map.myCreditSectionClick();

   }


   @Then("^customer click on Purchases section in my account$")
   public void customer_click_on_Purchases_section_in_my_account(){
      map = new MyAccountPage(driver);
      map.purchasesSectionClick();
   }
    @Then("^customer click on myAccount top item$")
    public void customerClickOnMyAccountTopItem() {
        map = new MyAccountPage(driver);
        map.clickOnMyAccountToPNavigation();
    }

    @And("^user click on saved card from myAccount page$")
    public void userClickOnSavedCardFromMyAccountPage() {
        map = new MyAccountPage(driver);
        map.clickOnSavedCardLinkFromMyAccountPage();
    }
}
