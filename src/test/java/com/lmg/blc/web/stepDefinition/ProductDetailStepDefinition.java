package com.lmg.blc.web.stepDefinition;

import com.landmarkshops.Web_Mweb.launcher.TestHarness;
import com.landmarkshops.Web_Mweb.utilities.StepContext;
import com.lmg.blc.web.PageObjects.ProductDetailPage;
import cucumber.api.DataTable;
import cucumber.api.PendingException;
import cucumber.api.java.en.And;
import cucumber.api.java.en.Then;
import cucumber.api.java.en.When;

import java.util.Map;

public class ProductDetailStepDefinition extends TestHarness {

    ProductDetailPage pdp;

    @When("^User navigate to product detail page$")
    public void userNavigateToProductDetailsPage() {
        pdp = new  ProductDetailPage(driver);
        pdp.userNavigateToPDP();

    }

    @When("^User navigate to variant product detail page$")
    public void userNavigateToVariantProductDetailsPage() {
        pdp = new  ProductDetailPage(driver);
        pdp.userNavigateToVariantPDP();

    }

    @Then("^Verify user navigate to PDP page$")
    public void verifyUserNavigateToPDP() {
        pdp = new  ProductDetailPage(driver);
        pdp.verifyUserNavigateToPDP();

    }

    @And("^User navigates to product detail page$")
    public void userNavigatesToProductDetailsPage() {
        pdp = new  ProductDetailPage(driver);
        pdp.userNavigateToPDP();
    }

    @Then("^Verify UI components in Product Details page$")
    public void verifyUiComponentsInProductDetail() {
        pdp = new ProductDetailPage(driver);
        pdp.verifyProductTitle();
        pdp.verifyProductImageSection();
        pdp.verifyProductPrice();
        pdp.verifyProductFreeShipping();
        pdp.verifyProductsShukran();
        pdp.verifyProductColor();
        pdp.verifyProductSize();
    }

    @And("^Verify product title$")
    public void verifyProductTitle() throws Throwable {
        pdp = new ProductDetailPage(driver);
        pdp.verifyProductTitle();
    }

    @And("^Verify product title as \"([^\"]*)\"$")
    public void verifyProductTitle(String title) throws Throwable {
        pdp = new ProductDetailPage(driver);
        pdp.verifyProductTitle(title);
    }

    @And("^Verify product image$")
    public void verifyProductImage() throws Throwable {
        pdp = new ProductDetailPage(driver);
        pdp.verifyProductImageSection();
    }

    @And("^Verify product price section$")
    public void verifyProductPrice() throws Throwable {
        pdp = new ProductDetailPage(driver);
        pdp.verifyProductPrice();
    }

    @And("^Verify product price as \"([^\"]*)\"$")
    public void verifyProductPrice(String price) throws Throwable {
        pdp = new ProductDetailPage(driver);
        pdp.verifyProductPrice(price);
    }

    @And("^Verify product free shipping section$")
    public void verifyProductFreeShipping() throws Throwable {
        pdp = new ProductDetailPage(driver);
        pdp.verifyProductFreeShipping();
    }

    @And("^Verify products shukran section$")
    public void verifyProductsShukran() throws Throwable {
        pdp = new ProductDetailPage(driver);
        pdp.verifyProductsShukran();
    }

    @And("^Verify product color$")
    public void verifyProductColor() throws Throwable {
        pdp = new ProductDetailPage(driver);
        pdp.verifyProductColor();
    }

    @And("^Verify product color as \"([^\"]*)\"$")
    public void verifyProductColor(String color) throws Throwable {
        pdp = new ProductDetailPage(driver);
        pdp.verifyProductColor(color);
    }

    @And("^Click on color$")
    public void clickOnColor(DataTable color) {
        pdp = new ProductDetailPage(driver);
        for (Map<String, String> data : color.asMaps(String.class, String.class)) {
            pdp.clickColorByName(data.get("color"));
        }
    }

    @And("^Verify product size$")
    public void verifyProductSize() throws Throwable {
        pdp = new ProductDetailPage(driver);
        pdp.verifyProductSize();
    }

    @And("^Verify product size as \"([^\"]*)\"$")
    public void verifyProductSize(String size) throws Throwable {
        pdp = new ProductDetailPage(driver);
        pdp.verifyProductSize(size);
    }

    @And("^Click on size$")
    public void clickOnSizeByName(DataTable size) {
        pdp = new ProductDetailPage(driver);
        for (Map<String, String> data : size.asMaps(String.class, String.class)) {
            pdp.clickSizeByName(data.get("size"));
        }
    }

    @Then("^Verify Recipient Details section$")
    public void verifyRecipientDetailsSection() throws Throwable {
        pdp = new ProductDetailPage(driver);
        pdp.verifyRecipientDetailsSection();
    }

    @And("^Enter details in Recipient Details section$")
    public void enterDetailsInRecipientDetailsSection(DataTable testData) throws Throwable {
        pdp = new ProductDetailPage(driver);
        Map<String, String> data = testData.asMap(String.class, String.class);
        pdp.enterFullNameInRecipientDetailsSection(data.get("fullName"));
        pdp.enterEmailInRecipientDetailsSection(data.get("email"));
        pdp.enterWriteNoteInRecipientDetailsSection(data.get("writeNote"));
    }

    @And("^User click on Add to Basket button$")
    public void clickOnAddToBasketButton() {
        pdp = new ProductDetailPage(driver);
        pdp.userClickOnAddToBasket();
    }

    @Then("^User click on basket icon")
    public void clickOnBasket() {
        pdp = new ProductDetailPage(driver);
        pdp.clickMiniBasket();
    }

    @And("^User verify Add To List section")
    public void verifyAddToListSection() {
        pdp = new ProductDetailPage(driver);
        pdp.verifyAddToListSection();
    }

    @Then("^User click on Add To List")
    public void clickOnAddToList() {
        pdp = new ProductDetailPage(driver);
        pdp.clickAddToListButton();
    }

    @Then("^User add product to list")
    public void addProductsToList(DataTable ListName) {
        pdp = new ProductDetailPage(driver);
        for (Map<String, String> data : ListName.asMaps(String.class, String.class)) {
            pdp.addProductToList(data.get("ListName"));
        }
    }

    @Then("^User click on Done button in Add To List")
    public void clickOnDoneButtonInAddToList() {
        pdp = new ProductDetailPage(driver);
        pdp.clickDoneButtonInAddToList();

    }


    @When("^User Navigate to product detail page as$")
    public void userNavigateToProductDetailPageAs(DataTable productType) {
        pdp = new ProductDetailPage(driver);
        for (Map<String, String> data : productType.asMaps(String.class, String.class)) {
            pdp.productType(data.get("productType"));
        }
    }

    @When("^User navigate to \"([^\"]*)\" page, select Color and Size$")
    public void userNavigateToPage(String product) throws Throwable {
        pdp = new ProductDetailPage(driver);
        pdp.productType(product);
    }

    @And("^choose product colour as \"([^\"]*)\" and size as \"([^\"]*)\"$")
    public void chooseProductColourAsAndSizeAs(String arg0, String arg1) throws Throwable {
        pdp = new ProductDetailPage(driver);
    }

    @And("^User update quantity as \"([^\"]*)\"$")
    public void userUpdateQuantityAs(String qty) throws Throwable {
        pdp = new ProductDetailPage(driver);
        pdp.updateQuantity(qty);
        if(StepContext.mWeb){
            pdp.mwebUpdateQuantity();
        }
    }


}