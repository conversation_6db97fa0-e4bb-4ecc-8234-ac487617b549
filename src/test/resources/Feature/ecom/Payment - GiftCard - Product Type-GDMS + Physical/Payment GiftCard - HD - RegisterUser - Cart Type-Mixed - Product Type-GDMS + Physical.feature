@Payment-GiftCard
@HomeCentreEgypt1 @HomeCentreOman
Feature: Payment GiftCard - HD - RegisterUser - Cart Type->Mixed - Product Type->GDMS + Physical
  Verify E2E functional flows for the below scenario
  User 			- RegisterUser
  Cart Type		- Mixed
  Product Type	- GDMS + Physical
  Shipping Charges- Free
  Order Type 	- HD
  Payment Mode	- GiftCard

  Applicable Concept
  Egypt_MAX			- No
  Egypt_HomeCentre 	- Yes
  Oman_MAX			- No
  Oman_HomeCentre 	- Yes

  @RegisterUser-HD-GiftCard @ProductType-GDMS+Physical @ShippingCharge-Free
  Scenario: Verify Payment GiftCard - HD - RegisterUser - Cart Type->Mixed - Product Type->GDMS + Physical - Shipping Charges->Free
    Given User landed to home page
    When User Navigate to product detail page as
      | productType  |
      | gdmsproduct3 |
    And User update quantity as "1"
    Then User click on Add to Basket button
    When User Navigate to product detail page as
      | productType |
      | product2    |
    And User update quantity as "2"
    Then User click on Add to Basket button
    When User Navigate to product detail page as
      | productType |
      | product3    |
    And User update quantity as "3"
    Then User click on Add to Basket button
    Then User click on login link and navigate to login page
    Then User enters valid credential "TestUser1"
    Then User landed to registered home page
    Then User click on basket icon
    And  Verify user landed to cart page
    Then User click on checkout
    And Verify user landed to checkout page
    And User click on Proceed To Payment
    #And User create "1000" worth Gift Card and enters gift card number and pin
    And User create total worth Gift Card and enters gift card number and pin
    And click on Pay now Button
    And Check the Gift Card balance
    Then User navigate to thankyou page
    And user clear cache and launch browser home page
