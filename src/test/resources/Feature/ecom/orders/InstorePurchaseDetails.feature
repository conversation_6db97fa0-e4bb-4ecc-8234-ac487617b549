@MyAccountOrderHistory @RegressionSuite @Orders @InstoreDetails
Feature: Instore Purchase Details
   As a logged in shukran user, I can able view all details of My offline Purchases

   Scenario Outline: Verify placed order details in Purchases from my account
      Given User landed to home page
      When  User click on login link and navigate to login page
      Then customer enter valid "<email>" and password "<password>"
      Then User landed to registered home page
      And  User click on myAccount drop icon
      Then user click on myAccount section from dropdown icon
       Then verify customer landed to myAccount page
      Then customer click on Purchases section in my account
      And customer click on instore tab
      And Customer click on viewDetails Button in instore purchases

      @Auth
      Examples:
         | email                                 | password |
         | <EMAIL> | Ram@1234 |

 Scenario: Verify all UI elements in Instore Purchase Details section
       And verify customer landed to purchase details section page
      Then Verify all UI elements in Purchase Details for Instore Purchases


  Scenario: Verify user Should be click view receipts and verify all UI components
    And user click on view receipt from order details page
    Then verify all UI components in view receipts
    And close receipt popup