@MyAccountOrderDetails @RegressionSuite
Feature: order details page
  As a logged in user, I can able view order details and cancel in order details page

  Scenario Outline: Verify placed order details in Order Details from my account
    Given user relaunch browser page
    When  User click on login link and navigate to login page
    Then customer enter valid "<email>" and password "<password>"
    Then User landed to registered home page
    And  User click on myAccount drop icon
    Then user click on myAccount section from dropdown icon
    Then verify customer landed to myAccount page
    Then customer click on orders section in my account
    Then User Click on Order Id in Order History page



    @Auth
    Examples:
      | email                        | password     |
      | <EMAIL> | Wtpmjgda@689 |

  Scenario: Verify all UI elements in Order Details page in Order History
    Then Verify user landed to Details page
    And Verify all UI elements in Order Details page



  Scenario: sign out from my account dropdown
    And customer click on myAccount drop down icon for drop down items
    And customer click on sign out item from drop down

  Scenario: clear cache and land to browser home page
    And user clear cache and launch browser home page