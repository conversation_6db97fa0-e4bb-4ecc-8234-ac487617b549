@MyAccountOrder<PERSON><PERSON>ory @RegressionSuite @Order @Instore
Feature: Instore Purchases
   As a logged in user, I can able view all placed orders


   Scenario Outline: Verify placed order details in Purchases from my account
      Given user relaunch browser page
      When  User click on login link and navigate to login page
      Then customer enter valid "<email>" and password "<password>"
      Then User landed to registered home page
      And  User click on myAccount drop icon
      Then user click on myAccount section from dropdown icon
      Then verify customer landed to myAccount page
      Then customer click on Purchases section in my account
      And customer click on instore tab
      Then Verify all UI elements in Purchases instore Tab
      And customer click on myAccount drop down icon for drop down items
      And customer click on sign out item from drop down
      And user clear cache and launch browser home page


      @Auth
      Examples:
         | email                                 | password     |
         | <EMAIL> | Ram@1234     |
         | <EMAIL>               | Password@123 |
         | <EMAIL>               | Test@1234    |


   # Scenario: Verify all UI elements in Instore Purchases section
   #    #  And verify customer landed to orders section page
   #    And customer click on instore tab
   #    Then Verify all UI elements in Purchases instore Tab

   # Scenario: sign out from my account dropdown
   #    And customer click on myAccount drop down icon for drop down items
   #    And customer click on sign out item from drop down

   # Scenario: clear cache and land to browser home page
   #    And user clear cache and launch browser home page