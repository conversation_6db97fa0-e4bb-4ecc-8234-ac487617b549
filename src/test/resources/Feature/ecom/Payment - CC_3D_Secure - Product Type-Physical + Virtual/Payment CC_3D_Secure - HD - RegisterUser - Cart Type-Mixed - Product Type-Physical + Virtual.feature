@Payment-CC_3D_Secure
@MaxEgypt @HomeCentreEgypt
@MaxOman @HomeCentreOman @CentrePointstoresOman
Feature: Payment CC_3D_Secure - HD - RegisterUser - Cart Type->Mixed - Product Type->Physical + Virtual
  Verify E2E functional flows for the below scenario
  User 			- RegisterUser
  Cart Type		- Mixed
  Product Type	- Physical + Virtual
  Shipping Charges- Yes/Free
  Order Type 	- HD
  Payment Mode	- CC_3D_Secure

  Applicable Concept
  Egypt_MAX			- Yes
  Egypt_HomeCentre 	- Yes
  Oman_MAX			- Yes
  Oman_HomeCentre 	- Yes
  Oman_Centrepoint  - Yes

  @RegisterUser-HD-CC_3D_Secure @ProductType-Physical+Virtual @ShippingCharge-Free @runnow106 @needtodebug
  Scenario: Verify Payment CC_3D_Secure - HD - RegisterUser - Cart Type->Mixed - Product Type->Physical + Virtual - Shipping Charges->Free
    Given User landed to home page
    When User Navigate to product detail page as
      | productType |
      | product1    |
    And User update quantity as "1"
    Then User click on Add to Basket button
    When User Navigate to product detail page as
      | productType |
      | giftCard3   |
    #And User update quantity as "1"
    And Enter details in Recipient Details section
      | fullName | gokul |
      | email | <EMAIL> |
      | writeNote | Test Automation Card |
    Then User click on Add to Basket button
    When User Navigate to product detail page as
      | productType |
      | product2    |
    And User update quantity as "2"
    Then User click on Add to Basket button
    When User Navigate to product detail page as
      | productType |
      | product3    |
    And User update quantity as "3"
    Then User click on Add to Basket button
    Then User click on login link and navigate to login page
    Then User enters valid credential "TestUser1"
    Then User landed to registered home page
    Then User click on basket icon
    And  Verify user landed to cart page
    Then User click on checkout
    And Verify user landed to checkout page
    And User click on Proceed To Payment
    Then User enter "3-DSecureVisa" card details
    And click on Pay now Button
    And Enter Valid OTP and click on confirm button
    Then User navigate to thankyou page
    And customer click on myAccount drop down icon for drop down items
    And customer click on sign out item from drop down
    And user clear cache and launch browser home page

  @RegisterUser-HD-CC_3D_Secure @ProductType-Physical+Virtual @ShippingCharge-Yes
  Scenario: Verify Payment CC_3D_Secure - HD - RegisterUser - Cart Type->Mixed - Product Type->Physical + Virtual - Shipping Charges->Yes
    Given User landed to home page
    When User Navigate to product detail page as
      | productType |
      | product1    |
    And User update quantity as "1"
    Then User click on Add to Basket button
    When User Navigate to product detail page as
      | productType |
      | giftCard1   |
    #And User update quantity as "1"
    And Enter details in Recipient Details section
      | fullName | gokul |
      | email | <EMAIL> |
      | writeNote | Test Automation Card |
    Then User click on Add to Basket button
    Then User click on login link and navigate to login page
    Then User enters valid credential "TestUser1"
    Then User landed to registered home page
    Then User click on basket icon
    And  Verify user landed to cart page
    Then User click on checkout
    And Verify user landed to checkout page
    And User click on Proceed To Payment
    Then User enter "3-DSecureVisa" card details
    And click on Pay now Button
    And Enter Valid OTP and click on confirm button
    Then User navigate to thankyou page
    And customer click on myAccount drop down icon for drop down items
    And customer click on sign out item from drop down
    And user clear cache and launch browser home page
