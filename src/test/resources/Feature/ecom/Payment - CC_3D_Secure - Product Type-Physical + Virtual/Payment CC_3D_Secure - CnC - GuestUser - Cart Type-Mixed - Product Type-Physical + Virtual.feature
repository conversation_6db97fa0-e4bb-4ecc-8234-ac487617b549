@MwebTesting
@Payment-CC_3D_Secure
@MaxEgypt @HomeCentreEgypt
@MaxOman @HomeCentreOman @CentrePointstoresOman @errorFix1 @testNow123
Feature: Payment CC_3D_Secure - CnC - GuestUser - Cart Type->Mixed - Product Type->Physical + Virtual
  Verify E2E functional flows for the below scenario
  User 			- GuestUser
  Cart Type		- Mixed
  Product Type	- Physical + Virtual
  Shipping Charges- Yes/Free
  Order Type 	- CnC
  Payment Mode	- CC_3D_Secure

  Applicable Concept
  Egypt_MAX			- Yes
  Egypt_HomeCentre 	- Yes
  Oman_MAX			- Yes
  Oman_HomeCentre 	- Yes
  Oman_Centrepoint  - Yes

  @GuestUser-CnC-CC_3D_Secure @ProductType-Physical+Virtual @ShippingCharge-Free
  Scenario: Verify Payment CC_3D_Secure - CnC - GuestUser - Cart Type->Mixed - Product Type->Physical + Virtual - Shipping Charges->Free
    Given User landed to home page
    When User Navigate to product detail page as
      | productType |
      | product1 |
    And User update quantity as "2"
    Then User click on Add to Basket button
    When User Navigate to product detail page as
      | productType |
      | giftCard1 |
    #And User update quantity as "1"
    And Enter details in Recipient Details section
      | fullName | gokul |
      | email | <EMAIL> |
      | writeNote | Test Automation Card |
    Then User click on Add to Basket button
    Then User click on basket icon
    And  Verify user landed to cart page
    Then User click on checkout
    Then User click on "Checkout As A Guest"
    And Verify user landed to checkout page
    And User click delivery mode as
      | deliveryType    |
      | click & Collect |
    #Then User search and select "cairo" city
    Then Search "store1" and select the address in map
    And User enter "contact" details
    And User click on Proceed To Payment
    Then User enter "3-DSecureVisa" card details
    And click on Pay now Button
    And Enter Valid OTP and click on confirm button
    Then User navigate to thankyou page
    And user clear cache and launch browser home page

  @GuestUser-CnC-CC_3D_Secure @ProductType-Physical+Virtual @ShippingCharge-Yes @FailReRun @DoThExecutionNow
  Scenario: Verify Payment CC_3D_Secure - CnC - GuestUser - Cart Type->Mixed - Product Type->Physical + Virtual - Shipping Charges->Yes
    Given User landed to home page
    When User Navigate to product detail page as
      | productType |
      | product1 |
    And User update quantity as "1"
    Then User click on Add to Basket button
    When User Navigate to product detail page as
      | productType |
      | giftCard1 |
    #And User update quantity as "1"
    And Enter details in Recipient Details section
      | fullName | gokul |
      | email | <EMAIL> |
      | writeNote | Test Automation Card |
    Then User click on Add to Basket button
    Then User click on basket icon
    And  Verify user landed to cart page
    Then User click on checkout
    Then User click on "Checkout As A Guest"
    And Verify user landed to checkout page
    And User click delivery mode as
      | deliveryType    |
      | click & Collect |
    #Then User search and select "cairo" city
    Then Search "store1" and select the address in map
    And User enter "contact" details
    And User click on Proceed To Payment
    Then User enter "3-DSecureVisa" card details
    And click on Pay now Button
    And Enter Valid OTP and click on confirm button
    Then User navigate to thankyou page
    And user clear cache and launch browser home page
