@Payment-CC
@MaxEgypt @HomeCentreEgypt @HomeCentreOman
Feature: Payment CC - HD - GuestUser - Cart Type->Mixed - Product Type->Physical + Virtual
  Verify E2E functional flows for the below scenario
  User 			- GuestUser
  Cart Type		- Mixed
  Product Type	- Physical + Virtual
  Shipping Charges- Yes/Free
  Order Type 	- HD
  Payment Mode	- CC

  Applicable Concept
  Egypt_MAX			- Yes
  Egypt_HomeCentre 	- Yes
  Oman_MAX			- No
  Oman_HomeCentre 	- No
  Oman_Centrepoint  - No

  @GuestUser-HD-CC @ProductType-Physical+Virtual @ShippingCharge-Free
  Scenario: Verify Payment CC - HD - GuestUser - Cart Type->Mixed - Product Type->Physical + Virtual - Shipping Charges->Free
    Given User landed to home page
    When User Navigate to product detail page as
      | productType |
      | product2    |
    And User update quantity as "1"
    Then User click on Add to Basket button
    When User Navigate to product detail page as
      | productType |
      | product3    |
    And User update quantity as "3"
    Then User click on Add to Basket button
    When User Navigate to product detail page as
      | productType |
      | giftCard1   |
    And Enter details in Recipient Details section
      | fullName | gokul |
      | email | <EMAIL> |
      | writeNote | Test Automation Card |
    #And User update quantity as "4"
    Then User click on Add to Basket button
    Then User click on basket icon
    And  Verify user landed to cart page
    Then User click on checkout
    Then User click on "Checkout As A Guest"
    And Verify user landed to checkout page
    And Verify product details in checkout page
#    And User click delivery mode as
#      | deliveryType  |
#      | Home Delivery |
    Then enter shipping "Address" details for guest user
    And User enter "contact" details
    Then User change address type as "Home" for guest user
    And User click on Proceed To Payment
    Then User enter "visa" card details
    And click on Pay now Button
    Then User navigate to thankyou page
    And user clear cache and launch browser home page

  @GuestUser-HD-CC @ProductType-Physical+Virtual @ShippingCharge-Yes
  Scenario: Verify Payment CC - HD - GuestUser - Cart Type->Mixed - Product Type->Physical + Virtual - Shipping Charges->Yes
    Given User landed to home page
    When User Navigate to product detail page as
      | productType |
      | product1    |
    And User update quantity as "1"
    Then User click on Add to Basket button
    When User Navigate to product detail page as
      | productType |
      | giftCard1   |
    #And User update quantity as "1"
    And Enter details in Recipient Details section
      | fullName | gokul |
      | email | <EMAIL> |
      | writeNote | Test Automation Card |
    Then User click on Add to Basket button
    Then User click on basket icon
    And  Verify user landed to cart page
    Then User click on checkout
    Then User click on "Checkout As A Guest"
    And Verify user landed to checkout page
    And Verify product details in checkout page
#    And User click delivery mode as
#      | deliveryType  |
#      | Home Delivery |
    Then enter shipping "Address" details for guest user
    And User enter "contact" details
    Then User change address type as "Home" for guest user
    And User click on Proceed To Payment
    Then User enter "visa" card details
    And click on Pay now Button
    Then User navigate to thankyou page
    And user clear cache and launch browser home page