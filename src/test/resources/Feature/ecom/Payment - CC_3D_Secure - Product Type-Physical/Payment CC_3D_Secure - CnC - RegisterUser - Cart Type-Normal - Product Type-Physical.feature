@Payment-CC_3D_Secure
@MaxEgypt @HomeCentreEgypt
@MaxOman @HomeCentreOman @CentrePointstoresOman
Feature: Payment CC_3D_Secure - CnC - RegisterUser - Cart Type->Normal - Product Type->Physical
  Verify E2E functional flows for the below scenario
  User 			- RegisterUser
  Cart Type		- Normal
  Product Type	- Physical
  Shipping Charges- Yes/Free
  Order Type 	- CnC
  Payment Mode	- CC_3D_Secure

  Applicable Concept
  Egypt_MAX			- Yes
  Egypt_HomeCentre 	- Yes
  Oman_MAX			- Yes
  Oman_HomeCentre 	- Yes
  Oman_Centrepoint  - Yes

  @RegisterUser-CnC-CC_3D_Secure @ProductType-Physical @ShippingCharge-Free @DoThExecutionNow
  Scenario: Verify Payment CC_3D_Secure - CnC - RegisterUser - Cart Type->Normal - Product Type->Physical - Shipping Charges->Free
    Given User landed to home page
    When User Navigate to product detail page as
      | productType |
      | product1    |
    And User update quantity as "2"
    Then User click on Add to Basket button
    When User Navigate to product detail page as
      | productType |
      | product2    |
    And User update quantity as "2"
    Then User click on Add to Basket button
    When User Navigate to product detail page as
      | productType |
      | product3    |
    And User update quantity as "1"
    Then User click on Add to Basket button
    Then User click on login link and navigate to login page
    Then User enters valid credential "TestUser1"
    Then User landed to registered home page
    Then User click on basket icon
    And  Verify user landed to cart page
    Then User click on checkout
    And User click delivery mode as
      | deliveryType    |
      | click & Collect |
    Then Search "store1" and select the address in map
    And User enter "contact" details
    And User click on Proceed To Payment
    Then User enter "3-DSecureVisa" card details
    And click on Pay now Button
    And Enter Valid OTP and click on confirm button
    Then User navigate to thankyou page
    And customer click on myAccount drop down icon for drop down items
    And customer click on sign out item from drop down
    And user clear cache and launch browser home page

  @RegisterUser-CnC-CC_3D_Secure @ProductType-Physical @ShippingCharge-Yes
  Scenario: Verify Payment CC_3D_Secure - CnC - RegisterUser - Cart Type->Normal - Product Type->Physical - Shipping Charges->Yes
    Given User landed to home page
    When User Navigate to product detail page as
      | productType |
      | product1    |
    And User update quantity as "1"
    Then User click on Add to Basket button
    Then User click on login link and navigate to login page
    Then User enters valid credential "TestUser1"
    Then User landed to registered home page
    Then User click on basket icon
    And  Verify user landed to cart page
    Then User click on checkout
    And User click delivery mode as
      | deliveryType    |
      | click & Collect |
    Then Search "store1" and select the address in map
    And User enter "contact" details
    And User click on Proceed To Payment
    Then User enter "3-DSecureVisa" card details
    And click on Pay now Button
    And Enter Valid OTP and click on confirm button
    Then User navigate to thankyou page
    And customer click on myAccount drop down icon for drop down items
    And customer click on sign out item from drop down
    And user clear cache and launch browser home page
