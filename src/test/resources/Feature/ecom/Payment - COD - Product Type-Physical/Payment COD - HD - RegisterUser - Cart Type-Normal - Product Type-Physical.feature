@Payment-COD @FailReRun1
@MaxEgypt @HomeCentreEgypt
@MaxOman @HomeCentreOman @CentrePointstoresOman
Feature: Payment COD - HD - RegisterUser - Cart Type->Normal - Product Type->Physical
  Verify E2E functional flows for the below scenario
  User 			- RegisterUser
  Cart Type		- Normal
  Product Type	- Physical
  Shipping Charges- Yes/Free
  Order Type 	- HD
  Payment Mode	- COD

  Applicable Concept
  Egypt_MAX			- Yes
  Egypt_HomeCentre 	- Yes
  Oman_MAX			- Yes
  Oman_HomeCentre 	- Yes

  @RegisterUser-HD-COD @ProductType-Physical @ShippingCharge-Yes/Free
  Scenario: Verify Payment COD - HD - RegisterUser - Cart Type->Normal - Product Type->Physical - Shipping Charges->Yes/Free
    Given User landed to home page
    When User Navigate to product detail page as
      | productType |
      | product1    |
    And User update quantity as "1"
    Then User click on Add to Basket button
    When User Navigate to product detail page as
      | productType |
      | product2    |
    And User update quantity as "2"
    Then User click on Add to Basket button
    When User Navigate to product detail page as
      | productType |
      | product3    |
    And User update quantity as "3"
    Then User click on Add to Basket button
    Then User click on login link and navigate to login page
    Then User enters valid credential "TestUser1"
    Then User landed to registered home page
    Then User click on basket icon
    And  Verify user landed to cart page
    Then User click on checkout
    And Verify user landed to checkout page
    And User click on Proceed To Payment
    Then User choose Cash on Delivery
    And click on Pay now Button
    Then User navigate to thankyou page
    And customer click on myAccount drop down icon for drop down items
    And customer click on sign out item from drop down
    And user clear cache and launch browser home page

  @SanityMAXEgypt @SanityMAXOman
  @RegisterUser-HD-COD @ProductType-Physical @ShippingCharge-Yes/Free
  Scenario: Verify Payment COD - HD - RegisterUser - Cart Type->Normal - Product Type->Physical - Shipping Charges->Yes/Free
    Given User landed to home page
    When User Navigate to product detail page as
      | productType |
      | product1    |
    And User update quantity as "1"
    Then User click on Add to Basket button
    Then User click on login link and navigate to login page
    Then User enters valid credential "TestUser1"
    Then User landed to registered home page
    Then User click on basket icon
    And  Verify user landed to cart page
    Then User click on checkout
    And Verify user landed to checkout page
    And User click on Proceed To Payment
    Then User choose Cash on Delivery
    And click on Pay now Button
    Then User navigate to thankyou page
    And customer click on myAccount drop down icon for drop down items
    And customer click on sign out item from drop down
    And user clear cache and launch browser home page