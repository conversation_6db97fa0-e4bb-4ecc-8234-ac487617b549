@Payment-CC_3D_Secure
@HomeCentreEgypt @HomeCentreOman
Feature: Payment CC_3D_Secure - HD - GuestUser - Cart Type->Mixed - Product Type->GDMS + Physical
  Verify E2E functional flows for the below scenario
  User 			- GuestUser
  Cart Type		- Mixed
  Product Type	- GDMS + Physical
  Shipping Charges- Yes/Free
  Order Type 	- HD
  Payment Mode	- CC_3D_Secure

  Applicable Concept
  Egypt_MAX			- No
  Egypt_HomeCentre 	- Yes
  Oman_MAX			- Yes
  Oman_HomeCentre 	- Yes
  Oman_Centrepoint  - Yes

  @GuestUser-HD-CC_3D_Secure @ProductType-GDMS+Physical @ShippingCharge-Free
  Scenario: Verify Payment CC_3D_Secure - HD - GuestUser - Cart Type->Mixed - Product Type->GDMS + Physical - Shipping Charges->Free
    Given User landed to home page
    When User Navigate to product detail page as
      | productType  |
      | gdmsproduct2 |
    And User update quantity as "1"
    Then User click on Add to Basket button
    When User Navigate to product detail page as
      | productType  |
      | gdmsproduct3 |
    And User update quantity as "3"
    Then User click on Add to Basket button
    When User Navigate to product detail page as
      | productType |
      | product1    |
    And User update quantity as "4"
    Then User click on Add to Basket button
    Then User click on basket icon
    And  Verify user landed to cart page
    Then User click on checkout
    Then User click on "Checkout As A Guest"
    And Verify user landed to checkout page
    And Verify product details in checkout page
#    And User click delivery mode as
#      | deliveryType  |
#      | Home Delivery |
    Then enter shipping "Address" details for guest user
    And User enter "contact" details
    Then User change address type as "Home" for guest user
    And User click on Proceed To Payment
    Then User enter "3-DSecureVisa" card details
    And click on Pay now Button
    And Enter Valid OTP and click on confirm button
    Then User navigate to thankyou page
    And user clear cache and launch browser home page

  @GuestUser-HD-CC_3D_Secure @ProductType-GDMS+Physical @ShippingCharge-Yes
  Scenario: Verify Payment CC_3D_Secure - HD - GuestUser - Cart Type->Mixed - Product Type->GDMS + Physical - Shipping Charges->Yes
    Given User landed to home page
    When User Navigate to product detail page as
      | productType  |
      | gdmsproduct1 |
    And User update quantity as "1"
    Then User click on Add to Basket button
    When User Navigate to product detail page as
      | productType |
      | product1    |
    And User update quantity as "1"
    Then User click on Add to Basket button
    Then User click on basket icon
    And  Verify user landed to cart page
    Then User click on checkout
    Then User click on "Checkout As A Guest"
    And Verify user landed to checkout page
    And Verify product details in checkout page
#    And User click delivery mode as
#      | deliveryType  |
#      | Home Delivery |
    Then enter shipping "Address" details for guest user
    And User enter "contact" details
    Then User change address type as "Home" for guest user
    And User click on Proceed To Payment
    Then User enter "3-DSecureVisa" card details
    And click on Pay now Button
    And Enter Valid OTP and click on confirm button
    Then User navigate to thankyou page
    And user clear cache and launch browser home page