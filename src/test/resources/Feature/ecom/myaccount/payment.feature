@My @MyAccountPayment1234 @RegressionSuite
Feature: MyAccount Payment
  As a logged in user, I can able to add, delete and choose default payment preferences

  Scenario Outline: Verify added Payment card details in Payment from my account
    Given user relaunch browser page
    When  User click on login link and navigate to login page
    Then  customer enter valid "<email>" and password "<password>"
    Then User landed to registered home page
    And  User click on myAccount drop icon
    Then user click on myAccount section from dropdown icon
    Then verify customer landed to myAccount page
    And user click on saved card from myAccount page


    @Auth
    Examples:
      | email                   | password     |
      | <EMAIL> | Munagala@123 |


#
#  Scenario: verify all UI components in payment page
#    Then delete all payment cards from payment list from my account payments
#    Then Verify all ui components in payment page
#    And Verify error message components in payment page
#
#  Scenario: Verify user having Payment in my list and delete Payment card
#    Then delete all payment cards from payment list from my account payments
#    Then verify customer landed to my account payment page
#    Then user click on add a card from add payment page
#    And user add new payment "visa" Card details in payment page
#    Then user click on save button for add payment card
#    And Verify saved payment details in payment list in myAccount
#
#
#  Scenario: Verify user able to delete Payment and validate confirmation message
#    Then click on delete payment card from payment list in Payment Page
#    And verify delete conformation message and validation in Payment page
#    Then User click on neverMind not to delete payment
#    And verify payment card not deleted from payment list in Payment Page
#    Then click on delete payment from payment list in Payment Page
#    And confirm deletion of payment from payment list in Payment Page
#    Then verify customer landed to my account payment page


  Scenario: Add multiple Payments and change as default payment card
    Then user click on add a card from add payment page
    And user add new payment "visa" Card details in payment page
    And user click on save button for add payment card
    And Verify saved payment details in payment list in myAccount
    Then user click on add second card from add payment page
    And user add new payment "visa" Card details in payment page
    Then user click on save button for add payment card
    And Verify saved payment details in payment list in myAccount
    Then change payment as default one from payment list in my account


  Scenario: Delete all Payments from the Payment list
    Then delete all payment cards from payment list from my account payments



  Scenario: sign out from my account dropdown
    And  customer click on myAccount drop down icon for drop down items
    And customer click on sign out item from drop down

  Scenario: clear cache and land to browser home page
    And user clear cache and launch browser home page