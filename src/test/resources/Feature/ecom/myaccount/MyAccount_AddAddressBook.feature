@MAA @MyAccountAddressBook @RegressionSuite
Feature: MyAccount AddressBook
  As a logged in user, I can able to add ,edit ,delete and choose default address

  Scenario Outline: Verify added address details in AddressBook from my account
    Given user relaunch browser page
    When  User click on login link and navigate to login page
    Then  customer enter valid "<email>" and password "<password>"
    Then User landed to registered home page
    And  User click on myAccount drop icon
    Then user click on myAccount section from dropdown icon
    Then verify customer landed to myAccount page
    Then customer click on add address section my account
    Then user click on Address Book link

    @Auth
    Examples:
      | email                        | password     |
      |<EMAIL> | Pass1word! |


  Scenario: Verify user having addresses in my address book then delete  all addresses
    Then delete all address from address list from my account addresses

  Scenario: Verify user able to add new address from  my account
    Then verify user landed to add address page
    Then user click on add address from add address page
    And user add new address details in add address page
    Then user click on save button for add address
    And Verify saved address details in address list in myAccount


  Scenario: Verify user able to delete address and validate confirmation message
    Then click on delete address from address book in Address Page
    And verify delete conformation message and validation in Address page
    Then User click on neverMind not to delete address
    And verify address not deleted from address book in my account
    Then click on delete address from address book in Address Page
    And confirm deletion of address from address book in my account
    Then verify user landed to add address page


  Scenario: Verify user able to edit address and validate updated details
    Then user click on add address from add address page
    And user add new address details in add address page
    And User click on save button in my account address book
    And Verify saved address details in address list in myAccount
    And update address from address book in my account
    And verify updated address details in address book in my account

#
  Scenario: Add multiple addresses and change as default address
    Given user click on add address link in Address page
    When user add new address details in add address page
    Then User click on confirm save button in my account address
    And verify number of address added to address book in my account
    Then change address as default one from address list in my account

  Scenario: Delete all address from the address list
    Then delete all address from address list from my account addresses

  Scenario: sign out from my account dropdown
    And customer click on myAccount drop down icon for drop down items
    And customer click on sign out item from drop down

  Scenario: clear cache and land to browser home page
    And user clear cache and launch browser home page

