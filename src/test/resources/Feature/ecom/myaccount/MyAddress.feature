Feature: My Account - Address
  1.Sign in as register user
  2.Delete all saved address and add new address
  3.Add 1 address and make it as default
  4.Add 4 more address

  @Testingfeature
  Scenario: Delete all saved address and add new address
    Given User landed to home page
    When User click on login link and navigate to login page
    Then User enters valid credential "Testingfeature"
    Then User landed to registered home page
    And User click on myAccount drop icon
    Then customer click on my addresses Item from my account dropdown

  @Testingfeature
  Scenario: Delete all address from the address list
    Then delete all address from address list from my account addresses

  @Testingfeature
  Scenario: Verify user able to add new first address from my account
    Then verify user landed to add address page
    Then user click on add address from add address page
    And user add new address details in add address page
    Then user click on save button for add address
    And Verify saved address details in address list in myAccount

  @Testingfeature
  Scenario: Verify user able to add new second address from my account
    Then verify user landed to add address page
    Then user click on add address from add address page
    And User add "UserDetails2" details in add address page
    Then user click on save button for add address
    And Verify saved address details in address list in myAccount

  @Testingfeature
  Scenario: Verify user able to add new third address from my account
    Then verify user landed to add address page
    Then user click on add address from add address page
    And User add "UserDetails3" details in add address page
    Then user click on save button for add address
    And Verify saved address details in address list in myAccount

  @Testingfeature
  Scenario: Verify user able to add new fourth address from my account
    Then verify user landed to add address page
    Then user click on add address from add address page
    And User add "UserDetails4" details in add address page
    Then user click on save button for add address
    And Verify saved address details in address list in myAccount

  @Testingfeature
  Scenario: Verify user able to edit address and validate updated details
    Then user click on add address from add address page
    And User add "UserDetails5" details in add address page
    And User click on save button in my account address book
    And Verify saved address details in address list in myAccount