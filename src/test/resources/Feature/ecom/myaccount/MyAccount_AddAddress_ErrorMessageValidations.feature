@AddressErrorMessages @RegressionSuite
Feature: My Add Address error message
  Verify all Error messages for add address form from my account

  Scenario Outline: Verify added address details in AddressBook from my account
    Given user relaunch browser page
    When  User click on login link and navigate to login page
    Then  customer enter valid "<email>" and password "<password>"
    Then User landed to registered home page


    @Auth
    Examples:
      | email                        | password     |
      |<EMAIL> | Pass1word! |

  Scenario: verify  user landed to add address from my account
    And  User click on myAccount drop icon
    Then user click on myAccount section from dropdown icon
    Then verify customer landed to myAccount page
  #And verify all ui components in myAccount page
    Then customer click on add address section my account
    And verify user landed to add address page
    Then user click on Address Book link



  Scenario: Verify user having address in my list and delete address
    Then delete all address from address list from my account addresses


  Scenario: Verify user click on save button without address details
    And user click on add address from add address page
    Then user click on save button for add address

#
#  Scenario: Verify validation messages from add address form
#    And verify error messages for add address form
#      | city | area | Building Name | Street Name | full name | mobile number |

  Scenario: sign out from my account dropdown
    And  customer click on myAccount drop down icon for drop down items
    And customer click on sign out item from drop down

  Scenario: clear cache and land to browser home page
    And user clear cache and launch browser home page