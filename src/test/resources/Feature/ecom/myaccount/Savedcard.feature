Feature: Saved Cards
  1.Sign in as register user
  2.Delete all saved cards and add new card details
  3.Add 1 card details and make it as default
  4. Add 2 card details

  @Savedcard
  Scenario: Verify my account page UI components
    Given User landed to home page
    When  User click on login link and navigate to login page
    Then User enters valid credential "Testingfeature"
    Then User landed to registered home page
    And  User click on myAccount drop icon
    Then customer click on saved cards Item from my account dropdown

  @Savedcard
  Scenario: Delete all Payments from the Payment list
    Then delete all payment cards from payment list from my account payments

  @Savedcard
  Scenario: Add First Payments and change as default payment card
    Then user click on add a card from add payment page
    And user add new payment "visa" Card details in payment page
    And user click on save button for add payment card
    And Verify saved payment details in payment list in myAccount
    Then change payment as default one from payment list in my account
