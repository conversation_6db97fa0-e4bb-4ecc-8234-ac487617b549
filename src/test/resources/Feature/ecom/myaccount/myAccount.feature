@MyAccountPageMax @RegressionSuite
Feature: MyAccount page
  MyAccount page navigation and UI components verification

  Scenario Outline: Verify my account page UI components
    Given user clear cache and launch browser home page
    When  User click on login link and navigate to login page
    Then  customer enter valid "<email>" and password "<password>"
    Then User landed to registered home page
    And  User click on myAccount drop icon
    Then user click on myAccount section from dropdown icon


    @Auth
    Examples:
      | email                        | password     |
      |<EMAIL> | Pass1word! |

  Scenario: Verify user landed to my account page
    Then verify customer landed to myAccount page
    #And verify all ui components in myAccount page


  Scenario:Verify user landed to my account profile page from my account page
    Then customer click on profile section in my account
    And verify customer landed to profile page
    Then customer click on myAccount top item


  Scenario:Verify user landed to my account myList page  from my account page
    Then customer click on myList section in my account
    And  Verify user landed to myList page
    Then customer click on myAccount top item

  Scenario:Verify user landed to my account reviews page  from my account page
    Then customer click on reviews section in my account
    Then verify user landed to reviews page in my account section
    Then customer click on myAccount top item

  Scenario:Verify user landed to my account payment page from my account page
    Then customer click on payment section in my account
    And verify customer landed to my account payment page
    Then customer click on myAccount top item

  Sc<PERSON>rio:Verify user landed to my account My Shukran card page from my account page
    Then customer click on my shukran card section in my account
    And verify customer landed to my shukran card page
    Then customer click on myAccount top item

  Scenario:Verify user landed to my account addresses page from my account page
    Then customer click on add address section my account
    And verify user landed to add address page
    Then customer click on myAccount top item

#  Scenario:Verify user landed to my account myCredit page from my account page
#    Then customer click on mycredit from drop down
#    And verify customer landed to myCrdit page
#    And customer navigate to previous page

  Scenario:Verify user landed to orders history from my account page
    Then customer click on orders section in my account
    And verify customer landed to orders section page
    Then customer click on myAccount top item

  Scenario: sign out from my account dropdown
    And  User click on myAccount drop icon
    And User navigate to Sign out from my account

  Scenario: clear cache and land to browser home page
    And user clear cache and launch browser home page












