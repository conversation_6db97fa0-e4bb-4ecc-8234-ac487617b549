@MaxRegression @HomeCentreRegression @Payment-GiftCard
Feature: Payment GiftCard - CnC - GuestUser - Cart Type->Normal - Product Type->Physical
  Verify E2E functional flows for the below scenario
    User 			- GuestUser
    Cart Type		- Normal
    Product Type	- Physical
    Shipping Charges- Free
    Order Type 	    - CnC
    Payment Mode	- GiftCard

    Applicable Concept
    MAX			- Yes
    HomeCentre 	- Yes

  @GuestUser-CnC-GiftCard @ProductType-Physical @ShippingCharge-Free
  Scenario: Verify Payment GiftCard - CnC - GuestUser - Cart Type->Normal - Product Type->Physical - Shipping Charges->Free
    Given User landed to home page
    When User Navigate to product detail page as
      | productType |
      | variant MEN |
    Then User click on Add to Basket button
    Then User click on basket icon
    And  Verify user landed to cart page
    Then User click on checkout
    Then User click on "Checkout As A Guest"
    And Verify user landed to checkout page
    And User click delivery mode as
      | deliveryType    |
      | click & Collect |
    Then User search and select "cairo" city
    And User enter "contact" details
    And User click on Proceed To Payment
    And User create "1000" worth Gift Card and enters gift card number and pin
#    And User create total worth Gift Card and enters gift card number and pin
    And click on Pay now Button
    And Check the Gift Card balance
    Then User navigate to thankyou page
    And user clear cache and launch browser home page