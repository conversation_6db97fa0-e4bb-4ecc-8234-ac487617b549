@Payment-CC
@HomeCentreEgypt @HomeCentreOman
Feature: Payment CC - HD - RegisterUser - Cart Type->Normal - Product Type->GDMS
  Verify E2E functional flows for the below scenario
  User 			- RegisterUser
  Cart Type		- Normal
  Product Type	- GDMS
  Shipping Charges- Yes/Free
  Order Type 	- HD
  Payment Mode	- CC

  Applicable Concept
  Egypt_MAX			- No
  Egypt_HomeCentre 	- Yes
  Oman_MAX			- No
  Oman_HomeCentre 	- No
  Oman_Centrepoint  - No

  @RegisterUser-HD-CC @ProductType-GDMS @ShippingCharge-Free
  Scenario: Verify Payment CC - HD - RegisterUser - Cart Type->Normal - Product Type->GDMS - Shipping Charges->Free
    Given User landed to home page
    When User Navigate to product detail page as
      | productType  |
      | gdmsproduct1 |
    And User update quantity as "1"
    Then User click on Add to Basket button
    When User Navigate to product detail page as
      | productType  |
      | gdmsproduct3 |
    And User update quantity as "2"
    Then User click on Add to Basket button
    Then User click on login link and navigate to login page
    Then User enters valid credential "TestUser1"
    Then User landed to registered home page
    Then User click on basket icon
    And  Verify user landed to cart page
    Then User click on checkout
    And Verify user landed to checkout page
    And User click on Proceed To Payment
    Then User enter "visa" card details
    And click on Pay now Button
    Then User navigate to thankyou page
    And customer click on myAccount drop down icon for drop down items
    And customer click on sign out item from drop down
    And user clear cache and launch browser home page

  @RegisterUser-HD-CC @ProductType-GDMS  @ShippingCharge-Yes
  Scenario: Verify Payment CC - HD - RegisterUser - Cart Type->Normal - Product Type->GDMS - Shipping Charges->Yes
    Given User landed to home page
    When User Navigate to product detail page as
      | productType  |
      | gdmsproduct1 |
    And User update quantity as "1"
    Then User click on Add to Basket button
    Then User click on login link and navigate to login page
    Then User enters valid credential "TestUser1"
    Then User landed to registered home page
    Then User click on basket icon
    And  Verify user landed to cart page
    Then User click on checkout
    And Verify user landed to checkout page
    And User click on Proceed To Payment
    Then User enter "visa" card details
    And click on Pay now Button
    Then User navigate to thankyou page
    And customer click on myAccount drop down icon for drop down items
    And customer click on sign out item from drop down
    And user clear cache and launch browser home page