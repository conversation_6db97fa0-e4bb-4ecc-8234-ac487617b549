@MyCredit @RegressionSuite
Feature: Register User place HomeDelivery order with MyCredit
  Verify functional flows in checkout page Home Delivery-My Credit-Register User

  Scenario:User place order with My Credit with HomeDelivery Option
    Given User landed to home page
    When User Navigate to product detail page as
      | productType |
      | variant MEN |
    And User update quantity as "2"
    Then User click on Add to Basket button
    Then User click on login link and navigate to login page
#    Then Customer enters valid user id and password
#      | email | <EMAIL> |
#      | password | Testing@12345 |
    Then User enters valid credential "TestUser1"
    Then User landed to registered home page
#    And  User click on myAccount drop icon
#    And customer click on my addresses Item from my account dropdown
#    Then delete all address from address list from my account addresses
#    Then verify user landed to add address page
#    Then user click on add address from add address page
#    And user add new address details in add address page
#    Then user click on save button for add address
#    And Verify saved address details in address list in myAccount
    Then User click on basket icon
    And  Verify user landed to cart page
    Then User click on checkout
    And Verify user landed to checkout page
    And User click on Proceed To Payment
    Then User select MyCredit as payment mode
    And click on Pay now Button
    Then User navigate to thankyou page
    And customer click on myAccount drop down icon for drop down items
    And customer click on sign out item from drop down
    And user clear cache and launch browser home page