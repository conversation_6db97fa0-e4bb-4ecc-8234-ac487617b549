@Payment-GiftCard
@MaxEgypt1 @HomeCentreEgypt1 @MaxOman1 @HomeCentreOman1
Feature: Payment GiftCard - CnC - RegisterUser - Cart Type->Normal - Product Type->Physical
  Verify E2E functional flows for the below scenario
  User 			- RegisterUser
  Cart Type		- Normal
  Product Type	- Physical
  Shipping Charges- Free
  Order Type 	- CnC
  Payment Mode	- GiftCard

  Applicable Concept
  Egypt_MAX			- Yes
  Egypt_HomeCentre 	- Yes
  Oman_MAX			- Yes
  Oman_HomeCentre 	- Yes

  @RegisterUser-CnC-GiftCard @ProductType-Physical @ShippingCharge-Free
  Scenario: Verify Payment GiftCard - CnC - RegisterUser - Cart Type->Normal - Product Type->Physical - Shipping Charges->Free
    Given User landed to home page
    When User Navigate to product detail page as
      | productType |
      | product1    |
    And User update quantity as "2"
    Then User click on Add to Basket button
    When User Navigate to product detail page as
      | productType |
      | product2    |
    And User update quantity as "2"
    Then User click on Add to Basket button
    When User Navigate to product detail page as
      | productType |
      | product3    |
    And User update quantity as "1"
    Then User click on Add to Basket button
    Then User click on login link and navigate to login page
    Then User enters valid credential "TestUser1"
    Then User landed to registered home page
    Then User click on basket icon
    And  Verify user landed to cart page
    Then User click on checkout
    And User click delivery mode as
      | deliveryType    |
      | click & Collect |
    Then Search "store1" and select the address in map
    And User enter "contact" details
    And User click on Proceed To Payment
    #And User create "1000" worth Gift Card and enters gift card number and pin
    And User create total worth Gift Card and enters gift card number and pin
    And click on Pay now Button
    And Check the Gift Card balance
    Then User navigate to thankyou page
    And user clear cache and launch browser home page
