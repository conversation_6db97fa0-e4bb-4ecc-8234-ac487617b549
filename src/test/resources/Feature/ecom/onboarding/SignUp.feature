@MaxSignUp @OnBoarding @RegressionSuite @Sanity @SmokeMax
Feature: Sign up
  Max SignUp application by providing mandatory details
  As Guest want to be a register member ,then user should complete signUp journey


  @BLCSignUp
  Scenario: As Max user, complete the SignUp journey and he can able to sign as well
    Given user clear cache and launch browser home page
    When user click on sign up link from header section of page
    And Verify customer landed to sign up screen
    Then enter random phone number
    Then User clicking on verify button
    And Fetch the otp from API call
    And Enter the OTP in verify your number popup
    And Click on Confirm button
    And customer enter valid emailId
    And customer enter first Name
    And customer enter last Name
    And customer enter full Name
    And  customer enter valid password
    And customer enter confirm password
    And choose date of birth in sign up
    And click on signUp btn for signUp
    Then User landed to registered home page
    And  User click on myAccount drop icon
    Then user click on myAccount section from dropdown icon
    Then verify customer landed to myAccount page
    #Then User verify email from mailinator

#Scenario: verify user landed to OTP page
#  Then verify customer landed to otp verify page
#  And verify all UI components in OTP page
#

  Scenario: Enter invalid otp and verify it error message
    Given user clear cache and launch browser home page
    When user click on sign up link from header section of page
    Then enter random phone number
    Then User clicking on verify button
    And enter invalid otp number for mobile verification
    And Click on Confirm button
    Then verify error message for otp message


  Sc<PERSON><PERSON>: Enter invalid otp and verify it error message and enters correct OTP and log in successful
    Given user clear cache and launch browser home page
    When user click on sign up link from header section of page
    And Verify customer landed to sign up screen
    Then enter random phone number
    Then User clicking on verify button
    And enter invalid otp number for mobile verification
    And Click on Confirm button
    Then verify error message for otp message
    And Fetch the otp from API call
    And Enter the OTP in verify your number popup
    And Click on Confirm button
    And customer enter valid emailId
    And customer enter first Name
    And customer enter last Name
    And customer enter full Name
    And choose date of birth in sign up
    And  customer enter valid password
    And customer enter confirm password
    And click on signUp btn for signUp


  Scenario: verify user landed to home page once user completed sign up
    Given user clear cache and launch browser home page
    When user click on sign up link from header section of page
    And Verify customer landed to sign up screen
    Then enter random phone number
    Then User clicking on verify button
    And Fetch the otp from API call
    And Enter the OTP in verify your number popup
    And Click on Confirm button
    And customer enter valid emailId
    And customer enter first Name
    And customer enter last Name
    And customer enter full Name
    And choose date of birth in sign up
    And  customer enter valid password
    And customer enter confirm password
    And click on signUp btn for signUp
    Then User landed to registered home page
    And  User click on myAccount drop icon
    Then user click on myAccount section from dropdown icon
    Then verify customer landed to myAccount page

#
#  Scenario: verify sign up details in profile page
#    And user click on myAccount section from dropdown icon
#    Then customer click on profile section in my account
#    And Verify user landed to profile page
#    Then Verify all ui components in profile page
#
#  Scenario: verify user able to sign out and re login with sign up credentials
#    And  User click on myAccount drop icon
#    And customer click on sign out item from drop down
#    And user click on sign in link from header
#    Then user entered sign up completed credentials to verify user able to sign in
#
#
#
#  Scenario: Sign up validation messages verification
#    Given user clear cache and launch browser home page
#    When user click on sign up link from header section of page
#    And Verify customer landed to sign up screen
#    And validate labels in sing up page
#    Then validate required error message for sign up page
#    And validate invalid error message in sign up page
#    Then validate show password and confirm show password
#
#
#
# edit
# phone and email duplicate
#
#
