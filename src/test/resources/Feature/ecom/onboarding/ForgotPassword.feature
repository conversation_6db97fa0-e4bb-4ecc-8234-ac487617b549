@RegressionSuite @BLCOnBoarding @ForgotPassword

Feature: Forgot password
  Verify user able to complete forgot password flow

  Scenario: verify user landed to sign in page from reset password page
    Given user clear cache and launch browser home page
    When User click on login link and navigate to login page
    Then verify user landed to signIn page
    Then customer click on forgot password link
    And verify all UI components in forgot password screen
    Then user enter valid email as "<EMAIL>"
    And verify all UI components in reset password page
    Then user click on sign in link from reset password page
    And verify user landed to signIn page

  Scenario: verify user able to logged in once user reset the password

    Given customer click on forgot password link
    And verify all UI components in forgot password screen
    Then user enter valid email as "<EMAIL>"
    And verify all UI components in reset password page
    And click on link to reset the password from mailinator site
    And enter valid email as "<EMAIL>" for reset password
    Then enter new password as "Test@12345"
    And user click on reset password button
    #Then verify customer landed to otp verify page
    And Customer lander home page
    And customer click on myAccount drop down icon for drop down items
    And customer click on sign out item from drop down



  Scenario: verify user able to click on resend in Reset password screen to reset password
    Given User click on login link and navigate to login page
    Then customer click on forgot password link
    And verify all UI components in forgot password screen
    Then user enter valid email as "<EMAIL>"
    Then user click on resend link in reset password page
    And verify all UI components in forgot password screen
    Then user enter valid email as "<EMAIL>"
    And click on link to reset the password from mailinator site
    And enter valid email as "<EMAIL>" for reset password
    Then enter new password as "Pass1word!"
    And user click on reset password button
    #Then verify customer landed to otp verify page
    And Customer lander home page
    And customer click on myAccount drop down icon for drop down items
    And customer click on sign out item from drop down





  Scenario: clear cache and land to browser home page
    And user clear cache and launch browser home page
