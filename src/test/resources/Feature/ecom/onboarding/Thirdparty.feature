@BLCOnBoarding @OnBoarding @RegressionSuite
Feature: Facebook login  User able to signIn with facebook account




  Scenario: User landed to sign in page
    Given user clear cache and launch browser home page
    When verify user landed to home page
    When user click on sign up link from header section of page
    And Verify customer landed to sign up screen
    Then verify customer sign up with facebook label in sign up page
    Then user click on not now from facebook page
    Then verify user landed to signIn page



    Scenario: Verify user navigate back to sign in page if user click on NotNow from facebook login page
      Given user clear cache and launch browser home page
      When verify user landed to home page
      When User click on login link and navigate to login page
      Then verify user landed to signIn page
      Then verify all UI components in sign in screen
      Then verify customer sign in with facebook label in sign in page
      Then user click on not now from facebook page
      Then verify user landed to signIn page


  Scenario Outline: Verify user sign in with valid facebook account from sign up
    Given user clear cache and launch browser home page
    When verify user landed to home page
    When user click on sign up link from header section of page
    And Verify customer landed to sign up screen
    Then verify customer sign up with facebook label in sign up page
    And user enter facebook valid "<email>" and password "<password>"
    Then User landed to registered home page
    And customer click on myAccount drop down icon for drop down items
    And customer click on sign out item from drop down



    Examples:
      | email                   | password   |
      | sachin_xpsrjk<PERSON>_<PERSON><PERSON><PERSON><PERSON>@tfbnw.net | landmark@123 |



  Scenario Outline: Verify user sign in with valid facebook account from sign in screen
    Given user clear cache and launch browser home page
    When verify user landed to home page
    Then  User click on login link and navigate to login page
    And verify all UI components in sign in screen
    Then verify customer sign in with facebook label in sign in page
    And user enter facebook valid "<email>" and password "<password>"
#    Then verify user landed to home page of lmg ecommerce site
    Then User landed to registered home page
    And customer click on myAccount drop down icon for drop down items
    And customer click on sign out item from drop down



    @loginValidation
    @Auth

    Examples:
      | email                   | password   |
      | <EMAIL> | landmark@123 |

