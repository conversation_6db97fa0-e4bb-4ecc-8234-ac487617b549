@MaxCheckOut @HomeDelivery-COD-GuestUser @RegressionSuite @Sanity @SmokeMax
Feature:Guest user place cash on Delivery order with HD
  Verify functional flows in checkout page Home Delivery-Cash on Delivery-Guest User

@GuestHDCOD
  Scenario: Verify user able to place cod order with HD option
    Given User landed to home page
    When User Navigate to product detail page as
      | productType |
      | variant MEN |
#      | variant WOMEN |
#      | variant KIDS  |
#      | standard      |
    Then User click on Add to Basket button
    Then User click on basket icon
    And  Verify user landed to cart page
    Then User click on checkout
    Then User click on "Checkout As A Guest"
    And Verify user landed to checkout page
    And Verify product details in checkout page
#    And User click delivery mode as
#      | deliveryType  |
#      | Home Delivery |
    Then enter shipping "Address" details for guest user
    And User enter "contact" details
    And User change address type as "Home" for guest user
    And User click on Proceed To Payment
    Then User choose Cash on Delivery
    And click on Pay now Button
    Then User navigate to thankyou page

  Scenario: clear cache and land to browser home page
    And user clear cache and launch browser home page