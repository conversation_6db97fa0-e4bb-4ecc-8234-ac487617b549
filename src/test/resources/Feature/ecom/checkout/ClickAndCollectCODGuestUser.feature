    @MaxCheckOut @ClickAndCollect-COD-GuestUser123 @RegressionSuite @Sanity
Feature: Checkout: Guest User-CNC-COD
  Verify Cash On Delivery visible in Disabled mode in Click & Collect-Guest User

  Scenario: Verify Cash On Delivery visible in Disabled mode in Click & Collect-Guest User
    Given User landed to home page
    When User Navigate to product detail page as
      | productType  |
      | gdmsproduct1 |
#      | variant WOMEN |
#      | variant KIDS  |
#      | standard      |
    Then User click on Add to Basket button
    Then User click on basket icon
    Then User click on checkout
    Then User click on "Checkout As A Guest"
    And Verify user landed to checkout page
    And Verify product details in checkout page
    And User click delivery mode as
      | deliveryType    |
      | click & Collect |
    Then User search and select "cairo" city
    And User enter "contact" details
    And User click on Proceed To Payment
    Then Verify Cash on Delivery option disabled

  Scenario: clear cache and land to browser home page
    And user clear cache and launch browser home page